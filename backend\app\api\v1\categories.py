"""
分类管理API
用于管理内容分类的CRUD操作
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Request
from pymongo.database import Database
from bson import ObjectId
from datetime import datetime

from app.core.security import get_current_user
from app.core.schemas.content_models import ContentCategory, CategoryTreeNode

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/categories",
    tags=["categories"],
    dependencies=[Depends(get_current_user)]
)


def get_db(request: Request) -> Database:
    """获取数据库连接"""
    return request.app.state.mongo_db


@router.get("/tree", response_model=List[CategoryTreeNode])
async def get_category_tree(
    include_count: bool = True,
    db: Database = Depends(get_db)
):
    """
    获取分类树结构
    
    Args:
        include_count: 是否包含每个分类的内容数量统计
    """
    try:
        logger.info("获取分类树结构")
        
        # 获取所有分类
        categories = list(db.content_categories.find({"is_active": True}).sort("sort_order", 1))
        
        # 转换ObjectId为字符串
        for category in categories:
            category["_id"] = str(category["_id"])
            if category.get("parent_id"):
                category["parent_id"] = str(category["parent_id"])
        
        # 如果需要统计内容数量
        content_counts = {}
        if include_count:
            # 聚合查询获取每个分类的内容数量
            pipeline = [
                {"$group": {"_id": "$metadata.category", "count": {"$sum": 1}}}
            ]
            count_results = list(db.competitor_content.aggregate(pipeline))
            content_counts = {result["_id"]: result["count"] for result in count_results if result["_id"]}
        
        # 构建树结构
        tree = build_category_tree(categories, content_counts)
        
        return tree
        
    except Exception as e:
        logger.error(f"获取分类树失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取分类树失败: {str(e)}")


def build_category_tree(categories: List[Dict], content_counts: Dict[str, int]) -> List[CategoryTreeNode]:
    """构建分类树结构"""
    # 创建分类映射
    category_map = {cat["_id"]: cat for cat in categories}
    
    # 找出根分类（没有父分类的）
    root_categories = [cat for cat in categories if not cat.get("parent_id")]
    
    def build_node(category: Dict) -> CategoryTreeNode:
        # 获取子分类
        children = [
            build_node(child_cat) 
            for child_cat in categories 
            if child_cat.get("parent_id") == category["_id"]
        ]
        
        # 获取内容数量
        content_count = content_counts.get(category["name"], 0)
        
        return CategoryTreeNode(
            id=category["_id"],
            name=category["name"],
            description=category.get("description"),
            color=category.get("color"),
            icon=category.get("icon"),
            children=children,
            content_count=content_count
        )
    
    return [build_node(cat) for cat in root_categories]


@router.post("", response_model=Dict[str, Any])
async def create_category(
    category_data: Dict[str, Any],
    db: Database = Depends(get_db)
):
    """创建分类"""
    try:
        logger.info(f"创建分类: {category_data.get('name')}")
        
        # 验证必要字段
        if not category_data.get("name"):
            raise HTTPException(status_code=400, detail="分类名称不能为空")
        
        # 检查分类名称是否已存在
        existing = db.content_categories.find_one({"name": category_data["name"]})
        if existing:
            raise HTTPException(status_code=400, detail="分类名称已存在")
        
        # 验证父分类ID
        if category_data.get("parent_id"):
            if not ObjectId.is_valid(category_data["parent_id"]):
                raise HTTPException(status_code=400, detail="无效的父分类ID格式")
            
            parent = db.content_categories.find_one({"_id": ObjectId(category_data["parent_id"])})
            if not parent:
                raise HTTPException(status_code=400, detail="父分类不存在")
            
            category_data["parent_id"] = ObjectId(category_data["parent_id"])
        
        # 设置默认值
        category_data.setdefault("is_active", True)
        category_data.setdefault("sort_order", 0)
        category_data["created_at"] = datetime.now()
        category_data["updated_at"] = datetime.now()
        
        # 插入数据库
        result = db.content_categories.insert_one(category_data)
        category_id = str(result.inserted_id)
        
        logger.info(f"分类创建成功: {category_id}")
        
        return {
            "category_id": category_id,
            "message": "分类创建成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建分类失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建分类失败: {str(e)}")


@router.put("/{category_id}")
async def update_category(
    category_id: str,
    update_data: Dict[str, Any],
    db: Database = Depends(get_db)
):
    """更新分类"""
    try:
        logger.info(f"更新分类: {category_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(category_id):
            raise HTTPException(status_code=400, detail="无效的分类ID格式")
        
        # 检查分类是否存在
        existing = db.content_categories.find_one({"_id": ObjectId(category_id)})
        if not existing:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 如果更新名称，检查是否重复
        if "name" in update_data and update_data["name"] != existing["name"]:
            name_exists = db.content_categories.find_one({
                "name": update_data["name"],
                "_id": {"$ne": ObjectId(category_id)}
            })
            if name_exists:
                raise HTTPException(status_code=400, detail="分类名称已存在")
        
        # 验证父分类ID
        if "parent_id" in update_data and update_data["parent_id"]:
            if not ObjectId.is_valid(update_data["parent_id"]):
                raise HTTPException(status_code=400, detail="无效的父分类ID格式")
            
            # 检查不能设置自己为父分类
            if update_data["parent_id"] == category_id:
                raise HTTPException(status_code=400, detail="不能设置自己为父分类")
            
            parent = db.content_categories.find_one({"_id": ObjectId(update_data["parent_id"])})
            if not parent:
                raise HTTPException(status_code=400, detail="父分类不存在")
            
            update_data["parent_id"] = ObjectId(update_data["parent_id"])
        
        # 添加更新时间
        update_data["updated_at"] = datetime.now()
        
        # 更新数据库
        result = db.content_categories.update_one(
            {"_id": ObjectId(category_id)},
            {"$set": update_data}
        )
        
        return {
            "message": "分类更新成功",
            "modified_count": result.modified_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新分类失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新分类失败: {str(e)}")


@router.delete("/{category_id}")
async def delete_category(
    category_id: str,
    force: bool = False,
    db: Database = Depends(get_db)
):
    """
    删除分类
    
    Args:
        category_id: 分类ID
        force: 是否强制删除（即使有子分类或内容）
    """
    try:
        logger.info(f"删除分类: {category_id}, force={force}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(category_id):
            raise HTTPException(status_code=400, detail="无效的分类ID格式")
        
        # 检查分类是否存在
        category = db.content_categories.find_one({"_id": ObjectId(category_id)})
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 检查是否有子分类
        child_count = db.content_categories.count_documents({"parent_id": ObjectId(category_id)})
        if child_count > 0 and not force:
            raise HTTPException(
                status_code=400, 
                detail=f"该分类下有 {child_count} 个子分类，请先删除子分类或使用强制删除"
            )
        
        # 检查是否有内容使用该分类
        content_count = db.competitor_content.count_documents({"metadata.category": category["name"]})
        if content_count > 0 and not force:
            raise HTTPException(
                status_code=400,
                detail=f"该分类下有 {content_count} 个内容，请先移动内容或使用强制删除"
            )
        
        if force:
            # 强制删除：删除所有子分类和相关内容的分类标记
            if child_count > 0:
                db.content_categories.delete_many({"parent_id": ObjectId(category_id)})
                logger.info(f"已删除 {child_count} 个子分类")
            
            if content_count > 0:
                # 清除内容的分类标记
                db.competitor_content.update_many(
                    {"metadata.category": category["name"]},
                    {"$unset": {"metadata.category": ""}}
                )
                logger.info(f"已清除 {content_count} 个内容的分类标记")
        
        # 删除分类
        result = db.content_categories.delete_one({"_id": ObjectId(category_id)})
        
        return {
            "message": "分类删除成功",
            "deleted_count": result.deleted_count,
            "child_categories_deleted": child_count if force else 0,
            "content_updated": content_count if force else 0
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除分类失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除分类失败: {str(e)}")


@router.get("", response_model=List[Dict[str, Any]])
async def get_categories(
    parent_id: Optional[str] = None,
    include_inactive: bool = False,
    db: Database = Depends(get_db)
):
    """
    获取分类列表
    
    Args:
        parent_id: 父分类ID，为空时获取根分类
        include_inactive: 是否包含未激活的分类
    """
    try:
        logger.info(f"获取分类列表: parent_id={parent_id}")
        
        # 构建查询条件
        query = {}
        
        if parent_id:
            if not ObjectId.is_valid(parent_id):
                raise HTTPException(status_code=400, detail="无效的父分类ID格式")
            query["parent_id"] = ObjectId(parent_id)
        else:
            query["parent_id"] = {"$exists": False}
        
        if not include_inactive:
            query["is_active"] = True
        
        # 查询分类
        categories = list(db.content_categories.find(query).sort("sort_order", 1))
        
        # 转换ObjectId为字符串
        for category in categories:
            category["_id"] = str(category["_id"])
            if category.get("parent_id"):
                category["parent_id"] = str(category["parent_id"])
        
        return categories
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分类列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")


@router.post("/{category_id}/move")
async def move_category(
    category_id: str,
    move_data: Dict[str, Any],
    db: Database = Depends(get_db)
):
    """
    移动分类到新的父分类下
    
    Args:
        category_id: 要移动的分类ID
        move_data: 移动数据，包含 new_parent_id 和 new_sort_order
    """
    try:
        logger.info(f"移动分类: {category_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(category_id):
            raise HTTPException(status_code=400, detail="无效的分类ID格式")
        
        # 检查分类是否存在
        category = db.content_categories.find_one({"_id": ObjectId(category_id)})
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        new_parent_id = move_data.get("new_parent_id")
        new_sort_order = move_data.get("new_sort_order", 0)
        
        update_data = {
            "sort_order": new_sort_order,
            "updated_at": datetime.now()
        }
        
        if new_parent_id:
            if not ObjectId.is_valid(new_parent_id):
                raise HTTPException(status_code=400, detail="无效的新父分类ID格式")
            
            # 检查不能移动到自己或自己的子分类下
            if new_parent_id == category_id:
                raise HTTPException(status_code=400, detail="不能移动到自己下面")
            
            # TODO: 检查是否移动到自己的子分类下（需要递归检查）
            
            new_parent = db.content_categories.find_one({"_id": ObjectId(new_parent_id)})
            if not new_parent:
                raise HTTPException(status_code=400, detail="新父分类不存在")
            
            update_data["parent_id"] = ObjectId(new_parent_id)
        else:
            # 移动到根级别
            update_data["$unset"] = {"parent_id": ""}
        
        # 更新分类
        result = db.content_categories.update_one(
            {"_id": ObjectId(category_id)},
            {"$set": update_data} if "$unset" not in update_data else {
                "$set": {k: v for k, v in update_data.items() if k != "$unset"},
                "$unset": update_data["$unset"]
            }
        )
        
        return {
            "message": "分类移动成功",
            "modified_count": result.modified_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移动分类失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"移动分类失败: {str(e)}")
