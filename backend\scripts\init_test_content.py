#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试内容数据初始化脚本
用于创建包含不同时长视频的测试数据
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# from app.config.database import DatabaseConfig

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestContentInitializer:
    def __init__(self):
        """初始化数据库连接"""
        # 直接使用MongoDB连接
        mongodb_url = "mongodb://***************:27017"
        mongodb_name = "thunderhub"
        self.client = MongoClient(mongodb_url)
        self.db = self.client[mongodb_name]
    
    def create_test_content(self):
        """创建测试内容数据"""
        logger.info("开始创建测试内容数据...")
        
        # 测试内容数据
        test_contents = [
            # 短视频（小于35秒）
            {
                "title": "短视频测试 - 产品介绍",
                "description": "这是一个短视频测试内容，时长30秒",
                "platform": "douyin",
                "original_url": "https://douyin.com/video/test1",
                "author": {
                    "name": "测试账号1",
                    "channel_id": "test_channel_1",
                    "avatar_url": "https://example.com/avatar1.jpg"
                },
                "content_type": "video",
                "file_info": {
                    "local_path": "/test/videos/short_video_1.mp4",
                    "file_size": 5242880,  # 5MB
                    "file_format": "mp4",
                    "duration": 30,  # 30秒
                    "resolution": "1080x1920",
                    "hash": "hash_short_1"
                },
                "metadata": {
                    "tags": ["产品", "介绍", "短视频"],
                    "category": "产品展示",
                    "language": "zh-CN",
                    "publish_date": datetime.now() - timedelta(days=1),
                    "view_count": 15000,
                    "like_count": 800,
                    "comment_count": 50
                },
                "download_info": {
                    "download_date": datetime.now(),
                    "download_source": "douyin-downloader",
                    "quality": "1080p",
                    "status": "downloaded"
                },
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "created_by": "test_user"
            },
            {
                "title": "快速教程 - 25秒学会技巧",
                "description": "快速教程视频，25秒内学会实用技巧",
                "platform": "tiktok",
                "original_url": "https://tiktok.com/video/test2",
                "author": {
                    "name": "技巧达人",
                    "channel_id": "skill_master",
                    "avatar_url": "https://example.com/avatar2.jpg"
                },
                "content_type": "video",
                "file_info": {
                    "local_path": "/test/videos/short_video_2.mp4",
                    "file_size": 4194304,  # 4MB
                    "file_format": "mp4",
                    "duration": 25,  # 25秒
                    "resolution": "1080x1920",
                    "hash": "hash_short_2"
                },
                "metadata": {
                    "tags": ["教程", "技巧", "快速"],
                    "category": "教育内容",
                    "language": "zh-CN",
                    "publish_date": datetime.now() - timedelta(days=2),
                    "view_count": 8500,
                    "like_count": 420,
                    "comment_count": 35
                },
                "download_info": {
                    "download_date": datetime.now(),
                    "download_source": "tiktok-downloader",
                    "quality": "1080p",
                    "status": "downloaded"
                },
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "created_by": "test_user"
            },
            # 长视频（超过35秒）
            {
                "title": "深度解析 - 完整教程视频",
                "description": "这是一个长视频教程，详细讲解产品使用方法",
                "platform": "youtube",
                "original_url": "https://youtube.com/watch?v=test3",
                "author": {
                    "name": "专业讲师",
                    "channel_id": "pro_teacher",
                    "avatar_url": "https://example.com/avatar3.jpg"
                },
                "content_type": "video",
                "file_info": {
                    "local_path": "/test/videos/long_video_1.mp4",
                    "file_size": 52428800,  # 50MB
                    "file_format": "mp4",
                    "duration": 180,  # 3分钟
                    "resolution": "1920x1080",
                    "hash": "hash_long_1"
                },
                "metadata": {
                    "tags": ["教程", "深度", "完整"],
                    "category": "教育内容",
                    "language": "zh-CN",
                    "publish_date": datetime.now() - timedelta(days=3),
                    "view_count": 25000,
                    "like_count": 1200,
                    "comment_count": 150
                },
                "download_info": {
                    "download_date": datetime.now(),
                    "download_source": "yt-dlp",
                    "quality": "1080p",
                    "status": "downloaded"
                },
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "created_by": "test_user"
            },
            {
                "title": "产品评测 - 详细分析报告",
                "description": "详细的产品评测视频，包含多个测试环节",
                "platform": "youtube",
                "original_url": "https://youtube.com/watch?v=test4",
                "author": {
                    "name": "评测专家",
                    "channel_id": "review_expert",
                    "avatar_url": "https://example.com/avatar4.jpg"
                },
                "content_type": "video",
                "file_info": {
                    "local_path": "/test/videos/long_video_2.mp4",
                    "file_size": 104857600,  # 100MB
                    "file_format": "mp4",
                    "duration": 420,  # 7分钟
                    "resolution": "1920x1080",
                    "hash": "hash_long_2"
                },
                "metadata": {
                    "tags": ["评测", "分析", "详细"],
                    "category": "产品评测",
                    "language": "zh-CN",
                    "publish_date": datetime.now() - timedelta(days=4),
                    "view_count": 35000,
                    "like_count": 1800,
                    "comment_count": 200
                },
                "download_info": {
                    "download_date": datetime.now(),
                    "download_source": "yt-dlp",
                    "quality": "1080p",
                    "status": "downloaded"
                },
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "created_by": "test_user"
            },
            # 边界测试（正好35秒）
            {
                "title": "边界测试 - 正好35秒视频",
                "description": "这是一个正好35秒的视频，用于测试边界条件",
                "platform": "instagram",
                "original_url": "https://instagram.com/p/test5",
                "author": {
                    "name": "边界测试员",
                    "channel_id": "boundary_tester",
                    "avatar_url": "https://example.com/avatar5.jpg"
                },
                "content_type": "video",
                "file_info": {
                    "local_path": "/test/videos/boundary_video.mp4",
                    "file_size": 7340032,  # 7MB
                    "file_format": "mp4",
                    "duration": 35,  # 正好35秒
                    "resolution": "1080x1080",
                    "hash": "hash_boundary"
                },
                "metadata": {
                    "tags": ["边界", "测试", "35秒"],
                    "category": "测试内容",
                    "language": "zh-CN",
                    "publish_date": datetime.now() - timedelta(days=5),
                    "view_count": 5000,
                    "like_count": 250,
                    "comment_count": 20
                },
                "download_info": {
                    "download_date": datetime.now(),
                    "download_source": "instagram-downloader",
                    "quality": "1080p",
                    "status": "downloaded"
                },
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "created_by": "test_user"
            }
        ]
        
        # 插入测试数据
        try:
            # 清除现有测试数据
            self.db.competitor_content.delete_many({"created_by": "test_user"})
            logger.info("清除现有测试数据")
            
            # 插入新的测试数据
            result = self.db.competitor_content.insert_many(test_contents)
            logger.info(f"成功创建 {len(result.inserted_ids)} 个测试内容")
            
            # 显示统计信息
            total_count = self.db.competitor_content.count_documents({})
            video_count = self.db.competitor_content.count_documents({"content_type": "video"})
            short_video_count = self.db.competitor_content.count_documents({
                "content_type": "video",
                "file_info.duration": {"$lt": 35}
            })
            long_video_count = self.db.competitor_content.count_documents({
                "content_type": "video", 
                "file_info.duration": {"$gt": 35}
            })
            
            logger.info(f"数据库统计:")
            logger.info(f"  总内容数: {total_count}")
            logger.info(f"  视频数: {video_count}")
            logger.info(f"  短视频数 (<35秒): {short_video_count}")
            logger.info(f"  长视频数 (>35秒): {long_video_count}")
            
        except Exception as e:
            logger.error(f"创建测试数据失败: {str(e)}")
            raise
    
    def run(self):
        """运行初始化"""
        try:
            logger.info("开始初始化测试内容数据...")
            self.create_test_content()
            logger.info("测试内容数据初始化完成！")
            
        except Exception as e:
            logger.error(f"初始化失败: {str(e)}")
            raise
        finally:
            if hasattr(self, 'client'):
                self.client.close()
                logger.info("数据库连接已关闭")

def main():
    """主函数"""
    try:
        initializer = TestContentInitializer()
        initializer.run()
        print("\n✅ 测试内容数据初始化成功！")
        print("\n现在可以在内容管理页面看到:")
        print("- 2个短视频 (< 35秒)")
        print("- 2个长视频 (> 35秒)")
        print("- 1个边界视频 (= 35秒)")
        
    except Exception as e:
        print(f"\n❌ 初始化失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
