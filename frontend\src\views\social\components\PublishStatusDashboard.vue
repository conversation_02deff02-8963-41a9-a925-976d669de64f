<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>任务状态</span>
            </div>
          </template>
          <el-progress 
            type="dashboard" 
            :percentage="taskStatus.percentage"
            :status="taskStatus.status"
          />
          <div class="status-text">{{ taskStatus.text }}</div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>设备资源</span>
            </div>
          </template>
          <el-progress 
            :percentage="deviceUsage.cpu" 
            :format="formatCpuUsage"
          />
          <el-progress 
            :percentage="deviceUsage.memory" 
            status="warning"
          />
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>发布进度</span>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(log, index) in progressLogs"
              :key="index"
              :timestamp="log.time"
            >
              {{ log.message }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

const taskStatus = reactive({
  percentage: 60,
  status: 'success',
  text: '6/10 任务已完成'
})

const deviceUsage = reactive({
  cpu: 45,
  memory: 70
})

const progressLogs = reactive([
  { time: '2025-04-14 14:30', message: '任务1 发布成功' },
  { time: '2025-04-14 14:25', message: '任务2 正在发布' },
  { time: '2025-04-14 14:20', message: '任务3 等待执行' }
])

const formatCpuUsage = () => {
  return `CPU: ${deviceUsage.cpu}%`
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}
.card-header {
  font-weight: bold;
}
.status-text {
  text-align: center;
  margin-top: 10px;
}
</style>