import axios from 'axios'
import { ElMessage } from 'element-plus'

const baseURL = import.meta.env.VITE_API_URL
console.log('API基础URL:', baseURL)

const service = axios.create({
  baseURL,
  timeout: 300000, // 增加超时时间到5分钟，适应长时间任务
  withCredentials: true
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${token}`
    }

    console.log(`发送请求到: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`)
    console.log('请求配置:', {
      headers: config.headers,
      params: config.params,
      data: config.data
    })
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    console.log('完整响应:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data,
      config: {
        url: response.config.url,
        method: response.config.method,
        params: response.config.params
      }
    })

    // 确保返回统一格式
    if (response.status === 200) {
      return {
        success: true,
        data: response.data
      }
    }

    throw new Error(`请求失败: ${response.status}`)
  },
  error => {
    // 检查是否为CORS错误
    if (error.message && error.message.includes('Network Error')) {
      console.error('CORS或网络错误:', error.message)
      ElMessage.error('网络连接错误，可能是CORS策略问题，请检查后端服务是否正常运行')
    } else {
      console.error('请求失败详情:', {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        data: error.response?.data
      })

      // 根据错误类型显示不同消息
      if (error.response?.status === 401) {
        ElMessage.error('认证失败，请重新登录')
        // 可以在这里添加重定向到登录页面的逻辑
      } else if (error.response?.status === 403) {
        ElMessage.error('没有权限访问该资源')
      } else if (error.response?.status === 404) {
        ElMessage.error('请求的资源不存在')
      } else if (error.response?.status === 500) {
        ElMessage.error('服务器内部错误，请稍后再试')
      } else {
        ElMessage.error(error.response?.data?.message || error.message || '请求失败')
      }
    }

    // 对于设备列表API，返回空数组而不是拒绝Promise
    if (error.config?.url?.includes('/api/devices')) {
      console.warn('设备API错误，返回空数组')
      return Promise.resolve({ success: false, data: [] })
    }

    return Promise.reject(error)
  }
)

export default service