import socket
import sys
from subprocess import Popen
import os

def is_port_in_use(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def find_available_port(start_port=8000, max_attempts=100):
    port = start_port
    while port <= start_port + max_attempts:
        if not is_port_in_use(port):
            return port
        port += 1
    raise RuntimeError(f"No available port found after {max_attempts} attempts")

def main():
    # 设置 Python 路径
    backend_path = os.path.dirname(os.path.abspath(__file__))  # /
    os.environ['PYTHONPATH'] = os.pathsep.join([backend_path, os.environ.get('PYTHONPATH', '')])
    sys.path.insert(0, backend_path)
    
    # 验证路径设置
    print("=== Python路径调试信息 ===")
    print(f"项目根目录: {backend_path}")
    print(f"后端目录: {backend_path}")
    print(f"系统PATH: {os.environ.get('PATH', '')}")
    print(f"Python路径: {sys.path}")
    
    
    
    # 查找可用端口
    port = find_available_port()
    
    # 启动 uvicorn
    reload = os.getenv("ENV") == "development"
    # 使用虚拟环境Python解释器运行uvicorn模块
    python_exe = sys.executable
    cmd = [
        python_exe,
        "-m",
        "uvicorn",
        "app.main:app",  # 使用相对路径
        "--host", "0.0.0.0",
        f"--port={port}",
        "--reload",
        "--workers", "1",  # 避免多进程问题
        "--no-access-log",  # 减少日志干扰
        "--app-dir", backend_path  # 明确指定应用目录
    ]
    if reload:
        cmd.append("--reload")
    print(f"Starting server on port {port}")
    process = Popen(cmd, shell=False, stdout=sys.stdout, stderr=sys.stderr)  # 捕获子进程输出
    print(f"Server started on port {port}")
    
    # 等待子进程
    return_code = process.wait()
    return return_code

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)