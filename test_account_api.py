#!/usr/bin/env python3
"""
测试账号API的last_updated_date字段功能
"""

import json
import requests
from datetime import datetime, timedelta

# API基础URL
BASE_URL = "http://localhost:8000"

def test_account_creation_with_expiry_date():
    """测试创建带有过期日期的账号"""
    
    # 准备测试数据
    test_account = {
        "username": "test_expiry_account",
        "password": "test123",
        "display_name": "测试过期账号",
        "platform_id": "youtube",
        "core_service_id": "test_core",
        "status": "active",
        "description": "用于测试过期功能的账号",
        "last_updated_date": (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d")
    }
    
    print("测试数据:")
    print(json.dumps(test_account, indent=2, ensure_ascii=False))
    
    try:
        # 发送创建账号请求
        response = requests.post(
            f"{BASE_URL}/api/v1/social/accounts",
            json=test_account,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            account_data = response.json()
            print("\n✅ 账号创建成功!")
            print(f"账号ID: {account_data.get('id')}")
            print(f"更新日期: {account_data.get('last_updated_date')}")
            return account_data.get('id')
        else:
            print(f"\n❌ 账号创建失败: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("\n⚠️  无法连接到后端服务，请确保后端服务正在运行")
        return None
    except Exception as e:
        print(f"\n❌ 请求失败: {str(e)}")
        return None

def test_account_update_with_expiry_date(account_id):
    """测试更新账号的过期日期"""
    
    if not account_id:
        print("❌ 没有有效的账号ID，跳过更新测试")
        return
    
    # 准备更新数据
    update_data = {
        "last_updated_date": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    }
    
    print(f"\n更新账号 {account_id} 的过期日期:")
    print(json.dumps(update_data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.put(
            f"{BASE_URL}/api/v1/social/accounts/{account_id}",
            json=update_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("\n✅ 账号更新成功!")
        else:
            print(f"\n❌ 账号更新失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("\n⚠️  无法连接到后端服务")
    except Exception as e:
        print(f"\n❌ 更新请求失败: {str(e)}")

def test_account_list_with_expiry_info():
    """测试获取账号列表，验证过期信息"""
    
    print("\n获取账号列表，检查过期信息:")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/social/accounts")
        
        print(f"\n响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            accounts = data.get('data', [])
            
            print(f"\n✅ 获取到 {len(accounts)} 个账号")
            
            for account in accounts:
                print(f"\n账号: {account.get('username')}")
                print(f"  显示名称: {account.get('display_name')}")
                print(f"  更新日期: {account.get('last_updated_date', '未设置')}")
                
                # 计算过期状态
                last_updated = account.get('last_updated_date')
                if last_updated:
                    try:
                        update_date = datetime.strptime(last_updated, "%Y-%m-%d")
                        days_diff = (update_date - datetime.now()).days
                        
                        if days_diff < 0:
                            status = f"已过期 {abs(days_diff)} 天"
                        elif days_diff == 0:
                            status = "今天过期"
                        elif days_diff <= 3:
                            status = f"{days_diff}天后过期 (警告)"
                        else:
                            status = f"{days_diff}天后过期"
                            
                        print(f"  过期状态: {status}")
                    except ValueError:
                        print(f"  过期状态: 日期格式错误")
                else:
                    print(f"  过期状态: 未设置更新日期")
        else:
            print(f"\n❌ 获取账号列表失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("\n⚠️  无法连接到后端服务")
    except Exception as e:
        print(f"\n❌ 获取列表失败: {str(e)}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("账号过期日期功能测试")
    print("=" * 60)
    
    # 测试创建账号
    account_id = test_account_creation_with_expiry_date()
    
    # 测试更新账号
    test_account_update_with_expiry_date(account_id)
    
    # 测试获取账号列表
    test_account_list_with_expiry_info()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
