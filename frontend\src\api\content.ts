/**
 * 文件管理模块API接口
 */

import request from '@/utils/request'

// 通用请求函数，适配现有的request工具
const apiRequest = async <T = any>(config: any): Promise<T> => {
  const response = await request(config)
  return response.data
}

// 数据类型定义
export interface AuthorInfo {
  name: string
  channel_id?: string
  avatar_url?: string
}

export interface FileInfo {
  local_path: string
  file_size: number
  file_format: string
  duration?: number
  resolution?: string
  hash: string
}

export interface ContentMetadata {
  tags: string[]
  category?: string
  language?: string
  publish_date?: string
  view_count?: number
  like_count?: number
  comment_count?: number
}

export interface DownloadInfo {
  download_date: string
  download_source: string
  quality?: string
  status: string
}

export interface ContentAnalysis {
  sentiment?: string
  keywords: string[]
  topics: string[]
  engagement_rate?: number
}

export interface CompetitorContent {
  _id?: string
  title: string
  description?: string
  platform: string
  original_url: string
  author: AuthorInfo
  content_type: string
  file_info: FileInfo
  metadata: ContentMetadata
  download_info: DownloadInfo
  analysis?: ContentAnalysis
  created_at: string
  updated_at: string
  created_by: string
}

export interface ContentListResponse {
  items: CompetitorContent[]
  total: number
  page: number
  limit: number
  has_next: boolean
}

export interface ContentCategory {
  _id?: string
  name: string
  description?: string
  parent_id?: string
  color?: string
  icon?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CategoryTreeNode {
  id: string
  name: string
  description?: string
  color?: string
  icon?: string
  children: CategoryTreeNode[]
  content_count: number
}

export interface DownloadConfig {
  quality: string
  format: string
  include_subtitles: boolean
  include_thumbnail: boolean
  include_metadata: boolean
}

export interface FilterConfig {
  date_range?: {
    start: string
    end: string
  }
  min_duration?: number
  max_duration?: number
  keywords: string[]
  exclude_keywords: string[]
}

export interface TaskProgress {
  total_items: number
  completed_items: number
  failed_items: number
  current_item?: string
  percentage: number
}

export interface TaskResult {
  success_count: number
  failed_count: number
  total_size: number
  failed_urls: string[]
}

export interface DownloadTask {
  _id?: string
  task_name: string
  task_type: string
  source_urls: string[]
  target_platform: string
  download_config: DownloadConfig
  filter_config?: FilterConfig
  progress: TaskProgress
  status: string
  result?: TaskResult
  created_at: string
  updated_at: string
  created_by: string
}

// 内容管理API

// 获取内容列表
export const getContentList = (params: {
  page?: number
  limit?: number
  platform?: string
  category?: string
  tags?: string
  search?: string
  content_type?: string
}) => {
  return apiRequest<ContentListResponse>({
    url: '/api/v1/content/list',
    method: 'get',
    params
  })
}

// 获取内容详情
export const getContentDetail = (contentId: string) => {
  return apiRequest<CompetitorContent>({
    url: `/api/v1/content/${contentId}`,
    method: 'get'
  })
}

// 更新内容
export const updateContent = (contentId: string, data: Partial<CompetitorContent>) => {
  return apiRequest({
    url: `/api/v1/content/${contentId}`,
    method: 'put',
    data
  })
}

// 删除内容
export const deleteContent = (contentId: string) => {
  return apiRequest({
    url: `/api/v1/content/${contentId}`,
    method: 'delete'
  })
}

// 批量操作
export const batchOperation = (data: {
  action: 'delete' | 'categorize' | 'tag'
  content_ids: string[]
  data?: any
}) => {
  return apiRequest({
    url: '/api/v1/content/batch',
    method: 'post',
    data
  })
}

// 获取内容统计
export const getContentStats = () => {
  return apiRequest({
    url: '/api/v1/content/stats/summary',
    method: 'get'
  })
}

// 分类管理API

// 获取分类树
export const getCategoryTree = (includeCount = true) => {
  return apiRequest<CategoryTreeNode[]>({
    url: '/api/v1/categories/tree',
    method: 'get',
    params: { include_count: includeCount }
  })
}

// 创建分类
export const createCategory = (data: Partial<ContentCategory>) => {
  return apiRequest({
    url: '/api/v1/categories',
    method: 'post',
    data
  })
}

// 更新分类
export const updateCategory = (categoryId: string, data: Partial<ContentCategory>) => {
  return apiRequest({
    url: `/api/v1/categories/${categoryId}`,
    method: 'put',
    data
  })
}

// 删除分类
export const deleteCategory = (categoryId: string, force = false) => {
  return apiRequest({
    url: `/api/v1/categories/${categoryId}`,
    method: 'delete',
    params: { force }
  })
}

// 移动分类
export const moveCategory = (categoryId: string, data: {
  new_parent_id?: string
  new_sort_order: number
}) => {
  return apiRequest({
    url: `/api/v1/categories/${categoryId}/move`,
    method: 'post',
    data
  })
}

// 获取分类列表
export const getCategories = (params?: {
  parent_id?: string
  include_inactive?: boolean
}) => {
  return apiRequest<ContentCategory[]>({
    url: '/api/v1/categories',
    method: 'get',
    params
  })
}

// 下载任务API

// 创建下载任务
export const createDownloadTask = (data: {
  task_name: string
  task_type: string
  source_urls: string[]
  target_platform: string
  download_config?: Partial<DownloadConfig>
  filter_config?: Partial<FilterConfig>
}) => {
  return apiRequest({
    url: '/api/v1/download/tasks',
    method: 'post',
    data
  })
}

// 获取下载任务列表
export const getDownloadTasks = (params?: {
  page?: number
  limit?: number
  status?: string
  task_type?: string
}) => {
  return apiRequest<DownloadTask[]>({
    url: '/api/v1/download/tasks',
    method: 'get',
    params
  })
}

// 获取下载任务详情
export const getDownloadTaskDetail = (taskId: string) => {
  return apiRequest<DownloadTask>({
    url: `/api/v1/download/tasks/${taskId}`,
    method: 'get'
  })
}

// 取消下载任务
export const cancelDownloadTask = (taskId: string) => {
  return apiRequest({
    url: `/api/v1/download/tasks/${taskId}/cancel`,
    method: 'post'
  })
}

// 重试下载任务
export const retryDownloadTask = (taskId: string) => {
  return apiRequest({
    url: `/api/v1/download/tasks/${taskId}/retry`,
    method: 'post'
  })
}

// 对标账号管理API

// 对标账号数据类型
export interface BenchmarkAccountData {
  followers?: number
  following?: number
  posts_count?: number
  avg_views?: number
  avg_likes?: number
  engagement_rate?: number
  last_post_date?: string
  growth_rate?: number
}

export interface BenchmarkAccount {
  _id?: string
  our_account_id: string
  our_account_info?: {
    id: string
    username: string
    display_name: string
    platform_id: string
    status: string
  }
  platform: string
  account_name: string
  account_url: string
  benchmark_type: 'original' | 'recreate' | 'repost'
  description?: string
  avatar_url?: string
  account_data: BenchmarkAccountData
  tags: string[]
  priority: number
  status: 'active' | 'inactive' | 'monitoring'
  notes?: string
  created_at: string
  updated_at: string
  created_by: string
}

export interface BenchmarkAccountListResponse {
  items: BenchmarkAccount[]
  total: number
  page: number
  limit: number
  has_next: boolean
}

export interface BenchmarkAccountStats {
  total_count: number
  by_platform: Record<string, number>
  by_type: Record<string, number>
  by_status: Record<string, number>
  top_performers: BenchmarkAccount[]
}

// 获取对标账号列表
export const getBenchmarkAccounts = (params?: {
  page?: number
  limit?: number
  our_account_id?: string
  platform?: string
  benchmark_type?: string
  status?: string
  search?: string
  our_account_search?: string
}) => {
  return apiRequest<BenchmarkAccountListResponse>({
    url: '/api/v1/benchmark/accounts',
    method: 'get',
    params
  })
}

// 检查对标账号URL是否重复
export const checkBenchmarkAccountUrl = (data: {
  account_url: string
  our_account_id?: string
}) => {
  return apiRequest<{
    exists: boolean
    is_same_our_account?: boolean
    existing_account?: {
      account_name: string
      our_account_name: string
      our_account_id: string
      platform: string
      benchmark_type: string
    }
    message: string
  }>({
    url: '/api/v1/benchmark/check-url',
    method: 'post',
    data
  })
}

// 创建对标账号
export const createBenchmarkAccount = (data: {
  our_account_id: string
  platform: string
  account_name: string
  account_url: string
  benchmark_type: string
  description?: string
  tags?: string[]
  priority?: number
}) => {
  return apiRequest({
    url: '/api/v1/benchmark/accounts',
    method: 'post',
    data
  })
}

// 获取对标账号详情
export const getBenchmarkAccountDetail = (accountId: string) => {
  return apiRequest<BenchmarkAccount>({
    url: `/api/v1/benchmark/accounts/${accountId}`,
    method: 'get'
  })
}

// 更新对标账号
export const updateBenchmarkAccount = (accountId: string, data: Partial<BenchmarkAccount>) => {
  return apiRequest({
    url: `/api/v1/benchmark/accounts/${accountId}`,
    method: 'put',
    data
  })
}

// 删除对标账号
export const deleteBenchmarkAccount = (accountId: string) => {
  return apiRequest({
    url: `/api/v1/benchmark/accounts/${accountId}`,
    method: 'delete'
  })
}

// 获取对标账号统计
export const getBenchmarkStats = (ourAccountId?: string) => {
  return apiRequest<BenchmarkAccountStats>({
    url: '/api/v1/benchmark/stats',
    method: 'get',
    params: ourAccountId ? { our_account_id: ourAccountId } : undefined
  })
}

// 更新对标账号数据
export const updateBenchmarkAccountData = (accountId: string, data: BenchmarkAccountData) => {
  return apiRequest({
    url: `/api/v1/benchmark/accounts/${accountId}/update-data`,
    method: 'post',
    data
  })
}

// 创建对标账号下载任务
export const createBenchmarkDownloadTask = (data: {
  our_account_id: string
  our_account_name: string
  benchmark_account_id: string
  benchmark_account_name: string
  benchmark_account_url: string
  platform: string
  download_path: string
  download_config: {
    max_videos?: number
    video_quality?: 'high' | 'medium' | 'low'
    include_metadata?: boolean
    skip_existing?: boolean
    date_range?: {
      start_date?: string
      end_date?: string
    }
  }
}) => {
  return apiRequest({
    url: '/api/v1/benchmark/download/tasks',
    method: 'post',
    data
  })
}

// 创建批量对标账号下载任务
export const createBatchBenchmarkDownloadTasks = (tasks: Array<{
  our_account_id: string
  our_account_name: string
  benchmark_account_id: string
  benchmark_account_name: string
  benchmark_account_url: string
  platform: string
  download_path: string
  download_config: {
    max_videos?: number
    video_quality?: 'high' | 'medium' | 'low'
    include_metadata?: boolean
    skip_existing?: boolean
  }
}>) => {
  return apiRequest({
    url: '/api/v1/benchmark/download/batch-tasks',
    method: 'post',
    data: { tasks }
  })
}

// 获取对标账号下载任务状态
export const getBenchmarkDownloadTaskStatus = (taskId: string) => {
  return apiRequest({
    url: `/api/v1/benchmark/download/tasks/${taskId}/status`,
    method: 'get'
  })
}

// 获取对标账号下载任务列表
export const getBenchmarkDownloadTasks = (params?: {
  page?: number
  limit?: number
  status?: string
  our_account_id?: string
}) => {
  return apiRequest({
    url: '/api/v1/benchmark/download/tasks',
    method: 'get',
    params
  })
}
