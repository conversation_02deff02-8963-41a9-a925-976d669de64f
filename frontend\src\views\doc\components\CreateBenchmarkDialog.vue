<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加对标账号"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="我们的账号" prop="our_account_id">
        <el-select
          v-model="form.our_account_id"
          placeholder="请选择我们的账号"
          style="width: 100%"
        >
          <el-option
            v-for="account in ourAccounts"
            :key="account.id"
            :label="formatAccountLabel(account)"
            :value="account.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="平台" prop="platform">
        <el-select
          v-model="form.platform"
          placeholder="请选择平台"
          style="width: 100%"
        >
          <el-option label="YouTube" value="youtube" />
          <el-option label="TikTok" value="tiktok" />
          <el-option label="Instagram" value="instagram" />
          <el-option label="微博" value="weibo" />
          <el-option label="小红书" value="xiaohongshu" />
          <el-option label="抖音" value="douyin" />
          <el-option label="快手" value="kuaishou" />
          <el-option label="B站" value="bilibili" />
        </el-select>
      </el-form-item>

      <el-form-item label="账号名称" prop="account_name">
        <el-input
          v-model="form.account_name"
          placeholder="请输入对标账号名称"
        />
      </el-form-item>

      <el-form-item label="账号链接" prop="account_url">
        <el-input
          v-model="form.account_url"
          placeholder="请输入对标账号链接"
          @blur="handleUrlBlur"
          :class="{ 'url-warning': urlCheckResult.exists }"
        />

        <!-- URL重复警告 -->
        <div v-if="urlCheckResult.exists" class="url-warning-message">
          <el-alert
            :title="urlCheckResult.message"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="warning-details">
                <p>{{ urlCheckResult.message }}</p>
                <div v-if="urlCheckResult.existing_account" class="existing-info">
                  <p><strong>现有对标账号信息：</strong></p>
                  <ul>
                    <li>账号名称：{{ urlCheckResult.existing_account.account_name }}</li>
                    <li>关联账号：{{ urlCheckResult.existing_account.our_account_name }}</li>
                    <li>平台：{{ urlCheckResult.existing_account.platform }}</li>
                    <li>类型：{{ getBenchmarkTypeText(urlCheckResult.existing_account.benchmark_type) }}</li>
                  </ul>
                </div>
              </div>
            </template>
          </el-alert>
        </div>

        <div v-else class="form-tip">
          输入账号主页链接，系统会自动提取账号信息
        </div>
      </el-form-item>

      <el-form-item label="对标类型" prop="benchmark_type">
        <el-radio-group v-model="form.benchmark_type">
          <el-radio value="original">原创</el-radio>
          <el-radio value="recreate">二创</el-radio>
          <el-radio value="repost">搬运</el-radio>
        </el-radio-group>
        <div class="form-tip">
          原创：完全原创内容；二创：基于原内容进行创作；搬运：直接转载内容
        </div>
      </el-form-item>

      <el-form-item label="账号描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入账号描述（可选）"
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="请选择或输入标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
        <div class="form-tip">
          可以选择常用标签或输入自定义标签
        </div>
      </el-form-item>

      <el-form-item label="优先级">
        <el-rate
          v-model="form.priority"
          :max="5"
          show-text
          :texts="['很低', '低', '中等', '高', '很高']"
        />
        <div class="form-tip">
          设置对标账号的重要程度，影响监控频率
        </div>
      </el-form-item>

      <!-- 🎬 视频采集配置 -->
      <el-divider content-position="left">
        <span style="color: #409eff; font-weight: 600;">🎬 视频采集配置</span>
      </el-divider>

      <el-form-item label="启用采集">
        <el-switch
          v-model="form.enable_video_collect"
          active-text="立即采集视频"
          inactive-text="仅添加账号"
        />
        <div class="form-tip">
          开启后将在添加对标账号的同时自动采集该账号的视频信息
        </div>
      </el-form-item>

      <template v-if="form.enable_video_collect">
        <el-form-item label="采集模式">
          <el-radio-group v-model="form.collect_mode">
            <el-radio value="basic_collect">基础采集</el-radio>
            <el-radio value="full_collect">完整采集</el-radio>
            <el-radio value="collect_and_download">采集并下载</el-radio>
          </el-radio-group>
          <div class="form-tip">
            基础采集：只获取视频基础信息；完整采集：获取真实下载地址；采集并下载：直接下载视频文件
          </div>
        </el-form-item>

        <el-form-item label="采集数量">
          <el-input-number
            v-model="form.max_videos"
            :min="10"
            :max="500"
            :step="10"
            style="width: 150px"
          />
          <span style="margin-left: 8px; color: #909399;">个视频</span>
          <div class="form-tip">
            设置最多采集多少个视频，建议首次采集不超过100个
          </div>
        </el-form-item>

        <el-form-item label="月份筛选" v-if="form.platform === 'douyin'">
          <el-switch
            v-model="form.use_month_filter"
            active-text="按月份采集"
            inactive-text="全部采集"
          />
          <div class="form-tip">
            抖音平台支持按月份筛选采集，可以更精确地获取特定时间段的内容
          </div>
        </el-form-item>

        <el-form-item label="目标月份" v-if="form.use_month_filter && form.platform === 'douyin'">
          <el-select
            v-model="form.target_months"
            multiple
            placeholder="选择要采集的月份"
            style="width: 100%"
          >
            <el-option
              v-for="month in availableMonths"
              :key="`${month.year}-${month.month}`"
              :label="month.display"
              :value="month"
            />
          </el-select>
          <div class="form-tip">
            留空则采集所有可用月份，建议选择最近几个月的内容
          </div>
        </el-form-item>

        <el-form-item label="下载路径">
          <el-input
            v-model="form.download_path"
            placeholder="自动生成下载路径"
            readonly
            style="width: 100%"
          >
            <template #prepend>📁</template>
          </el-input>
          <div class="form-tip">
            {{ getDownloadPathDescription() }}
          </div>
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { createBenchmarkAccount, checkBenchmarkAccountUrl } from '@/api/content'

interface Props {
  modelValue: boolean
  ourAccounts: any[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const urlChecking = ref(false)

// URL检查结果
const urlCheckResult = reactive({
  exists: false,
  is_same_our_account: false,
  existing_account: null as any,
  message: ''
})

// 表单数据
const form = reactive({
  our_account_id: '',
  platform: '',
  account_name: '',
  account_url: '',
  benchmark_type: 'original',
  description: '',
  tags: [] as string[],
  priority: 3,
  // 🎬 视频采集配置
  enable_video_collect: false,
  collect_mode: 'basic_collect',
  max_videos: 50,
  use_month_filter: false,
  target_months: [] as any[],
  download_path: ''
})

// 常用标签
const commonTags = [
  '科技', '教育', '娱乐', '生活', '美食', '旅游', '时尚', '健康',
  '财经', '游戏', '音乐', '电影', '体育', '新闻', '搞笑', '萌宠',
  '美妆', '母婴', '汽车', '房产', '职场', '创业', '投资', '理财'
]

// 表单验证规则
const rules: FormRules = {
  our_account_id: [
    { required: true, message: '请选择我们的账号', trigger: 'change' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  account_name: [
    { required: true, message: '请输入账号名称', trigger: 'blur' },
    { min: 1, max: 100, message: '账号名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  account_url: [
    { required: true, message: '请输入账号链接', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  benchmark_type: [
    { required: true, message: '请选择对标类型', trigger: 'change' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  }
})

// 工具方法
const getPlatformDisplayName = (platformId: string) => {
  // 根据数据库中的平台ObjectId映射到显示名称
  const platformIdMap = {
    '681efeeecd836bd64b9c2a1e': 'YouTube', // 油管
    '681efeeecd836bd64b9c2a20': 'TikTok',  // TT
    '681efeeecd836bd64b9c2a22': '抖音',     // 抖音
    '6822ecaa62fd956eb6d2c071': 'Facebook', // 脸书
    '6822ebfc05340d5a3d867138': 'AWS'       // AWS
  }

  // 如果是ObjectId格式，使用映射表
  if (platformIdMap[platformId]) {
    return platformIdMap[platformId]
  }

  // 如果是传统的平台ID格式，使用原来的映射
  const platformMap = {
    youtube: 'YouTube',
    tiktok: 'TikTok',
    instagram: 'Instagram',
    weibo: '微博',
    xiaohongshu: '小红书',
    douyin: '抖音',
    kuaishou: '快手',
    bilibili: 'B站'
  }

  return platformMap[platformId as keyof typeof platformMap] || platformId
}

const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    'original': '原创',
    'recreate': '二创',
    'repost': '搬运'
  }
  return typeMap[type] || type
}

const formatAccountLabel = (account: any) => {
  console.log('CreateBenchmarkDialog formatAccountLabel 输入数据:', account)

  // 获取平台名称 - 优先使用后端提供的platform_name
  let platformName = account.platform_name || account.platform_display_name || '未知平台'

  // 获取账号名称，优先使用display_name，然后是username的前缀部分
  let name = account.display_name
  if (!name && account.username) {
    // 如果username是邮箱格式，取@前面的部分
    name = account.username.includes('@') ? account.username.split('@')[0] : account.username
  }
  if (!name) {
    name = '未命名账号'
  }

  const result = `${platformName}-${name}`
  console.log('CreateBenchmarkDialog formatAccountLabel 结果:', result)
  return result
}

// 监听器
watch(() => form.our_account_id, () => {
  // 当关联账号改变时，重新检查URL
  if (form.account_url) {
    checkUrlDuplicate()
  }
})

// 方法
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    our_account_id: '',
    platform: '',
    account_name: '',
    account_url: '',
    benchmark_type: 'original',
    description: '',
    tags: [],
    priority: 3
  })

  // 重置URL检查结果
  Object.assign(urlCheckResult, {
    exists: false,
    is_same_our_account: false,
    existing_account: null,
    message: ''
  })
}

// URL检查函数
const checkUrlDuplicate = async () => {
  if (!form.account_url || !form.our_account_id) {
    // 重置检查结果
    Object.assign(urlCheckResult, {
      exists: false,
      is_same_our_account: false,
      existing_account: null,
      message: ''
    })
    return
  }

  try {
    urlChecking.value = true
    const response = await checkBenchmarkAccountUrl({
      account_url: form.account_url,
      our_account_id: form.our_account_id
    })

    Object.assign(urlCheckResult, response)
  } catch (error) {
    console.error('检查URL重复失败:', error)
    // 检查失败时不显示错误，允许用户继续操作
    Object.assign(urlCheckResult, {
      exists: false,
      is_same_our_account: false,
      existing_account: null,
      message: ''
    })
  } finally {
    urlChecking.value = false
  }
}

// 处理URL输入框失焦事件
const handleUrlBlur = async () => {
  await checkUrlDuplicate()
  extractAccountInfo()
}

const extractAccountInfo = () => {
  // 从URL中提取账号信息
  const url = form.account_url
  if (!url) return

  try {
    const urlObj = new URL(url)
    const hostname = urlObj.hostname.toLowerCase()

    // 根据域名自动设置平台
    if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
      form.platform = 'youtube'
      // 尝试从URL中提取频道名称
      const pathParts = urlObj.pathname.split('/')
      if (pathParts.includes('channel') || pathParts.includes('c') || pathParts.includes('@')) {
        const channelPart = pathParts[pathParts.length - 1]
        if (channelPart && !form.account_name) {
          form.account_name = channelPart.replace('@', '')
        }
      }
    } else if (hostname.includes('tiktok.com')) {
      form.platform = 'tiktok'
      const pathParts = urlObj.pathname.split('/')
      if (pathParts[1] && pathParts[1].startsWith('@')) {
        form.account_name = pathParts[1].replace('@', '')
      }
    } else if (hostname.includes('instagram.com')) {
      form.platform = 'instagram'
      const pathParts = urlObj.pathname.split('/')
      if (pathParts[1] && !form.account_name) {
        form.account_name = pathParts[1]
      }
    } else if (hostname.includes('weibo.com')) {
      form.platform = 'weibo'
    } else if (hostname.includes('xiaohongshu.com') || hostname.includes('xhs.com')) {
      form.platform = 'xiaohongshu'
    } else if (hostname.includes('douyin.com')) {
      form.platform = 'douyin'
    } else if (hostname.includes('kuaishou.com')) {
      form.platform = 'kuaishou'
    } else if (hostname.includes('bilibili.com')) {
      form.platform = 'bilibili'
    }

    ElMessage.success('已自动识别平台信息')
  } catch (error) {
    console.warn('URL解析失败:', error)
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 如果URL重复，给出确认提示
    if (urlCheckResult.exists) {
      const confirmMessage = urlCheckResult.is_same_our_account
        ? `该URL已存在于当前关联账号下，确定要继续添加吗？\n\n现有对标账号：${urlCheckResult.existing_account?.account_name}`
        : `该URL已被其他关联账号使用，确定要继续添加吗？\n\n现有对标账号：${urlCheckResult.existing_account?.our_account_name} -> ${urlCheckResult.existing_account?.account_name}`

      const confirmed = await ElMessageBox.confirm(
        confirmMessage,
        'URL重复确认',
        {
          confirmButtonText: '继续添加',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).catch(() => false)

      if (!confirmed) {
        return
      }
    }

    submitting.value = true

    await createBenchmarkAccount({
      our_account_id: form.our_account_id,
      platform: form.platform,
      account_name: form.account_name,
      account_url: form.account_url,
      benchmark_type: form.benchmark_type,
      description: form.description || undefined,
      tags: form.tags,
      priority: form.priority
    })

    ElMessage.success('对标账号添加成功')
    emit('success')
  } catch (error: any) {
    console.error('创建对标账号失败:', error)

    // 提取错误信息
    let errorMessage = '创建对标账号失败'
    if (error?.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error?.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-rate__text) {
  font-size: 12px;
  color: #606266;
}

.url-warning {
  border-color: #E6A23C !important;
}

.url-warning-message {
  margin-top: 8px;
}

.warning-details {
  font-size: 14px;
}

.existing-info {
  margin-top: 8px;
  padding: 8px;
  background-color: #fdf6ec;
  border-radius: 4px;
}

.existing-info ul {
  margin: 4px 0 0 0;
  padding-left: 16px;
}

.existing-info li {
  margin: 2px 0;
  font-size: 13px;
}
</style>
