# 视频去重功能集成指南

## 📋 概述

本文档介绍如何在现有的视频上传系统中集成视频去重功能，避免重复上传相同的视频文件。

## 🎯 核心策略

### 1. 唯一标识策略
- **主要标识**: 文件内容SHA-256哈希值
- **辅助标识**: 文件大小 + 视频时长 + 分辨率
- **快速预检**: 文件大小 + 时长（避免每次都计算完整哈希）

### 2. 重复检查层级
1. **快速检查**: 文件大小 + 时长匹配
2. **精确验证**: SHA-256哈希值比较
3. **账号级检查**: 检查是否在指定账号上传过

## 🗄️ 数据库设计

### uploaded_videos 集合
```javascript
{
  "_id": ObjectId,
  "file_hash": String,        // SHA-256哈希值（主键）
  "file_size": Number,        // 文件大小（字节）
  "duration": Number,         // 视频时长（秒）
  "resolution": String,       // 分辨率 "1920x1080"
  "original_filename": String, // 原始文件名
  "upload_records": [         // 上传记录数组
    {
      "platform_id": String,   // 平台ID
      "account_id": String,     // 账号ID
      "youtube_video_id": String, // YouTube返回的视频ID
      "upload_time": Date,      // 上传时间
      "task_id": String,        // 关联的任务ID
      "title": String,          // 上传时使用的标题
      "status": String          // "success", "failed", "pending"
    }
  ],
  "first_upload": Date,       // 首次上传时间
  "last_upload": Date,        // 最后上传时间
  "upload_count": Number,     // 上传次数统计
  "created_at": Date,
  "updated_at": Date
}
```

## 🔧 后端集成

### 1. 在上传任务创建时检查重复

```python
# 在 youtube_upload.py 中
from app.core.services.video_deduplication import VideoDeduplicationService

@router.post("/uploads")
async def create_upload_task(task: UploadTaskCreate, db_service: SocialDatabaseService):
    # 初始化去重服务
    dedup_service = VideoDeduplicationService(db_service.db)
    
    # 检查重复（除非强制上传）
    if not task.force_upload and task.selectedFiles:
        duplicate_files = []
        for video_file in task.selectedFiles:
            full_path = os.path.join(task.folderPath, video_file)
            is_duplicate, duplicate_record = dedup_service.is_duplicate_for_account(
                full_path, platform_id, task.accountId
            )
            
            if is_duplicate:
                duplicate_files.append({
                    "file": video_file,
                    "duplicate_record": duplicate_record
                })
        
        # 如果有重复文件，返回警告
        if duplicate_files:
            return {
                "task_id": None,
                "status": "duplicate_detected",
                "duplicate_info": {"duplicate_files": duplicate_files}
            }
    
    # 创建任务并记录上传尝试
    task_id = db_service.create_task(task_data)
    dedup_service.record_upload_attempt(
        full_path, platform_id, task.accountId, task_id, title, "pending"
    )
```

### 2. 在Core服务中更新上传状态

```python
# 在 youtube_uploader_v3.py 中
from backend.app.utils.video_upload_tracker import get_upload_tracker

class YouTubeUploaderV3:
    def __init__(self, device_id: str):
        self.upload_tracker = get_upload_tracker()
    
    async def execute_upload_task(self, video_path: str, title: str, ...):
        try:
            # 执行上传逻辑
            youtube_video_id = await self._perform_upload(...)
            
            # 更新成功状态
            self.upload_tracker.update_upload_success(
                video_path, self.task_id, youtube_video_id
            )
            
        except Exception as e:
            # 更新失败状态
            self.upload_tracker.update_upload_failure(
                video_path, self.task_id, str(e)
            )
```

## 🎨 前端集成

### 1. 在上传页面中使用重复检查组件

```vue
<template>
  <div class="upload-page">
    <!-- 现有的上传表单 -->
    <upload-form @submit="handleSubmit" />
    
    <!-- 重复检查组件 -->
    <duplicate-video-checker 
      ref="duplicateChecker"
      :show-statistics="true"
      @force-upload="handleForceUpload"
    />
  </div>
</template>

<script setup>
import DuplicateVideoChecker from '@/components/DuplicateVideoChecker.vue'

const duplicateChecker = ref()

const handleSubmit = async (formData) => {
  // 先检查重复
  const hasDuplicates = await duplicateChecker.value.checkDuplicates(
    formData.folderPath,
    formData.selectedFiles,
    formData.accountId
  )
  
  if (!hasDuplicates) {
    // 没有重复，直接上传
    await submitUploadTask(formData)
  }
  // 如果有重复，组件会显示对话框
}

const handleForceUpload = async () => {
  // 强制上传
  await submitUploadTask({ ...formData, force_upload: true })
}
</script>
```

## 📊 API 端点

### 1. 检查重复视频
```
POST /api/v1/social/youtube/check-duplicates
{
  "folderPath": "/path/to/videos",
  "selectedFiles": ["video1.mp4", "video2.mp4"],
  "accountId": "account123"
}
```

### 2. 获取重复统计
```
GET /api/v1/social/youtube/duplicate-statistics
```

### 3. 创建上传任务（支持强制上传）
```
POST /api/v1/social/youtube/uploads
{
  "folderPath": "/path/to/videos",
  "selectedFiles": ["video1.mp4"],
  "accountId": "account123",
  "metadata": {...},
  "force_upload": false  // 新增字段
}
```

## 🔄 工作流程

### 1. 正常上传流程
```mermaid
graph TD
    A[用户选择视频] --> B[检查重复]
    B --> C{是否重复?}
    C -->|否| D[创建上传任务]
    C -->|是| E[显示重复警告]
    E --> F[用户选择]
    F -->|强制上传| G[创建上传任务 force_upload=true]
    F -->|取消| H[返回选择页面]
    D --> I[执行上传]
    G --> I
    I --> J[更新上传状态]
```

### 2. 重复检查逻辑
```mermaid
graph TD
    A[输入视频文件] --> B[获取文件大小和时长]
    B --> C[查询相同大小和时长的记录]
    C --> D{找到候选记录?}
    D -->|否| E[返回无重复]
    D -->|是| F[计算文件哈希]
    F --> G[比较哈希值]
    G --> H{哈希匹配?}
    H -->|否| E
    H -->|是| I[检查账号上传记录]
    I --> J{该账号已上传?}
    J -->|是| K[返回重复]
    J -->|否| L[返回其他账号重复]
```

## 🎯 使用效果

### 1. 避免重复上传
- 自动检测相同内容的视频文件
- 即使文件名不同也能准确识别
- 支持跨账号重复检测

### 2. 提供详细信息
- 显示重复视频的上传历史
- 展示之前上传的标题和时间
- 提供YouTube视频ID链接

### 3. 灵活的处理方式
- 支持强制上传重复视频
- 提供重复统计信息
- 可配置的检查策略

## 📈 性能优化

### 1. 快速预检
- 优先使用文件大小和时长进行筛选
- 只有候选匹配时才计算完整哈希
- 减少不必要的计算开销

### 2. 数据库索引
- 文件哈希唯一索引
- 文件大小和时长组合索引
- 平台和账号组合索引

### 3. 缓存策略
- 可考虑缓存常用文件的哈希值
- 使用Redis缓存重复检查结果

## 🔧 配置选项

### 1. 环境变量
```bash
# 是否启用视频去重功能
VIDEO_DEDUPLICATION_ENABLED=true

# 哈希计算超时时间（秒）
VIDEO_HASH_TIMEOUT=300

# 清理失败记录的天数
CLEANUP_FAILED_RECORDS_DAYS=7
```

### 2. 可配置参数
- 时长匹配误差范围（默认1秒）
- 是否需要ffprobe依赖
- 重复检查的严格程度

这个视频去重系统提供了完整的解决方案，既能有效避免重复上传，又保持了系统的灵活性和用户体验。
