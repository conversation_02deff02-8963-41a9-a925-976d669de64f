#!/usr/bin/env python3
"""
简化的代理IP数据初始化脚本
直接使用pymongo创建示例数据
"""

from pymongo import MongoClient
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_proxy_data():
    """初始化代理IP数据"""
    try:
        # 连接MongoDB
        client = MongoClient("mongodb://***************:27017")
        db = client["thunderhub"]
        
        # 清空现有数据
        db.proxy_ips.delete_many({})
        db.device_proxy_mappings.delete_many({})
        logger.info("已清空现有代理IP数据")
        
        # 示例代理IP数据
        sample_proxies = [
            # HK代理
            {
                "region": "HK",
                "ip_address": "**************",
                "port": 16881,
                "proxy_type": "vless",
                "v2ray_config": "vless://d5d80529-f6d8-4127-b81e-7b02eb032eb8@**************:16881?type=ws&security=none&path=%2F#B-US-1-2-16-KR01-HK",
                "socks_config": "**************:16882:a0XAHFvWEM:WNlVbQfrSi",
                "username": "a0XAHFvWEM",
                "password": "WNlVbQfrSi",
                "expire_date": datetime(2026, 3, 15),
                "payment_card": "兴业 3509",
                "provider": "B-US-1-2-16-KR01-HK",
                "status": "active",
                "notes": "**************:16882:a0XAHFvWEM:WNlVbQfrSi",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "region": "HK",
                "ip_address": "**************",
                "port": 16889,
                "proxy_type": "vless",
                "v2ray_config": "vless://e015a71e-a2d4-4270-ac16-0f59d9ce9932@**************:16889?type=tcp&security=none#B-HK-1-2-23-002-HK",
                "socks_config": "**************:16882:Htuhcra0PA:SefXvAzYpd",
                "username": "Htuhcra0PA",
                "password": "SefXvAzYpd",
                "expire_date": datetime(2026, 3, 14),
                "payment_card": "招商 6017",
                "provider": "B-HK-1-2-23-002-HK",
                "status": "active",
                "notes": "**************:16882:Htuhcra0PA:SefXvAzYpd",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            # US代理
            {
                "region": "US",
                "ip_address": "************",
                "port": 15537,
                "proxy_type": "vless",
                "v2ray_config": "vless://f5926af2-69a9-49f2-e5b0-7b8ebffe1f8a@************:15537?encryption=none&security=none&type=ws&path=%2F",
                "socks_config": "************:15538:user1:pass1",
                "expire_date": datetime.now() + timedelta(days=365),
                "status": "active",
                "notes": "美国代理服务器1",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "region": "US",
                "ip_address": "***************",
                "port": 17110,
                "proxy_type": "vless",
                "v2ray_config": "vless://ec506c2f-194a-4ed7-d447-07ab352e2bda@***************:17110?type=ws&security=none&path=%2F",
                "socks_config": "***************:46114:RntuMdKfCL:z3nBPDGsaD",
                "username": "RntuMdKfCL",
                "password": "z3nBPDGsaD",
                "expire_date": datetime.now() + timedelta(days=365),
                "status": "active",
                "notes": "***************:46114:RntuMdKfCL:z3nBPDGsaD",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            # 其他地区代理
            {
                "region": "JP",
                "ip_address": "************",
                "port": 8080,
                "proxy_type": "http",
                "username": "user123",
                "password": "pass123",
                "expire_date": datetime.now() + timedelta(days=30),
                "payment_card": "工商 1234",
                "provider": "日本代理服务商",
                "status": "active",
                "notes": "日本HTTP代理",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "region": "SG",
                "ip_address": "*************",
                "port": 1080,
                "proxy_type": "socks5",
                "username": "sguser",
                "password": "sgpass",
                "expire_date": datetime.now() + timedelta(days=7),  # 即将到期
                "payment_card": "建设 5678",
                "provider": "新加坡代理服务商",
                "status": "active",
                "notes": "新加坡SOCKS5代理",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "region": "KR",
                "ip_address": "************",
                "port": 3128,
                "proxy_type": "http",
                "expire_date": datetime.now() - timedelta(days=5),  # 已过期
                "payment_card": "农业 9012",
                "provider": "韩国代理服务商",
                "status": "expired",
                "notes": "韩国HTTP代理 - 已过期",
                "created_at": datetime.now() - timedelta(days=30),
                "updated_at": datetime.now()
            }
        ]
        
        # 插入代理IP数据
        result = db.proxy_ips.insert_many(sample_proxies)
        logger.info(f"成功创建 {len(result.inserted_ids)} 个代理IP记录")
        
        # 创建索引
        db.proxy_ips.create_index("region")
        db.proxy_ips.create_index("status")
        db.proxy_ips.create_index("expire_date")
        db.proxy_ips.create_index([("ip_address", 1), ("port", 1)], unique=True)
        
        db.device_proxy_mappings.create_index("device_id")
        db.device_proxy_mappings.create_index("proxy_id")
        db.device_proxy_mappings.create_index([("device_id", 1), ("proxy_id", 1)])
        
        logger.info("数据库索引创建完成")
        logger.info("代理IP示例数据创建完成！")
        
        # 显示创建的数据
        logger.info("\n创建的代理IP列表：")
        for proxy in db.proxy_ips.find():
            logger.info(f"- {proxy['region']}: {proxy['ip_address']}:{proxy['port']} ({proxy['proxy_type']}) - {proxy['status']}")
        
        client.close()
        
    except Exception as e:
        logger.error(f"初始化失败: {str(e)}")
        raise

if __name__ == "__main__":
    init_proxy_data()
