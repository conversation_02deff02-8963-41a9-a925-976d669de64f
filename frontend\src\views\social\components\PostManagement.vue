<template>
  <div class="post-management">
    <h1 style="color: #4361ee; margin: 10px 0 20px">内容管理</h1>
    <div class="header">
      <el-button type="primary" @click="showPostDialog = true">
        发布内容
      </el-button>
      <el-select
        v-model="filterStatus"
        placeholder="状态筛选"
        style="width: 120px; margin-left: 10px"
        clearable
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>

    <el-table :data="filteredPosts" style="width: 100%">
      <el-table-column prop="content" label="内容" width="300">
        <template #default="scope">
          <div class="post-content">
            {{ scope.row.content }}
            <div v-if="scope.row.mediaUrls" class="media-preview">
              <el-image
                v-for="(url, index) in scope.row.mediaUrls.slice(0, 3)"
                :key="index"
                :src="url"
                fit="cover"
                style="width: 60px; height: 60px; margin-right: 5px"
              />
              <span v-if="scope.row.mediaUrls.length > 3" class="more-media">
                +{{ scope.row.mediaUrls.length - 3 }}
              </span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="accountId" label="发布账号">
        <template #default="scope">
          {{ getAccountName(scope.row.accountId) }}
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="scheduledTime" label="发布时间" />
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" @click="viewAnalytics(scope.row)">
            分析
          </el-button>
          <el-button
            size="small"
            type="danger"
            plain
            @click="deletePost(scope.row.id)"
            :disabled="scope.row.status === 'published'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container mt-4">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[5, 10, 20]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="fetchPosts"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 发布内容对话框 -->
    <el-dialog v-model="showPostDialog" title="发布内容">
      <el-form :model="postForm" label-width="80px">
        <el-form-item label="内容" required>
          <el-input
            v-model="postForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="媒体文件">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :on-change="handleMediaChange"
            :on-remove="handleMediaRemove"
            multiple
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="发布账号" required>
          <el-select v-model="postForm.accountId" placeholder="请选择账号">
            <el-option
              v-for="account in accounts"
              :key="account.id"
              :label="account.username"
              :value="account.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="定时发布">
          <el-date-picker
            v-model="postForm.scheduledTime"
            type="datetime"
            placeholder="选择发布时间"
            :disabled-date="disabledDate"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPostDialog = false">取消</el-button>
        <el-button type="primary" @click="submitPost">
          发布
        </el-button>
      </template>
    </el-dialog>

    <!-- 内容分析对话框 -->
    <el-dialog v-model="showAnalyticsDialog" title="内容分析">
      <div v-if="currentPostAnalytics" class="analytics-container">
        <div class="metrics">
          <el-statistic title="浏览量" :value="currentPostAnalytics.views" />
          <el-statistic title="点赞数" :value="currentPostAnalytics.likes" />
          <el-statistic title="评论数" :value="currentPostAnalytics.comments" />
          <el-statistic title="分享数" :value="currentPostAnalytics.shares" />
        </div>
        <el-divider />
        <div class="analytics-chart">
          <!-- 这里可以放置图表组件 -->
          <div class="chart-placeholder">
            <el-icon :size="50"><DataAnalysis /></el-icon>
            <p>互动数据趋势图</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Plus, DataAnalysis } from '@element-plus/icons-vue'
import { getPosts, createPost, deletePost } from '@/api/social'
import { getAccounts } from '@/api/social'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { SocialPost, SocialAccount } from '@/types/social'

const props = defineProps<{
  appId: string
}>()

const posts = ref<SocialPost[]>([])
const accounts = ref<SocialAccount[]>([])
const filterStatus = ref('')
const showPostDialog = ref(false)
const showAnalyticsDialog = ref(false)
const currentPostAnalytics = ref<any>(null)
const mediaFiles = ref<any[]>([])

const postForm = ref({
  content: '',
  accountId: '',
  scheduledTime: '',
  mediaUrls: [] as string[]
})

const statusOptions = [
  { value: 'draft', label: '草稿' },
  { value: 'scheduled', label: '定时' },
  { value: 'published', label: '已发布' },
  { value: 'failed', label: '失败' }
]

const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

// 获取内容列表
const fetchPosts = async () => {
  try {
    const res = await getPosts(props.appId)
    posts.value = res
    pagination.value.total = res.length
  } catch (error) {
    ElMessage.error('获取内容列表失败')
    console.error(error)
  }
}

// 获取账号列表
const fetchAccounts = async () => {
  try {
    const res = await getAccounts(props.appId)
    accounts.value = res
  } catch (error) {
    console.error('获取账号列表失败', error)
  }
}

// 过滤内容
const filteredPosts = computed(() => {
  let result = posts.value
  if (filterStatus.value) {
    result = result.filter(post => post.status === filterStatus.value)
  }
  return result
})

// 获取账号名称
const getAccountName = (accountId: string) => {
  const account = accounts.value.find(a => a.id === accountId)
  return account ? account.username : '未知账号'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    scheduled: '定时',
    published: '已发布',
    failed: '失败'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    published: 'success',
    failed: 'danger',
    scheduled: 'warning'
  }
  return typeMap[status] || 'info'
}

// 分页大小变化处理
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  pagination.value.page = 1
  fetchPosts()
}

// 处理媒体文件变化
const handleMediaChange = (file: any) => {
  mediaFiles.value.push(file)
  // TODO: 实际上传逻辑
}

// 处理媒体文件移除
const handleMediaRemove = (file: any) => {
  const index = mediaFiles.value.findIndex(f => f.uid === file.uid)
  if (index !== -1) {
    mediaFiles.value.splice(index, 1)
  }
}

// 禁用过去的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7
}

// 提交内容
const submitPost = async () => {
  try {
    await createPost({
      ...postForm.value,
      mediaUrls: mediaFiles.value.map(f => f.url) // TODO: 替换为实际URL
    })
    ElMessage.success('内容已提交')
    showPostDialog.value = false
    await fetchPosts()
  } catch (error) {
    ElMessage.error('提交失败')
    console.error(error)
  }
}

// 查看分析
const viewAnalytics = (post: SocialPost) => {
  currentPostAnalytics.value = post.publishResult || {
    views: 0,
    likes: 0,
    comments: 0,
    shares: 0
  }
  showAnalyticsDialog.value = true
}

// 删除内容
const deletePost = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定删除该内容吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deletePost(id)
    ElMessage.success('删除成功')
    await fetchPosts()
  } catch (error) {
    console.error('取消删除或删除失败', error)
  }
}

// 监听appId变化
watch(() => props.appId, (newVal) => {
  if (newVal) {
    fetchPosts()
    fetchAccounts()
  }
})

onMounted(() => {
  fetchPosts()
  fetchAccounts()
})
</script>

<style scoped>
.post-management {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
}

.header {
  display: flex;
  margin-bottom: 20px;
}

.post-content {
  white-space: pre-wrap;
  word-break: break-word;
}

.media-preview {
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.more-media {
  margin-left: 5px;
  color: #999;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.analytics-container {
  padding: 0 20px;
}

.metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #999;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>