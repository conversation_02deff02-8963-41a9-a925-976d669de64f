from pydantic_settings import BaseSettings, SettingsConfigDict

class DatabaseConfig(BaseSettings):
    """数据库配置类"""

    mongodb_url: str
    mongodb_name: str
    redis_url: str
    consul_url: str
    pool_min: int = 2
    pool_max: int = 20
    echo: bool = False

    model_config = SettingsConfigDict(
        env_file="../.env",  # 相对于app目录的路径
        env_file_encoding="utf-8",
        env_prefix="DB_",
        extra="ignore"
    )