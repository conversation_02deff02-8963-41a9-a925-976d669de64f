"""
Consul服务注册模块
提供Core服务向Consul注册的功能
"""

import os
import socket
import logging
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class ConsulRegistrationService:
    """Consul服务注册类"""
    
    def __init__(
        self,
        consul_host: str,
        consul_port: int,
        service_id: str,
        service_name: str,
        service_port: int,
        tags: List[str] = None,
        check_interval: str = "10s",
        check_timeout: str = "5s"
    ):
        """初始化Consul服务注册
        
        Args:
            consul_host: Consul主机地址
            consul_port: Consul端口
            service_id: 服务ID
            service_name: 服务名称
            service_port: 服务端口
            tags: 服务标签
            check_interval: 健康检查间隔
            check_timeout: 健康检查超时
        """
        self.consul_host = consul_host
        self.consul_port = consul_port
        self.service_id = service_id
        self.service_name = service_name
        self.service_port = service_port
        self.tags = tags or []
        self.check_interval = check_interval
        self.check_timeout = check_timeout
        
        # 获取本机IP
        self.host_ip = self._get_host_ip()
        
        # 构建API URL
        self.consul_url = f"http://{consul_host}:{consul_port}/v1"
        
        logger.info(f"Consul服务注册初始化，服务ID: {service_id}, 名称: {service_name}, 端口: {service_port}")
    
    async def register(self) -> bool:
        """注册服务到Consul"""
        # 构建注册数据
        registration_data = {
            "ID": self.service_id,
            "Name": self.service_name,
            "Address": self.host_ip,
            "Port": self.service_port,
            "Tags": self.tags,
            "Check": {
                # 使用TCP检查替代HTTP检查，因为我们是gRPC服务
                "TCP": f"{self.host_ip}:{self.service_port}",
                "Interval": self.check_interval,
                "Timeout": self.check_timeout,
                "DeregisterCriticalServiceAfter": "1m"
            },
            "Meta": {
                "version": "1.0.0",
                "platform": "windows",
                "type": "core"
            }
        }
        
        # 发送注册请求
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/agent/service/register"
                async with session.put(url, json=registration_data) as response:
                    if response.status == 200:
                        logger.info(f"服务{self.service_id}已成功注册到Consul")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"注册服务到Consul失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(f"注册服务到Consul异常: {str(e)}", exc_info=True)
            return False
    
    async def deregister(self) -> bool:
        """从Consul注销服务"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/agent/service/deregister/{self.service_id}"
                async with session.put(url) as response:
                    if response.status == 200:
                        logger.info(f"服务{self.service_id}已从Consul注销")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"从Consul注销服务失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(f"从Consul注销服务异常: {str(e)}", exc_info=True)
            return False
    
    async def check_service_health(self) -> bool:
        """检查服务健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.consul_url}/health/service/{self.service_name}"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        for service in data:
                            if service.get("Service", {}).get("ID") == self.service_id:
                                checks = service.get("Checks", [])
                                for check in checks:
                                    if check.get("Status") != "passing":
                                        logger.warning(f"服务{self.service_id}健康检查未通过: {check.get('Status')}")
                                        return False
                                logger.debug(f"服务{self.service_id}健康检查通过")
                                return True
                        logger.warning(f"未找到服务{self.service_id}的健康检查信息")
                        return False
                    else:
                        error_text = await response.text()
                        logger.error(f"获取服务健康状态失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(f"检查服务健康状态异常: {str(e)}", exc_info=True)
            return False
    
    def _get_host_ip(self) -> str:
        """获取本机IP地址"""
        try:
            # 创建一个临时socket连接，获取本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception as e:
            logger.warning(f"获取本机IP失败: {str(e)}, 使用localhost")
            return "127.0.0.1"


