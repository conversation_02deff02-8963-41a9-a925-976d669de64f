# ThunderHub 数据库初始化工具

这个工具用于初始化ThunderHub的MongoDB数据库，包括创建必要的集合、索引，以及添加初始数据。

## 功能特点

- 从Core服务同步设备数据（真实数据）
- 初始化社交媒体平台数据
- 添加社交媒体账号数据（需手动维护）
- 支持数据导入/导出功能
- 交互式操作界面

## 使用方法

### 命令行参数

```bash
python init_database.py [--reset] [--import-file FILE] [--export-file FILE]
```

参数说明：
- `--reset`: 重置数据库（删除现有集合）
- `--import-file FILE`: 从指定的JSON文件导入数据
- `--export-file FILE`: 将数据导出到指定的JSON文件

### 交互式操作

如果不提供任何命令行参数，脚本将进入交互式操作模式，提供以下功能：

1. 从Core服务同步设备数据
2. 添加社交媒体账号
3. 导出数据到文件
4. 导入数据从文件
0. 退出

### 示例用法

#### 初始化数据库

```bash
python init_database.py
```

#### 重置数据库并导入示例数据

```bash
python init_database.py --reset --import-file sample_data.json
```

#### 导出当前数据

```bash
python init_database.py --export-file backup_data.json
```

## 数据文件格式

导入/导出的JSON文件格式如下：

```json
{
  "platforms": [
    {
      "id": "platform_id",
      "name": "平台名称",
      "icon": "图标文件名",
      "status": "状态",
      "features": ["功能1", "功能2"],
      "created_at": "创建时间",
      "updated_at": "更新时间"
    }
  ],
  "accounts": [
    {
      "id": "账号ID",
      "username": "用户名",
      "display_name": "显示名称",
      "platform_id": "平台ID",
      "device_id": "设备ID",
      "status": "状态",
      "avatar": "头像URL",
      "description": "描述",
      "followers": 粉丝数,
      "following": 关注数,
      "posts_count": 发布数,
      "created_at": "创建时间",
      "updated_at": "更新时间",
      "auth_data": {
        "token": "加密的令牌",
        "cookies": "加密的Cookie",
        "expires_at": "过期时间"
      },
      "settings": {
        "auto_reply": true/false,
        "notification": true/false,
        "privacy_level": "隐私级别"
      },
      "tags": ["标签1", "标签2"]
    }
  ],
  "export_time": "导出时间"
}
```

## 注意事项

1. 设备数据是从Core服务实时获取的，不需要手动维护
2. 社交媒体平台和账号数据需要手动维护
3. 建议定期导出数据作为备份
4. 添加账号时，确保关联的设备ID存在于系统中
5. 添加账号时，确保关联的平台ID存在于系统中

## 依赖项

- Python 3.7+
- pymongo
- redis
- consul

## 作者

ThunderHub开发团队
