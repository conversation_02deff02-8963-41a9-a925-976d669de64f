<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>样式调试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .test-container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }
    .test-button {
      background-color: #409EFF;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      margin: 5px;
    }
    .test-button:hover {
      background-color: #337ecc;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .status.success {
      background-color: #f0f9ff;
      border: 1px solid #67c23a;
      color: #67c23a;
    }
    .status.error {
      background-color: #fef0f0;
      border: 1px solid #f56c6c;
      color: #f56c6c;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>ThunderHub 样式调试页面</h1>
    <p>这个页面用于测试样式文件的加载情况</p>
    
    <div class="test-container">
      <h2>1. 基础样式测试</h2>
      <button class="test-button">测试按钮</button>
      <div class="status success">成功状态样式</div>
      <div class="status error">错误状态样式</div>
    </div>

    <div class="test-container">
      <h2>2. CSS 文件加载检测</h2>
      <div id="css-status">检测中...</div>
      <ul id="css-files"></ul>
    </div>

    <div class="test-container">
      <h2>3. 网络请求检测</h2>
      <button onclick="checkNetworkRequests()">检查网络请求</button>
      <div id="network-status"></div>
    </div>
  </div>

  <script>
    // 检测 CSS 文件加载
    function checkCSSFiles() {
      const cssFiles = [];
      const links = document.querySelectorAll('link[rel="stylesheet"]');
      const statusDiv = document.getElementById('css-status');
      const filesList = document.getElementById('css-files');
      
      if (links.length === 0) {
        statusDiv.innerHTML = '<div class="status error">未找到任何 CSS 文件链接</div>';
        return;
      }

      links.forEach((link, index) => {
        const li = document.createElement('li');
        const href = link.href;
        
        // 检查文件是否可以加载
        fetch(href, { method: 'HEAD' })
          .then(response => {
            if (response.ok) {
              li.innerHTML = `✅ ${href} (${response.status})`;
              li.style.color = '#67c23a';
            } else {
              li.innerHTML = `❌ ${href} (${response.status})`;
              li.style.color = '#f56c6c';
            }
          })
          .catch(error => {
            li.innerHTML = `❌ ${href} (加载失败: ${error.message})`;
            li.style.color = '#f56c6c';
          });
        
        filesList.appendChild(li);
      });

      statusDiv.innerHTML = `<div class="status success">找到 ${links.length} 个 CSS 文件</div>`;
    }

    // 检查网络请求
    function checkNetworkRequests() {
      const statusDiv = document.getElementById('network-status');
      statusDiv.innerHTML = '检查中...';
      
      // 检查主要的静态资源
      const resources = [
        '/assets/element-plus.c37a37ff.css',
        '/assets/index.c81c8524.css',
        '/assets/index.c67fa94b.js'
      ];
      
      const results = [];
      
      Promise.all(resources.map(resource => 
        fetch(resource, { method: 'HEAD' })
          .then(response => ({
            resource,
            status: response.status,
            ok: response.ok,
            size: response.headers.get('content-length')
          }))
          .catch(error => ({
            resource,
            error: error.message,
            ok: false
          }))
      )).then(results => {
        let html = '<h3>资源检查结果:</h3><ul>';
        results.forEach(result => {
          if (result.ok) {
            html += `<li style="color: #67c23a;">✅ ${result.resource} (${result.status}, ${result.size || 'unknown'} bytes)</li>`;
          } else {
            html += `<li style="color: #f56c6c;">❌ ${result.resource} (${result.error || result.status})</li>`;
          }
        });
        html += '</ul>';
        statusDiv.innerHTML = html;
      });
    }

    // 页面加载完成后自动检测
    document.addEventListener('DOMContentLoaded', function() {
      checkCSSFiles();
      
      // 检查是否有 Element Plus 样式
      setTimeout(() => {
        const hasElementStyles = getComputedStyle(document.body).getPropertyValue('--el-color-primary');
        if (hasElementStyles) {
          document.getElementById('css-status').innerHTML += '<div class="status success">Element Plus 样式变量已加载</div>';
        } else {
          document.getElementById('css-status').innerHTML += '<div class="status error">Element Plus 样式变量未加载</div>';
        }
      }, 1000);
    });
  </script>
</body>
</html>
