<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号排序功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .controls button:hover {
            background: #337ecc;
        }
        .controls button.secondary {
            background: #67c23a;
        }
        .controls button.secondary:hover {
            background: #529b2e;
        }
        .account-list {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .account-item {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid #eee;
            align-items: center;
            transition: all 0.3s ease;
        }
        .account-item:last-child {
            border-bottom: none;
        }
        .account-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .account-info {
            flex: 1;
            margin-right: 15px;
        }
        .account-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .account-platform {
            color: #666;
            font-size: 12px;
        }
        .update-date {
            flex: 0 0 120px;
            text-align: center;
            margin-right: 15px;
        }
        .expiry-status {
            flex: 0 0 120px;
            text-align: center;
            font-weight: 600;
        }
        /* 整行背景色样式 */
        .account-item.row-expired {
            background-color: #fef0f0;
            border-left: 4px solid #f56c6c;
        }
        .account-item.row-warning-1 {
            background-color: #fdf6ec;
            border-left: 4px solid #e6a23c;
        }
        .account-item.row-warning-2 {
            background-color: #fef5f5;
            border-left: 4px solid #f78989;
        }
        .account-item.row-warning-3 {
            background-color: #fffbf0;
            border-left: 4px solid #b88230;
        }
        /* 过期状态文本颜色 */
        .expiry-text-expired { color: #f56c6c; }
        .expiry-text-warning-1 { color: #e6a23c; }
        .expiry-text-warning-2 { color: #f78989; }
        .expiry-text-warning-3 { color: #b88230; }
        .expiry-text-normal { color: #67c23a; }
        .no-expiry { color: #909399; }
    </style>
</head>
<body>
    <div class="container">
        <h1>账号排序功能测试</h1>
        
        <div class="controls">
            <button onclick="sortByExpiryStatus()">按过期状态排序</button>
            <button onclick="sortByUpdateDate()">按更新日期排序</button>
            <button class="secondary" onclick="resetOrder()">恢复原始顺序</button>
            <button class="secondary" onclick="shuffleAccounts()">随机打乱</button>
        </div>
        
        <div class="account-list" id="accountList">
            <!-- 账号列表将在这里动态生成 -->
        </div>
    </div>

    <script>
        // 过期状态枚举
        const ExpiryStatus = {
            NORMAL: 'normal',
            WARNING_3: 'warning_3',
            WARNING_2: 'warning_2',
            WARNING_1: 'warning_1',
            EXPIRED: 'expired'
        };

        // 计算账号过期状态
        function calculateExpiryStatus(lastUpdatedDate) {
            if (!lastUpdatedDate) {
                return {
                    status: ExpiryStatus.NORMAL,
                    daysRemaining: 0,
                    message: '未设置更新日期',
                    color: '#909399',
                    bgColor: '#f4f4f5'
                };
            }

            const now = new Date();
            const updateDate = new Date(lastUpdatedDate);
            const timeDiff = updateDate.getTime() - now.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

            if (daysDiff < 0) {
                return {
                    status: ExpiryStatus.EXPIRED,
                    daysRemaining: daysDiff,
                    message: `已过期 ${Math.abs(daysDiff)} 天`,
                    color: '#f56c6c'
                };
            } else if (daysDiff === 0) {
                return {
                    status: ExpiryStatus.WARNING_1,
                    daysRemaining: daysDiff,
                    message: '今天过期',
                    color: '#e6a23c'
                };
            } else if (daysDiff === 1) {
                return {
                    status: ExpiryStatus.WARNING_1,
                    daysRemaining: daysDiff,
                    message: '1天后过期',
                    color: '#e6a23c'
                };
            } else if (daysDiff === 2) {
                return {
                    status: ExpiryStatus.WARNING_2,
                    daysRemaining: daysDiff,
                    message: '2天后过期',
                    color: '#f78989'
                };
            } else if (daysDiff === 3) {
                return {
                    status: ExpiryStatus.WARNING_3,
                    daysRemaining: daysDiff,
                    message: '3天后过期',
                    color: '#b88230'
                };
            } else {
                return {
                    status: ExpiryStatus.NORMAL,
                    daysRemaining: daysDiff,
                    message: `${daysDiff}天后过期`,
                    color: '#67c23a'
                };
            }
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN');
            } catch (error) {
                return dateString;
            }
        }

        // 获取行类名
        function getRowClassName(status) {
            switch (status) {
                case 'expired': return 'row-expired';
                case 'warning_1': return 'row-warning-1';
                case 'warning_2': return 'row-warning-2';
                case 'warning_3': return 'row-warning-3';
                default: return '';
            }
        }

        // 获取文本类名
        function getTextClassName(status) {
            switch (status) {
                case 'expired': return 'expiry-text-expired';
                case 'warning_1': return 'expiry-text-warning-1';
                case 'warning_2': return 'expiry-text-warning-2';
                case 'warning_3': return 'expiry-text-warning-3';
                default: return 'expiry-text-normal';
            }
        }

        // 测试数据
        let originalAccounts = [];
        let currentAccounts = [];

        // 生成测试数据
        function generateTestData() {
            const today = new Date();
            const platforms = ['YouTube', 'TikTok', '抖音', 'Instagram', 'Facebook'];
            
            originalAccounts = [
                { name: '测试账号1', platform: 'YouTube', last_updated_date: getDateString(today, -5) },
                { name: '测试账号2', platform: 'TikTok', last_updated_date: getDateString(today, 1) },
                { name: '测试账号3', platform: '抖音', last_updated_date: getDateString(today, -2) },
                { name: '测试账号4', platform: 'Instagram', last_updated_date: getDateString(today, 3) },
                { name: '测试账号5', platform: 'Facebook', last_updated_date: null },
                { name: '测试账号6', platform: 'YouTube', last_updated_date: getDateString(today, 0) },
                { name: '测试账号7', platform: 'TikTok', last_updated_date: getDateString(today, 2) },
                { name: '测试账号8', platform: '抖音', last_updated_date: getDateString(today, -10) },
                { name: '测试账号9', platform: 'Instagram', last_updated_date: getDateString(today, 7) },
                { name: '测试账号10', platform: 'Facebook', last_updated_date: getDateString(today, -1) }
            ];
            
            currentAccounts = [...originalAccounts];
        }

        // 获取日期字符串
        function getDateString(baseDate, dayOffset) {
            const date = new Date(baseDate);
            date.setDate(date.getDate() + dayOffset);
            return date.toISOString().split('T')[0];
        }

        // 渲染账号列表
        function renderAccounts() {
            const listElement = document.getElementById('accountList');
            
            const html = currentAccounts.map(account => {
                const expiryInfo = calculateExpiryStatus(account.last_updated_date);
                const rowClass = getRowClassName(expiryInfo.status);
                const textClass = account.last_updated_date ? getTextClassName(expiryInfo.status) : 'no-expiry';
                
                return `
                    <div class="account-item ${rowClass}">
                        <div class="account-info">
                            <div class="account-name">${account.name}</div>
                            <div class="account-platform">${account.platform}</div>
                        </div>
                        <div class="update-date">
                            ${formatDate(account.last_updated_date)}
                        </div>
                        <div class="expiry-status ${textClass}">
                            ${expiryInfo.message}
                        </div>
                    </div>
                `;
            }).join('');
            
            listElement.innerHTML = html;
        }

        // 按过期状态排序
        function sortByExpiryStatus() {
            currentAccounts.sort((a, b) => {
                const statusA = calculateExpiryStatus(a.last_updated_date);
                const statusB = calculateExpiryStatus(b.last_updated_date);
                
                const statusPriority = {
                    'expired': 1,
                    'warning_1': 2,
                    'warning_2': 3,
                    'warning_3': 4,
                    'normal': 5
                };
                
                const priorityA = statusPriority[statusA.status] || 6;
                const priorityB = statusPriority[statusB.status] || 6;
                
                if (priorityA === priorityB) {
                    return statusA.daysRemaining - statusB.daysRemaining;
                }
                
                return priorityA - priorityB;
            });
            
            renderAccounts();
        }

        // 按更新日期排序
        function sortByUpdateDate() {
            currentAccounts.sort((a, b) => {
                if (!a.last_updated_date && !b.last_updated_date) return 0;
                if (!a.last_updated_date) return 1;
                if (!b.last_updated_date) return -1;
                
                const dateA = new Date(a.last_updated_date);
                const dateB = new Date(b.last_updated_date);
                
                return dateB.getTime() - dateA.getTime();
            });
            
            renderAccounts();
        }

        // 恢复原始顺序
        function resetOrder() {
            currentAccounts = [...originalAccounts];
            renderAccounts();
        }

        // 随机打乱
        function shuffleAccounts() {
            for (let i = currentAccounts.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [currentAccounts[i], currentAccounts[j]] = [currentAccounts[j], currentAccounts[i]];
            }
            renderAccounts();
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateTestData();
            renderAccounts();
        });
    </script>
</body>
</html>
