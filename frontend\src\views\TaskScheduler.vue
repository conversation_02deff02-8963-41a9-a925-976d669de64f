<template>
  <div class="task-scheduler">
    <div class="header">
      <h1>任务调度管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建调度任务
      </el-button>
    </div>

    <!-- 任务列表 -->
    <el-card class="task-list">
      <template #header>
        <div class="card-header">
          <span>调度任务列表</span>
          <el-button text @click="loadTasks">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="tasks" v-loading="loading">
        <el-table-column prop="name" label="任务名称" width="200" />
        <el-table-column prop="task_type" label="任务类型" width="120">
          <template #default="{ row }">
            <el-tag :type="row.task_type === 'scheduled' ? 'primary' : 'success'">
              {{ row.task_type === 'scheduled' ? '定时任务' : '触发任务' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="trigger_type" label="触发器类型" width="120">
          <template #default="{ row }">
            <el-tag>{{ getTriggerTypeName(row.trigger_type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="execution_count" label="执行次数" width="100" />
        <el-table-column prop="last_execution" label="最后执行" width="180">
          <template #default="{ row }">
            {{ row.last_execution ? formatTime(row.last_execution) : '未执行' }}
          </template>
        </el-table-column>
        <el-table-column prop="next_execution" label="下次执行" width="180">
          <template #default="{ row }">
            {{ row.next_execution ? formatTime(row.next_execution) : '无计划' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button-group>
              <el-button 
                size="small" 
                :type="row.status === 'active' ? 'warning' : 'success'"
                @click="toggleTask(row)"
              >
                {{ row.status === 'active' ? '暂停' : '启用' }}
              </el-button>
              <el-button size="small" @click="editTask(row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteTask(row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑任务对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      :title="editingTask ? '编辑调度任务' : '创建调度任务'"
      width="800px"
    >
      <el-form :model="taskForm" :rules="taskRules" ref="taskFormRef" label-width="120px">
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input 
            v-model="taskForm.description" 
            type="textarea" 
            placeholder="请输入任务描述"
            :rows="2"
          />
        </el-form-item>

        <el-form-item label="任务类型" prop="task_type">
          <el-radio-group v-model="taskForm.task_type">
            <el-radio value="scheduled">定时任务</el-radio>
            <el-radio value="triggered">触发任务</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="触发器类型" prop="trigger_type">
          <el-select v-model="taskForm.trigger_type" placeholder="请选择触发器类型">
            <el-option 
              v-for="option in triggerTypeOptions" 
              :key="option.value"
              :label="option.label" 
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 定时配置 -->
        <div v-if="taskForm.task_type === 'scheduled'">
          <el-form-item 
            v-if="taskForm.trigger_type === 'cron'" 
            label="Cron表达式" 
            prop="cron_expression"
          >
            <el-input 
              v-model="taskForm.cron_expression" 
              placeholder="例如: 0 0 * * * (每天午夜执行)"
            />
            <div class="form-tip">
              格式：秒 分 时 日 月 周，例如：0 0 12 * * * (每天中午12点执行)
            </div>
          </el-form-item>
          
          <el-form-item 
            v-if="taskForm.trigger_type === 'interval'" 
            label="间隔时间(秒)" 
            prop="interval_seconds"
          >
            <el-input-number 
              v-model="taskForm.interval_seconds" 
              :min="60" 
              :max="86400"
              placeholder="间隔秒数"
            />
          </el-form-item>
        </div>

        <!-- 触发配置 -->
        <div v-if="taskForm.task_type === 'triggered'">
          <el-form-item 
            v-if="taskForm.trigger_type === 'file_watch'" 
            label="监控路径" 
            prop="watch_path"
          >
            <el-input v-model="taskForm.watch_path" placeholder="请输入要监控的文件或文件夹路径" />
          </el-form-item>
          
          <el-form-item 
            v-if="taskForm.trigger_type === 'account_update'" 
            label="账号ID" 
            prop="account_id"
          >
            <el-input v-model="taskForm.account_id" placeholder="请输入要监控的账号ID" />
          </el-form-item>
        </div>

        <!-- 任务模板配置 -->
        <el-divider content-position="left">任务模板配置</el-divider>
        
        <el-form-item label="任务类型" prop="template_task_type">
          <el-select v-model="taskForm.template_task_type" placeholder="请选择要执行的任务类型">
            <el-option label="YouTube上传" value="youtube_upload" />
            <el-option label="视频采集" value="collect" />
            <el-option label="对标下载" value="benchmark_download" />
          </el-select>
        </el-form-item>

        <el-form-item label="平台ID" prop="platform_id">
          <el-input v-model="taskForm.platform_id" placeholder="请输入平台ID" />
        </el-form-item>

        <el-form-item label="账号ID" prop="template_account_id">
          <el-input v-model="taskForm.template_account_id" placeholder="请输入账号ID" />
        </el-form-item>

        <el-form-item label="设备ID" prop="device_id">
          <el-input v-model="taskForm.device_id" placeholder="请输入设备ID" />
        </el-form-item>

        <el-form-item label="内容路径" prop="content_path">
          <el-input v-model="taskForm.content_path" placeholder="请输入内容路径" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTask">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { taskSchedulerApi } from '@/api/taskScheduler'

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const showCreateDialog = ref(false)
const editingTask = ref(null)
const taskFormRef = ref()

// 表单数据
const taskForm = reactive({
  name: '',
  description: '',
  task_type: 'scheduled',
  trigger_type: 'cron',
  cron_expression: '',
  interval_seconds: 3600,
  watch_path: '',
  account_id: '',
  template_task_type: '',
  platform_id: '',
  template_account_id: '',
  device_id: '',
  content_path: ''
})

// 表单验证规则
const taskRules = {
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  task_type: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  trigger_type: [{ required: true, message: '请选择触发器类型', trigger: 'change' }],
  template_task_type: [{ required: true, message: '请选择要执行的任务类型', trigger: 'change' }]
}

// 触发器类型选项
const triggerTypeOptions = [
  { label: 'Cron表达式', value: 'cron' },
  { label: '间隔时间', value: 'interval' },
  { label: '文件监控', value: 'file_watch' },
  { label: '账号更新', value: 'account_update' }
]

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    const response = await taskSchedulerApi.getTasks()
    tasks.value = response.data.tasks || []
  } catch (error) {
    ElMessage.error('加载任务列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const getTriggerTypeName = (type) => {
  const option = triggerTypeOptions.find(opt => opt.value === type)
  return option ? option.label : type
}

const getStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'paused': 'warning',
    'disabled': 'info',
    'expired': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusName = (status) => {
  const statusMap = {
    'active': '活跃',
    'paused': '暂停',
    'disabled': '禁用',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

const formatTime = (timeStr) => {
  if (!timeStr) return ''
  return new Date(timeStr).toLocaleString('zh-CN')
}

const toggleTask = async (task) => {
  try {
    if (task.status === 'active') {
      await taskSchedulerApi.pauseTask(task.task_id)
      ElMessage.success('任务已暂停')
    } else {
      await taskSchedulerApi.resumeTask(task.task_id)
      ElMessage.success('任务已启用')
    }
    await loadTasks()
  } catch (error) {
    ElMessage.error('操作失败：' + error.message)
  }
}

const editTask = (task) => {
  editingTask.value = task
  // 填充表单数据
  Object.assign(taskForm, {
    name: task.name,
    description: task.description,
    task_type: task.task_type,
    trigger_type: task.trigger_type,
    cron_expression: task.schedule_config?.cron_expression || '',
    interval_seconds: task.schedule_config?.interval_seconds || 3600,
    watch_path: task.trigger_config?.watch_path || '',
    account_id: task.trigger_config?.account_id || '',
    template_task_type: task.task_template?.task_type || '',
    platform_id: task.task_template?.platform_id || '',
    template_account_id: task.task_template?.account_id || '',
    device_id: task.task_template?.device_id || '',
    content_path: task.task_template?.content_path || ''
  })
  showCreateDialog.value = true
}

const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务"${task.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await taskSchedulerApi.deleteTask(task.task_id)
    ElMessage.success('任务已删除')
    await loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

const saveTask = async () => {
  try {
    await taskFormRef.value.validate()
    
    const taskData = {
      name: taskForm.name,
      description: taskForm.description,
      task_type: taskForm.task_type,
      trigger_type: taskForm.trigger_type,
      schedule_config: {},
      trigger_config: {},
      task_template: {
        task_type: taskForm.template_task_type,
        platform_id: taskForm.platform_id,
        account_id: taskForm.template_account_id,
        device_id: taskForm.device_id,
        content_path: taskForm.content_path
      },
      enabled: true
    }
    
    // 设置调度配置
    if (taskForm.task_type === 'scheduled') {
      if (taskForm.trigger_type === 'cron') {
        taskData.schedule_config.cron_expression = taskForm.cron_expression
      } else if (taskForm.trigger_type === 'interval') {
        taskData.schedule_config.interval_seconds = taskForm.interval_seconds
      }
    }
    
    // 设置触发配置
    if (taskForm.task_type === 'triggered') {
      if (taskForm.trigger_type === 'file_watch') {
        taskData.trigger_config.watch_path = taskForm.watch_path
      } else if (taskForm.trigger_type === 'account_update') {
        taskData.trigger_config.account_id = taskForm.account_id
      }
    }
    
    if (editingTask.value) {
      await taskSchedulerApi.updateTask(editingTask.value.task_id, taskData)
      ElMessage.success('任务已更新')
    } else {
      await taskSchedulerApi.createTask(taskData)
      ElMessage.success('任务已创建')
    }
    
    showCreateDialog.value = false
    resetForm()
    await loadTasks()
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  }
}

const resetForm = () => {
  Object.assign(taskForm, {
    name: '',
    description: '',
    task_type: 'scheduled',
    trigger_type: 'cron',
    cron_expression: '',
    interval_seconds: 3600,
    watch_path: '',
    account_id: '',
    template_task_type: '',
    platform_id: '',
    template_account_id: '',
    device_id: '',
    content_path: ''
  })
  editingTask.value = null
}

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.task-scheduler {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  color: #303133;
}

.task-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
