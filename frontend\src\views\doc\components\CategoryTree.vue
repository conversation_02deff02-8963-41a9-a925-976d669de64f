<template>
  <div class="category-tree">
    <div 
      v-for="category in categories" 
      :key="category.id"
      class="category-node"
    >
      <div 
        class="category-item"
        :class="{ 
          active: selectedId === category.id,
          'has-children': category.children.length > 0
        }"
        @click="selectCategory(category.id)"
      >
        <div class="category-content">
          <el-icon 
            v-if="category.children.length > 0"
            class="expand-icon"
            :class="{ expanded: expandedIds.includes(category.id) }"
            @click.stop="toggleExpand(category.id)"
          >
            <ArrowRight />
          </el-icon>
          
          <el-icon class="category-icon" :style="{ color: category.color }">
            <component :is="getIconComponent(category.icon)" />
          </el-icon>
          
          <span class="category-name">{{ category.name }}</span>
          
          <span class="content-count">({{ category.content_count }})</span>
        </div>
      </div>
      
      <!-- 子分类 -->
      <div 
        v-if="category.children.length > 0 && expandedIds.includes(category.id)"
        class="category-children"
      >
        <CategoryTree 
          :categories="category.children"
          :selected-id="selectedId"
          :level="level + 1"
          @select="$emit('select', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  ArrowRight,
  Folder,
  VideoPlay,
  Picture,
  Headphones,
  Document,
  Star,
  Tag
} from '@element-plus/icons-vue'
import type { CategoryTreeNode } from '@/api/content'

interface Props {
  categories: CategoryTreeNode[]
  selectedId?: string | null
  level?: number
}

const props = withDefaults(defineProps<Props>(), {
  selectedId: null,
  level: 0
})

const emit = defineEmits<{
  select: [categoryId: string]
}>()

// 展开的分类ID列表
const expandedIds = ref<string[]>([])

// 图标映射
const iconMap = {
  video: VideoPlay,
  image: Picture,
  audio: Headphones,
  text: Document,
  fire: Star,
  tag: Tag,
  folder: Folder
}

// 获取图标组件
const getIconComponent = (iconName?: string) => {
  if (!iconName) return Folder
  return iconMap[iconName as keyof typeof iconMap] || Folder
}

// 选择分类
const selectCategory = (categoryId: string) => {
  emit('select', categoryId)
}

// 切换展开状态
const toggleExpand = (categoryId: string) => {
  const index = expandedIds.value.indexOf(categoryId)
  if (index > -1) {
    expandedIds.value.splice(index, 1)
  } else {
    expandedIds.value.push(categoryId)
  }
}

// 计算缩进
const indentStyle = computed(() => ({
  paddingLeft: `${props.level * 20 + 20}px`
}))
</script>

<style scoped>
.category-tree {
  width: 100%;
}

.category-node {
  width: 100%;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  cursor: pointer;
  transition: all 0.2s;
  color: #606266;
  border-radius: 4px;
  margin: 0 8px;
}

.category-item:hover {
  background: #f5f7fa;
  color: #409eff;
}

.category-item.active {
  background: #ecf5ff;
  color: #409eff;
  font-weight: 500;
}

.category-content {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
  padding: 0 12px;
}

.expand-icon {
  font-size: 12px;
  transition: transform 0.2s;
  color: #909399;
  cursor: pointer;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.category-icon {
  font-size: 14px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-name {
  flex: 1;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content-count {
  font-size: 11px;
  color: #909399;
  font-weight: normal;
}

.category-children {
  margin-left: 16px;
}

/* 不同层级的样式 */
.category-item.has-children .category-content {
  font-weight: 500;
}

/* 动画效果 */
.category-children {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-item {
    margin: 0 4px;
  }
  
  .category-content {
    padding: 0 8px;
  }
  
  .category-name {
    font-size: 12px;
  }
  
  .content-count {
    font-size: 10px;
  }
}
</style>
