# Python 编码规范

基于 PEP 8（Python 风格指南）和 PEP 257（docstring 规范），结合通用实践。

## 1. 命名规范

- 变量和函数：使用小写字母加下划线（snake_case），如 `user_name`、`calculate_total`。
- 类名：使用帕斯卡命名法（PascalCase），如 `UserProfile`。
- 常量：全大写加下划线，如 `MAX_RETRIES`。
- 私有：以单下划线开头表示受保护（如 `_internal_data`），双下划线表示私有（如 `__private_method`）。
- 避免：拼音、单字母名称（除计数器如 i、j）、与关键字冲突的名称。

## 2. 代码格式化

### 缩进
- 使用 4 个空格，避免制表符（Tab）。

### 行长度
- 每行最多 79 字符（或 120 字符，视团队约定）。长行使用换行符 `\` 或括号对齐：

```python
long_function_name(
    param1, param2,
    param3
)
```

### 空格
- 操作符两侧加空格，如 `x = y + z`。
- 逗号后加空格，如 `func(a, b, c)`。
- 函数参数默认值等号两侧无空格，如 `def func(x=0)`。

### 空行
- 函数和类定义之间空 2 行。
- 类方法之间空 1 行。
- 逻辑块内根据需要空 1 行。

### 文件编码
- 使用 UTF-8，文件开头添加：

```python
# -*- coding: utf-8 -*-
```

## 3. 注释和文档

### 行内注释
- 简短，放在代码上方或行尾（至少 2 个空格间隔）。

例如：

```python
total += item.price  # 添加商品价格到总计
```

### 文档字符串（Docstring）
- 所有模块、类、公共函数使用三引号 `"""` 编写 docstring。
- 遵循 Google 或 NumPy 风格，例如：

```python
def calculate_total(items: list) -> float:
    """Calculate the total price of items.

    Args:
        items: List of items with price attribute.

    Returns:
        Total price as a float.
    """
    return sum(item.price for item in items)
```

- 避免无意义注释：如 `x = x + 1  # 增加 x`。

## 4. 函数和类设计

### 函数
- 单一职责，长度控制在 50 行以内。
- 参数尽量少于 5 个，过多时使用字典或数据类。
- 明确类型注解（如 `def func(x: int) -> str`）。

### 类
- 遵循封装原则，优先使用属性（@property）管理访问。
- 方法名使用 snake_case，私有方法以 `_` 开头。

### 异常处理
- 使用具体异常，如 `except ValueError:` 而非 `except:`。
- 避免空异常块，提供错误信息或日志。

## 5. 导入规范

### 顺序
1. 标准库（如 `os`、`sys`）。
2. 第三方库（如 `requests`、`numpy`）。
3. 本地模块（如 `from my_module import my_func`）。
- 每组之间空 1 行。

```python
import os
import sys

import requests

from my_module import my_func
```

- 避免：`from module import *`。
- 相对导入：在包内使用显式相对导入，如 `from .utils import helper`。

## 6. 日志和错误处理

### 日志
- 使用 `logging` 模块，而非 `print`。

```python
import logging
logging.basicConfig(level=logging.INFO)
logging.info("Processing data")
```

### 错误处理
- 提供清晰的错误信息。
- 记录异常堆栈：

```python
try:
    result = risky_operation()
except ValueError as e:
    logging.error(f"Operation failed: {e}")
    raise
```

## 7. 工具和检查

- 格式化：使用 `black` 自动格式化代码。
- Lint 检查：使用 `flake8` 或 `pylint` 检查风格和潜在错误。
- 类型检查：使用 `mypy` 进行静态类型检查。
- 配置文件：项目根目录添加 `.flake8` 或 `pyproject.toml` 配置规范。

## 8. 其他

### 文件结构
- 按功能模块化，如 `models/`、`utils/`、`tests/`。
- 每个模块职责单一。

### 测试
- 编写单元测试，使用 `unittest` 或 `pytest`。

### 安全
- 避免硬编码敏感信息，使用环境变量（如 `os.getenv`）。
- 对用户输入进行验证，防止注入攻击。
