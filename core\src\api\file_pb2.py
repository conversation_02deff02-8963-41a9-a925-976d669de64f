# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: file.proto
# Protobuf Python Version: 6.30.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    30,
    0,
    '',
    'file.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nfile.proto\x12\x04\x66ile\"\'\n\x10\x46ilePathsRequest\x12\x13\n\x0bplatform_id\x18\x01 \x01(\t\"\x88\x01\n\x11\x46ilePathsResponse\x12\x16\n\x0e\x66ile_root_path\x18\x01 \x01(\t\x12\x1e\n\x16platform_path_template\x18\x02 \x01(\t\x12\x1c\n\x14\x64\x65vice_path_template\x18\x03 \x01(\t\x12\x1d\n\x15\x63ontent_path_template\x18\x04 \x01(\t\"p\n\x14ListDirectoryRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x19\n\x11\x66ilter_extensions\x18\x02 \x03(\t\x12\x13\n\x0binclude_md5\x18\x03 \x01(\x08\x12\x1a\n\x12include_media_info\x18\x04 \x01(\x08\"6\n\x15ListDirectoryResponse\x12\x1d\n\x05\x66iles\x18\x01 \x03(\x0b\x32\x0e.file.FileInfo\"!\n\x11PathExistsRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\":\n\x12PathExistsResponse\x12\x0e\n\x06\x65xists\x18\x01 \x01(\x08\x12\x14\n\x0cis_directory\x18\x02 \x01(\x08\"(\n\x12\x44\x65leteFilesRequest\x12\x12\n\nfile_paths\x18\x01 \x03(\t\"b\n\x13\x44\x65leteFilesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rdeleted_count\x18\x02 \x01(\x05\x12\x13\n\x0btotal_count\x18\x03 \x01(\x05\x12\x0e\n\x06\x65rrors\x18\x04 \x03(\t\"?\n\x10MoveFilesRequest\x12+\n\noperations\x18\x01 \x03(\x0b\x32\x17.file.FileMoveOperation\"=\n\x11\x46ileMoveOperation\x12\x13\n\x0bsource_path\x18\x01 \x01(\t\x12\x13\n\x0btarget_path\x18\x02 \x01(\t\"^\n\x11MoveFilesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x13\n\x0bmoved_count\x18\x02 \x01(\x05\x12\x13\n\x0btotal_count\x18\x03 \x01(\x05\x12\x0e\n\x06\x65rrors\x18\x04 \x03(\t\"H\n\x16\x43reateDirectoryRequest\x12\x16\n\x0e\x64irectory_path\x18\x01 \x01(\t\x12\x16\n\x0e\x63reate_parents\x18\x02 \x01(\x08\"O\n\x17\x43reateDirectoryResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12\x14\n\x0c\x63reated_path\x18\x03 \x01(\t\"\xee\x01\n\x1c\x41rchivePublishedFilesRequest\x12\x13\n\x0b\x66older_path\x18\x01 \x01(\t\x12\x1b\n\x13\x61rchive_folder_name\x18\x02 \x01(\t\x12\x11\n\tplatforms\x18\x03 \x03(\t\x12Q\n\x10published_status\x18\x04 \x03(\x0b\x32\x37.file.ArchivePublishedFilesRequest.PublishedStatusEntry\x1a\x36\n\x14PublishedStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"\x9f\x01\n\x1d\x41rchivePublishedFilesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x16\n\x0e\x61rchived_count\x18\x02 \x01(\x05\x12\x15\n\rskipped_count\x18\x03 \x01(\x05\x12\x16\n\x0e\x61rchive_folder\x18\x04 \x01(\t\x12\x16\n\x0e\x61rchived_files\x18\x05 \x03(\t\x12\x0e\n\x06\x65rrors\x18\x06 \x03(\t\"\x98\x01\n\x08\x46ileInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x14\n\x0cis_directory\x18\x03 \x01(\x08\x12\x0c\n\x04size\x18\x04 \x01(\x03\x12\x15\n\rmodified_time\x18\x05 \x01(\x03\x12\x10\n\x08md5_hash\x18\x06 \x01(\t\x12#\n\nmedia_info\x18\x07 \x01(\x0b\x32\x0f.file.MediaInfo\"\x80\x01\n\tMediaInfo\x12\x10\n\x08\x64uration\x18\x01 \x01(\x05\x12\x12\n\nresolution\x18\x02 \x01(\t\x12\x13\n\x0bvideo_codec\x18\x03 \x01(\t\x12\x13\n\x0b\x61udio_codec\x18\x04 \x01(\t\x12\x12\n\nframe_rate\x18\x05 \x01(\x02\x12\x0f\n\x07\x62itrate\x18\x06 \x01(\x05\"\x9d\x01\n\x18\x43reateTripleVideoRequest\x12\x13\n\x0b\x66older_path\x18\x01 \x01(\t\x12\x13\n\x0boutput_path\x18\x02 \x01(\t\x12\"\n\x1avideo_duration_per_segment\x18\x03 \x01(\x05\x12\x1b\n\x13transition_duration\x18\x04 \x01(\x02\x12\x16\n\x0eoutput_quality\x18\x05 \x01(\t\"j\n\x19\x43reateTripleVideoResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12\x13\n\x0boutput_file\x18\x03 \x01(\t\x12\x18\n\x10processed_videos\x18\x04 \x01(\x05\"\xb5\x01\n\x12MergeVideosRequest\x12\x13\n\x0b\x66older_path\x18\x01 \x01(\t\x12\x1b\n\x13target_duration_min\x18\x02 \x01(\x05\x12\x1b\n\x13target_duration_max\x18\x03 \x01(\x05\x12\x1a\n\x12\x65nable_transitions\x18\x04 \x01(\x08\x12\x16\n\x0eoutput_quality\x18\x05 \x01(\t\x12\x1c\n\x14max_videos_per_merge\x18\x06 \x01(\x05\"\x80\x01\n\x13MergeVideosResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12\x14\n\x0coutput_files\x18\x03 \x03(\t\x12\x18\n\x10processed_videos\x18\x04 \x01(\x05\x12\x19\n\x11successful_merges\x18\x05 \x01(\x05\"\xa9\x01\n\x16\x44\x65tectWatermarkRequest\x12\x12\n\nvideo_path\x18\x01 \x01(\t\x12\x16\n\x0e\x64\x65tection_mode\x18\x02 \x01(\t\x12\x15\n\rtemplate_path\x18\x03 \x01(\t\x12\x18\n\x10\x64\x65tection_region\x18\x04 \x01(\t\x12\x13\n\x0bsensitivity\x18\x05 \x01(\x02\x12\x1d\n\x15save_detection_result\x18\x06 \x01(\x08\"\xb8\x01\n\x17\x44\x65tectWatermarkResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12\x1a\n\x12watermark_detected\x18\x03 \x01(\x08\x12\'\n\nwatermarks\x18\x04 \x03(\x0b\x32\x13.file.WatermarkInfo\x12\x1d\n\x15\x64\x65tection_result_path\x18\x05 \x01(\t\x12\x19\n\x11\x64\x65tection_time_ms\x18\x06 \x01(\x03\"\xc9\x01\n\x16RemoveWatermarkRequest\x12\x18\n\x10input_video_path\x18\x01 \x01(\t\x12\x19\n\x11output_video_path\x18\x02 \x01(\t\x12\x14\n\x0cremoval_mode\x18\x03 \x01(\t\x12\x19\n\x11watermark_regions\x18\x04 \x03(\t\x12\x16\n\x0einpaint_method\x18\x05 \x01(\t\x12\x16\n\x0eoutput_quality\x18\x06 \x01(\t\x12\x19\n\x11preserve_encoding\x18\x07 \x01(\x08\"\xc7\x01\n\x17RemoveWatermarkResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12\x18\n\x10output_file_path\x18\x03 \x01(\t\x12\x1a\n\x12processing_time_ms\x18\x04 \x01(\x03\x12\x1a\n\x12original_file_size\x18\x05 \x01(\x03\x12\x18\n\x10output_file_size\x18\x06 \x01(\x03\x12 \n\x18removed_watermarks_count\x18\x07 \x01(\x05\"\x9a\x02\n\x1c\x42\x61tchProcessWatermarkRequest\x12\x19\n\x11input_folder_path\x18\x01 \x01(\t\x12\x1a\n\x12output_folder_path\x18\x02 \x01(\t\x12\x14\n\x0cprocess_mode\x18\x03 \x01(\t\x12\x36\n\x10\x64\x65tection_config\x18\x04 \x01(\x0b\x32\x1c.file.DetectWatermarkRequest\x12\x34\n\x0eremoval_config\x18\x05 \x01(\x0b\x32\x1c.file.RemoveWatermarkRequest\x12\x14\n\x0c\x66ile_filters\x18\x06 \x03(\t\x12\x11\n\trecursive\x18\x07 \x01(\x08\x12\x16\n\x0emax_concurrent\x18\x08 \x01(\x05\"\xd1\x01\n\x1d\x42\x61tchProcessWatermarkResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12)\n\x07results\x18\x03 \x03(\x0b\x32\x18.file.BatchProcessResult\x12\x13\n\x0btotal_files\x18\x04 \x01(\x05\x12\x18\n\x10successful_files\x18\x05 \x01(\x05\x12\x14\n\x0c\x66\x61iled_files\x18\x06 \x01(\x05\x12 \n\x18total_processing_time_ms\x18\x07 \x01(\x03\"v\n\rWatermarkInfo\x12\x16\n\x0ewatermark_type\x18\x01 \x01(\t\x12\x10\n\x08position\x18\x02 \x01(\t\x12\x12\n\nconfidence\x18\x03 \x01(\x02\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x12\n\ntime_range\x18\x05 \x01(\t\"\xda\x01\n\x12\x42\x61tchProcessResult\x12\x11\n\tfile_path\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x15\n\rerror_message\x18\x03 \x01(\t\x12\x37\n\x10\x64\x65tection_result\x18\x04 \x01(\x0b\x32\x1d.file.DetectWatermarkResponse\x12\x35\n\x0eremoval_result\x18\x05 \x01(\x0b\x32\x1d.file.RemoveWatermarkResponse\x12\x1a\n\x12processing_time_ms\x18\x06 \x01(\x03\"\x8d\x01\n\x13RotateVideosRequest\x12\x13\n\x0bvideo_paths\x18\x01 \x03(\t\x12\x16\n\x0erotation_angle\x18\x02 \x01(\x05\x12\x16\n\x0eoutput_quality\x18\x03 \x01(\t\x12\x1a\n\x12overwrite_original\x18\x04 \x01(\x08\x12\x15\n\routput_suffix\x18\x05 \x01(\t\"\xb4\x01\n\x14RotateVideosResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12*\n\x07results\x18\x03 \x03(\x0b\x32\x19.file.VideoRotationResult\x12\x18\n\x10successful_count\x18\x04 \x01(\x05\x12\x14\n\x0c\x66\x61iled_count\x18\x05 \x01(\x05\x12 \n\x18total_processing_time_ms\x18\x06 \x01(\x03\"\xbb\x01\n\x13VideoRotationResult\x12\x15\n\roriginal_path\x18\x01 \x01(\t\x12\x13\n\x0boutput_path\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\x12\x15\n\rerror_message\x18\x04 \x01(\t\x12\x1a\n\x12processing_time_ms\x18\x05 \x01(\x03\x12\x1a\n\x12original_file_size\x18\x06 \x01(\x03\x12\x18\n\x10output_file_size\x18\x07 \x01(\x03\"\xa8\x01\n\x1dGenerateVideoThumbnailRequest\x12\x12\n\nvideo_path\x18\x01 \x01(\t\x12\x16\n\x0ethumbnail_path\x18\x02 \x01(\t\x12\x11\n\ttimestamp\x18\x03 \x01(\x02\x12\r\n\x05width\x18\x04 \x01(\x05\x12\x0e\n\x06height\x18\x05 \x01(\x05\x12\x0f\n\x07quality\x18\x06 \x01(\x05\x12\x18\n\x10\x66orce_regenerate\x18\x07 \x01(\x08\"\xb7\x01\n\x1eGenerateVideoThumbnailResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12\x16\n\x0ethumbnail_path\x18\x03 \x01(\t\x12\x15\n\rthumbnail_url\x18\x04 \x01(\t\x12\x16\n\x0ethumbnail_size\x18\x05 \x01(\x03\x12\x1a\n\x12generation_time_ms\x18\x06 \x01(\x03\x12\x12\n\nfrom_cache\x18\x07 \x01(\x08\"n\n\x1aGetVideoPreviewInfoRequest\x12\x12\n\nvideo_path\x18\x01 \x01(\t\x12\x19\n\x11include_thumbnail\x18\x02 \x01(\x08\x12!\n\x19include_detailed_metadata\x18\x03 \x01(\x08\"\xcc\x01\n\x1bGetVideoPreviewInfoResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12#\n\nmedia_info\x18\x03 \x01(\x0b\x32\x0f.file.MediaInfo\x12\x30\n\x0ethumbnail_info\x18\x04 \x01(\x0b\x32\x18.file.VideoThumbnailInfo\x12\x36\n\x11\x64\x65tailed_metadata\x18\x05 \x01(\x0b\x32\x1b.file.VideoDetailedMetadata\"\xa8\x01\n\x1fGenerateVideoPreviewClipRequest\x12\x12\n\nvideo_path\x18\x01 \x01(\t\x12\x19\n\x11preview_clip_path\x18\x02 \x01(\t\x12\x12\n\nstart_time\x18\x03 \x01(\x02\x12\x10\n\x08\x64uration\x18\x04 \x01(\x02\x12\x16\n\x0eoutput_quality\x18\x05 \x01(\t\x12\x18\n\x10\x66orce_regenerate\x18\x06 \x01(\x08\"\xa8\x01\n GenerateVideoPreviewClipResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12\x19\n\x11preview_clip_path\x18\x03 \x01(\t\x12\x19\n\x11preview_clip_size\x18\x04 \x01(\x03\x12\x1a\n\x12generation_time_ms\x18\x05 \x01(\x03\x12\x12\n\nfrom_cache\x18\x06 \x01(\x08\"\x8b\x01\n\x12VideoThumbnailInfo\x12\x16\n\x0ethumbnail_path\x18\x01 \x01(\t\x12\x16\n\x0ethumbnail_size\x18\x02 \x01(\x03\x12\r\n\x05width\x18\x03 \x01(\x05\x12\x0e\n\x06height\x18\x04 \x01(\x05\x12\x16\n\x0egenerated_time\x18\x05 \x01(\x03\x12\x0e\n\x06\x65xists\x18\x06 \x01(\x08\"\x90\x02\n\x15VideoDetailedMetadata\x12\x0e\n\x06\x66ormat\x18\x01 \x01(\t\x12,\n\rvideo_streams\x18\x02 \x03(\x0b\x32\x15.file.VideoStreamInfo\x12,\n\raudio_streams\x18\x03 \x03(\x0b\x32\x15.file.AudioStreamInfo\x12\x32\n\x10subtitle_streams\x18\x04 \x03(\x0b\x32\x18.file.SubtitleStreamInfo\x12\x15\n\rcreation_time\x18\x05 \x01(\t\x12\r\n\x05title\x18\x06 \x01(\t\x12\x0e\n\x06\x61uthor\x18\x07 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x08 \x01(\t\x12\x0c\n\x04tags\x18\t \x03(\t\"\x93\x01\n\x0fVideoStreamInfo\x12\r\n\x05index\x18\x01 \x01(\x05\x12\r\n\x05\x63odec\x18\x02 \x01(\t\x12\x12\n\nresolution\x18\x03 \x01(\t\x12\x12\n\nframe_rate\x18\x04 \x01(\x02\x12\x0f\n\x07\x62itrate\x18\x05 \x01(\x05\x12\x14\n\x0cpixel_format\x18\x06 \x01(\t\x12\x13\n\x0b\x63olor_space\x18\x07 \x01(\t\"\x7f\n\x0f\x41udioStreamInfo\x12\r\n\x05index\x18\x01 \x01(\x05\x12\r\n\x05\x63odec\x18\x02 \x01(\t\x12\x13\n\x0bsample_rate\x18\x03 \x01(\x05\x12\x10\n\x08\x63hannels\x18\x04 \x01(\x05\x12\x0f\n\x07\x62itrate\x18\x05 \x01(\x05\x12\x16\n\x0e\x63hannel_layout\x18\x06 \x01(\t\"S\n\x12SubtitleStreamInfo\x12\r\n\x05index\x18\x01 \x01(\x05\x12\r\n\x05\x63odec\x18\x02 \x01(\t\x12\x10\n\x08language\x18\x03 \x01(\t\x12\r\n\x05title\x18\x04 \x01(\t2\xc1\n\n\x0b\x46ileService\x12\x41\n\x0cGetFilePaths\x12\x16.file.FilePathsRequest\x1a\x17.file.FilePathsResponse\"\x00\x12J\n\rListDirectory\x12\x1a.file.ListDirectoryRequest\x1a\x1b.file.ListDirectoryResponse\"\x00\x12\x46\n\x0f\x43heckPathExists\x12\x17.file.PathExistsRequest\x1a\x18.file.PathExistsResponse\"\x00\x12\x44\n\x0b\x44\x65leteFiles\x12\x18.file.DeleteFilesRequest\x1a\x19.file.DeleteFilesResponse\"\x00\x12>\n\tMoveFiles\x12\x16.file.MoveFilesRequest\x1a\x17.file.MoveFilesResponse\"\x00\x12P\n\x0f\x43reateDirectory\x12\x1c.file.CreateDirectoryRequest\x1a\x1d.file.CreateDirectoryResponse\"\x00\x12\x62\n\x15\x41rchivePublishedFiles\x12\".file.ArchivePublishedFilesRequest\x1a#.file.ArchivePublishedFilesResponse\"\x00\x12V\n\x11\x43reateTripleVideo\x12\x1e.file.CreateTripleVideoRequest\x1a\x1f.file.CreateTripleVideoResponse\"\x00\x12\x44\n\x0bMergeVideos\x12\x18.file.MergeVideosRequest\x1a\x19.file.MergeVideosResponse\"\x00\x12P\n\x0f\x44\x65tectWatermark\x12\x1c.file.DetectWatermarkRequest\x1a\x1d.file.DetectWatermarkResponse\"\x00\x12P\n\x0fRemoveWatermark\x12\x1c.file.RemoveWatermarkRequest\x1a\x1d.file.RemoveWatermarkResponse\"\x00\x12\x62\n\x15\x42\x61tchProcessWatermark\x12\".file.BatchProcessWatermarkRequest\x1a#.file.BatchProcessWatermarkResponse\"\x00\x12G\n\x0cRotateVideos\x12\x19.file.RotateVideosRequest\x1a\x1a.file.RotateVideosResponse\"\x00\x12\x65\n\x16GenerateVideoThumbnail\x12#.file.GenerateVideoThumbnailRequest\x1a$.file.GenerateVideoThumbnailResponse\"\x00\x12\\\n\x13GetVideoPreviewInfo\x12 .file.GetVideoPreviewInfoRequest\x1a!.file.GetVideoPreviewInfoResponse\"\x00\x12k\n\x18GenerateVideoPreviewClip\x12%.file.GenerateVideoPreviewClipRequest\x1a&.file.GenerateVideoPreviewClipResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'file_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_ARCHIVEPUBLISHEDFILESREQUEST_PUBLISHEDSTATUSENTRY']._loaded_options = None
  _globals['_ARCHIVEPUBLISHEDFILESREQUEST_PUBLISHEDSTATUSENTRY']._serialized_options = b'8\001'
  _globals['_FILEPATHSREQUEST']._serialized_start=20
  _globals['_FILEPATHSREQUEST']._serialized_end=59
  _globals['_FILEPATHSRESPONSE']._serialized_start=62
  _globals['_FILEPATHSRESPONSE']._serialized_end=198
  _globals['_LISTDIRECTORYREQUEST']._serialized_start=200
  _globals['_LISTDIRECTORYREQUEST']._serialized_end=312
  _globals['_LISTDIRECTORYRESPONSE']._serialized_start=314
  _globals['_LISTDIRECTORYRESPONSE']._serialized_end=368
  _globals['_PATHEXISTSREQUEST']._serialized_start=370
  _globals['_PATHEXISTSREQUEST']._serialized_end=403
  _globals['_PATHEXISTSRESPONSE']._serialized_start=405
  _globals['_PATHEXISTSRESPONSE']._serialized_end=463
  _globals['_DELETEFILESREQUEST']._serialized_start=465
  _globals['_DELETEFILESREQUEST']._serialized_end=505
  _globals['_DELETEFILESRESPONSE']._serialized_start=507
  _globals['_DELETEFILESRESPONSE']._serialized_end=605
  _globals['_MOVEFILESREQUEST']._serialized_start=607
  _globals['_MOVEFILESREQUEST']._serialized_end=670
  _globals['_FILEMOVEOPERATION']._serialized_start=672
  _globals['_FILEMOVEOPERATION']._serialized_end=733
  _globals['_MOVEFILESRESPONSE']._serialized_start=735
  _globals['_MOVEFILESRESPONSE']._serialized_end=829
  _globals['_CREATEDIRECTORYREQUEST']._serialized_start=831
  _globals['_CREATEDIRECTORYREQUEST']._serialized_end=903
  _globals['_CREATEDIRECTORYRESPONSE']._serialized_start=905
  _globals['_CREATEDIRECTORYRESPONSE']._serialized_end=984
  _globals['_ARCHIVEPUBLISHEDFILESREQUEST']._serialized_start=987
  _globals['_ARCHIVEPUBLISHEDFILESREQUEST']._serialized_end=1225
  _globals['_ARCHIVEPUBLISHEDFILESREQUEST_PUBLISHEDSTATUSENTRY']._serialized_start=1171
  _globals['_ARCHIVEPUBLISHEDFILESREQUEST_PUBLISHEDSTATUSENTRY']._serialized_end=1225
  _globals['_ARCHIVEPUBLISHEDFILESRESPONSE']._serialized_start=1228
  _globals['_ARCHIVEPUBLISHEDFILESRESPONSE']._serialized_end=1387
  _globals['_FILEINFO']._serialized_start=1390
  _globals['_FILEINFO']._serialized_end=1542
  _globals['_MEDIAINFO']._serialized_start=1545
  _globals['_MEDIAINFO']._serialized_end=1673
  _globals['_CREATETRIPLEVIDEOREQUEST']._serialized_start=1676
  _globals['_CREATETRIPLEVIDEOREQUEST']._serialized_end=1833
  _globals['_CREATETRIPLEVIDEORESPONSE']._serialized_start=1835
  _globals['_CREATETRIPLEVIDEORESPONSE']._serialized_end=1941
  _globals['_MERGEVIDEOSREQUEST']._serialized_start=1944
  _globals['_MERGEVIDEOSREQUEST']._serialized_end=2125
  _globals['_MERGEVIDEOSRESPONSE']._serialized_start=2128
  _globals['_MERGEVIDEOSRESPONSE']._serialized_end=2256
  _globals['_DETECTWATERMARKREQUEST']._serialized_start=2259
  _globals['_DETECTWATERMARKREQUEST']._serialized_end=2428
  _globals['_DETECTWATERMARKRESPONSE']._serialized_start=2431
  _globals['_DETECTWATERMARKRESPONSE']._serialized_end=2615
  _globals['_REMOVEWATERMARKREQUEST']._serialized_start=2618
  _globals['_REMOVEWATERMARKREQUEST']._serialized_end=2819
  _globals['_REMOVEWATERMARKRESPONSE']._serialized_start=2822
  _globals['_REMOVEWATERMARKRESPONSE']._serialized_end=3021
  _globals['_BATCHPROCESSWATERMARKREQUEST']._serialized_start=3024
  _globals['_BATCHPROCESSWATERMARKREQUEST']._serialized_end=3306
  _globals['_BATCHPROCESSWATERMARKRESPONSE']._serialized_start=3309
  _globals['_BATCHPROCESSWATERMARKRESPONSE']._serialized_end=3518
  _globals['_WATERMARKINFO']._serialized_start=3520
  _globals['_WATERMARKINFO']._serialized_end=3638
  _globals['_BATCHPROCESSRESULT']._serialized_start=3641
  _globals['_BATCHPROCESSRESULT']._serialized_end=3859
  _globals['_ROTATEVIDEOSREQUEST']._serialized_start=3862
  _globals['_ROTATEVIDEOSREQUEST']._serialized_end=4003
  _globals['_ROTATEVIDEOSRESPONSE']._serialized_start=4006
  _globals['_ROTATEVIDEOSRESPONSE']._serialized_end=4186
  _globals['_VIDEOROTATIONRESULT']._serialized_start=4189
  _globals['_VIDEOROTATIONRESULT']._serialized_end=4376
  _globals['_GENERATEVIDEOTHUMBNAILREQUEST']._serialized_start=4379
  _globals['_GENERATEVIDEOTHUMBNAILREQUEST']._serialized_end=4547
  _globals['_GENERATEVIDEOTHUMBNAILRESPONSE']._serialized_start=4550
  _globals['_GENERATEVIDEOTHUMBNAILRESPONSE']._serialized_end=4733
  _globals['_GETVIDEOPREVIEWINFOREQUEST']._serialized_start=4735
  _globals['_GETVIDEOPREVIEWINFOREQUEST']._serialized_end=4845
  _globals['_GETVIDEOPREVIEWINFORESPONSE']._serialized_start=4848
  _globals['_GETVIDEOPREVIEWINFORESPONSE']._serialized_end=5052
  _globals['_GENERATEVIDEOPREVIEWCLIPREQUEST']._serialized_start=5055
  _globals['_GENERATEVIDEOPREVIEWCLIPREQUEST']._serialized_end=5223
  _globals['_GENERATEVIDEOPREVIEWCLIPRESPONSE']._serialized_start=5226
  _globals['_GENERATEVIDEOPREVIEWCLIPRESPONSE']._serialized_end=5394
  _globals['_VIDEOTHUMBNAILINFO']._serialized_start=5397
  _globals['_VIDEOTHUMBNAILINFO']._serialized_end=5536
  _globals['_VIDEODETAILEDMETADATA']._serialized_start=5539
  _globals['_VIDEODETAILEDMETADATA']._serialized_end=5811
  _globals['_VIDEOSTREAMINFO']._serialized_start=5814
  _globals['_VIDEOSTREAMINFO']._serialized_end=5961
  _globals['_AUDIOSTREAMINFO']._serialized_start=5963
  _globals['_AUDIOSTREAMINFO']._serialized_end=6090
  _globals['_SUBTITLESTREAMINFO']._serialized_start=6092
  _globals['_SUBTITLESTREAMINFO']._serialized_end=6175
  _globals['_FILESERVICE']._serialized_start=6178
  _globals['_FILESERVICE']._serialized_end=7523

# 手动添加视频加速相关类
class AccelerateVideosRequest:
    def __init__(self):
        self.video_paths = []
        self.target_duration = 59
        self.output_quality = 'medium'
        self.overwrite_original = False
        self.output_suffix = ''

    @staticmethod
    def FromString(data):
        # 简单的反序列化实现
        return AccelerateVideosRequest()

    def SerializeToString(self):
        # 简单的序列化实现
        return b''

class VideoAccelerationResult:
    def __init__(self):
        self.original_path = ''
        self.output_path = ''
        self.success = False
        self.error_message = ''
        self.processing_time_ms = 0
        self.original_file_size = 0
        self.output_file_size = 0
        self.original_duration = 0.0
        self.output_duration = 0.0
        self.speed_factor = 0.0

class AccelerateVideosResponse:
    def __init__(self):
        self.success = False
        self.error = ''
        self.results = []
        self.total_processing_time_ms = 0
        self.successful_count = 0
        self.failed_count = 0

    @staticmethod
    def FromString(data):
        # 简单的反序列化实现
        return AccelerateVideosResponse()

    def SerializeToString(self):
        # 简单的序列化实现
        return b''

# @@protoc_insertion_point(module_scope)
