<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`🎬 视频预览 - ${videoInfo?.name || '未知视频'}`"
    width="900px"
    @close="handleClose"
  >
    <div class="video-preview-container" v-loading="loading">
      <!-- 错误信息显示 -->
      <div v-if="errorMessage" class="error-message">
        <el-alert
          :title="errorMessage"
          type="error"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>可能的解决方案：</p>
            <ul>
              <li>确保Backend服务运行在端口8000</li>
              <li>确保Core服务运行在端口50051</li>
              <li>确保文件服务器运行在端口8001</li>
              <li>检查视频文件是否存在且可访问</li>
              <li>检查防火墙设置</li>
            </ul>
          </template>
        </el-alert>
      </div>

      <!-- 视频信息头部 -->
      <div class="video-header" v-if="videoInfo">
        <div class="video-basic-info">
          <h3 class="video-title">{{ videoInfo.name }}</h3>
          <div class="video-meta">
            <span class="meta-item">
              <el-icon><Document /></el-icon>
              {{ formatFileSize(videoInfo.size) }}
            </span>
            <span class="meta-item" v-if="previewInfo?.media_info">
              <el-icon><Timer /></el-icon>
              {{ formatDuration(previewInfo.media_info.duration) }}
            </span>
            <span class="meta-item" v-if="previewInfo?.media_info">
              <el-icon><Monitor /></el-icon>
              {{ previewInfo.media_info.resolution }}
            </span>
          </div>
        </div>
      </div>

      <!-- 视频预览区域 -->
      <div class="video-preview-area">
        <!-- 缩略图显示 -->
        <div class="thumbnail-section" v-if="!showVideoPlayer">
          <div class="thumbnail-container">
            <img 
              v-if="thumbnailUrl" 
              :src="thumbnailUrl" 
              :alt="videoInfo?.name"
              class="video-thumbnail"
              @error="handleThumbnailError"
            />
            <div v-else class="thumbnail-placeholder">
              <el-icon size="64"><VideoPlay /></el-icon>
              <p>缩略图生成中...</p>
            </div>
            
            <!-- 播放按钮覆盖层 -->
            <div class="play-overlay" @click="startVideoPreview">
              <el-button type="primary" size="large" circle>
                <el-icon size="24"><VideoPlay /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 视频播放器 -->
        <div class="video-player-section" v-if="showVideoPlayer">
          <div v-if="!videoUrl" class="video-error-placeholder">
            <el-icon size="64"><Warning /></el-icon>
            <p>无法加载视频文件</p>
            <p class="error-detail">由于浏览器安全限制，无法直接播放本地视频文件</p>
            <el-button type="primary" @click="openVideoInNewTab">
              <el-icon><FolderOpened /></el-icon>
              在文件管理器中打开
            </el-button>
          </div>

          <video
            v-else
            ref="videoPlayer"
            :src="videoUrl"
            controls
            preload="metadata"
            class="video-player"
            @loadedmetadata="handleVideoLoaded"
            @error="handleVideoError"
          >
            您的浏览器不支持视频播放。
          </video>

          <!-- 播放器控制按钮 -->
          <div class="player-controls">
            <el-button @click="showVideoPlayer = false">
              <el-icon><Back /></el-icon>
              返回缩略图
            </el-button>
            <el-button @click="generatePreviewClip" :loading="generatingClip">
              <el-icon><Edit /></el-icon>
              生成预览片段
            </el-button>
            <el-button @click="openVideoInNewTab">
              <el-icon><FolderOpened /></el-icon>
              打开文件位置
            </el-button>
          </div>
        </div>
      </div>

      <!-- 视频详细信息 -->
      <div class="video-details" v-if="previewInfo?.media_info">
        <el-descriptions title="视频信息" :column="2" border>
          <el-descriptions-item label="时长">
            {{ formatDuration(previewInfo.media_info.duration) }}
          </el-descriptions-item>
          <el-descriptions-item label="分辨率">
            {{ previewInfo.media_info.resolution }}
          </el-descriptions-item>
          <el-descriptions-item label="视频编码">
            {{ previewInfo.media_info.video_codec }}
          </el-descriptions-item>
          <el-descriptions-item label="音频编码">
            {{ previewInfo.media_info.audio_codec || '无音频' }}
          </el-descriptions-item>
          <el-descriptions-item label="帧率">
            {{ previewInfo.media_info.frame_rate?.toFixed(2) }} fps
          </el-descriptions-item>
          <el-descriptions-item label="比特率">
            {{ formatBitrate(previewInfo.media_info.bitrate) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 错误信息显示 -->
      <div class="error-section" v-if="errorMessage">
        <el-alert
          :title="errorMessage"
          type="error"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="errorMessage" type="warning" @click="retryLoad" :loading="loading">
          <el-icon><Refresh /></el-icon>
          重试
        </el-button>
        <el-button type="primary" @click="openVideoInNewTab" v-if="videoInfo">
          <el-icon><Link /></el-icon>
          在新窗口打开
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document, Timer, Monitor, VideoPlay, Back, Link, Edit, Warning, FolderOpened, Refresh
} from '@element-plus/icons-vue'
import { 
  getVideoPreviewInfo, 
  generateVideoThumbnail, 
  generateVideoPreviewClip,
  type VideoPreviewInfoResponse 
} from '@/api/social'

interface Props {
  modelValue: boolean
  videoInfo: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const showVideoPlayer = ref(false)
const generatingClip = ref(false)
const errorMessage = ref('')

const previewInfo = ref<VideoPreviewInfoResponse | null>(null)
const thumbnailUrl = ref('')
const videoUrl = ref('')
const videoPlayer = ref<HTMLVideoElement>()

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val && props.videoInfo) {
    // 检查服务状态
    checkServiceStatus()
    // 加载视频预览
    loadVideoPreview()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetState()
  }
})

// 计算属性
const videoPath = computed(() => props.videoInfo?.path || '')

// 方法
const resetState = () => {
  showVideoPlayer.value = false
  generatingClip.value = false
  errorMessage.value = ''
  previewInfo.value = null
  thumbnailUrl.value = ''
  videoUrl.value = ''
}

const loadVideoPreview = async () => {
  if (!props.videoInfo?.path) return

  loading.value = true
  errorMessage.value = ''

  try {
    console.log('开始加载视频预览:', props.videoInfo.path)

    // 获取视频预览信息
    const response = await getVideoPreviewInfo({
      video_path: props.videoInfo.path,
      include_thumbnail: true,
      include_detailed_metadata: false
    })

    console.log('视频预览信息响应:', response)

    if (response.success) {
      previewInfo.value = response

      // 如果有缩略图信息，使用HTTP URL
      if (response.thumbnail_info?.exists && response.thumbnail_info?.thumbnail_url) {
        console.log('使用现有缩略图:', response.thumbnail_info.thumbnail_url)
        thumbnailUrl.value = response.thumbnail_info.thumbnail_url
      } else {
        console.log('生成新缩略图')
        // 生成缩略图
        await generateThumbnail()
      }

      // 设置视频URL - 需要通过后端API提供视频文件访问
      videoUrl.value = getVideoHttpUrl(props.videoInfo.path)
      console.log('设置视频URL:', videoUrl.value)
    } else {
      const error = response.error || '获取视频预览信息失败'
      console.error('视频预览信息获取失败:', error)
      errorMessage.value = error

      // 显示详细错误信息
      ElMessage.error({
        message: `视频预览加载失败: ${error}`,
        duration: 5000,
        showClose: true
      })
    }
  } catch (error) {
    console.error('加载视频预览失败:', error)
    const errorMsg = error.response?.data?.detail || error.message || '网络连接失败'
    errorMessage.value = `加载视频预览失败: ${errorMsg}`

    // 显示详细错误信息
    ElMessage.error({
      message: `视频预览加载失败: ${errorMsg}`,
      duration: 5000,
      showClose: true
    })
  } finally {
    loading.value = false
  }
}

const generateThumbnail = async () => {
  try {
    console.log('开始生成缩略图:', props.videoInfo.path)

    const response = await generateVideoThumbnail({
      video_path: props.videoInfo.path,
      max_width: 640,
      max_height: 360,
      quality: 85
    })

    console.log('缩略图生成响应:', response)

    if (response.success && response.thumbnail_url) {
      console.log('缩略图生成成功:', response.thumbnail_url)
      thumbnailUrl.value = response.thumbnail_url
    } else {
      const error = response.error || '缩略图生成失败'
      console.error('缩略图生成失败:', error)
      ElMessage.warning({
        message: `缩略图生成失败: ${error}`,
        duration: 3000
      })
    }
  } catch (error) {
    console.error('生成缩略图异常:', error)
    const errorMsg = error.response?.data?.detail || error.message || '网络连接失败'
    ElMessage.warning({
      message: `缩略图生成失败: ${errorMsg}`,
      duration: 3000
    })
  }
}

// 获取视频的HTTP URL
const getVideoHttpUrl = (videoPath: string): string => {
  // 由于浏览器安全限制，不能直接使用file://协议
  // 这里需要通过后端提供视频文件的HTTP访问

  // 如果Core服务的文件服务器正在运行，可以尝试构建HTTP URL
  try {
    // 将Windows路径转换为URL路径
    const urlPath = videoPath.replace(/\\/g, '/').replace(/^[A-Z]:/, '')
    const httpUrl = `http://localhost:8001/files${urlPath}`
    console.log('构建的视频HTTP URL:', httpUrl)
    return httpUrl
  } catch (error) {
    console.error('构建视频HTTP URL失败:', error)
    return ''
  }
}

// 检查服务连接状态
const checkServiceStatus = async () => {
  const services = [
    { name: 'Backend API', url: 'http://localhost:8000/docs' },
    { name: 'Core文件服务器', url: 'http://localhost:8001/health' }
  ]

  for (const service of services) {
    try {
      const response = await fetch(service.url, { method: 'GET' })
      if (response.ok) {
        console.log(`✓ ${service.name}: 连接正常`)
      } else {
        console.warn(`⚠ ${service.name}: 响应异常 ${response.status}`)
      }
    } catch (error) {
      console.error(`✗ ${service.name}: 连接失败`, error)
    }
  }
}

const startVideoPreview = () => {
  showVideoPlayer.value = true
  nextTick(() => {
    if (videoPlayer.value) {
      videoPlayer.value.load()
    }
  })
}

const generatePreviewClip = async () => {
  generatingClip.value = true
  
  try {
    const response = await generateVideoPreviewClip({
      video_path: props.videoInfo.path,
      start_time: 0,
      duration: 30,
      output_quality: 'medium'
    })

    if (response.success) {
      ElMessage.success('预览片段生成成功')
      // 可以在这里添加下载或播放预览片段的逻辑
    } else {
      ElMessage.error(response.error || '生成预览片段失败')
    }
  } catch (error) {
    console.error('生成预览片段失败:', error)
    ElMessage.error('生成预览片段失败')
  } finally {
    generatingClip.value = false
  }
}

const handleVideoLoaded = () => {
  console.log('视频加载完成')
}

const handleVideoError = (event: Event) => {
  console.error('视频加载错误:', event)
  ElMessage.error('视频加载失败')
}

const handleThumbnailError = () => {
  console.error('缩略图加载失败')
  thumbnailUrl.value = ''
}

const openVideoInNewTab = () => {
  if (props.videoInfo?.path) {
    // 由于浏览器安全限制，无法直接打开本地文件
    // 提示用户手动打开文件位置
    ElMessage.info({
      message: `文件位置: ${props.videoInfo.path}`,
      duration: 5000,
      showClose: true
    })

    // 尝试复制路径到剪贴板
    if (navigator.clipboard) {
      navigator.clipboard.writeText(props.videoInfo.path).then(() => {
        ElMessage.success('文件路径已复制到剪贴板')
      }).catch(() => {
        console.warn('无法复制到剪贴板')
      })
    }
  }
}

const retryLoad = async () => {
  console.log('重试加载视频预览')
  errorMessage.value = ''
  await checkServiceStatus()
  await loadVideoPreview()
}

const handleClose = () => {
  dialogVisible.value = false
}

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

const formatBitrate = (bitrate: number): string => {
  if (bitrate === 0) return '未知'
  if (bitrate < 1000) return `${bitrate} bps`
  if (bitrate < 1000000) return `${(bitrate / 1000).toFixed(1)} Kbps`
  return `${(bitrate / 1000000).toFixed(1)} Mbps`
}
</script>

<style scoped>
.video-preview-container {
  min-height: 400px;
}

.video-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.video-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.video-meta {
  display: flex;
  gap: 20px;
  color: #909399;
  font-size: 14px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.video-preview-area {
  margin-bottom: 20px;
}

.thumbnail-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
  min-height: 300px;
}

.video-thumbnail {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
}

.thumbnail-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #909399;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: opacity 0.3s;
}

.play-overlay:hover {
  opacity: 0.8;
}

.video-player {
  width: 100%;
  max-height: 500px;
  border-radius: 8px;
}

.player-controls {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  justify-content: center;
}

.video-error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 2px dashed #dcdfe6;
  text-align: center;
  min-height: 300px;
}

.video-error-placeholder .el-icon {
  color: #909399;
  margin-bottom: 16px;
}

.video-error-placeholder p {
  margin: 8px 0;
  color: #606266;
}

.video-error-placeholder .error-detail {
  font-size: 14px;
  color: #909399;
  margin-bottom: 20px;
}

.error-message {
  margin-bottom: 20px;
}

.error-message ul {
  margin: 10px 0;
  padding-left: 20px;
}

.error-message li {
  margin: 5px 0;
  font-size: 14px;
}

.video-details {
  margin-top: 20px;
}

.error-section {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
}
</style>
