# ThunderHub 服务启动脚本 (PowerShell)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "ThunderHub 服务启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# 检查依赖
Write-Host "检查依赖..." -ForegroundColor Yellow

# 检查Python
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Python 未安装或不在PATH中" -ForegroundColor Red
    exit 1
}

# 检查Node.js
try {
    $nodeVersion = node --version 2>&1
    Write-Host "✓ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Node.js 未安装或不在PATH中" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 启动服务
Write-Host "正在启动服务..." -ForegroundColor Yellow
Write-Host ""

# 启动Backend服务
Write-Host "[1/3] 启动 Backend 服务 (端口 8000)..." -ForegroundColor Cyan
$backendPath = Join-Path $scriptPath "backend"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$backendPath'; python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload" -WindowStyle Normal
Start-Sleep -Seconds 3

# 启动Core服务
Write-Host "[2/3] 启动 Core 服务 (端口 50051 和 8001)..." -ForegroundColor Cyan
$corePath = Join-Path $scriptPath "core"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$corePath'; python src/run.py" -WindowStyle Normal
Start-Sleep -Seconds 3

# 启动Frontend服务
Write-Host "[3/3] 启动 Frontend 服务 (端口 3000)..." -ForegroundColor Cyan
$frontendPath = Join-Path $scriptPath "frontend"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$frontendPath'; npm run dev" -WindowStyle Normal
Start-Sleep -Seconds 2

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "服务启动完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "请等待几秒钟让服务完全启动，然后访问：" -ForegroundColor Yellow
Write-Host ""
Write-Host "Frontend:     http://localhost:3000" -ForegroundColor White
Write-Host "Backend:      http://localhost:8000/docs" -ForegroundColor White
Write-Host "File Server:  http://localhost:8001/health" -ForegroundColor White
Write-Host ""

Write-Host "如果遇到视频加载问题，请检查：" -ForegroundColor Yellow
Write-Host "1. 所有服务是否正常启动" -ForegroundColor White
Write-Host "2. 防火墙是否阻止了端口访问" -ForegroundColor White
Write-Host "3. 视频文件路径是否正确" -ForegroundColor White
Write-Host "4. 查看浏览器开发者工具的Console和Network标签页" -ForegroundColor White
Write-Host ""

# 等待用户输入
Write-Host "按任意键关闭此窗口..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
