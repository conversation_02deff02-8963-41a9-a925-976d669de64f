"""
任务调度器服务
负责管理长期任务的调度，支持定时任务和触发条件任务
"""

import os
import logging
import asyncio
import datetime
import json
import time
from typing import Dict, List, Any, Optional, Callable
from enum import Enum
import croniter
from pathlib import Path
import hashlib

# 尝试导入redis.asyncio，如果失败则使用标准redis
try:
    import redis.asyncio as redis
    logger = logging.getLogger(__name__)
    logger.info("成功导入redis.asyncio模块")
except ImportError:
    import redis
    logger = logging.getLogger(__name__)
    logger.warning("redis.asyncio模块导入失败，使用标准redis模块")

logger = logging.getLogger(__name__)

class TaskType(Enum):
    """任务类型枚举"""
    IMMEDIATE = "immediate"  # 即时任务
    SCHEDULED = "scheduled"  # 定时任务
    TRIGGERED = "triggered"  # 触发任务

class TriggerType(Enum):
    """触发器类型枚举"""
    CRON = "cron"  # Cron表达式
    INTERVAL = "interval"  # 间隔时间
    FILE_WATCH = "file_watch"  # 文件监控
    ACCOUNT_UPDATE = "account_update"  # 账号更新监控
    CUSTOM = "custom"  # 自定义触发器

class TaskStatus(Enum):
    """任务状态枚举"""
    ACTIVE = "active"  # 活跃
    PAUSED = "paused"  # 暂停
    DISABLED = "disabled"  # 禁用
    EXPIRED = "expired"  # 已过期

class ScheduledTask:
    """调度任务类"""
    
    def __init__(self, task_id: str, task_config: Dict[str, Any]):
        self.task_id = task_id
        self.task_config = task_config
        self.task_type = TaskType(task_config.get("task_type", "immediate"))
        self.trigger_type = TriggerType(task_config.get("trigger_type", "cron"))
        self.status = TaskStatus(task_config.get("status", "active"))
        
        # 调度配置
        self.schedule_config = task_config.get("schedule_config", {})
        self.trigger_config = task_config.get("trigger_config", {})
        
        # 执行统计
        self.execution_count = task_config.get("execution_count", 0)
        self.last_execution = task_config.get("last_execution")
        self.next_execution = task_config.get("next_execution")
        
        # 任务模板
        self.task_template = task_config.get("task_template", {})
        
        # 计算下次执行时间
        self._calculate_next_execution()
    
    def _calculate_next_execution(self):
        """计算下次执行时间"""
        if self.task_type != TaskType.SCHEDULED:
            return
            
        now = datetime.datetime.now()
        
        if self.trigger_type == TriggerType.CRON:
            cron_expr = self.schedule_config.get("cron_expression")
            if cron_expr:
                try:
                    cron = croniter.croniter(cron_expr, now)
                    self.next_execution = cron.get_next(datetime.datetime).isoformat()
                except Exception as e:
                    logger.error(f"解析Cron表达式失败: {cron_expr}, 错误: {e}")
                    
        elif self.trigger_type == TriggerType.INTERVAL:
            interval_seconds = self.schedule_config.get("interval_seconds", 3600)
            if self.last_execution:
                last_time = datetime.datetime.fromisoformat(self.last_execution)
                next_time = last_time + datetime.timedelta(seconds=interval_seconds)
            else:
                next_time = now + datetime.timedelta(seconds=interval_seconds)
            self.next_execution = next_time.isoformat()
    
    def should_execute(self) -> bool:
        """检查是否应该执行"""
        if self.status != TaskStatus.ACTIVE:
            return False
            
        if self.task_type != TaskType.SCHEDULED:
            return False
            
        if not self.next_execution:
            return False
            
        now = datetime.datetime.now()
        next_time = datetime.datetime.fromisoformat(self.next_execution)
        
        return now >= next_time
    
    def update_execution(self):
        """更新执行统计"""
        self.execution_count += 1
        self.last_execution = datetime.datetime.now().isoformat()
        self._calculate_next_execution()

class TaskScheduler:
    """任务调度器类"""
    
    def __init__(self, main_service: 'CoreMainService'):
        """初始化任务调度器
        
        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        self.scheduled_tasks: Dict[str, ScheduledTask] = {}
        self.trigger_handlers: Dict[TriggerType, Callable] = {}
        self.redis_client = None
        self.is_running = False
        self.scheduler_task = None
        
        # 文件监控器
        self.file_watchers: Dict[str, Any] = {}
        
        # 注册默认触发器处理器
        self._register_default_triggers()
        
        logger.info("任务调度器初始化")
    
    async def initialize(self) -> bool:
        """初始化任务调度器"""
        try:
            # 连接Redis
            redis_connected = await self._connect_redis()
            if not redis_connected:
                logger.warning("Redis连接失败，调度器将使用内存存储")
            
            # 加载已保存的调度任务
            await self._load_scheduled_tasks()
            
            # 启动调度器
            await self.start_scheduler()
            
            logger.info("任务调度器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化任务调度器异常: {str(e)}", exc_info=True)
            return False
    
    async def _connect_redis(self) -> bool:
        """连接Redis"""
        try:
            # 获取Redis URL
            redis_url = None
            if hasattr(self.main_service, 'settings') and self.main_service.settings:
                redis_url = getattr(self.main_service.settings, 'redis_url', None)
            
            if not redis_url:
                redis_url = os.environ.get("REDIS_URL", "redis://192.168.123.137:6379/1")
            
            self.redis_client = redis.from_url(redis_url)
            await self.redis_client.ping()
            logger.info(f"Redis连接成功: {redis_url}")
            return True
            
        except Exception as e:
            logger.error(f"Redis连接失败: {str(e)}")
            self.redis_client = None
            return False
    
    async def _load_scheduled_tasks(self):
        """加载已保存的调度任务"""
        try:
            if self.redis_client:
                # 从Redis加载
                keys = await self.redis_client.keys("scheduled_task:*")
                for key in keys:
                    task_data = await self.redis_client.get(key)
                    if task_data:
                        task_config = json.loads(task_data)
                        task_id = task_config.get("task_id")
                        if task_id:
                            scheduled_task = ScheduledTask(task_id, task_config)
                            self.scheduled_tasks[task_id] = scheduled_task
                            logger.info(f"加载调度任务: {task_id}")
            
            logger.info(f"共加载 {len(self.scheduled_tasks)} 个调度任务")
            
        except Exception as e:
            logger.error(f"加载调度任务失败: {str(e)}", exc_info=True)
    
    def _register_default_triggers(self):
        """注册默认触发器处理器"""
        self.trigger_handlers[TriggerType.FILE_WATCH] = self._handle_file_watch_trigger
        self.trigger_handlers[TriggerType.ACCOUNT_UPDATE] = self._handle_account_update_trigger
    
    async def create_scheduled_task(self, task_config: Dict[str, Any]) -> str:
        """创建调度任务
        
        Args:
            task_config: 任务配置
            
        Returns:
            str: 任务ID
        """
        try:
            # 生成任务ID
            task_id = task_config.get("task_id")
            if not task_id:
                task_id = f"scheduled_{int(time.time())}_{hashlib.md5(json.dumps(task_config, sort_keys=True).encode()).hexdigest()[:8]}"
                task_config["task_id"] = task_id
            
            # 创建调度任务
            scheduled_task = ScheduledTask(task_id, task_config)
            self.scheduled_tasks[task_id] = scheduled_task
            
            # 保存到Redis
            await self._save_scheduled_task(task_id, task_config)
            
            # 如果是触发任务，设置触发器
            if scheduled_task.task_type == TaskType.TRIGGERED:
                await self._setup_trigger(scheduled_task)
            
            logger.info(f"创建调度任务成功: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"创建调度任务失败: {str(e)}", exc_info=True)
            raise
    
    async def _save_scheduled_task(self, task_id: str, task_config: Dict[str, Any]):
        """保存调度任务到Redis"""
        try:
            if self.redis_client:
                key = f"scheduled_task:{task_id}"
                await self.redis_client.set(key, json.dumps(task_config))
                logger.debug(f"调度任务已保存到Redis: {task_id}")
        except Exception as e:
            logger.error(f"保存调度任务到Redis失败: {str(e)}")
    
    async def _setup_trigger(self, scheduled_task: ScheduledTask):
        """设置触发器"""
        try:
            trigger_type = scheduled_task.trigger_type
            
            if trigger_type == TriggerType.FILE_WATCH:
                await self._setup_file_watch_trigger(scheduled_task)
            elif trigger_type == TriggerType.ACCOUNT_UPDATE:
                await self._setup_account_update_trigger(scheduled_task)
            
            logger.info(f"触发器设置完成: {scheduled_task.task_id}, 类型: {trigger_type}")
            
        except Exception as e:
            logger.error(f"设置触发器失败: {str(e)}", exc_info=True)

    async def _setup_file_watch_trigger(self, scheduled_task: ScheduledTask):
        """设置文件监控触发器"""
        try:
            trigger_config = scheduled_task.trigger_config
            watch_path = trigger_config.get("watch_path")

            if not watch_path or not os.path.exists(watch_path):
                logger.error(f"监控路径不存在: {watch_path}")
                return

            # 这里可以使用watchdog库来监控文件变化
            # 为了简化，我们使用定时检查的方式
            self.file_watchers[scheduled_task.task_id] = {
                "path": watch_path,
                "last_check": time.time(),
                "last_mtime": os.path.getmtime(watch_path) if os.path.isfile(watch_path) else 0
            }

            logger.info(f"文件监控触发器设置完成: {watch_path}")

        except Exception as e:
            logger.error(f"设置文件监控触发器失败: {str(e)}", exc_info=True)

    async def _setup_account_update_trigger(self, scheduled_task: ScheduledTask):
        """设置账号更新触发器"""
        try:
            trigger_config = scheduled_task.trigger_config
            account_id = trigger_config.get("account_id")
            platform_id = trigger_config.get("platform_id")

            # 这里可以设置对特定账号的监控
            # 实际实现中可以通过数据库变更监听或API轮询
            logger.info(f"账号更新触发器设置完成: account_id={account_id}, platform_id={platform_id}")

        except Exception as e:
            logger.error(f"设置账号更新触发器失败: {str(e)}", exc_info=True)

    async def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已经在运行")
            return

        self.is_running = True
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        logger.info("任务调度器已启动")

    async def stop_scheduler(self):
        """停止调度器"""
        self.is_running = False
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        logger.info("任务调度器已停止")

    async def _scheduler_loop(self):
        """调度器主循环"""
        logger.info("调度器主循环开始")

        while self.is_running:
            try:
                # 检查定时任务
                await self._check_scheduled_tasks()

                # 检查触发任务
                await self._check_triggered_tasks()

                # 等待下次检查
                await asyncio.sleep(10)  # 每10秒检查一次

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"调度器循环异常: {str(e)}", exc_info=True)
                await asyncio.sleep(30)  # 出错后等待30秒再继续

        logger.info("调度器主循环结束")

    async def _check_scheduled_tasks(self):
        """检查定时任务"""
        try:
            current_time = datetime.datetime.now()

            for task_id, scheduled_task in list(self.scheduled_tasks.items()):
                if scheduled_task.task_type == TaskType.SCHEDULED and scheduled_task.should_execute():
                    logger.info(f"执行定时任务: {task_id}")

                    # 创建实际执行任务
                    await self._execute_scheduled_task(scheduled_task)

                    # 更新执行统计
                    scheduled_task.update_execution()

                    # 保存更新后的任务配置
                    await self._save_scheduled_task(task_id, scheduled_task.task_config)

        except Exception as e:
            logger.error(f"检查定时任务异常: {str(e)}", exc_info=True)

    async def _check_triggered_tasks(self):
        """检查触发任务"""
        try:
            for task_id, scheduled_task in list(self.scheduled_tasks.items()):
                if scheduled_task.task_type == TaskType.TRIGGERED:

                    # 检查文件监控触发器
                    if scheduled_task.trigger_type == TriggerType.FILE_WATCH:
                        if await self._check_file_watch_trigger(scheduled_task):
                            logger.info(f"文件监控触发任务: {task_id}")
                            await self._execute_scheduled_task(scheduled_task)
                            scheduled_task.update_execution()

                    # 检查账号更新触发器
                    elif scheduled_task.trigger_type == TriggerType.ACCOUNT_UPDATE:
                        if await self._check_account_update_trigger(scheduled_task):
                            logger.info(f"账号更新触发任务: {task_id}")
                            await self._execute_scheduled_task(scheduled_task)
                            scheduled_task.update_execution()

        except Exception as e:
            logger.error(f"检查触发任务异常: {str(e)}", exc_info=True)

    async def _check_file_watch_trigger(self, scheduled_task: ScheduledTask) -> bool:
        """检查文件监控触发器"""
        try:
            task_id = scheduled_task.task_id
            if task_id not in self.file_watchers:
                return False

            watcher = self.file_watchers[task_id]
            watch_path = watcher["path"]

            if not os.path.exists(watch_path):
                return False

            current_time = time.time()

            # 检查是否是文件夹
            if os.path.isdir(watch_path):
                # 检查文件夹内容变化
                try:
                    files = list(Path(watch_path).iterdir())
                    if len(files) > 0:
                        # 有新文件，触发任务
                        trigger_config = scheduled_task.trigger_config
                        min_interval = trigger_config.get("min_interval", 300)  # 最小间隔5分钟

                        if current_time - watcher["last_check"] >= min_interval:
                            watcher["last_check"] = current_time
                            return True
                except Exception:
                    pass
            else:
                # 检查文件修改时间
                try:
                    current_mtime = os.path.getmtime(watch_path)
                    if current_mtime > watcher["last_mtime"]:
                        watcher["last_mtime"] = current_mtime
                        return True
                except Exception:
                    pass

            return False

        except Exception as e:
            logger.error(f"检查文件监控触发器异常: {str(e)}", exc_info=True)
            return False

    async def _check_account_update_trigger(self, scheduled_task: ScheduledTask) -> bool:
        """检查账号更新触发器"""
        try:
            # 这里可以实现账号更新检查逻辑
            # 例如：检查数据库中账号的最后更新时间
            # 或者调用API检查账号是否有新内容

            trigger_config = scheduled_task.trigger_config
            account_id = trigger_config.get("account_id")
            check_interval = trigger_config.get("check_interval", 3600)  # 默认1小时检查一次

            # 简化实现：基于时间间隔触发
            last_execution = scheduled_task.last_execution
            if last_execution:
                last_time = datetime.datetime.fromisoformat(last_execution)
                if (datetime.datetime.now() - last_time).total_seconds() >= check_interval:
                    return True
            else:
                return True  # 首次执行

            return False

        except Exception as e:
            logger.error(f"检查账号更新触发器异常: {str(e)}", exc_info=True)
            return False

    async def _execute_scheduled_task(self, scheduled_task: ScheduledTask):
        """执行调度任务"""
        try:
            # 从任务模板创建实际任务
            task_template = scheduled_task.task_template

            # 生成新的任务ID
            execution_id = f"{scheduled_task.task_id}_exec_{int(time.time())}"

            # 创建任务数据
            task_data = {
                "task_id": execution_id,
                "parent_scheduled_task_id": scheduled_task.task_id,
                "created_at": datetime.datetime.now().isoformat(),
                **task_template
            }

            # 通过任务执行器创建并启动任务
            if hasattr(self.main_service, 'task_executor') and self.main_service.task_executor:
                # 创建任务
                create_success = await self.main_service.task_executor.create_task(task_data)
                if create_success:
                    # 启动任务
                    start_success = await self.main_service.task_executor.start_task(execution_id)
                    if start_success:
                        logger.info(f"调度任务执行成功: {execution_id}")
                    else:
                        logger.error(f"调度任务启动失败: {execution_id}")
                else:
                    logger.error(f"调度任务创建失败: {execution_id}")
            else:
                logger.error("任务执行器不可用")

        except Exception as e:
            logger.error(f"执行调度任务异常: {str(e)}", exc_info=True)

    async def pause_scheduled_task(self, task_id: str) -> bool:
        """暂停调度任务"""
        try:
            if task_id in self.scheduled_tasks:
                self.scheduled_tasks[task_id].status = TaskStatus.PAUSED
                await self._save_scheduled_task(task_id, self.scheduled_tasks[task_id].task_config)
                logger.info(f"调度任务已暂停: {task_id}")
                return True
            else:
                logger.error(f"调度任务不存在: {task_id}")
                return False
        except Exception as e:
            logger.error(f"暂停调度任务异常: {str(e)}", exc_info=True)
            return False

    async def resume_scheduled_task(self, task_id: str) -> bool:
        """恢复调度任务"""
        try:
            if task_id in self.scheduled_tasks:
                self.scheduled_tasks[task_id].status = TaskStatus.ACTIVE
                await self._save_scheduled_task(task_id, self.scheduled_tasks[task_id].task_config)
                logger.info(f"调度任务已恢复: {task_id}")
                return True
            else:
                logger.error(f"调度任务不存在: {task_id}")
                return False
        except Exception as e:
            logger.error(f"恢复调度任务异常: {str(e)}", exc_info=True)
            return False

    async def delete_scheduled_task(self, task_id: str) -> bool:
        """删除调度任务"""
        try:
            if task_id in self.scheduled_tasks:
                # 从内存中删除
                del self.scheduled_tasks[task_id]

                # 从Redis中删除
                if self.redis_client:
                    key = f"scheduled_task:{task_id}"
                    await self.redis_client.delete(key)

                # 清理文件监控器
                if task_id in self.file_watchers:
                    del self.file_watchers[task_id]

                logger.info(f"调度任务已删除: {task_id}")
                return True
            else:
                logger.error(f"调度任务不存在: {task_id}")
                return False
        except Exception as e:
            logger.error(f"删除调度任务异常: {str(e)}", exc_info=True)
            return False

    async def get_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """获取所有调度任务"""
        try:
            tasks = []
            for task_id, scheduled_task in self.scheduled_tasks.items():
                task_info = {
                    "task_id": task_id,
                    "task_type": scheduled_task.task_type.value,
                    "trigger_type": scheduled_task.trigger_type.value,
                    "status": scheduled_task.status.value,
                    "execution_count": scheduled_task.execution_count,
                    "last_execution": scheduled_task.last_execution,
                    "next_execution": scheduled_task.next_execution,
                    "schedule_config": scheduled_task.schedule_config,
                    "trigger_config": scheduled_task.trigger_config,
                    "task_template": scheduled_task.task_template
                }
                tasks.append(task_info)

            return tasks

        except Exception as e:
            logger.error(f"获取调度任务列表异常: {str(e)}", exc_info=True)
            return []

    async def shutdown(self):
        """关闭调度器"""
        try:
            logger.info("正在关闭任务调度器...")

            # 停止调度器
            await self.stop_scheduler()

            # 关闭Redis连接
            if self.redis_client:
                try:
                    await self.redis_client.close()
                    logger.info("Redis连接已关闭")
                except Exception as redis_error:
                    logger.error(f"关闭Redis连接失败: {str(redis_error)}")

            self.redis_client = None
            logger.info("任务调度器已关闭")

        except Exception as e:
            logger.error(f"关闭任务调度器异常: {str(e)}", exc_info=True)

    async def _handle_file_watch_trigger(self, scheduled_task: ScheduledTask):
        """处理文件监控触发器"""
        # 这是一个示例处理器，可以根据需要扩展
        pass

    async def _handle_account_update_trigger(self, scheduled_task: ScheduledTask):
        """处理账号更新触发器"""
        # 这是一个示例处理器，可以根据需要扩展
        pass
