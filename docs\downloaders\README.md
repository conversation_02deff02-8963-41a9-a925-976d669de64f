# 对标账号下载器系统

## 概述

对标账号下载器系统是一个基于工作流的多平台内容下载解决方案，参考YouTube上传器的架构设计，支持抖音、YouTube等平台的内容批量下载。

## 系统架构

```
前端 (Vue3) → Backend API (FastAPI) → Core服务 → 下载器模块 → 平台API
```

### 核心组件

1. **前端界面** - 对标账号管理和下载任务创建
2. **Backend API** - 任务管理和状态跟踪
3. **Core服务** - 下载任务执行和资源管理
4. **下载器模块** - 平台特定的下载实现
5. **工作流引擎** - 可配置的下载流程

## 功能特性

### ✅ 已实现功能

- **多平台支持**: 抖音、TikTok、YouTube
- **批量下载**: 支持单个和批量下载任务
- **工作流驱动**: 基于YAML配置的可定制下载流程
- **进度跟踪**: 实时下载进度和状态更新
- **元数据保存**: 自动保存视频信息和统计数据
- **过滤功能**: 支持播放量、点赞数、关键词等过滤
- **文件管理**: 智能文件命名和目录组织
- **错误处理**: 完善的重试机制和错误恢复
- **日志记录**: 详细的下载日志和报告生成

### 🚧 开发中功能

- **Instagram支持**: Instagram内容下载
- **增量更新**: 只下载新发布的内容
- **去重检测**: 基于哈希值的重复内容检测
- **任务调度**: 定时下载和自动更新
- **性能优化**: 并发下载和断点续传

## 目录结构

```
core/
├── src/
│   ├── downloaders/                 # 下载器模块
│   │   ├── __init__.py
│   │   ├── base_downloader.py       # 基础下载器类
│   │   ├── douyin_downloader.py     # 抖音下载器
│   │   ├── youtube_downloader.py    # YouTube下载器
│   │   ├── downloader_factory.py    # 下载器工厂
│   │   └── download_task_executor.py # 任务执行器
│   └── main_service.py              # Core主服务（已集成）
├── config/
│   ├── downloaders/
│   │   └── downloader_config.yaml   # 下载器配置
│   └── platforms/
│       ├── douyin/
│       │   └── workflows/
│       │       └── content_download.yaml # 抖音下载工作流
│       └── youtube/
│           └── workflows/
│               └── content_download.yaml # YouTube下载工作流

backend/
├── app/
│   └── api/
│       └── v1/
│           └── benchmark_download.py # 下载API

frontend/
├── src/
│   ├── views/
│   │   └── doc/
│   │       └── BenchmarkDownload.vue # 下载界面
│   └── api/
│       └── content.ts               # API调用
```

## 使用指南

### 1. 前端操作

1. **查看对标账号**: 在对标下载页面查看已配置的对标账号
2. **创建下载任务**: 点击"下载"按钮创建单个下载任务
3. **批量下载**: 点击"批量下载"创建多个下载任务
4. **监控进度**: 实时查看下载进度和状态

### 2. API调用

```javascript
// 创建单个下载任务
const task = {
  our_account_id: "account_id",
  our_account_name: "账号名称",
  benchmark_account_id: "benchmark_id",
  benchmark_account_name: "对标账号名称",
  benchmark_account_url: "https://www.douyin.com/user/xxx",
  platform: "douyin",
  download_path: "H:\\PublishSystem\\YouTube\\账号\\对标账号\\2024-01",
  download_config: {
    max_videos: 50,
    video_quality: "high",
    include_metadata: true,
    skip_existing: true
  }
}

const response = await createBenchmarkDownloadTask(task)
```

### 3. Core服务集成

```python
# 在Core服务中执行下载任务
success = await main_service.execute_download_task(task_id, task_data)

# 批量下载
success = await main_service.execute_batch_download_task(batch_id, tasks)

# 取消任务
success = await main_service.cancel_download_task(task_id)
```

## 配置说明

### 下载器配置 (downloader_config.yaml)

```yaml
# 全局配置
global:
  max_concurrent_downloads: 3
  network:
    timeout: 30
    retry_count: 3

# 平台配置
platforms:
  douyin:
    enabled: true
    api:
      base_url: "https://www.douyin.com"
    download:
      max_videos_per_request: 20
      quality_options:
        high: "1080p"
        medium: "720p"
        low: "480p"
```

### 工作流配置 (content_download.yaml)

```yaml
workflow:
  name: "抖音内容下载"
  steps:
    - name: "获取账号信息"
      action: "fetch_account_info"
      required: true
    - name: "获取视频列表"
      action: "fetch_video_list"
      required: true
    - name: "下载视频文件"
      action: "download_batch"
      required: true
```

## 扩展开发

### 添加新平台支持

1. **创建下载器类**:
```python
class NewPlatformDownloader(BaseDownloader):
    def __init__(self, download_path: str):
        super().__init__("new_platform", download_path)
    
    async def download_account_content(self, account_url, account_name, config):
        # 实现下载逻辑
        pass
```

2. **注册下载器**:
```python
DownloaderFactory.register_downloader("new_platform", NewPlatformDownloader)
```

3. **创建工作流配置**:
```yaml
# platforms/new_platform/workflows/content_download.yaml
workflow:
  name: "新平台内容下载"
  steps:
    # 定义下载步骤
```

### 自定义过滤器

```python
def custom_filter(video_info: Dict[str, Any], filters: Dict[str, Any]) -> bool:
    # 实现自定义过滤逻辑
    return True
```

## 依赖要求

### Python依赖
- `aiohttp`: HTTP客户端
- `yt-dlp`: YouTube下载器 (可选)
- `pyyaml`: YAML配置解析

### 系统要求
- Python 3.8+
- 足够的磁盘空间用于存储下载内容
- 稳定的网络连接

## 故障排除

### 常见问题

1. **yt-dlp不可用**
   ```bash
   pip install yt-dlp
   ```

2. **网络连接超时**
   - 检查网络连接
   - 调整timeout配置
   - 考虑使用代理

3. **下载失败**
   - 检查账号URL是否正确
   - 查看错误日志
   - 验证平台API是否可用

### 日志查看

下载日志保存在以下位置：
- Core服务日志: `logs/core.log`
- 下载任务日志: `H:\PublishSystem\平台\账号\logs\`
- 下载报告: `H:\PublishSystem\平台\账号\download_report.json`

## 性能优化

### 并发控制
- 调整 `max_concurrent_downloads` 参数
- 根据网络带宽设置合适的并发数

### 存储优化
- 使用SSD存储提高I/O性能
- 定期清理临时文件和日志

### 网络优化
- 配置合适的请求频率限制
- 使用CDN或代理加速下载

## 安全考虑

- 遵守平台的使用条款和API限制
- 实施合理的请求频率控制
- 保护用户隐私和数据安全
- 定期更新依赖库以修复安全漏洞

## 更新日志

### v1.0.0 (2024-01-16)
- ✅ 实现基础下载器架构
- ✅ 支持抖音/TikTok下载
- ✅ 支持YouTube下载 (基于yt-dlp)
- ✅ 工作流配置系统
- ✅ 前端界面集成
- ✅ 批量下载功能
- ✅ 进度跟踪和状态管理

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
