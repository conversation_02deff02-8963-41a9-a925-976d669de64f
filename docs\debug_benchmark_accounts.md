# 对标账号功能调试指南

## 问题现状

对标账号管理页面中的"选择我们的账号"下拉框仍然显示假数据或为空，需要调试真实的数据流。

## 调试步骤

### 1. 检查前端页面

访问对标账号管理页面：
- 路径：`/social/benchmark`
- 菜单：社媒管理 → 对标账号

### 2. 查看浏览器控制台

打开浏览器开发者工具，查看Console输出：

**期望看到的日志**：
```
对标账号页面已挂载，开始加载数据
开始加载我们的账号列表
当前用户认证状态: true
当前用户信息: {username: "admin", ...}
获取账号API响应: {success: true, data: {data: [...], total: 2, ...}}
从response.data.data提取账号数据: [{id: "xxx", username: "xxx", ...}]
最终的账号列表: [{id: "xxx", username: "xxx", ...}]
成功加载 2 个账号
```

**可能的错误情况**：
```
加载账号列表失败: 401 Unauthorized
加载账号列表失败: Network Error
未知的响应格式: {success: false, ...}
没有找到任何账号数据
```

### 3. 检查网络请求

在Network面板中查看API请求：

**请求信息**：
- URL: `GET /api/v1/social/accounts`
- Headers: 包含Authorization头
- Status: 200 OK

**响应格式**：
```json
{
  "data": [
    {
      "id": "account_id_1",
      "username": "test_account",
      "display_name": "测试账号",
      "platform_id": "youtube",
      "core_service_id": "core1",
      "status": "active"
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 100
}
```

### 4. 检查认证状态

确认用户已登录：
- 检查localStorage中的token
- 确认authStore.isAuthenticated为true
- 验证API请求包含正确的Authorization头

### 5. 检查后端服务

确认后端服务正常运行：
- 后端服务在8000端口运行
- MongoDB数据库连接正常
- social_accounts集合中有数据

## 常见问题排查

### 1. 认证问题

**症状**: API返回401错误
**解决方案**:
- 重新登录系统
- 检查token是否过期
- 确认API路由有正确的认证依赖

### 2. 数据库问题

**症状**: API返回空数据
**解决方案**:
- 检查MongoDB连接
- 确认social_accounts集合存在
- 验证集合中有数据记录

### 3. 数据格式问题

**症状**: 前端无法解析响应数据
**解决方案**:
- 检查API响应格式
- 确认request.ts的响应拦截器
- 验证数据提取逻辑

### 4. 路由问题

**症状**: 页面无法访问
**解决方案**:
- 检查路由配置
- 确认组件导入路径
- 验证菜单权限设置

## 修复建议

### 1. 临时解决方案

如果需要快速验证功能，可以在数据库中手动添加测试账号：

```javascript
// 在MongoDB中执行
db.social_accounts.insertMany([
  {
    "id": "test_youtube_1",
    "username": "test_youtube_account",
    "display_name": "YouTube测试账号",
    "platform_id": "youtube",
    "core_service_id": "core1",
    "status": "active",
    "created_at": new Date(),
    "updated_at": new Date()
  },
  {
    "id": "test_tiktok_1", 
    "username": "test_tiktok_account",
    "display_name": "TikTok测试账号",
    "platform_id": "tiktok",
    "core_service_id": "core1",
    "status": "active",
    "created_at": new Date(),
    "updated_at": new Date()
  }
])
```

### 2. 数据流验证

添加更详细的日志来跟踪数据流：

```javascript
// 在loadOurAccounts方法中添加
console.log('API调用前的状态检查')
console.log('- 认证状态:', authStore.isAuthenticated)
console.log('- 用户信息:', authStore.user)
console.log('- Token存在:', !!localStorage.getItem('token'))

// API调用后
console.log('API响应详情:')
console.log('- 响应状态:', response.success)
console.log('- 响应数据:', response.data)
console.log('- 数据类型:', typeof response.data)
console.log('- 是否有data字段:', 'data' in response.data)
```

### 3. 错误处理增强

改进错误处理逻辑：

```javascript
catch (error) {
  console.error('API调用失败详情:')
  console.error('- 错误类型:', error.constructor.name)
  console.error('- 错误消息:', error.message)
  console.error('- 响应状态:', error.response?.status)
  console.error('- 响应数据:', error.response?.data)
  console.error('- 完整错误:', error)
}
```

## 验证清单

### 前端验证
- [ ] 页面能正常访问
- [ ] 控制台无JavaScript错误
- [ ] API请求正常发送
- [ ] 响应数据格式正确
- [ ] 数据提取逻辑正确

### 后端验证
- [ ] 服务正常运行
- [ ] API端点可访问
- [ ] 认证中间件正常
- [ ] 数据库连接正常
- [ ] 查询逻辑正确

### 数据验证
- [ ] 数据库中有账号记录
- [ ] 账号数据格式正确
- [ ] ID字段映射正确
- [ ] 关联关系正确

## 下一步行动

1. **立即检查**: 打开浏览器控制台，查看具体的错误信息
2. **数据验证**: 确认数据库中是否有social_accounts数据
3. **API测试**: 使用Postman或curl直接测试API
4. **逐步调试**: 从认证开始，逐步验证每个环节

## 联系支持

如果问题仍然存在，请提供以下信息：
- 浏览器控制台的完整日志
- Network面板的API请求详情
- 后端服务的日志输出
- 数据库中的账号数据示例

---

**注意**: 这个调试指南提供了系统性的问题排查方法，请按步骤进行检查以快速定位问题根源。
