<template>
  <div class="social-media">
    
    <router-view
      v-if="$route.name !== 'SocialMedia'"
      :appId="selectedAppId"
    ></router-view>
    <template v-else>
      <el-table :data="paginatedApps" style="width: 100%">
        <el-table-column prop="name" label="应用名称" />
        <el-table-column prop="platform" label="平台" />
        <el-table-column label="状态">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status === 'running' ? '运行中' : '已停止' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="controlApp(scope.row, 'start')"
              :disabled="scope.row.status === 'running'">
              启动
            </el-button>
            <el-button size="small" type="danger" plain
              @click="controlApp(scope.row, 'stop')"
              :disabled="scope.row.status !== 'running'">
              停止
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container mt-4">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[5, 10, 20]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="fetchApps"
          @size-change="handleSizeChange"
        />
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { getSocialApps, controlApp } from '@/api/social'
import { ElMessage } from 'element-plus'

const selectedAppId = ref('')
const apps = ref<SocialApp[]>([])

const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

// 计算分页后的应用数据
const paginatedApps = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return apps.value.slice(start, end)
})

// 获取社媒应用列表
const fetchApps = async () => {
  try {
    const res = await getSocialApps()
    apps.value = res
    pagination.value.total = res.length
    
    if (res.length > 0) {
      selectedAppId.value = res[0].id
    } else {
      selectedAppId.value = ''
    }
  } catch (error) {
    ElMessage.error('获取社媒应用列表失败')
    console.error(error)
  }
}

// 控制应用状态
const controlApp = async (app: SocialApp, action: 'start' | 'stop' | 'clear') => {
  try {
    await controlApp({
      deviceId: '', // TODO: 获取当前设备ID
      appId: app.id,
      action
    })
    
    ElMessage.success(`${action === 'start' ? '启动' : '停止'}成功`)
    await fetchApps() // 刷新状态
  } catch (error) {
    ElMessage.error('操作失败')
    console.error(error)
  }
}

// 分页大小变化处理
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  pagination.value.page = 1
  fetchApps()
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  return status === 'running' ? 'success' : 'info'
}

onMounted(fetchApps)
</script>

<style scoped>
.social-media {
  /* padding: 20px; */
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>