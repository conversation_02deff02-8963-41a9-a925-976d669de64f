# 视频自动去重功能使用指南

## 🎯 功能概述

视频自动去重功能基于文件内容的**SHA-256哈希值**自动检测并过滤重复的视频文件，确保不会重复上传相同内容的视频，即使文件名不同也能准确识别。

## 📍 使用位置

### 主要入口
```
前端路径：社交媒体 → 发布管理 → 第一步：选择内容来源
具体页面：frontend/src/views/social/PublishManagement.vue
```

### 操作流程
1. 选择发布平台（如YouTube）
2. 选择Core服务
3. 选择发布账号
4. 选择内容路径
5. 系统自动进行去重检查

## 🔧 工作原理

### 1. 自动去重机制
- **默认行为**：系统自动检测重复视频并过滤
- **检测方式**：基于文件内容SHA-256哈希值
- **检测范围**：检查该账号是否已上传过相同内容的视频
- **处理方式**：自动过滤重复文件，只处理新视频

### 2. 检测流程
```mermaid
graph TD
    A[用户选择视频文件] --> B[计算文件SHA-256哈希]
    B --> C[查询数据库中的上传记录]
    C --> D{该账号是否已上传?}
    D -->|是| E[自动过滤该文件]
    D -->|否| F[保留该文件]
    E --> G[记录过滤信息]
    F --> H[继续处理]
    G --> I[显示过滤结果]
    H --> I
```

## 🎨 用户界面

### 1. 自动去重提示
当选择了内容路径和账号后，会显示：
```
🔍 自动去重保护
系统将自动检测并过滤重复视频文件（基于文件内容hash值）
如需上传重复视频，请在创建任务时勾选"允许重复上传"选项
```

### 2. 允许重复上传选项
```
☐ 🔄 允许重复上传（忽略去重检查）
```

勾选后会显示警告：
```
⚠️ 已启用重复上传，可能会上传相同内容的视频
```

## 📊 处理结果

### 1. 全部过滤情况
如果所有选中的视频都是重复的：
- **提示消息**：`所有 X 个视频文件都已上传过，已自动过滤`
- **详细对话框**：显示被过滤的具体文件列表
- **操作建议**：如需重新上传，请勾选"允许重复上传"选项

### 2. 部分过滤情况
如果部分视频是重复的：
- **提示消息**：`已自动过滤 X 个重复视频，继续处理其余文件`
- **任务创建**：继续创建任务处理非重复文件
- **日志记录**：记录过滤的文件信息

### 3. 无重复情况
如果没有重复视频：
- **正常处理**：直接创建上传任务
- **提示消息**：`任务创建成功`

## 🔄 强制上传模式

### 何时使用
- 需要重新上传已上传过的视频
- 视频内容有更新但文件名相同
- 需要在不同时间段重复发布相同内容

### 如何启用
1. 在创建任务页面勾选"允许重复上传"
2. 系统会跳过去重检查
3. 所有选中的视频都会被处理

### 注意事项
- 启用后会忽略所有去重检查
- 可能导致相同内容的视频重复上传
- 建议仅在确实需要时使用

## 📈 性能优化

### 1. 快速预检
- 优先使用文件大小和时长进行筛选
- 只有候选匹配时才计算完整哈希
- 减少不必要的计算开销

### 2. 哈希计算
- 使用SHA-256算法确保准确性
- 分块读取文件避免内存问题
- 支持大文件的高效处理

### 3. 数据库优化
- 文件哈希建立唯一索引
- 文件大小和时长组合索引
- 快速查询重复记录

## 🛠️ 技术实现

### 后端API
```
POST /api/v1/social/youtube/uploads
{
  "folderPath": "/path/to/videos",
  "selectedFiles": ["video1.mp4", "video2.mp4"],
  "accountId": "account123",
  "metadata": {...},
  "force_upload": false  // 控制是否允许重复上传
}
```

### 响应格式
```json
{
  "task_id": "task-uuid",
  "status": "pending",
  "duplicate_info": {
    "filtered_count": 2,
    "duplicate_files": [
      {
        "file": "video1.mp4",
        "duplicate_record": {...},
        "previous_uploads": [...]
      }
    ],
    "message": "已自动过滤 2 个重复视频"
  }
}
```

## 📋 使用示例

### 场景1：正常上传（无重复）
1. 用户选择包含3个新视频的文件夹
2. 系统检测无重复
3. 创建任务处理所有3个视频
4. 显示"任务创建成功"

### 场景2：部分重复
1. 用户选择包含5个视频的文件夹
2. 系统检测到2个重复视频
3. 自动过滤2个重复视频
4. 创建任务处理剩余3个新视频
5. 显示"已自动过滤 2 个重复视频，继续处理其余文件"

### 场景3：全部重复
1. 用户选择包含3个视频的文件夹
2. 系统检测到所有视频都是重复的
3. 显示详细的过滤信息对话框
4. 提示用户勾选"允许重复上传"如需重新上传

### 场景4：强制上传
1. 用户勾选"允许重复上传"选项
2. 选择包含重复视频的文件夹
3. 系统跳过去重检查
4. 创建任务处理所有视频（包括重复的）

## 🔍 故障排除

### 常见问题

**Q: 为什么我的视频被误判为重复？**
A: 系统基于文件内容哈希判断，如果两个文件内容完全相同（即使文件名不同），会被识别为重复。

**Q: 如何查看被过滤的视频列表？**
A: 当有视频被过滤时，系统会显示详细的对话框列出所有被过滤的文件。

**Q: 强制上传会影响其他功能吗？**
A: 不会，强制上传只影响当前任务的去重检查，不会影响其他功能。

**Q: 去重检查会影响上传速度吗？**
A: 影响很小，系统使用了优化的快速预检机制，只在必要时计算完整哈希。

### 日志查看
- 后端日志：查看 `backend/logs/` 目录
- 前端控制台：打开浏览器开发者工具查看
- 任务日志：在任务管理页面查看具体任务的执行日志

## 🎯 最佳实践

1. **默认使用自动去重**：大多数情况下保持默认设置即可
2. **谨慎使用强制上传**：只在确实需要重复上传时启用
3. **定期清理**：定期清理失败的上传记录以保持数据库整洁
4. **监控统计**：通过重复统计API监控去重效果

这个自动去重功能能够有效避免重复上传，提高系统效率，同时保持用户操作的简单性。
