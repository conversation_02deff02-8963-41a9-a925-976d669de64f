# 账号过期提醒功能说明

## 功能概述

账号管理系统现在支持设置账号的更新日期，并通过**整行背景色**的方式提供醒目的过期提醒。系统会根据距离更新日期的天数，使用不同的背景色来标识账号的过期状态。

## 视觉提醒效果

### 🔴 已过期账号
- **背景色**: 浅红色 (`#fef0f0`)
- **悬停效果**: 深浅红色 (`#fde2e2`)
- **文字颜色**: 红色 (`#f56c6c`)
- **触发条件**: 当前日期已超过设置的更新日期

### 🟠 1天内过期警告
- **背景色**: 浅橙色 (`#fdf6ec`)
- **悬停效果**: 深浅橙色 (`#faecd8`)
- **文字颜色**: 橙色 (`#e6a23c`)
- **触发条件**: 距离更新日期还有0-1天

### 🟡 2天内过期警告
- **背景色**: 极浅红色 (`#fef5f5`)
- **悬停效果**: 浅红色 (`#fce8e8`)
- **文字颜色**: 浅红色 (`#f78989`)
- **触发条件**: 距离更新日期还有2天

### 🟨 3天内过期警告
- **背景色**: 极浅黄色 (`#fffbf0`)
- **悬停效果**: 浅黄色 (`#fef7e6`)
- **文字颜色**: 棕黄色 (`#b88230`)
- **触发条件**: 距离更新日期还有3天

### 🟢 正常状态
- **背景色**: 默认白色
- **文字颜色**: 绿色 (`#67c23a`)
- **触发条件**: 距离更新日期超过3天或未设置更新日期

## 使用方法

### 1. 设置账号更新日期

1. 在账号管理页面找到需要设置的账号
2. 点击该账号行的"编辑"按钮
3. 在弹出的编辑对话框中找到"更新日期"字段
4. 选择账号的更新日期（建议设置为账号需要更新的日期）
5. 点击"确定"保存设置

### 2. 查看过期提醒

- 账号列表会自动显示每个账号的过期状态
- **整行背景色**会根据过期程度显示不同颜色
- "过期状态"列显示具体的剩余天数或过期天数
- "更新日期"列显示设置的更新日期

## 功能特点

### ✨ 醒目的视觉提醒
- 使用整行背景色，比单独的标签更加醒目
- 不同颜色代表不同的紧急程度
- 鼠标悬停时背景色会加深，提供更好的交互反馈

### 🎯 精确的状态计算
- 实时计算距离更新日期的天数
- 自动判断过期状态（已过期、即将过期、正常）
- 支持清除更新日期设置

### 🔄 响应式更新
- 修改更新日期后立即生效
- 无需刷新页面即可看到状态变化
- 支持批量查看多个账号的过期状态

### 📱 用户友好
- 直观的颜色编码系统
- 清晰的状态描述文字
- 简单的设置操作流程

## 技术实现

### 前端组件
- `AccountManagement.vue`: 主要的账号列表页面
- `AccountFormDialog.vue`: 账号编辑对话框
- `accountExpiry.ts`: 过期状态计算工具

### 核心功能
- 过期状态计算算法
- 动态行样式应用
- 日期格式化和验证

### 样式系统
- CSS深度选择器实现表格行样式
- 响应式颜色主题
- 悬停效果和过渡动画

## 最佳实践

### 建议的更新日期设置
- **社交媒体账号**: 建议每30-90天更新一次
- **重要业务账号**: 建议每15-30天更新一次
- **测试账号**: 可根据测试周期设置

### 日常使用建议
1. 定期查看账号列表，关注红色和橙色背景的账号
2. 优先处理已过期（红色背景）的账号
3. 提前准备即将过期（橙色/黄色背景）的账号更新工作
4. 建立定期检查和更新的工作流程

## 故障排除

### 常见问题
1. **背景色不显示**: 检查浏览器是否支持CSS深度选择器
2. **日期格式错误**: 确保使用YYYY-MM-DD格式
3. **状态不更新**: 尝试刷新页面或重新设置日期

### 🔄 排序功能

### 列排序
- **更新日期列**: 点击列标题可按更新日期排序（最新日期在前）
- **过期状态列**: 点击列标题可按过期状态排序（紧急程度从高到低）

### 快速排序按钮
在操作区域提供了两个快速排序按钮：

#### 📅 按更新日期排序
- 将账号按更新日期从新到旧排列
- 未设置更新日期的账号排在最后
- 方便查看最近更新的账号

#### ⚠️ 按过期状态排序
- 按紧急程度排序：已过期 → 1天内过期 → 2天内过期 → 3天内过期 → 正常状态
- 相同状态下按剩余天数排序（天数少的在前）
- 方便优先处理紧急账号

### 排序优先级
过期状态排序的优先级顺序：
1. 🔴 **已过期** - 最高优先级
2. 🟠 **1天内过期** - 高优先级
3. 🟡 **2天内过期** - 中高优先级
4. 🟨 **3天内过期** - 中等优先级
5. 🟢 **正常状态** - 低优先级
6. ⚪ **未设置** - 最低优先级

## 技术支持
如遇到问题，请检查：
- 浏览器控制台是否有JavaScript错误
- 网络连接是否正常
- 后端API是否正常响应
