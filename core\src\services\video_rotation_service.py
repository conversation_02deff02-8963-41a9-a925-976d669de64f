"""
视频旋转服务
支持将视频旋转90度（向左/向右）或180度
"""

import os
import asyncio
import subprocess
import logging
from typing import List, Dict, Any, Tuple
import time

logger = logging.getLogger(__name__)

class VideoRotationService:
    """视频旋转服务类"""

    def __init__(self):
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v', 'webm', '3gp', 'ts']

    def is_video_file(self, file_path: str) -> bool:
        """判断是否为视频文件"""
        if not os.path.isfile(file_path):
            return False
        
        ext = os.path.splitext(file_path.lower())[1][1:]  # 去掉点号
        return ext in self.supported_video_extensions

    def get_output_path(self, input_path: str, overwrite_original: bool, output_suffix: str) -> str:
        """生成输出文件路径"""
        if overwrite_original:
            return input_path
        
        # 不覆盖原文件，生成新的文件名
        dir_path = os.path.dirname(input_path)
        filename = os.path.basename(input_path)
        name, ext = os.path.splitext(filename)
        
        # 添加后缀
        if not output_suffix:
            output_suffix = "_rotated"
        
        new_filename = f"{name}{output_suffix}{ext}"
        return os.path.join(dir_path, new_filename)

    def get_rotation_filter(self, rotation_angle: int) -> str:
        """根据旋转角度获取ffmpeg滤镜"""
        if rotation_angle == 90:
            # 向右旋转90度（顺时针）
            return "transpose=1"
        elif rotation_angle == -90:
            # 向左旋转90度（逆时针）
            return "transpose=2"
        elif rotation_angle == 180:
            # 旋转180度
            return "transpose=1,transpose=1"
        else:
            raise ValueError(f"不支持的旋转角度: {rotation_angle}。支持的角度: 90, -90, 180")

    async def rotate_single_video(self, input_path: str, output_path: str, 
                                rotation_angle: int, output_quality: str) -> Dict[str, Any]:
        """旋转单个视频文件"""
        start_time = time.time()
        
        try:
            logger.info(f"开始旋转视频: {input_path} -> {output_path}, 角度: {rotation_angle}°")
            
            # 检查输入文件
            if not os.path.exists(input_path):
                raise FileNotFoundError(f"输入文件不存在: {input_path}")
            
            if not self.is_video_file(input_path):
                raise ValueError(f"不是支持的视频文件: {input_path}")
            
            # 获取原始文件大小
            original_file_size = os.path.getsize(input_path)
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            
            # 获取旋转滤镜
            rotation_filter = self.get_rotation_filter(rotation_angle)
            
            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y', '-i', input_path]
            
            # 添加旋转滤镜
            cmd.extend(['-vf', rotation_filter])
            
            # 设置编码参数
            cmd.extend(['-c:v', 'libx264', '-preset', 'fast'])
            
            # 根据质量设置CRF
            if output_quality == 'high':
                cmd.extend(['-crf', '18'])
            elif output_quality == 'medium':
                cmd.extend(['-crf', '23'])
            else:  # low
                cmd.extend(['-crf', '28'])
            
            # 音频直接复制
            cmd.extend(['-c:a', 'copy'])
            
            # 输出文件
            cmd.append(output_path)
            
            logger.debug(f"执行ffmpeg命令: {' '.join(cmd[:10])}...")
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "未知错误"
                logger.error(f"ffmpeg执行失败: {error_msg}")
                raise RuntimeError(f"视频旋转失败: {error_msg}")
            
            # 检查输出文件是否生成
            if not os.path.exists(output_path):
                raise RuntimeError("输出文件未生成")
            
            # 获取输出文件大小
            output_file_size = os.path.getsize(output_path)
            
            processing_time = int((time.time() - start_time) * 1000)
            
            logger.info(f"视频旋转成功: {output_path}, 耗时: {processing_time}ms")
            
            return {
                'success': True,
                'original_path': input_path,
                'output_path': output_path,
                'error_message': '',
                'processing_time_ms': processing_time,
                'original_file_size': original_file_size,
                'output_file_size': output_file_size
            }
            
        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            logger.error(f"旋转视频失败 {input_path}: {error_msg}", exc_info=True)
            
            return {
                'success': False,
                'original_path': input_path,
                'output_path': output_path,
                'error_message': error_msg,
                'processing_time_ms': processing_time,
                'original_file_size': 0,
                'output_file_size': 0
            }

    async def rotate_videos(self, video_paths: List[str], rotation_angle: int,
                          output_quality: str = 'medium', overwrite_original: bool = False,
                          output_suffix: str = '_rotated') -> Dict[str, Any]:
        """批量旋转视频"""
        start_time = time.time()
        
        try:
            logger.info(f"开始批量旋转视频: {len(video_paths)} 个文件, 角度: {rotation_angle}°")
            
            if not video_paths:
                raise ValueError("视频文件路径列表不能为空")
            
            # 验证旋转角度
            if rotation_angle not in [90, -90, 180]:
                raise ValueError(f"不支持的旋转角度: {rotation_angle}。支持的角度: 90, -90, 180")
            
            results = []
            successful_count = 0
            failed_count = 0
            
            # 逐个处理视频文件
            for video_path in video_paths:
                try:
                    # 生成输出路径
                    output_path = self.get_output_path(video_path, overwrite_original, output_suffix)
                    
                    # 旋转视频
                    result = await self.rotate_single_video(
                        video_path, output_path, rotation_angle, output_quality
                    )
                    
                    results.append(result)
                    
                    if result['success']:
                        successful_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    error_msg = str(e)
                    logger.error(f"处理视频失败 {video_path}: {error_msg}")
                    
                    results.append({
                        'success': False,
                        'original_path': video_path,
                        'output_path': '',
                        'error_message': error_msg,
                        'processing_time_ms': 0,
                        'original_file_size': 0,
                        'output_file_size': 0
                    })
                    failed_count += 1
            
            total_processing_time = int((time.time() - start_time) * 1000)
            
            logger.info(f"批量旋转完成: 成功 {successful_count} 个, 失败 {failed_count} 个, 总耗时: {total_processing_time}ms")
            
            return {
                'success': True,
                'error': '',
                'results': results,
                'successful_count': successful_count,
                'failed_count': failed_count,
                'total_processing_time_ms': total_processing_time
            }
            
        except Exception as e:
            total_processing_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            logger.error(f"批量旋转视频失败: {error_msg}", exc_info=True)
            
            return {
                'success': False,
                'error': error_msg,
                'results': [],
                'successful_count': 0,
                'failed_count': len(video_paths),
                'total_processing_time_ms': total_processing_time
            }
