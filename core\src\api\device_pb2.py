# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: device.proto
# Protobuf Python Version: 6.30.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# 移除 runtime_version 检查以兼容旧版本 protobuf
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x64\x65vice.proto\x12\x06\x64\x65vice\"#\n\x11\x44\x65viceListRequest\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\"5\n\x12\x44\x65viceListResponse\x12\x1f\n\x07\x64\x65vices\x18\x01 \x03(\x0b\x32\x0e.device.Device\"\"\n\rDeviceRequest\x12\x11\n\tdevice_id\x18\x01 \x01(\t\"0\n\x0e\x44\x65viceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"4\n\x12\x44\x65viceInfoResponse\x12\x1e\n\x06\x64\x65vice\x18\x01 \x01(\x0b\x32\x0e.device.Device\"\x84\x01\n\x13\x43reateDeviceRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\nresolution\x18\x02 \x01(\t\x12\x11\n\tcpu_count\x18\x03 \x01(\x05\x12\x13\n\x0bmemory_size\x18\x04 \x01(\x05\x12\x14\n\x0cmanufacturer\x18\x05 \x01(\t\x12\r\n\x05model\x18\x06 \x01(\t\"I\n\x14\x43reateDeviceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x11\n\tdevice_id\x18\x02 \x01(\t\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"\x97\x01\n\x0e\x43ommandRequest\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63ommand\x18\x02 \x01(\t\x12\x32\n\x06params\x18\x03 \x03(\x0b\x32\".device.CommandRequest.ParamsEntry\x1a-\n\x0bParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"A\n\x0f\x43ommandResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0e\n\x06output\x18\x02 \x01(\t\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"\x8c\x03\n\x06\x44\x65vice\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65vice_type\x18\x04 \x01(\t\x12\x33\n\x0bwindow_info\x18\x05 \x03(\x0b\x32\x1e.device.Device.WindowInfoEntry\x12\x35\n\x0cprocess_info\x18\x06 \x03(\x0b\x32\x1f.device.Device.ProcessInfoEntry\x12\x35\n\x0c\x64isplay_info\x18\x07 \x03(\x0b\x32\x1f.device.Device.DisplayInfoEntry\x1a\x31\n\x0fWindowInfoEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x32\n\x10ProcessInfoEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x32\n\x10\x44isplayInfoEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x32\xb3\x04\n\rDeviceService\x12H\n\rGetDeviceList\x12\x19.device.DeviceListRequest\x1a\x1a.device.DeviceListResponse\"\x00\x12>\n\x0bStartDevice\x12\x15.device.DeviceRequest\x1a\x16.device.DeviceResponse\"\x00\x12=\n\nStopDevice\x12\x15.device.DeviceRequest\x1a\x16.device.DeviceResponse\"\x00\x12@\n\rRestartDevice\x12\x15.device.DeviceRequest\x1a\x16.device.DeviceResponse\"\x00\x12\x44\n\rGetDeviceInfo\x12\x15.device.DeviceRequest\x1a\x1a.device.DeviceInfoResponse\"\x00\x12K\n\x0c\x43reateDevice\x12\x1b.device.CreateDeviceRequest\x1a\x1c.device.CreateDeviceResponse\"\x00\x12?\n\x0cRemoveDevice\x12\x15.device.DeviceRequest\x1a\x16.device.DeviceResponse\"\x00\x12\x43\n\x0e\x45xecuteCommand\x12\x16.device.CommandRequest\x1a\x17.device.CommandResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'device_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_COMMANDREQUEST_PARAMSENTRY']._loaded_options = None
  _globals['_COMMANDREQUEST_PARAMSENTRY']._serialized_options = b'8\001'
  _globals['_DEVICE_WINDOWINFOENTRY']._loaded_options = None
  _globals['_DEVICE_WINDOWINFOENTRY']._serialized_options = b'8\001'
  _globals['_DEVICE_PROCESSINFOENTRY']._loaded_options = None
  _globals['_DEVICE_PROCESSINFOENTRY']._serialized_options = b'8\001'
  _globals['_DEVICE_DISPLAYINFOENTRY']._loaded_options = None
  _globals['_DEVICE_DISPLAYINFOENTRY']._serialized_options = b'8\001'
  _globals['_DEVICELISTREQUEST']._serialized_start=24
  _globals['_DEVICELISTREQUEST']._serialized_end=59
  _globals['_DEVICELISTRESPONSE']._serialized_start=61
  _globals['_DEVICELISTRESPONSE']._serialized_end=114
  _globals['_DEVICEREQUEST']._serialized_start=116
  _globals['_DEVICEREQUEST']._serialized_end=150
  _globals['_DEVICERESPONSE']._serialized_start=152
  _globals['_DEVICERESPONSE']._serialized_end=200
  _globals['_DEVICEINFORESPONSE']._serialized_start=202
  _globals['_DEVICEINFORESPONSE']._serialized_end=254
  _globals['_CREATEDEVICEREQUEST']._serialized_start=257
  _globals['_CREATEDEVICEREQUEST']._serialized_end=389
  _globals['_CREATEDEVICERESPONSE']._serialized_start=391
  _globals['_CREATEDEVICERESPONSE']._serialized_end=464
  _globals['_COMMANDREQUEST']._serialized_start=467
  _globals['_COMMANDREQUEST']._serialized_end=618
  _globals['_COMMANDREQUEST_PARAMSENTRY']._serialized_start=573
  _globals['_COMMANDREQUEST_PARAMSENTRY']._serialized_end=618
  _globals['_COMMANDRESPONSE']._serialized_start=620
  _globals['_COMMANDRESPONSE']._serialized_end=685
  _globals['_DEVICE']._serialized_start=688
  _globals['_DEVICE']._serialized_end=1084
  _globals['_DEVICE_WINDOWINFOENTRY']._serialized_start=931
  _globals['_DEVICE_WINDOWINFOENTRY']._serialized_end=980
  _globals['_DEVICE_PROCESSINFOENTRY']._serialized_start=982
  _globals['_DEVICE_PROCESSINFOENTRY']._serialized_end=1032
  _globals['_DEVICE_DISPLAYINFOENTRY']._serialized_start=1034
  _globals['_DEVICE_DISPLAYINFOENTRY']._serialized_end=1084
  _globals['_DEVICESERVICE']._serialized_start=1087
  _globals['_DEVICESERVICE']._serialized_end=1650
# @@protoc_insertion_point(module_scope)
