import hashlib
import os
import subprocess
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import logging
from pymongo import MongoClient
from pymongo.collection import Collection

logger = logging.getLogger(__name__)

class VideoDeduplicationService:
    """视频去重服务"""
    
    def __init__(self, mongo_db):
        self.db = mongo_db
        self.uploaded_videos: Collection = mongo_db.uploaded_videos
        
        # 创建索引以提高查询性能
        self._ensure_indexes()
    
    def _ensure_indexes(self):
        """确保必要的索引存在"""
        try:
            # 文件哈希唯一索引
            self.uploaded_videos.create_index("file_hash", unique=True)
            # 文件大小和时长组合索引（用于快速预检）
            self.uploaded_videos.create_index([("file_size", 1), ("duration", 1)])
            # 平台和账号组合索引
            self.uploaded_videos.create_index([("upload_records.platform_id", 1), ("upload_records.account_id", 1)])
            logger.info("视频去重索引创建完成")
        except Exception as e:
            logger.warning(f"创建索引时出现警告: {str(e)}")
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件的SHA-256哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: SHA-256哈希值
        """
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                # 分块读取，避免大文件内存问题
                for chunk in iter(lambda: f.read(8192), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {str(e)}")
            raise
    
    def get_video_metadata(self, file_path: str) -> Dict:
        """获取视频元数据
        
        Args:
            file_path: 视频文件路径
            
        Returns:
            dict: 包含时长、分辨率等信息的字典
        """
        try:
            # 使用ffprobe获取视频信息
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', file_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                logger.warning(f"ffprobe执行失败: {result.stderr}")
                return {"duration": 0, "resolution": "unknown"}
            
            data = json.loads(result.stdout)
            
            # 提取视频流信息
            video_stream = None
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            duration = float(data.get('format', {}).get('duration', 0))
            
            if video_stream:
                width = video_stream.get('width', 0)
                height = video_stream.get('height', 0)
                resolution = f"{width}x{height}" if width and height else "unknown"
            else:
                resolution = "unknown"
            
            return {
                "duration": duration,
                "resolution": resolution
            }
            
        except subprocess.TimeoutExpired:
            logger.error(f"ffprobe超时: {file_path}")
            return {"duration": 0, "resolution": "unknown"}
        except Exception as e:
            logger.error(f"获取视频元数据失败: {str(e)}")
            return {"duration": 0, "resolution": "unknown"}
    
    def generate_video_fingerprint(self, file_path: str) -> Dict:
        """生成视频文件指纹
        
        Args:
            file_path: 视频文件路径
            
        Returns:
            dict: 视频指纹信息
        """
        try:
            # 获取文件基本信息
            stat = os.stat(file_path)
            file_size = stat.st_size
            original_filename = os.path.basename(file_path)
            
            # 获取视频元数据
            metadata = self.get_video_metadata(file_path)
            
            # 计算文件哈希
            file_hash = self.calculate_file_hash(file_path)
            
            return {
                "file_hash": file_hash,
                "file_size": file_size,
                "duration": metadata["duration"],
                "resolution": metadata["resolution"],
                "original_filename": original_filename
            }
            
        except Exception as e:
            logger.error(f"生成视频指纹失败: {str(e)}")
            raise
    
    def quick_duplicate_check(self, file_path: str) -> Optional[Dict]:
        """快速重复检查（基于文件大小和时长）
        
        Args:
            file_path: 视频文件路径
            
        Returns:
            dict: 如果找到重复视频，返回视频记录；否则返回None
        """
        try:
            file_size = os.path.getsize(file_path)
            metadata = self.get_video_metadata(file_path)
            duration = metadata["duration"]
            
            # 查找相同大小和相近时长的视频（允许1秒误差）
            query = {
                "file_size": file_size,
                "duration": {
                    "$gte": duration - 1,
                    "$lte": duration + 1
                }
            }
            
            candidates = list(self.uploaded_videos.find(query))
            
            if not candidates:
                return None
            
            # 如果找到候选项，计算完整哈希进行精确比较
            file_hash = self.calculate_file_hash(file_path)
            
            for candidate in candidates:
                if candidate.get("file_hash") == file_hash:
                    logger.info(f"发现重复视频: {file_path} (哈希: {file_hash})")
                    return candidate
            
            return None

        except Exception as e:
            logger.error(f"快速重复检查失败: {str(e)}")
            return None

    def check_duplicate_by_hash(self, file_hash: str) -> Optional[Dict]:
        """通过哈希值检查重复

        Args:
            file_hash: 文件哈希值

        Returns:
            dict: 如果找到重复视频，返回视频记录；否则返回None
        """
        try:
            return self.uploaded_videos.find_one({"file_hash": file_hash})
        except Exception as e:
            logger.error(f"通过哈希检查重复失败: {str(e)}")
            return None

    def is_duplicate_for_account(self, file_path: str, platform_id: str, account_id: str) -> Tuple[bool, Optional[Dict]]:
        """检查视频是否已经在指定账号上传过

        Args:
            file_path: 视频文件路径
            platform_id: 平台ID
            account_id: 账号ID

        Returns:
            tuple: (是否重复, 视频记录)
        """
        try:
            # 先进行快速检查
            duplicate_record = self.quick_duplicate_check(file_path)

            if not duplicate_record:
                return False, None

            # 检查是否在指定账号上传过
            for upload_record in duplicate_record.get("upload_records", []):
                if (upload_record.get("platform_id") == platform_id and
                    upload_record.get("account_id") == account_id and
                    upload_record.get("status") == "success"):
                    logger.info(f"视频已在账号 {account_id} 上传过")
                    return True, duplicate_record

            return False, duplicate_record

        except Exception as e:
            logger.error(f"检查账号重复上传失败: {str(e)}")
            return False, None

    def record_upload_attempt(self, file_path: str, platform_id: str, account_id: str,
                            task_id: str, title: str, status: str = "pending",
                            youtube_video_id: str = None) -> bool:
        """记录上传尝试

        Args:
            file_path: 视频文件路径
            platform_id: 平台ID
            account_id: 账号ID
            task_id: 任务ID
            title: 视频标题
            status: 上传状态
            youtube_video_id: YouTube视频ID（如果成功）

        Returns:
            bool: 是否记录成功
        """
        try:
            # 生成视频指纹
            fingerprint = self.generate_video_fingerprint(file_path)
            file_hash = fingerprint["file_hash"]

            # 准备上传记录
            upload_record = {
                "platform_id": platform_id,
                "account_id": account_id,
                "task_id": task_id,
                "title": title,
                "status": status,
                "upload_time": datetime.now()
            }

            if youtube_video_id:
                upload_record["youtube_video_id"] = youtube_video_id

            # 查找现有记录
            existing_record = self.uploaded_videos.find_one({"file_hash": file_hash})

            if existing_record:
                # 更新现有记录
                self.uploaded_videos.update_one(
                    {"file_hash": file_hash},
                    {
                        "$push": {"upload_records": upload_record},
                        "$set": {
                            "last_upload": datetime.now(),
                            "updated_at": datetime.now()
                        },
                        "$inc": {"upload_count": 1}
                    }
                )
                logger.info(f"更新视频上传记录: {file_hash}")
            else:
                # 创建新记录
                new_record = {
                    "file_hash": file_hash,
                    "file_size": fingerprint["file_size"],
                    "duration": fingerprint["duration"],
                    "resolution": fingerprint["resolution"],
                    "original_filename": fingerprint["original_filename"],
                    "upload_records": [upload_record],
                    "first_upload": datetime.now(),
                    "last_upload": datetime.now(),
                    "upload_count": 1,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                }

                self.uploaded_videos.insert_one(new_record)
                logger.info(f"创建新视频上传记录: {file_hash}")

            return True

        except Exception as e:
            logger.error(f"记录上传尝试失败: {str(e)}")
            return False

    def update_upload_status(self, file_path: str, task_id: str, status: str,
                           youtube_video_id: str = None, error_message: str = None) -> bool:
        """更新上传状态

        Args:
            file_path: 视频文件路径
            task_id: 任务ID
            status: 新状态
            youtube_video_id: YouTube视频ID（如果成功）
            error_message: 错误信息（如果失败）

        Returns:
            bool: 是否更新成功
        """
        try:
            file_hash = self.calculate_file_hash(file_path)

            # 构建更新条件
            update_data = {
                "upload_records.$.status": status,
                "updated_at": datetime.now()
            }

            if youtube_video_id:
                update_data["upload_records.$.youtube_video_id"] = youtube_video_id

            if error_message:
                update_data["upload_records.$.error_message"] = error_message

            # 更新指定任务的上传记录
            result = self.uploaded_videos.update_one(
                {
                    "file_hash": file_hash,
                    "upload_records.task_id": task_id
                },
                {"$set": update_data}
            )

            if result.modified_count > 0:
                logger.info(f"更新上传状态成功: {task_id} -> {status}")
                return True
            else:
                logger.warning(f"未找到要更新的上传记录: {task_id}")
                return False

        except Exception as e:
            logger.error(f"更新上传状态失败: {str(e)}")
            return False

    def get_upload_history(self, file_path: str) -> List[Dict]:
        """获取视频的上传历史

        Args:
            file_path: 视频文件路径

        Returns:
            list: 上传历史记录列表
        """
        try:
            file_hash = self.calculate_file_hash(file_path)
            record = self.uploaded_videos.find_one({"file_hash": file_hash})

            if record:
                return record.get("upload_records", [])
            else:
                return []

        except Exception as e:
            logger.error(f"获取上传历史失败: {str(e)}")
            return []

    def get_duplicate_statistics(self) -> Dict:
        """获取重复上传统计信息

        Returns:
            dict: 统计信息
        """
        try:
            # 总视频数
            total_videos = self.uploaded_videos.count_documents({})

            # 重复上传的视频数（上传次数>1）
            duplicate_videos = self.uploaded_videos.count_documents({"upload_count": {"$gt": 1}})

            # 总上传次数
            pipeline = [
                {"$group": {"_id": None, "total_uploads": {"$sum": "$upload_count"}}}
            ]
            result = list(self.uploaded_videos.aggregate(pipeline))
            total_uploads = result[0]["total_uploads"] if result else 0

            # 节省的上传次数
            saved_uploads = total_uploads - total_videos

            return {
                "total_unique_videos": total_videos,
                "duplicate_videos": duplicate_videos,
                "total_upload_attempts": total_uploads,
                "saved_uploads": saved_uploads,
                "duplicate_rate": round(duplicate_videos / total_videos * 100, 2) if total_videos > 0 else 0
            }

        except Exception as e:
            logger.error(f"获取重复统计失败: {str(e)}")
            return {
                "total_unique_videos": 0,
                "duplicate_videos": 0,
                "total_upload_attempts": 0,
                "saved_uploads": 0,
                "duplicate_rate": 0
            }

    def cleanup_failed_records(self, days_old: int = 7) -> int:
        """清理失败的上传记录

        Args:
            days_old: 清理多少天前的失败记录

        Returns:
            int: 清理的记录数
        """
        try:
            from datetime import timedelta

            cutoff_date = datetime.now() - timedelta(days=days_old)

            # 删除只有失败记录且超过指定天数的视频记录
            result = self.uploaded_videos.delete_many({
                "upload_records": {
                    "$not": {
                        "$elemMatch": {"status": "success"}
                    }
                },
                "created_at": {"$lt": cutoff_date}
            })

            logger.info(f"清理了 {result.deleted_count} 条失败的上传记录")
            return result.deleted_count

        except Exception as e:
            logger.error(f"清理失败记录时出错: {str(e)}")
            return 0
