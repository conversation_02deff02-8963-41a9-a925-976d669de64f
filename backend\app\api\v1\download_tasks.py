"""
下载任务管理API
用于管理竞品内容下载任务
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request, BackgroundTasks
from pymongo.database import Database
from bson import ObjectId
from datetime import datetime
import redis

from app.core.security import get_current_user
from app.core.schemas.content_models import (
    DownloadTask, CreateDownloadTaskRequest, TaskProgress, TaskResult
)

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/download",
    tags=["download-tasks"],
    dependencies=[Depends(get_current_user)]
)


def get_db(request: Request) -> Database:
    """获取数据库连接"""
    return request.app.state.mongo_db


def get_redis(request: Request) -> redis.Redis:
    """获取Redis连接"""
    return request.app.state.redis_client


@router.post("/tasks", response_model=Dict[str, Any])
async def create_download_task(
    task_request: CreateDownloadTaskRequest,
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_user),
    db: Database = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """
    创建下载任务
    
    支持的任务类型：
    - single: 单个URL下载
    - batch: 批量URL下载
    - channel: 频道/用户所有内容下载
    - playlist: 播放列表下载
    """
    try:
        logger.info(f"创建下载任务: {task_request.task_name}")
        
        # 创建任务数据
        task_data = {
            "task_name": task_request.task_name,
            "task_type": task_request.task_type,
            "source_urls": task_request.source_urls,
            "target_platform": task_request.target_platform,
            "download_config": task_request.download_config.dict() if task_request.download_config else {},
            "filter_config": task_request.filter_config.dict() if task_request.filter_config else {},
            "progress": {
                "total_items": len(task_request.source_urls),
                "completed_items": 0,
                "failed_items": 0,
                "current_item": None,
                "percentage": 0.0
            },
            "status": "pending",
            "result": None,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "created_by": current_user.username
        }
        
        # 插入数据库
        result = db.download_tasks.insert_one(task_data)
        task_id = str(result.inserted_id)
        
        logger.info(f"下载任务创建成功: {task_id}")
        
        # 将任务添加到Redis队列
        task_queue_data = {
            "task_id": task_id,
            "task_type": task_request.task_type,
            "source_urls": task_request.source_urls,
            "target_platform": task_request.target_platform,
            "download_config": task_request.download_config.dict() if task_request.download_config else {},
            "filter_config": task_request.filter_config.dict() if task_request.filter_config else {}
        }
        
        # 推送到Redis队列
        redis_client.lpush("download_tasks_queue", str(task_queue_data))
        logger.info(f"任务已添加到队列: {task_id}")
        
        # 启动后台任务处理
        background_tasks.add_task(process_download_task, task_id, task_queue_data, db, redis_client)
        
        return {
            "task_id": task_id,
            "message": "下载任务创建成功",
            "status": "pending"
        }
        
    except Exception as e:
        logger.error(f"创建下载任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建下载任务失败: {str(e)}")


@router.get("/tasks", response_model=List[Dict[str, Any]])
async def get_download_tasks(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态过滤"),
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    db: Database = Depends(get_db)
):
    """获取下载任务列表"""
    try:
        logger.info(f"获取下载任务列表: page={page}, limit={limit}, status={status}")
        
        # 构建查询条件
        query = {}
        if status:
            query["status"] = status
        if task_type:
            query["task_type"] = task_type
        
        # 计算跳过的文档数
        skip = (page - 1) * limit
        
        # 查询任务列表
        cursor = db.download_tasks.find(query).skip(skip).limit(limit).sort("created_at", -1)
        tasks = await cursor.to_list(length=limit)
        
        # 转换ObjectId为字符串
        for task in tasks:
            task["_id"] = str(task["_id"])
        
        return tasks
        
    except Exception as e:
        logger.error(f"获取下载任务列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取下载任务列表失败: {str(e)}")


@router.get("/tasks/{task_id}")
async def get_download_task_detail(
    task_id: str,
    db: Database = Depends(get_db)
):
    """获取下载任务详情"""
    try:
        logger.info(f"获取下载任务详情: {task_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(task_id):
            raise HTTPException(status_code=400, detail="无效的任务ID格式")
        
        task = db.download_tasks.find_one({"_id": ObjectId(task_id)})
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 转换ObjectId为字符串
        task["_id"] = str(task["_id"])
        
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取下载任务详情失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取下载任务详情失败: {str(e)}")


@router.post("/tasks/{task_id}/cancel")
async def cancel_download_task(
    task_id: str,
    db: Database = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """取消下载任务"""
    try:
        logger.info(f"取消下载任务: {task_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(task_id):
            raise HTTPException(status_code=400, detail="无效的任务ID格式")
        
        # 检查任务是否存在
        task = db.download_tasks.find_one({"_id": ObjectId(task_id)})
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 检查任务状态
        if task["status"] in ["completed", "failed", "cancelled"]:
            raise HTTPException(status_code=400, detail=f"任务已{task['status']}，无法取消")
        
        # 更新任务状态
        result = db.download_tasks.update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": {
                    "status": "cancelled",
                    "updated_at": datetime.now()
                }
            }
        )
        
        # 发送取消信号到Redis
        redis_client.publish(f"task_cancel:{task_id}", "cancel")
        
        return {"message": "任务取消成功", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消下载任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"取消下载任务失败: {str(e)}")


@router.post("/tasks/{task_id}/retry")
async def retry_download_task(
    task_id: str,
    background_tasks: BackgroundTasks,
    db: Database = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """重试失败的下载任务"""
    try:
        logger.info(f"重试下载任务: {task_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(task_id):
            raise HTTPException(status_code=400, detail="无效的任务ID格式")
        
        # 检查任务是否存在
        task = db.download_tasks.find_one({"_id": ObjectId(task_id)})
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 检查任务状态
        if task["status"] not in ["failed", "cancelled"]:
            raise HTTPException(status_code=400, detail=f"只能重试失败或取消的任务")
        
        # 重置任务状态
        update_data = {
            "status": "pending",
            "updated_at": datetime.now(),
            "progress.completed_items": 0,
            "progress.failed_items": 0,
            "progress.current_item": None,
            "progress.percentage": 0.0
        }
        
        # 如果有失败的URL，只重试失败的部分
        if task.get("result") and task["result"].get("failed_urls"):
            failed_urls = task["result"]["failed_urls"]
            update_data["source_urls"] = failed_urls
            update_data["progress.total_items"] = len(failed_urls)
        
        result = db.download_tasks.update_one(
            {"_id": ObjectId(task_id)},
            {"$set": update_data}
        )
        
        # 重新添加到队列
        task_queue_data = {
            "task_id": task_id,
            "task_type": task["task_type"],
            "source_urls": update_data.get("source_urls", task["source_urls"]),
            "target_platform": task["target_platform"],
            "download_config": task.get("download_config", {}),
            "filter_config": task.get("filter_config", {})
        }
        
        redis_client.lpush("download_tasks_queue", str(task_queue_data))
        
        # 启动后台任务处理
        background_tasks.add_task(process_download_task, task_id, task_queue_data, db, redis_client)
        
        return {"message": "任务重试成功", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试下载任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"重试下载任务失败: {str(e)}")


async def process_download_task(
    task_id: str,
    task_data: Dict[str, Any],
    db: Database,
    redis_client: redis.Redis
):
    """处理下载任务的后台函数"""
    try:
        logger.info(f"开始处理下载任务: {task_id}")
        
        # 更新任务状态为运行中
        db.download_tasks.update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": {
                    "status": "running",
                    "updated_at": datetime.now()
                }
            }
        )
        
        # TODO: 这里应该集成实际的下载器（如yt-dlp）
        # 目前只是模拟处理过程
        
        source_urls = task_data["source_urls"]
        total_items = len(source_urls)
        completed_items = 0
        failed_items = 0
        failed_urls = []
        
        for i, url in enumerate(source_urls):
            # 检查是否收到取消信号
            cancel_signal = redis_client.get(f"task_cancel:{task_id}")
            if cancel_signal:
                logger.info(f"收到取消信号，停止处理任务: {task_id}")
                break
            
            try:
                # 更新当前处理项目
                current_progress = {
                    "total_items": total_items,
                    "completed_items": completed_items,
                    "failed_items": failed_items,
                    "current_item": url,
                    "percentage": (i / total_items) * 100
                }
                
                db.download_tasks.update_one(
                    {"_id": ObjectId(task_id)},
                    {
                        "$set": {
                            "progress": current_progress,
                            "updated_at": datetime.now()
                        }
                    }
                )
                
                # TODO: 实际的下载逻辑
                # success = download_content(url, task_data)
                success = True  # 模拟成功
                
                if success:
                    completed_items += 1
                else:
                    failed_items += 1
                    failed_urls.append(url)
                    
            except Exception as item_error:
                logger.error(f"处理URL失败: {url}, 错误: {str(item_error)}")
                failed_items += 1
                failed_urls.append(url)
        
        # 更新最终结果
        final_status = "cancelled" if cancel_signal else ("completed" if failed_items == 0 else "failed")
        final_progress = {
            "total_items": total_items,
            "completed_items": completed_items,
            "failed_items": failed_items,
            "current_item": None,
            "percentage": 100.0 if not cancel_signal else (completed_items / total_items) * 100
        }
        
        final_result = {
            "success_count": completed_items,
            "failed_count": failed_items,
            "total_size": 0,  # TODO: 计算实际下载大小
            "failed_urls": failed_urls
        }
        
        db.download_tasks.update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": {
                    "status": final_status,
                    "progress": final_progress,
                    "result": final_result,
                    "updated_at": datetime.now()
                }
            }
        )
        
        logger.info(f"下载任务处理完成: {task_id}, 状态: {final_status}")
        
    except Exception as e:
        logger.error(f"处理下载任务失败: {task_id}, 错误: {str(e)}", exc_info=True)
        
        # 更新任务状态为失败
        db.download_tasks.update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": {
                    "status": "failed",
                    "updated_at": datetime.now()
                }
            }
        )
