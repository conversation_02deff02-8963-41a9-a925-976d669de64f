# -*- coding: utf-8 -*-
import pytest
import sys
from pathlib import Path
from unittest.mock import AsyncMock, patch

sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))
from core.devices.ldplayer.controller import LDPlayerController

@pytest.mark.asyncio
async def test_sort_windows_success():
    with patch('asyncio.create_subprocess_shell') as mock_subprocess:
        mock_proc = AsyncMock()
        mock_proc.communicate = AsyncMock(return_value=(b"sort complete", b""))
        mock_proc.returncode = 0
        mock_subprocess.return_value = mock_proc

        controller = LDPlayerController({'device_id': 'test1'})
        result = await controller.sort_windows()
        assert result is True
        assert mock_proc.communicate.call_count >= 1  # 至少调用一次

@pytest.mark.asyncio
async def test_sort_windows_failure():
    with patch('asyncio.create_subprocess_shell') as mock_subprocess:
        mock_proc = AsyncMock()
        mock_proc.wait = AsyncMock(return_value=1)
        mock_proc.returncode = 1
        mock_subprocess.return_value = mock_proc

        controller = LDPlayerController({'device_id': 'test1'})
        result = await controller.sort_windows()
        assert result is False

@pytest.mark.asyncio
async def test_sort_windows_exception():
    with patch('asyncio.create_subprocess_shell', side_effect=Exception('Command failed')):
        controller = LDPlayerController({'device_id': 'test1'})
        result = await controller.sort_windows()
        assert result is False
