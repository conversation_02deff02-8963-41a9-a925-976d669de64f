# Core模块开发指南

## 当前目录结构（v1.5.0）

```目录结构
core/
├── backend/          # 后端服务
├── docs/             # 文档
├── poetry.lock       # Poetry依赖锁定文件
├── pyproject.toml    # Poetry项目配置
├── README.md         # 项目说明
├── scripts/          # 脚本
│   └── setup.ps1     # 安装脚本
├── src/              # 源代码
│   ├── __init__.py   # 包初始化文件
│   ├── main.py       # 主程序入口
│   ├── main_service.py # 主服务实现
│   ├── run.py        # 运行脚本
│   ├── api/          # gRPC接口定义
│   │   ├── device_pb2_grpc.py
│   │   ├── device_pb2.py
│   │   ├── device_service.py
│   │   └── device.proto
│   ├── config/       # 配置文件
│   │   ├── frida_config.yaml
│   │   └── ldplayer_config.yaml
│   ├── devices/      # 设备控制模块
│   │   ├── __init__.py
│   │   ├── base.py   # 设备抽象接口
│   │   ├── android/  # Android设备实现
│   │   │   └── adb.py
│   │   ├── ios/      # iOS设备实现
│   │   │   └── xctest.py
│   │   └── ldplayer/ # 雷电模拟器实现
│   │       ├── __init__.py
│   │       ├── controller.py
│   │       ├── manager.py
│   │       ├── rules.md
│   │       ├── test_controller.py
│   │       └── types.py
│   └── services/     # 服务模块
│       ├── __init__.py
│       ├── appium/   # Appium服务
│       │   ├── driver.py
│       │   └── service.py
│       ├── consul.py # Consul服务
│       ├── device_factory.py
│       ├── device_sync.py
│       └── mcp/      # MCP服务
│           ├── controller.py
│           └── server.py
├── venv/             # 虚拟环境目录
└── __pycache__/      # Python缓存目录
```

## 管理指南

### 依赖管理

Core模块使用Poetry进行依赖管理。

#### 安装依赖

```bash
cd core && 
.\venv\Scripts\activate &&
poetry install
```

#### 更新依赖

```bash
poetry update
```

### 运行Core模块

```bash
poetry run start
```

### 开发流程

1. 创建新功能分支
2. 修改代码
3. 运行测试
4. 提交更改
5. 创建Pull Request
