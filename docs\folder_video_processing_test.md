# 文件夹视频处理功能测试指南

## 🎯 测试目标

验证当用户选择包含多个视频文件的文件夹时，系统能够：
1. 自动扫描文件夹下的所有视频文件
2. 为每个视频文件创建独立的子任务
3. 正确设置主任务和子任务的关系

## 📋 测试准备

### 1. 准备测试文件夹
创建一个包含多个视频文件的测试文件夹：
```
H:\TestVideos\
├── video1.mp4
├── video2.avi
├── video3.mov
├── subfolder\
│   ├── video4.mp4
│   └── video5.mkv
└── other_file.txt (非视频文件，应被忽略)
```

### 2. 确保服务运行
- Backend服务：`python run_server.py`
- Frontend服务：`npm run dev`
- MongoDB数据库正常运行

## 🔧 测试步骤

### 步骤1：创建任务
1. 打开前端页面：`http://localhost:5173`
2. 进入：社交媒体 → 发布管理
3. 选择平台：YouTube
4. 选择Core服务
5. 选择发布账号
6. 选择内容路径：`H:\TestVideos\`
7. 点击"创建任务"

### 步骤2：验证任务创建
检查以下内容：

#### 后端日志应显示：
```
自动扫描文件夹 H:\TestVideos\，找到 5 个视频文件
检测到多个文件，将拆分为子任务: 5 个文件
创建主任务: [main-task-id]
创建子任务 1/5: [subtask-id-1], 文件: video1.mp4
创建子任务 2/5: [subtask-id-2], 文件: video2.avi
创建子任务 3/5: [subtask-id-3], 文件: video3.mov
创建子任务 4/5: [subtask-id-4], 文件: subfolder\video4.mp4
创建子任务 5/5: [subtask-id-5], 文件: subfolder\video5.mkv
```

#### 前端响应应包含：
```json
{
  "task_id": "main-task-uuid",
  "status": "pending",
  "task_type": "main",
  "subtask_ids": ["subtask-1", "subtask-2", "subtask-3", "subtask-4", "subtask-5"],
  "total_subtasks": 5
}
```

### 步骤3：验证数据库记录

#### 主任务记录：
```javascript
db.social_tasks.findOne({"task_type": "main"})
{
  "task_id": "main-task-uuid",
  "task_type": "main",
  "total_subtasks": 5,
  "completed_subtasks": 0,
  "selected_files": [
    "video1.mp4",
    "video2.avi", 
    "video3.mov",
    "subfolder\\video4.mp4",
    "subfolder\\video5.mkv"
  ],
  "content_path": "H:\\TestVideos\\",
  "status": "pending"
}
```

#### 子任务记录：
```javascript
db.social_tasks.find({"task_type": "subtask"}).sort({"subtask_index": 1})
[
  {
    "task_id": "subtask-1-uuid",
    "parent_task_id": "main-task-uuid",
    "task_type": "subtask",
    "subtask_index": 1,
    "content_path": "H:\\TestVideos\\video1.mp4",
    "video_file": "video1.mp4",
    "folder_path": "H:\\TestVideos\\",
    "status": "pending"
  },
  // ... 其他4个子任务
]
```

## 🔍 测试验证点

### 1. 文件扫描验证
- [ ] 系统正确识别了所有视频文件（5个）
- [ ] 忽略了非视频文件（other_file.txt）
- [ ] 正确处理了子文件夹中的视频文件
- [ ] 文件路径使用了正确的相对路径格式

### 2. 任务创建验证
- [ ] 创建了1个主任务
- [ ] 创建了5个子任务
- [ ] 主任务包含正确的子任务数量
- [ ] 子任务正确关联到主任务

### 3. 路径处理验证
- [ ] 主任务的content_path是文件夹路径
- [ ] 子任务的content_path是完整的文件路径
- [ ] 子任务保留了folder_path字段
- [ ] 相对路径和绝对路径处理正确

### 4. 数据结构验证
- [ ] 主任务包含selected_files数组
- [ ] 子任务包含video_file字段
- [ ] 子任务有正确的subtask_index
- [ ] 所有任务状态初始为pending

## 🐛 常见问题排查

### 问题1：没有创建子任务
**症状**：只创建了单个任务，没有子任务
**可能原因**：
- 文件夹扫描失败
- selected_files为空
- 文件夹路径不存在

**排查方法**：
1. 检查后端日志中的扫描结果
2. 验证文件夹路径是否正确
3. 确认文件夹中确实有视频文件

### 问题2：文件路径错误
**症状**：子任务的content_path不正确
**可能原因**：
- 相对路径和绝对路径处理错误
- 路径分隔符问题（Windows vs Linux）

**排查方法**：
1. 检查数据库中的content_path字段
2. 验证文件是否真实存在
3. 检查路径分隔符是否正确

### 问题3：扫描不到子文件夹中的文件
**症状**：只扫描到根目录的视频文件
**可能原因**：
- os.walk递归扫描失败
- 权限问题

**排查方法**：
1. 手动测试os.walk函数
2. 检查文件夹权限
3. 查看详细的错误日志

## 📊 性能测试

### 大文件夹测试
测试包含大量视频文件的文件夹：
- 100个视频文件
- 1000个视频文件
- 深层嵌套的文件夹结构

### 预期结果
- 扫描时间应该在合理范围内（<10秒）
- 内存使用稳定
- 数据库操作正常

## ✅ 测试通过标准

### 基本功能
- [x] 能够扫描文件夹中的所有视频文件
- [x] 正确创建主任务和子任务
- [x] 数据库记录结构正确
- [x] 前端显示正确的任务信息

### 边界情况
- [x] 空文件夹处理
- [x] 只有一个视频文件的文件夹
- [x] 包含非视频文件的文件夹
- [x] 深层嵌套的文件夹结构

### 错误处理
- [x] 文件夹不存在的情况
- [x] 权限不足的情况
- [x] 文件夹路径格式错误

## 🎯 测试结论

完成所有测试后，应该能够确认：
1. 文件夹视频处理功能正常工作
2. 子任务拆分逻辑正确
3. 数据结构设计合理
4. 错误处理机制完善

如果测试通过，用户就可以通过选择文件夹的方式批量上传多个视频文件了。
