<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加对标账号"
    width="600px"
    @close="handleClose"
  >
    <div class="add-benchmark-dialog">
      <!-- 我们的账号信息 -->
      <el-card class="our-account-card" style="margin-bottom: 20px;">
        <template #header>
          <span>👤 为以下账号添加对标账号</span>
        </template>
        
        <div v-if="ourAccount" class="our-account-info">
          <div class="account-display">
            <span class="account-name">{{ ourAccount.display_name || ourAccount.username }}</span>
            <el-tag type="primary" style="margin-left: 8px;">{{ ourAccount.platform_name }}</el-tag>
            <el-tag type="success" style="margin-left: 4px;">{{ ourAccount.core_service_name }}</el-tag>
          </div>
        </div>
      </el-card>

      <!-- 对标账号表单 -->
      <el-form :model="benchmarkForm" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="账号名称" prop="account_name">
          <el-input
            v-model="benchmarkForm.account_name"
            placeholder="请输入对标账号名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="账号链接" prop="account_url">
          <el-input
            v-model="benchmarkForm.account_url"
            placeholder="请输入对标账号链接"
            clearable
          />
        </el-form-item>

        <el-form-item label="平台" prop="platform">
          <el-select v-model="benchmarkForm.platform" placeholder="请选择平台" style="width: 100%">
            <el-option label="YouTube" value="youtube" />
            <el-option label="TikTok" value="tiktok" />
            <el-option label="Instagram" value="instagram" />
            <el-option label="抖音" value="douyin" />
            <el-option label="Facebook" value="facebook" />
            <el-option label="Twitter" value="twitter" />
          </el-select>
        </el-form-item>

        <el-form-item label="对标类型" prop="benchmark_type">
          <el-radio-group v-model="benchmarkForm.benchmark_type">
            <el-radio label="original">原创</el-radio>
            <el-radio label="recreate">二创</el-radio>
            <el-radio label="repost">搬运</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-rate
            v-model="benchmarkForm.priority"
            :max="5"
            show-text
            :texts="['很低', '较低', '一般', '较高', '很高']"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="benchmarkForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入对标账号描述（可选）"
          />
        </el-form-item>

        <el-form-item label="标签">
          <el-select
            v-model="benchmarkForm.tags"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in commonTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          添加对标账号
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
  ourAccount: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'added': []
}>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const benchmarkForm = reactive({
  account_name: '',
  account_url: '',
  platform: '',
  benchmark_type: 'original',
  priority: 3,
  description: '',
  tags: [] as string[]
})

// 常用标签
const commonTags = [
  '科技',
  '娱乐',
  '教育',
  '生活',
  '美食',
  '旅游',
  '时尚',
  '健康',
  '财经',
  '体育',
  '游戏',
  '音乐',
  '电影',
  '动漫'
]

// 表单验证规则
const formRules: FormRules = {
  account_name: [
    { required: true, message: '请输入账号名称', trigger: 'blur' },
    { min: 2, max: 50, message: '账号名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  account_url: [
    { required: true, message: '请输入账号链接', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  benchmark_type: [
    { required: true, message: '请选择对标类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' },
    { type: 'number', min: 1, max: 5, message: '优先级必须在 1 到 5 之间', trigger: 'change' }
  ]
}

// 监听器
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    resetForm()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 业务方法
const resetForm = () => {
  benchmarkForm.account_name = ''
  benchmarkForm.account_url = ''
  benchmarkForm.platform = ''
  benchmarkForm.benchmark_type = 'original'
  benchmarkForm.priority = 3
  benchmarkForm.description = ''
  benchmarkForm.tags = []
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 构建提交数据
    const submitData = {
      our_account_id: props.ourAccount?.id,
      account_name: benchmarkForm.account_name,
      account_url: benchmarkForm.account_url,
      platform: benchmarkForm.platform,
      benchmark_type: benchmarkForm.benchmark_type,
      priority: benchmarkForm.priority,
      description: benchmarkForm.description || undefined,
      tags: benchmarkForm.tags.length > 0 ? benchmarkForm.tags : undefined
    }
    
    // TODO: 调用API添加对标账号
    console.log('提交对标账号数据:', submitData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('对标账号添加成功')
    emit('added')
    handleClose()
    
  } catch (error) {
    console.error('添加对标账号失败:', error)
    ElMessage.error('添加对标账号失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.add-benchmark-dialog {
  max-height: 500px;
  overflow-y: auto;
}

.our-account-card {
  border: 1px solid #e4e7ed;
}

.our-account-info {
  padding: 10px 0;
}

.account-display {
  display: flex;
  align-items: center;
}

.account-name {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-rate__text) {
  color: #606266;
}
</style>
