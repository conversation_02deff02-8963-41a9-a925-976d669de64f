"""
下载器工厂
根据平台创建对应的下载器
"""

import logging
from typing import Dict, Type, Optional

from .base_downloader import BaseDownloader
from .douyin_downloader import DouyinDownloader
from .douyin_playwright_downloader import DouyinPlaywrightDownloader
from .youtube_downloader import YouTubeDownloader

logger = logging.getLogger(__name__)


class DownloaderFactory:
    """下载器工厂类"""
    
    # 注册的下载器类
    _downloaders: Dict[str, Type[BaseDownloader]] = {
        'douyin': DouyinPlaywrightDownloader,  # 🔧 使用Playwright下载器
        'tiktok': DouyinPlaywrightDownloader,  # TikTok使用相同的下载器
        'youtube': YouTubeDownloader,  # YouTube下载器
        # 'instagram': InstagramDownloader,  # TODO: 实现Instagram下载器

        # 备用：API下载器（如果Playwright不可用）
        'douyin_api': DouyinDownloader,
    }
    
    @classmethod
    def create_downloader(
        cls,
        platform: str,
        download_path: str,
        debug_mode: bool = False,  # 🔧 添加调试模式参数
        task_id: str = None  # 🔧 添加任务ID参数
    ) -> Optional[BaseDownloader]:
        """创建下载器实例

        Args:
            platform: 平台名称
            download_path: 下载路径
            debug_mode: 是否启用调试模式
            task_id: 任务ID

        Returns:
            BaseDownloader: 下载器实例，如果平台不支持则返回None
        """
        try:
            platform_lower = platform.lower()

            if platform_lower not in cls._downloaders:
                logger.error(f"不支持的平台: {platform}")
                return None

            downloader_class = cls._downloaders[platform_lower]

            # 🔧 根据下载器类型传递不同的参数
            if downloader_class.__name__ == 'DouyinPlaywrightDownloader':
                downloader = downloader_class(download_path, task_id=task_id, debug_mode=debug_mode)
            else:
                downloader = downloader_class(download_path)

            logger.info(f"创建{platform}下载器成功: {downloader_class.__name__} (调试模式: {debug_mode})")
            return downloader
            
        except Exception as e:
            logger.error(f"创建下载器失败: {str(e)}")
            return None
    
    @classmethod
    def register_downloader(cls, platform: str, downloader_class: Type[BaseDownloader]):
        """注册新的下载器
        
        Args:
            platform: 平台名称
            downloader_class: 下载器类
        """
        cls._downloaders[platform.lower()] = downloader_class
        logger.info(f"注册下载器: {platform} -> {downloader_class.__name__}")
    
    @classmethod
    def get_supported_platforms(cls) -> list:
        """获取支持的平台列表
        
        Returns:
            list: 支持的平台列表
        """
        return list(cls._downloaders.keys())
    
    @classmethod
    def is_platform_supported(cls, platform: str) -> bool:
        """检查平台是否支持
        
        Args:
            platform: 平台名称
            
        Returns:
            bool: 是否支持
        """
        return platform.lower() in cls._downloaders
