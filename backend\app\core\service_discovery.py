"""
服务发现工具模块
用于从 Consul 发现和连接到 Core 服务
"""

import os
import logging
import consul
from typing import Optional, Tuple
from app.core.client import CoreClient

logger = logging.getLogger(__name__)

class ServiceDiscovery:
    """服务发现工具类"""
    
    def __init__(self):
        self.consul_client = None
        self._init_consul()
    
    def _init_consul(self):
        """初始化 Consul 客户端"""
        try:
            consul_url = os.getenv('DB_CONSUL_URL', 'http://localhost:8500')
            consul_host = consul_url.replace('http://', '').replace('https://', '').split(':')[0]
            consul_port = int(consul_url.split(':')[-1])
            
            self.consul_client = consul.Consul(host=consul_host, port=consul_port)
            logger.info(f"Consul 客户端初始化成功: {consul_host}:{consul_port}")
        except Exception as e:
            logger.error(f"初始化 Consul 客户端失败: {str(e)}")
            self.consul_client = None
    
    def discover_core_service(self) -> Optional[Tuple[str, int]]:
        """
        从 Consul 发现 Core 服务
        
        Returns:
            Optional[Tuple[str, int]]: (host, port) 或 None
        """
        if not self.consul_client:
            logger.warning("Consul 客户端未初始化")
            return None
        
        try:
            # 查询 Consul 中的 Core 服务
            services = self.consul_client.health.service('thunderhub-core', passing=True)[1]
            
            if services:
                # 使用第一个健康的 Core 服务
                service = services[0]
                core_host = service['Service']['Address'] or service['Node']['Address']
                core_port = service['Service']['Port']
                logger.info(f"从 Consul 发现 Core 服务: {core_host}:{core_port}")
                return (core_host, core_port)
            else:
                logger.warning("Consul 中未找到健康的 Core 服务")
                return None
        except Exception as e:
            logger.error(f"从 Consul 发现 Core 服务失败: {str(e)}")
            return None
    
    def get_core_client(self) -> CoreClient:
        """
        获取 Core 服务客户端
        
        Returns:
            CoreClient: Core 服务客户端实例
        """
        # 尝试从 Consul 发现服务
        service_info = self.discover_core_service()
        
        if service_info:
            host, port = service_info
            logger.info(f"使用发现的 Core 服务: {host}:{port}")
            return CoreClient(host=host, port=port)
        else:
            logger.warning("未能从 Consul 发现 Core 服务，使用默认地址")
            return CoreClient()

# 全局服务发现实例
_service_discovery = None

def get_service_discovery() -> ServiceDiscovery:
    """获取全局服务发现实例"""
    global _service_discovery
    if _service_discovery is None:
        _service_discovery = ServiceDiscovery()
    return _service_discovery

def get_core_client() -> CoreClient:
    """
    便捷函数：获取 Core 服务客户端
    
    Returns:
        CoreClient: Core 服务客户端实例
    """
    return get_service_discovery().get_core_client()
