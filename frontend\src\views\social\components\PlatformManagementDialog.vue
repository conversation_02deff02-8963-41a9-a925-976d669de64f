<template>
  <el-dialog
    v-model="dialogVisible"
    title="平台管理"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="platform-management">
      <!-- 平台列表 -->
      <div class="platform-list">
        <div class="list-header">
          <h3>平台列表</h3>
          <el-button type="primary" size="small" @click="handleAddPlatform">
            <el-icon><Plus /></el-icon> 添加平台
          </el-button>
        </div>

        <el-table
          v-loading="loading"
          :data="platforms"
          style="width: 100%"
          max-height="400px"
          @row-click="handleSelectPlatform"
        >
          <el-table-column label="图标" width="80">
            <template #default="scope">
              <div class="platform-icon-container">
                <img
                  v-if="scope.row.icon"
                  :src="getPlatformIconUrl(scope.row.icon)"
                  class="platform-icon"
                  alt="平台图标"
                />
                <el-icon v-else><Link /></el-icon>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="名称" min-width="120" />

          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button size="small" @click.stop="handleEditPlatform(scope.row)">编辑</el-button>
              <el-button
                size="small"
                type="danger"
                @click.stop="handleDeletePlatform(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 平台详情 -->
      <div v-if="selectedPlatform" class="platform-details">
        <h3>平台详情</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="ID">{{ selectedPlatform.id }}</el-descriptions-item>
          <el-descriptions-item label="名称">{{ selectedPlatform.name }}</el-descriptions-item>
          <el-descriptions-item label="网站">
            <a :href="selectedPlatform.website" target="_blank">{{ selectedPlatform.website }}</a>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedPlatform.status)">
              {{ getStatusText(selectedPlatform.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="功能">
            <div class="features-list">
              <el-tag
                v-for="feature in selectedPlatform.features"
                :key="feature"
                size="small"
                class="feature-tag"
              >
                {{ feature }}
              </el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="应用信息" v-if="selectedPlatform.app_info">
            <div class="app-info">
              <p v-if="selectedPlatform.app_info.android_package">
                Android包名: {{ selectedPlatform.app_info.android_package }}
              </p>
              <p v-if="selectedPlatform.app_info.ios_bundle_id">
                iOS Bundle ID: {{ selectedPlatform.app_info.ios_bundle_id }}
              </p>
              <p>
                Appium支持:
                <el-tag :type="selectedPlatform.app_info.appium_support ? 'success' : 'danger'">
                  {{ selectedPlatform.app_info.appium_support ? '是' : '否' }}
                </el-tag>
              </p>
              <p>
                ADB支持:
                <el-tag :type="selectedPlatform.app_info.adb_support ? 'success' : 'danger'">
                  {{ selectedPlatform.app_info.adb_support ? '是' : '否' }}
                </el-tag>
              </p>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <!-- 平台表单对话框 -->
    <el-dialog
      v-model="platformFormDialog.visible"
      :title="platformFormDialog.isEdit ? '编辑平台' : '添加平台'"
      width="600px"
      append-to-body
    >
      <el-form
        ref="platformFormRef"
        :model="platformFormDialog.form"
        :rules="platformFormRules"
        label-width="100px"
      >
        <el-form-item label="ID" prop="id">
          <el-input v-model="platformFormDialog.form.id" :disabled="platformFormDialog.isEdit" />
        </el-form-item>

        <el-form-item label="名称" prop="name">
          <el-input v-model="platformFormDialog.form.name" />
        </el-form-item>

        <el-form-item label="图标" prop="icon">
          <div class="icon-upload-container">
            <el-upload
              class="icon-uploader"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleIconChange"
              accept="image/jpeg,image/png,image/gif,image/svg+xml"
            >
              <img v-if="iconPreview" :src="iconPreview" class="icon-preview" />
              <el-icon v-else class="icon-uploader-icon"><Plus /></el-icon>
            </el-upload>
            <div class="icon-input">
              <el-input v-model="platformFormDialog.form.icon" placeholder="图标路径" />
              <el-tooltip content="图标路径可以是上传后的相对路径，也可以是图标名称">
                <el-icon class="icon-help"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="网站" prop="website">
          <el-input v-model="platformFormDialog.form.website" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select v-model="platformFormDialog.form.status" style="width: 100%">
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
          </el-select>
        </el-form-item>

        <el-form-item label="功能" prop="features">
          <el-select
            v-model="platformFormDialog.form.features"
            multiple
            filterable
            allow-create
            default-first-option
            style="width: 100%"
          >
            <el-option label="发布" value="posts" />
            <el-option label="评论" value="comments" />
            <el-option label="点赞" value="likes" />
            <el-option label="分享" value="shares" />
            <el-option label="上传" value="uploads" />
          </el-select>
        </el-form-item>

        <el-form-item label="应用信息">
          <div class="app-info-form">
            <div class="app-info-row">
              <span class="app-info-label">Android包名:</span>
              <el-input v-model="platformFormDialog.form.app_info.android_package" />
            </div>

            <div class="app-info-row">
              <span class="app-info-label">iOS Bundle ID:</span>
              <el-input v-model="platformFormDialog.form.app_info.ios_bundle_id" />
            </div>

            <div class="app-info-row">
              <span class="app-info-label">Appium支持:</span>
              <el-switch v-model="platformFormDialog.form.app_info.appium_support" />
            </div>

            <div class="app-info-row">
              <span class="app-info-label">ADB支持:</span>
              <el-switch v-model="platformFormDialog.form.app_info.adb_support" />
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="platformFormDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleSavePlatform">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Link, QuestionFilled } from '@element-plus/icons-vue'
import type { SocialPlatform } from '@/types/social'
import { getPlatforms, createPlatform, updatePlatform, deletePlatform, uploadPlatformIcon } from '@/api/social'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 默认平台数据
const defaultPlatforms: SocialPlatform[] = [
  {
    id: 'youtube',
    name: 'YouTube',
    icon: 'youtube',
    website: 'https://www.youtube.com',
    status: 'active',
    features: ['uploads', 'comments', 'likes'],
    app_info: {
      android_package: 'com.google.android.youtube',
      ios_bundle_id: 'com.google.ios.youtube',
      appium_support: true,
      adb_support: true
    }
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: 'facebook',
    website: 'https://www.facebook.com',
    status: 'active',
    features: ['posts', 'comments', 'likes', 'shares'],
    app_info: {
      android_package: 'com.facebook.katana',
      ios_bundle_id: 'com.facebook.Facebook',
      appium_support: true,
      adb_support: true
    }
  },
  {
    id: 'aws',
    name: 'Amazon Web Services',
    icon: 'aws',
    website: 'https://aws.amazon.com',
    status: 'active',
    features: ['s3', 'ec2', 'lambda', 'cloudfront'],
    app_info: {
      android_package: 'com.amazon.aws.console.mobile',
      ios_bundle_id: 'com.amazon.aws.console.mobile',
      appium_support: false,
      adb_support: false
    }
  }
]

// 状态
const loading = ref(false)
const platforms = ref<SocialPlatform[]>([])
const selectedPlatform = ref<SocialPlatform | null>(null)
const iconPreview = ref<string>('')

// 定义表单类型
interface PlatformForm {
  id: string;
  name: string;
  icon: string;
  website: string;
  status: string;
  features: string[];
  app_info: {
    android_package: string;
    ios_bundle_id: string;
    appium_support: boolean;
    adb_support: boolean;
  };
}

// 平台表单对话框
const platformFormDialog = reactive({
  visible: false,
  isEdit: false,
  form: {
    id: '',
    name: '',
    icon: '',
    website: '',
    status: 'active',
    features: [] as string[],
    app_info: {
      android_package: '',
      ios_bundle_id: '',
      appium_support: false,
      adb_support: false
    }
  } as PlatformForm
})

// 表单验证规则
const platformFormRules = {
  id: [
    { required: true, message: '请输入平台ID', trigger: 'blur' },
    { pattern: /^[a-z0-9_-]+$/, message: 'ID只能包含小写字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入平台名称', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择平台状态', trigger: 'change' }
  ]
}

// 监听对话框可见性
watch(() => dialogVisible.value, (val) => {
  if (val) {
    fetchPlatforms()
  }
})

// 获取平台列表
const fetchPlatforms = async () => {
  loading.value = true
  try {
    console.log('开始获取平台列表...')
    const response = await getPlatforms()
    console.log('平台API响应:', response)

    // 处理Axios响应对象
    console.log('处理前的响应类型:', typeof response)

    // 使用any类型避免类型错误
    const result: any = response.data || response
    console.log('处理后的平台数据:', result)

    // 确保result是数组
    if (Array.isArray(result) && result.length > 0) {
      platforms.value = result
      console.log('设置平台列表成功，数量:', platforms.value.length)
    } else if (result && typeof result === 'object' && 'data' in result && Array.isArray(result.data) && result.data.length > 0) {
      // 如果result是包含data数组的对象
      platforms.value = result.data
      console.log('从result.data设置平台列表成功，数量:', platforms.value.length)
    } else {
      // 如果无法获取有效的平台数据，使用默认平台数据
      console.warn('无法获取有效的平台数据，使用默认平台数据')
      platforms.value = defaultPlatforms
      console.log('使用默认平台数据，数量:', platforms.value.length)
    }

    // 如果有选中的平台，更新它的信息
    if (selectedPlatform.value) {
      const updated = platforms.value.find(p => p.id === selectedPlatform.value?.id)
      if (updated) {
        selectedPlatform.value = updated
        console.log('更新选中的平台:', selectedPlatform.value)
      }
    }
  } catch (error) {
    console.error('获取平台列表失败:', error)
    ElMessage.error('获取平台列表失败')
  } finally {
    loading.value = false
  }
}

// 获取状态类型
const getStatusType = (status?: string) => {
  switch (status) {
    case 'active': return 'success'
    case 'inactive': return 'info'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status?: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'inactive': return '非活跃'
    default: return status || '未知'
  }
}

// 选择平台
const handleSelectPlatform = (row: SocialPlatform) => {
  selectedPlatform.value = row
}

// 添加平台
const handleAddPlatform = () => {
  platformFormDialog.isEdit = false
  platformFormDialog.form = {
    id: '',
    name: '',
    icon: '',
    website: '',
    status: 'active',
    features: [],
    app_info: {
      android_package: '',
      ios_bundle_id: '',
      appium_support: false,
      adb_support: false
    }
  }
  // 清空图标预览
  iconPreview.value = ''
  platformFormDialog.visible = true
}

// 编辑平台
const handleEditPlatform = (platform: SocialPlatform) => {
  platformFormDialog.isEdit = true

  // 确保app_info对象有所有必要的字段
  const appInfo = platform.app_info || {}

  // 创建一个符合PlatformForm类型的对象
  platformFormDialog.form = {
    id: platform.id || '',
    name: platform.name || '',
    icon: platform.icon || '',
    website: platform.website || '',
    status: platform.status || 'active',
    features: platform.features || [],
    app_info: {
      android_package: appInfo.android_package || '',
      ios_bundle_id: appInfo.ios_bundle_id || '',
      appium_support: appInfo.appium_support || false,
      adb_support: appInfo.adb_support || false
    }
  }

  // 设置图标预览
  updateIconPreview(platform.icon)

  platformFormDialog.visible = true
}

// 获取平台图标URL
const getPlatformIconUrl = (icon: string): string => {
  if (!icon) {
    return ''
  }

  // 如果图标是完整URL（包含http或https）
  if (icon.startsWith('http://') || icon.startsWith('https://')) {
    return icon
  }
  // 如果图标路径已经以/开头（标准URL路径）
  else if (icon.startsWith('/')) {
    // 确保不会出现双重/icons/前缀
    if (icon.includes('/icons/') && icon.startsWith('/icons/')) {
      return icon
    } else if (icon.includes('/icons/')) {
      // 如果包含/icons/但不是以它开头，提取文件名
      const parts = icon.split('/icons/')
      return `/icons/${parts[parts.length - 1]}`
    } else {
      return icon
    }
  }
  // 如果图标路径包含icons/前缀但不是以/开头
  else if (icon.includes('icons/')) {
    // 提取文件名
    const parts = icon.split('icons/')
    return `/icons/${parts[parts.length - 1]}`
  }
  // 如果图标只是名称，拼接路径（兼容旧数据）
  else {
    return `/icons/${icon}`
  }
}

// 更新图标预览
const updateIconPreview = (icon?: string) => {
  if (!icon) {
    iconPreview.value = ''
    return
  }

  const url = getPlatformIconUrl(icon)
  console.log('图标预览URL:', { 原始路径: icon, 处理后URL: url })
  iconPreview.value = url
}

// 处理图标选择
const handleIconChange = async (file: any) => {
  // 检查文件类型
  const isImage = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'].includes(file.raw.type)
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return
  }

  // 检查文件大小（限制为2MB）
  const isLt2M = file.raw.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB!')
    return
  }

  // 显示本地预览
  const reader = new FileReader()
  reader.onload = (e) => {
    iconPreview.value = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)

  try {
    // 上传图标，如果是编辑模式，传递平台ID
    ElMessage.info('正在上传图标...')
    const platformId = platformFormDialog.isEdit ? platformFormDialog.form.id : undefined

    // 打印详细的平台信息，帮助调试
    if (platformFormDialog.isEdit) {
      console.log('编辑模式，平台详情:', {
        id: platformFormDialog.form.id,
        name: platformFormDialog.form.name,
        当前图标: platformFormDialog.form.icon
      })
    }

    console.log('上传图标，平台ID:', platformId)
    const response = await uploadPlatformIcon(file.raw, platformId)
    console.log('图标上传响应:', response)

    // 处理响应
    const result = response.data || response
    let iconPath = result.path || result.data?.path || ''

    if (iconPath) {
      // 确保图标路径是完整的URL路径（以/开头）
      if (!iconPath.startsWith('/')) {
        iconPath = `/${iconPath}`
      }

      // 如果是编辑模式，后端已经自动更新了平台的图标路径
      // 我们只需要更新本地表单数据，以便预览
      if (platformFormDialog.isEdit) {
        platformFormDialog.form.icon = iconPath
        console.log('编辑模式：后端已自动更新平台图标路径，本地表单已更新:', iconPath)
      } else {
        // 如果是添加模式，设置表单的图标路径
        platformFormDialog.form.icon = iconPath
        console.log('添加模式：设置图标路径:', iconPath)
      }

      ElMessage.success('图标上传成功')

      // 如果是编辑模式，刷新平台列表以获取更新后的数据
      if (platformFormDialog.isEdit) {
        fetchPlatforms()
      }
    } else {
      ElMessage.warning('图标已预览，但上传可能失败，请手动输入图标路径')
    }
  } catch (error) {
    console.error('图标上传失败:', error)
    ElMessage.warning('图标已预览，但上传失败，请手动输入图标路径')
  }
}

// 删除平台
const handleDeletePlatform = async (id: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个平台吗？这将同时删除与该平台关联的所有账号。',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deletePlatform(id)
    ElMessage.success('平台删除成功')

    // 刷新平台列表
    fetchPlatforms()

    // 如果删除的是当前选中的平台，清除选中
    if (selectedPlatform.value?.id === id) {
      selectedPlatform.value = null
    }

    // 通知父组件刷新
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除平台失败:', error)
      ElMessage.error('删除平台失败')
    }
  }
}

// 保存平台
const handleSavePlatform = async () => {
  try {
    // 处理图标路径，确保是完整的URL路径
    let iconPath = platformFormDialog.form.icon || undefined
    if (iconPath && !iconPath.startsWith('/') && !iconPath.startsWith('http')) {
      iconPath = `/icons/${iconPath}`
      console.log('已将图标路径转换为完整URL路径:', iconPath)
    }

    // 创建一个符合SocialPlatform类型的对象
    const platformData: Partial<SocialPlatform> = {
      id: platformFormDialog.form.id,
      name: platformFormDialog.form.name,
      icon: iconPath,
      website: platformFormDialog.form.website || undefined,
      status: (platformFormDialog.form.status as 'active' | 'inactive' | 'maintenance') || 'active',
      features: platformFormDialog.form.features || [],
      app_info: platformFormDialog.form.app_info
    }

    if (platformFormDialog.isEdit) {
      // 更新平台
      await updatePlatform(platformData.id!, platformData)
      ElMessage.success('平台更新成功')
    } else {
      // 创建平台
      await createPlatform(platformData as Omit<SocialPlatform, '_id'>)
      ElMessage.success('平台创建成功')
    }

    // 关闭表单对话框
    platformFormDialog.visible = false

    // 刷新平台列表
    fetchPlatforms()

    // 通知父组件刷新
    emit('refresh')
  } catch (error) {
    console.error('保存平台失败:', error)
    ElMessage.error('保存平台失败')
  }
}

// 初始化平台数据
const initializePlatforms = async () => {
  try {
    // 先尝试获取平台列表
    await fetchPlatforms()

    // 如果平台列表为空，尝试创建默认平台
    if (platforms.value.length === 0) {
      console.log('平台列表为空，尝试创建默认平台')

      // 逐个创建默认平台
      for (const platform of defaultPlatforms) {
        try {
          console.log(`尝试创建平台: ${platform.name}`)
          await createPlatform(platform as Omit<SocialPlatform, '_id'>)
          console.log(`平台 ${platform.name} 创建成功`)
        } catch (error) {
          console.error(`创建平台 ${platform.name} 失败:`, error)
        }
      }

      // 重新获取平台列表
      await fetchPlatforms()
    }
  } catch (error) {
    console.error('初始化平台数据失败:', error)
    // 如果API调用失败，直接使用默认平台数据
    platforms.value = defaultPlatforms
  }
}

// 组件挂载时初始化
onMounted(() => {
  console.log('平台管理组件已挂载')
  // 初始化平台数据
  initializePlatforms()
})
</script>

<style scoped>
.platform-management {
  display: flex;
  gap: 20px;
}

.platform-list {
  flex: 1;
  min-width: 0;
}

.platform-details {
  flex: 1;
  min-width: 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.platform-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.platform-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.feature-tag {
  margin-right: 5px;
}

.app-info {
  font-size: 14px;
}

.app-info p {
  margin: 5px 0;
}

.app-info-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.app-info-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-info-label {
  min-width: 100px;
  text-align: right;
  color: #606266;
}

.icon-upload-container {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.icon-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: border-color 0.3s;
}

.icon-uploader:hover {
  border-color: #409EFF;
}

.icon-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.icon-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.icon-input {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 5px;
}

.icon-help {
  color: #909399;
  cursor: pointer;
}
</style>
