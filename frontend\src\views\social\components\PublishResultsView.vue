<template>
  <div class="results-view">
    <h2>执行结果</h2>

    <el-row :gutter="20">
      <!-- 结果摘要 -->
      <el-col :span="24">
        <el-card class="summary-card">
          <template #header>
            <div class="card-header">
              <span>结果摘要</span>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value success">{{ summary.success }}</div>
                <div class="summary-label">成功</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value warning">{{ summary.warning }}</div>
                <div class="summary-label">警告</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value error">{{ summary.error }}</div>
                <div class="summary-label">错误</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value">{{ summary.total }}</div>
                <div class="summary-label">总计</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <PublishStatsChart :task-id="taskId" />
      </el-col>
      <el-col :span="12">
        <PlatformPerformanceTable :task-id="taskId" />
      </el-col>
    </el-row>

    <DetailLogsTable :task-id="taskId" />

    <div class="action-buttons">
      <el-button type="primary" @click="exportReport">导出报告</el-button>
      <el-button @click="createNewTask">创建新任务</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getTaskResult } from '@/api/social'
import PublishStatsChart from './PublishStatsChart.vue'
import PlatformPerformanceTable from './PlatformPerformanceTable.vue'
import DetailLogsTable from './DetailLogsTable.vue'

const props = defineProps({
  taskId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['create-new-task'])

// 结果摘要
const summary = reactive({
  success: 0,
  warning: 0,
  error: 0,
  total: 0
})

// 加载状态
const loading = ref(false)

// 初始化
onMounted(async () => {
  await fetchTaskResult()
})

// 获取任务结果
const fetchTaskResult = async () => {
  try {
    loading.value = true
    console.log('获取任务结果:', props.taskId)

    // 调用真实的API获取任务结果
    const response = await getTaskResult(props.taskId)
    console.log('任务结果响应:', response)

    if (response && response.data) {
      const resultData = response.data

      // 更新结果摘要
      if (resultData.summary) {
        Object.assign(summary, {
          success: resultData.summary.success || 0,
          warning: resultData.summary.warning || 0,
          error: resultData.summary.error || 0,
          total: resultData.summary.total || 0
        })
        console.log('更新结果摘要:', summary)
      } else {
        // 如果没有摘要数据，显示默认值
        Object.assign(summary, {
          success: 0,
          warning: 0,
          error: 0,
          total: 0
        })
        console.log('使用默认结果摘要')
      }
    } else {
      console.warn('API响应数据格式异常:', response)
      ElMessage.warning('任务结果数据格式异常')
    }

  } catch (error) {
    console.error('获取任务结果失败:', error)
    ElMessage.error('获取任务结果失败')

    // 错误时显示默认值
    Object.assign(summary, {
      success: 0,
      warning: 0,
      error: 0,
      total: 0
    })
  } finally {
    loading.value = false
  }
}

// 导出报告
const exportReport = async () => {
  try {
    loading.value = true
    // 这里应该调用API导出报告
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('报告导出成功')
  } catch (error) {
    console.error('导出报告失败:', error)
    ElMessage.error('导出报告失败')
  } finally {
    loading.value = false
  }
}

// 创建新任务
const createNewTask = () => {
  emit('create-new-task')
}
</script>

<style scoped>
.results-view {
  padding: 20px;
}

.card-header {
  font-weight: bold;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart-row {
  margin-top: 20px;
}

.summary-card {
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 15px;
}

.summary-value {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 14px;
  color: #909399;
}

.summary-value.success {
  color: #67c23a;
}

.summary-value.warning {
  color: #e6a23c;
}

.summary-value.error {
  color: #f56c6c;
}

.action-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>