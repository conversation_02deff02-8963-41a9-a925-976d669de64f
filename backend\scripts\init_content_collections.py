#!/usr/bin/env python3
"""
初始化文件管理模块的数据库集合和索引
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from pymongo import MongoClient
    from app.config.database import DatabaseConfig
except ImportError as e:
    print(f"导入模块失败: {str(e)}")
    print("请确保已安装所需依赖: pip install pymongo")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ContentCollectionInitializer:
    """文件管理模块数据库初始化器"""
    
    def __init__(self):
        """初始化数据库连接"""
        try:
            self.db_config = DatabaseConfig()
            self.client = MongoClient(self.db_config.mongodb_url, serverSelectionTimeoutMS=5000)
            # 测试连接
            self.client.admin.command('ping')
            self.db = self.client[self.db_config.mongodb_name]
            logger.info(f"MongoDB连接成功: {self.db_config.mongodb_url}/{self.db_config.mongodb_name}")
        except Exception as e:
            logger.error(f"MongoDB连接失败: {str(e)}")
            raise
    
    def create_collections(self):
        """创建集合"""
        logger.info("开始创建文件管理模块集合...")
        
        collections_to_create = [
            "competitor_content",
            "content_categories", 
            "download_tasks"
        ]
        
        existing_collections = self.db.list_collection_names()
        
        for collection_name in collections_to_create:
            if collection_name not in existing_collections:
                self.db.create_collection(collection_name)
                logger.info(f"创建集合: {collection_name}")
            else:
                logger.info(f"集合已存在: {collection_name}")
    
    def create_indexes(self):
        """创建索引"""
        logger.info("开始创建索引...")
        
        # competitor_content 集合索引
        logger.info("创建 competitor_content 集合索引...")
        
        # 基础索引
        self.db.competitor_content.create_index([("platform", 1), ("content_type", 1)])
        self.db.competitor_content.create_index([("metadata.tags", 1)])
        self.db.competitor_content.create_index([("file_info.hash", 1)], unique=True)
        self.db.competitor_content.create_index([("download_info.download_date", -1)])
        self.db.competitor_content.create_index([("metadata.publish_date", -1)])
        self.db.competitor_content.create_index([("created_at", -1)])
        self.db.competitor_content.create_index([("metadata.category", 1)])
        
        # 文本搜索索引
        self.db.competitor_content.create_index([
            ("title", "text"),
            ("description", "text"),
            ("metadata.tags", "text")
        ])
        
        logger.info("competitor_content 索引创建完成")
        
        # content_categories 集合索引
        logger.info("创建 content_categories 集合索引...")
        
        self.db.content_categories.create_index([("parent_id", 1), ("sort_order", 1)])
        self.db.content_categories.create_index([("name", 1)], unique=True)
        self.db.content_categories.create_index([("is_active", 1)])
        
        logger.info("content_categories 索引创建完成")
        
        # download_tasks 集合索引
        logger.info("创建 download_tasks 集合索引...")
        
        self.db.download_tasks.create_index([("status", 1), ("created_at", -1)])
        self.db.download_tasks.create_index([("created_by", 1), ("created_at", -1)])
        self.db.download_tasks.create_index([("task_type", 1)])
        self.db.download_tasks.create_index([("target_platform", 1)])
        
        logger.info("download_tasks 索引创建完成")
        
        logger.info("所有索引创建完成")
    
    def create_default_categories(self):
        """创建默认分类"""
        logger.info("创建默认分类...")
        
        default_categories = [
            {
                "name": "视频内容",
                "description": "视频类型的竞品内容",
                "color": "#FF6B6B",
                "icon": "video",
                "sort_order": 1,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "name": "图片内容", 
                "description": "图片类型的竞品内容",
                "color": "#4ECDC4",
                "icon": "image",
                "sort_order": 2,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "name": "音频内容",
                "description": "音频类型的竞品内容", 
                "color": "#45B7D1",
                "icon": "audio",
                "sort_order": 3,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "name": "文本内容",
                "description": "文本类型的竞品内容",
                "color": "#96CEB4",
                "icon": "text",
                "sort_order": 4,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "name": "热门内容",
                "description": "高互动率的热门内容",
                "color": "#FFEAA7",
                "icon": "fire",
                "sort_order": 5,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        ]
        
        for category in default_categories:
            # 检查分类是否已存在
            existing = self.db.content_categories.find_one({"name": category["name"]})
            if not existing:
                result = self.db.content_categories.insert_one(category)
                logger.info(f"创建默认分类: {category['name']} (ID: {result.inserted_id})")
            else:
                logger.info(f"默认分类已存在: {category['name']}")
        
        logger.info("默认分类创建完成")
    
    def verify_setup(self):
        """验证设置"""
        logger.info("验证数据库设置...")
        
        # 检查集合
        collections = self.db.list_collection_names()
        required_collections = ["competitor_content", "content_categories", "download_tasks"]
        
        for collection in required_collections:
            if collection in collections:
                count = self.db[collection].count_documents({})
                logger.info(f"✓ 集合 {collection} 存在，文档数量: {count}")
            else:
                logger.error(f"✗ 集合 {collection} 不存在")
        
        # 检查索引
        for collection in required_collections:
            if collection in collections:
                indexes = list(self.db[collection].list_indexes())
                logger.info(f"✓ 集合 {collection} 索引数量: {len(indexes)}")
        
        # 检查默认分类
        category_count = self.db.content_categories.count_documents({})
        logger.info(f"✓ 默认分类数量: {category_count}")
        
        logger.info("数据库设置验证完成")
    
    def run(self):
        """运行初始化"""
        try:
            logger.info("开始初始化文件管理模块数据库...")
            
            self.create_collections()
            self.create_indexes()
            self.create_default_categories()
            self.verify_setup()
            
            logger.info("文件管理模块数据库初始化完成！")
            
        except Exception as e:
            logger.error(f"初始化失败: {str(e)}")
            raise
        finally:
            if hasattr(self, 'client'):
                self.client.close()
                logger.info("数据库连接已关闭")


def main():
    """主函数"""
    try:
        initializer = ContentCollectionInitializer()
        initializer.run()
        print("\n✅ 文件管理模块数据库初始化成功！")
        print("\n可以开始使用以下API：")
        print("- 内容管理: /api/v1/content/")
        print("- 下载任务: /api/v1/download/")
        print("- 分类管理: /api/v1/categories/")
        
    except Exception as e:
        print(f"\n❌ 初始化失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
