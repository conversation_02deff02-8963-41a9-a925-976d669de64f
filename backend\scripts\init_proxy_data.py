#!/usr/bin/env python3
"""
代理IP数据初始化脚本
用于创建示例代理IP数据
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config.database import DatabaseConfig
from motor.motor_asyncio import AsyncIOMotorClient
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProxyDataInitializer:
    """代理IP数据初始化器"""
    
    def __init__(self):
        """初始化数据库连接"""
        try:
            self.db_config = DatabaseConfig()
            self.client = AsyncIOMotorClient(self.db_config.mongodb_url)
            self.db = self.client[self.db_config.mongodb_name]
            logger.info(f"MongoDB连接成功: {self.db_config.mongodb_url}/{self.db_config.mongodb_name}")
        except Exception as e:
            logger.error(f"初始化数据库连接失败: {str(e)}")
            raise
    
    async def create_sample_proxy_data(self):
        """创建示例代理IP数据"""
        try:
            # 清空现有数据
            await self.db.proxy_ips.delete_many({})
            await self.db.device_proxy_mappings.delete_many({})
            logger.info("已清空现有代理IP数据")
            
            # 示例代理IP数据
            sample_proxies = [
                # HK代理
                {
                    "region": "HK",
                    "ip_address": "**************",
                    "port": 16881,
                    "proxy_type": "vless",
                    "config_url": "vless://d5d80529-f6d8-4127-b81e-7b02eb032eb8@**************:16881?type=ws&security=none&path=%2F#B-US-1-2-16-KR01-HK",
                    "username": "a0XAHFvWEM",
                    "password": "WNlVbQfrSi",
                    "expire_date": datetime(2026, 3, 15),
                    "payment_card": "兴业 3509",
                    "provider": "B-US-1-2-16-KR01-HK",
                    "status": "active",
                    "notes": "**************:16882:a0XAHFvWEM:WNlVbQfrSi",
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                },
                {
                    "region": "HK",
                    "ip_address": "**************",
                    "port": 16889,
                    "proxy_type": "vless",
                    "config_url": "vless://e015a71e-a2d4-4270-ac16-0f59d9ce9932@**************:16889?type=tcp&security=none#B-HK-1-2-23-002-HK",
                    "username": "Htuhcra0PA",
                    "password": "SefXvAzYpd",
                    "expire_date": datetime(2026, 3, 14),
                    "payment_card": "招商 6017",
                    "provider": "B-HK-1-2-23-002-HK",
                    "status": "active",
                    "notes": "**************:16882:Htuhcra0PA:SefXvAzYpd",
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                },
                # US代理
                {
                    "region": "US",
                    "ip_address": "************",
                    "port": 15537,
                    "proxy_type": "vless",
                    "config_url": "vless://f5926af2-69a9-49f2-e5b0-7b8ebffe1f8a@************:15537?encryption=none&security=none&type=ws&path=%2F",
                    "expire_date": datetime.now() + timedelta(days=365),
                    "status": "active",
                    "notes": "美国代理服务器1",
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                },
                {
                    "region": "US",
                    "ip_address": "***************",
                    "port": 17110,
                    "proxy_type": "vless",
                    "config_url": "vless://ec506c2f-194a-4ed7-d447-07ab352e2bda@***************:17110?type=ws&security=none&path=%2F",
                    "username": "RntuMdKfCL",
                    "password": "z3nBPDGsaD",
                    "expire_date": datetime.now() + timedelta(days=365),
                    "status": "active",
                    "notes": "***************:46114:RntuMdKfCL:z3nBPDGsaD",
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                },
                # 其他地区代理
                {
                    "region": "JP",
                    "ip_address": "************",
                    "port": 8080,
                    "proxy_type": "http",
                    "username": "user123",
                    "password": "pass123",
                    "expire_date": datetime.now() + timedelta(days=30),
                    "payment_card": "工商 1234",
                    "provider": "日本代理服务商",
                    "status": "active",
                    "notes": "日本HTTP代理",
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                },
                {
                    "region": "SG",
                    "ip_address": "*************",
                    "port": 1080,
                    "proxy_type": "socks5",
                    "username": "sguser",
                    "password": "sgpass",
                    "expire_date": datetime.now() + timedelta(days=7),  # 即将到期
                    "payment_card": "建设 5678",
                    "provider": "新加坡代理服务商",
                    "status": "active",
                    "notes": "新加坡SOCKS5代理",
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                },
                {
                    "region": "KR",
                    "ip_address": "************",
                    "port": 3128,
                    "proxy_type": "http",
                    "expire_date": datetime.now() - timedelta(days=5),  # 已过期
                    "payment_card": "农业 9012",
                    "provider": "韩国代理服务商",
                    "status": "expired",
                    "notes": "韩国HTTP代理 - 已过期",
                    "created_at": datetime.now() - timedelta(days=30),
                    "updated_at": datetime.now()
                }
            ]
            
            # 插入代理IP数据
            result = await self.db.proxy_ips.insert_many(sample_proxies)
            logger.info(f"成功创建 {len(result.inserted_ids)} 个代理IP记录")
            
            # 创建一些设备代理关联示例（假设有一些设备ID）
            sample_mappings = [
                {
                    "device_id": "0",  # 假设设备ID为0
                    "proxy_id": str(result.inserted_ids[0]),  # 关联第一个HK代理
                    "status": "active",
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                },
                {
                    "device_id": "1",  # 假设设备ID为1
                    "proxy_id": str(result.inserted_ids[2]),  # 关联第一个US代理
                    "status": "active",
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                }
            ]
            
            # 插入设备代理关联数据
            mapping_result = await self.db.device_proxy_mappings.insert_many(sample_mappings)
            logger.info(f"成功创建 {len(mapping_result.inserted_ids)} 个设备代理关联记录")
            
            logger.info("代理IP示例数据创建完成")
            
        except Exception as e:
            logger.error(f"创建示例数据失败: {str(e)}")
            raise
    
    async def create_indexes(self):
        """创建数据库索引"""
        try:
            # 为代理IP集合创建索引
            await self.db.proxy_ips.create_index("region")
            await self.db.proxy_ips.create_index("status")
            await self.db.proxy_ips.create_index("expire_date")
            await self.db.proxy_ips.create_index([("ip_address", 1), ("port", 1)], unique=True)
            
            # 为设备代理关联集合创建索引
            await self.db.device_proxy_mappings.create_index("device_id")
            await self.db.device_proxy_mappings.create_index("proxy_id")
            await self.db.device_proxy_mappings.create_index([("device_id", 1), ("proxy_id", 1)])
            
            logger.info("数据库索引创建完成")
            
        except Exception as e:
            logger.error(f"创建索引失败: {str(e)}")
            raise
    
    async def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()
            logger.info("数据库连接已关闭")


async def main():
    """主函数"""
    initializer = None
    try:
        logger.info("开始初始化代理IP数据...")
        
        initializer = ProxyDataInitializer()
        
        # 创建示例数据
        await initializer.create_sample_proxy_data()
        
        # 创建索引
        await initializer.create_indexes()
        
        logger.info("代理IP数据初始化完成！")
        
    except Exception as e:
        logger.error(f"初始化失败: {str(e)}")
        sys.exit(1)
    finally:
        if initializer:
            await initializer.close()


if __name__ == "__main__":
    asyncio.run(main())
