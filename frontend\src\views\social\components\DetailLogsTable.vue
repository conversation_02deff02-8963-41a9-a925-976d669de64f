<template>
  <el-card style="margin-top: 20px;">
    <template #header>
      <div class="card-header">
        <span>详细日志</span>
      </div>
    </template>
    <el-table :data="detailLogs" style="width: 100%">
      <el-table-column prop="time" label="时间" width="180" />
      <el-table-column prop="platform" label="平台" width="120" />
      <el-table-column prop="account" label="账号" width="120" />
      <el-table-column prop="content" label="内容" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === '成功' ? 'success' : 'danger'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script lang="ts" setup>
const detailLogs = [
  { 
    time: '2025-04-14 14:30', 
    platform: '微信', 
    account: '账号1',
    content: '测试内容1',
    status: '成功'
  },
  { 
    time: '2025-04-14 14:28', 
    platform: '微博', 
    account: '账号2',
    content: '测试内容2',
    status: '失败'
  }
]
</script>

<style scoped>
.card-header {
  font-weight: bold;
}
</style>