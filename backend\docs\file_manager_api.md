# 文件管理模块API文档

## 概述

文件管理模块提供了完整的竞品内容管理功能，包括内容管理、下载任务管理和分类管理三个主要部分。

## 快速开始

### 1. 初始化数据库

首先运行数据库初始化脚本：

```bash
cd backend
python scripts/init_content_collections.py
```

### 2. 启动服务器

```bash
python run_server.py
```

### 3. 测试API

```bash
python scripts/test_content_apis.py
```

## API接口

### 认证

所有API都需要JWT认证，请先通过 `/auth/token` 获取访问令牌。

```bash
curl -X POST "http://localhost:8000/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=test&password=test123"
```

### 内容管理API

#### 获取内容列表

```http
GET /api/v1/content/list?page=1&limit=20&platform=youtube&search=关键词
```

**参数：**
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20，最大100）
- `platform`: 平台过滤（youtube, tiktok, instagram等）
- `category`: 分类过滤
- `tags`: 标签过滤，逗号分隔
- `search`: 搜索关键词
- `content_type`: 内容类型过滤（video, image, audio, text）

**响应：**
```json
{
  "items": [...],
  "total": 100,
  "page": 1,
  "limit": 20,
  "has_next": true
}
```

#### 获取内容详情

```http
GET /api/v1/content/{content_id}
```

#### 更新内容

```http
PUT /api/v1/content/{content_id}
Content-Type: application/json

{
  "title": "新标题",
  "metadata": {
    "tags": ["标签1", "标签2"],
    "category": "视频内容"
  }
}
```

#### 删除内容

```http
DELETE /api/v1/content/{content_id}
```

#### 批量操作

```http
POST /api/v1/content/batch
Content-Type: application/json

{
  "action": "delete|categorize|tag",
  "content_ids": ["id1", "id2"],
  "data": {
    "category": "新分类",
    "tags": ["标签1", "标签2"]
  }
}
```

#### 获取内容统计

```http
GET /api/v1/content/stats/summary
```

### 下载任务API

#### 创建下载任务

```http
POST /api/v1/download/tasks
Content-Type: application/json

{
  "task_name": "下载任务名称",
  "task_type": "single|batch|channel|playlist",
  "source_urls": ["https://example.com/video1"],
  "target_platform": "youtube",
  "download_config": {
    "quality": "best",
    "format": "mp4",
    "include_subtitles": false,
    "include_thumbnail": true,
    "include_metadata": true
  },
  "filter_config": {
    "min_duration": 60,
    "max_duration": 3600,
    "keywords": ["关键词1"],
    "exclude_keywords": ["排除词"]
  }
}
```

#### 获取任务列表

```http
GET /api/v1/download/tasks?page=1&limit=20&status=pending&task_type=single
```

#### 获取任务详情

```http
GET /api/v1/download/tasks/{task_id}
```

#### 取消任务

```http
POST /api/v1/download/tasks/{task_id}/cancel
```

#### 重试任务

```http
POST /api/v1/download/tasks/{task_id}/retry
```

### 分类管理API

#### 获取分类树

```http
GET /api/v1/categories/tree?include_count=true
```

#### 创建分类

```http
POST /api/v1/categories
Content-Type: application/json

{
  "name": "分类名称",
  "description": "分类描述",
  "parent_id": "父分类ID（可选）",
  "color": "#FF5722",
  "icon": "video",
  "sort_order": 1
}
```

#### 更新分类

```http
PUT /api/v1/categories/{category_id}
Content-Type: application/json

{
  "name": "新名称",
  "description": "新描述",
  "color": "#2196F3"
}
```

#### 删除分类

```http
DELETE /api/v1/categories/{category_id}?force=false
```

#### 移动分类

```http
POST /api/v1/categories/{category_id}/move
Content-Type: application/json

{
  "new_parent_id": "新父分类ID",
  "new_sort_order": 5
}
```

#### 获取分类列表

```http
GET /api/v1/categories?parent_id=父分类ID&include_inactive=false
```

## 数据模型

### 竞品内容 (CompetitorContent)

```json
{
  "_id": "ObjectId",
  "title": "内容标题",
  "description": "内容描述",
  "platform": "youtube",
  "original_url": "https://example.com/video",
  "author": {
    "name": "作者名称",
    "channel_id": "频道ID",
    "avatar_url": "头像URL"
  },
  "content_type": "video",
  "file_info": {
    "local_path": "/path/to/file",
    "file_size": 1024000,
    "file_format": "mp4",
    "duration": 300,
    "resolution": "1920x1080",
    "hash": "文件哈希值"
  },
  "metadata": {
    "tags": ["标签1", "标签2"],
    "category": "视频内容",
    "language": "zh-CN",
    "publish_date": "2025-01-15T10:00:00Z",
    "view_count": 10000,
    "like_count": 500,
    "comment_count": 100
  },
  "download_info": {
    "download_date": "2025-01-15T12:00:00Z",
    "download_source": "yt-dlp",
    "quality": "1080p",
    "status": "downloaded"
  },
  "analysis": {
    "sentiment": "positive",
    "keywords": ["关键词1", "关键词2"],
    "topics": ["主题1", "主题2"],
    "engagement_rate": 0.05
  },
  "created_at": "2025-01-15T12:00:00Z",
  "updated_at": "2025-01-15T12:00:00Z",
  "created_by": "user_id"
}
```

### 内容分类 (ContentCategory)

```json
{
  "_id": "ObjectId",
  "name": "分类名称",
  "description": "分类描述",
  "parent_id": "父分类ObjectId",
  "color": "#FF5722",
  "icon": "video",
  "sort_order": 1,
  "is_active": true,
  "created_at": "2025-01-15T12:00:00Z",
  "updated_at": "2025-01-15T12:00:00Z"
}
```

### 下载任务 (DownloadTask)

```json
{
  "_id": "ObjectId",
  "task_name": "任务名称",
  "task_type": "single",
  "source_urls": ["https://example.com/video"],
  "target_platform": "youtube",
  "download_config": {
    "quality": "best",
    "format": "mp4",
    "include_subtitles": false,
    "include_thumbnail": true,
    "include_metadata": true
  },
  "filter_config": {
    "date_range": {
      "start": "2025-01-01T00:00:00Z",
      "end": "2025-01-31T23:59:59Z"
    },
    "min_duration": 60,
    "max_duration": 3600,
    "keywords": ["关键词"],
    "exclude_keywords": ["排除词"]
  },
  "progress": {
    "total_items": 10,
    "completed_items": 5,
    "failed_items": 1,
    "current_item": "当前处理项目",
    "percentage": 50.0
  },
  "status": "running",
  "result": {
    "success_count": 5,
    "failed_count": 1,
    "total_size": 1024000000,
    "failed_urls": ["失败的URL"]
  },
  "created_at": "2025-01-15T12:00:00Z",
  "updated_at": "2025-01-15T12:00:00Z",
  "created_by": "user_id"
}
```

## 错误处理

API使用标准HTTP状态码：

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

错误响应格式：
```json
{
  "detail": "错误描述信息"
}
```

## 注意事项

1. 所有时间字段使用ISO 8601格式
2. ObjectId字段在API响应中会自动转换为字符串
3. 文件哈希值用于去重检测，确保唯一性
4. 下载任务支持后台异步处理
5. 分类支持层级结构，删除父分类时需要处理子分类
