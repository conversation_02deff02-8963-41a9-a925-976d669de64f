# 文件夹视频处理完整指南

## 🎯 功能概述

当用户选择文件夹进行视频上传时，系统会自动扫描文件夹下的所有视频文件，并为每个视频创建独立的子任务。同时结合自动去重功能，确保不会重复上传相同内容的视频。

## 📍 使用位置

**主要入口**：
```
社交媒体 → 发布管理 → 第一步：选择内容来源
```

## 🔧 工作流程

### 1. 文件夹选择和扫描

```mermaid
graph TD
    A[用户选择文件夹] --> B[系统自动扫描]
    B --> C[识别视频文件]
    C --> D[检查重复状态]
    D --> E[过滤重复文件]
    E --> F[创建子任务]
    F --> G[开始上传]
```

### 2. 支持的视频格式
- **主流格式**：mp4, avi, mov, wmv, flv, mkv
- **其他格式**：webm, m4v, 3gp, f4v
- **扫描方式**：递归扫描子文件夹

### 3. 子任务创建规则
- **单个视频**：创建单个任务
- **多个视频**：创建1个主任务 + N个子任务
- **每个子任务**：处理1个视频文件
- **任务标题**：自动生成（如"我的视频 - 第1部分"）

## 🎨 用户界面操作

### 1. 选择内容路径
1. 点击"选择"按钮打开文件浏览器
2. 选择包含视频的文件夹
3. 系统显示选择的路径

### 2. 预览视频文件
1. 选择文件夹和账号后，点击"🎬 预览视频文件"
2. 查看文件夹中的视频文件列表
3. 查看重复状态统计信息

### 3. 自动去重设置
- **默认行为**：自动过滤重复视频
- **强制上传**：勾选"允许重复上传"忽略去重
- **实时提示**：显示处理说明和去重信息

## 📊 处理结果展示

### 1. 预览对话框内容
```
📁 文件夹：H:\Videos\MyContent

📊 统计信息：
• 总视频文件：15 个
• 新视频文件：12 个
• 重复视频文件：3 个

📋 视频文件列表：
1. video001.mp4 (✨ 新文件)
2. video002.mp4 (🔄 重复)
3. video003.avi (✨ 新文件)
...
```

### 2. 任务创建结果
- **全部过滤**：显示"所有视频都已上传过"
- **部分过滤**：显示"已自动过滤X个重复视频，继续处理其余文件"
- **正常处理**：显示"任务创建成功，将处理X个视频文件"

## 🔄 子任务管理

### 1. 主任务信息
```json
{
  "task_id": "main-task-uuid",
  "task_type": "main",
  "total_subtasks": 12,
  "completed_subtasks": 0,
  "status": "pending"
}
```

### 2. 子任务信息
```json
{
  "task_id": "subtask-uuid",
  "task_type": "subtask", 
  "parent_task_id": "main-task-uuid",
  "subtask_index": 1,
  "video_file": "video001.mp4",
  "metadata": {
    "title": "我的视频 - 第1部分"
  }
}
```

### 3. 任务执行顺序
- 子任务按文件名排序执行
- 每个子任务独立处理
- 支持单独重启失败的子任务
- 主任务状态根据子任务状态更新

## 🛠️ 技术实现

### 后端文件扫描
```python
def scan_video_files(folder_path: str) -> List[str]:
    """扫描文件夹下的所有视频文件"""
    video_extensions = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v', '.3gp', '.f4v'}
    video_files = []
    
    # 递归扫描文件夹
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            _, ext = os.path.splitext(file.lower())
            if ext in video_extensions:
                relative_path = os.path.relpath(os.path.join(root, file), folder_path)
                video_files.append(relative_path)
    
    return sorted(video_files)
```

### API端点
1. **创建任务**：`POST /api/v1/social/youtube/uploads`
2. **预览视频**：`POST /api/v1/social/youtube/preview-folder-videos`
3. **检查重复**：`POST /api/v1/social/youtube/check-duplicates`

## 📋 使用场景示例

### 场景1：批量上传新视频
1. 用户选择包含20个新视频的文件夹
2. 系统扫描并显示20个视频文件
3. 去重检查：0个重复，20个新文件
4. 创建1个主任务 + 20个子任务
5. 按顺序执行上传

### 场景2：混合内容文件夹
1. 用户选择包含各种文件的文件夹
2. 系统自动过滤，只识别视频文件
3. 找到8个视频，其中3个重复
4. 自动过滤3个重复视频
5. 创建1个主任务 + 5个子任务

### 场景3：全部重复的情况
1. 用户选择之前上传过的文件夹
2. 系统检测所有视频都是重复的
3. 显示详细的重复信息
4. 提示用户勾选"允许重复上传"
5. 用户可选择强制上传或取消

### 场景4：嵌套文件夹结构
```
MyVideos/
├── 2024/
│   ├── January/
│   │   ├── video1.mp4
│   │   └── video2.avi
│   └── February/
│       └── video3.mov
└── Drafts/
    └── video4.mp4
```
系统会递归扫描所有子文件夹，找到所有视频文件。

## ⚡ 性能优化

### 1. 文件扫描优化
- 只扫描视频文件，跳过其他格式
- 按文件名排序，确保处理顺序一致
- 使用相对路径，减少存储空间

### 2. 去重检查优化
- 快速预检：文件大小 + 时长
- 精确验证：SHA-256哈希值
- 批量处理：一次检查多个文件

### 3. 任务管理优化
- 主任务跟踪整体进度
- 子任务独立执行和重试
- 支持并发处理（如果配置允许）

## 🔍 故障排除

### 常见问题

**Q: 为什么某些视频文件没有被扫描到？**
A: 检查文件扩展名是否在支持列表中，确保文件没有损坏。

**Q: 子任务失败后如何处理？**
A: 可以在任务管理页面单独重启失败的子任务，不影响其他子任务。

**Q: 如何查看具体哪些文件被过滤了？**
A: 使用"预览视频文件"功能可以查看详细的文件列表和重复状态。

**Q: 文件夹很大时扫描会很慢吗？**
A: 系统只扫描文件名和扩展名，不读取文件内容，速度较快。

### 最佳实践

1. **文件夹组织**：建议按时间或主题组织视频文件
2. **命名规范**：使用有意义的文件名，便于识别
3. **定期清理**：删除不需要的视频文件，减少扫描时间
4. **预览确认**：上传前使用预览功能确认文件列表
5. **监控进度**：在任务管理页面监控上传进度

## 🎯 总结

文件夹视频处理功能提供了：
- **自动扫描**：无需手动选择每个视频文件
- **智能去重**：自动避免重复上传
- **批量处理**：一次操作处理多个视频
- **灵活控制**：支持预览和强制上传选项
- **任务管理**：清晰的主任务和子任务结构

这个功能大大简化了批量视频上传的操作流程，提高了工作效率。
