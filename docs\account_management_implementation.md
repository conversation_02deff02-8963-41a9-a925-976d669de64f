# 账号管理功能实现方案

## 1. 后端实现

### 1.1 数据库服务层

在`SocialDatabaseService`类中实现以下方法：

```python
class SocialDatabaseService:
    def __init__(self, db):
        self.db = db
        
    # 平台相关方法
    def get_platforms(self, status=None, skip=0, limit=100):
        """获取平台列表"""
        query = {}
        if status:
            query["status"] = status
        return list(self.db.social_platforms.find(query).skip(skip).limit(limit))
        
    def get_platform_apps(self, platform_id, app_type=None, skip=0, limit=100):
        """获取平台应用列表"""
        query = {"platform_id": platform_id}
        if app_type:
            query["type"] = app_type
        return list(self.db.platform_apps.find(query).skip(skip).limit(limit))
    
    # 账号相关方法
    def get_accounts(self, platform_id=None, core_service_id=None, status=None, skip=0, limit=100):
        """获取账号列表"""
        query = {}
        if platform_id:
            query["platform_id"] = platform_id
        if core_service_id:
            query["core_service_id"] = core_service_id
        if status:
            query["status"] = status
        return list(self.db.social_accounts.find(query).skip(skip).limit(limit))
    
    def get_account(self, account_id):
        """获取单个账号"""
        return self.db.social_accounts.find_one({"_id": ObjectId(account_id)})
    
    def create_account(self, account_data):
        """创建账号"""
        # 添加创建时间和更新时间
        now = datetime.datetime.now()
        account_data["created_at"] = now
        account_data["updated_at"] = now
        
        # 设置默认状态
        if "status" not in account_data:
            account_data["status"] = "active"
            
        result = self.db.social_accounts.insert_one(account_data)
        return str(result.inserted_id)
    
    def update_account(self, account_id, update_data):
        """更新账号"""
        # 更新时间
        update_data["updated_at"] = datetime.datetime.now()
        
        result = self.db.social_accounts.update_one(
            {"_id": ObjectId(account_id)},
            {"$set": update_data}
        )
        return result.modified_count > 0
    
    def delete_account(self, account_id):
        """删除账号"""
        # 先删除关联关系
        self.db.device_account_mappings.delete_many({"account_id": account_id})
        
        # 删除账号
        result = self.db.social_accounts.delete_one({"_id": ObjectId(account_id)})
        return result.deleted_count > 0
    
    def batch_update_accounts(self, account_ids, update_data):
        """批量更新账号"""
        # 更新时间
        update_data["updated_at"] = datetime.datetime.now()
        
        result = self.db.social_accounts.update_many(
            {"_id": {"$in": [ObjectId(id) for id in account_ids]}},
            {"$set": update_data}
        )
        return result.modified_count
    
    def batch_delete_accounts(self, account_ids):
        """批量删除账号"""
        # 先删除关联关系
        self.db.device_account_mappings.delete_many(
            {"account_id": {"$in": account_ids}}
        )
        
        # 删除账号
        result = self.db.social_accounts.delete_many(
            {"_id": {"$in": [ObjectId(id) for id in account_ids]}}
        )
        return result.deleted_count
    
    # 设备账号关联方法
    def get_device_accounts(self, device_id, platform_id=None):
        """获取设备关联的账号"""
        query = {"device_id": device_id}
        if platform_id:
            query["platform_id"] = platform_id
            
        mappings = list(self.db.device_account_mappings.find(query))
        
        # 获取关联的账号详情
        account_ids = [m["account_id"] for m in mappings]
        accounts = list(self.db.social_accounts.find(
            {"_id": {"$in": [ObjectId(id) for id in account_ids]}}
        ))
        
        # 合并映射信息和账号信息
        result = []
        for account in accounts:
            mapping = next((m for m in mappings if m["account_id"] == str(account["_id"])), {})
            account_info = {
                "id": str(account["_id"]),
                "username": account.get("username", ""),
                "platform_id": account.get("platform_id", ""),
                "status": account.get("status", ""),
                "mapping_id": str(mapping.get("_id", "")),
                "app_id": mapping.get("app_id", ""),
                "last_used": mapping.get("last_used", "")
            }
            result.append(account_info)
            
        return result
    
    def link_device_account(self, device_id, account_id, platform_id, app_id):
        """关联设备和账号"""
        # 检查是否已存在该平台的关联
        existing = self.db.device_account_mappings.find_one({
            "device_id": device_id,
            "platform_id": platform_id
        })
        
        if existing:
            # 更新现有关联
            result = self.db.device_account_mappings.update_one(
                {"_id": existing["_id"]},
                {
                    "$set": {
                        "account_id": account_id,
                        "app_id": app_id,
                        "updated_at": datetime.datetime.now()
                    }
                }
            )
            return result.modified_count > 0
        else:
            # 创建新关联
            mapping = {
                "device_id": device_id,
                "account_id": account_id,
                "platform_id": platform_id,
                "app_id": app_id,
                "status": "active",
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            }
            result = self.db.device_account_mappings.insert_one(mapping)
            return bool(result.inserted_id)
    
    def unlink_device_account(self, device_id, account_id):
        """解除设备和账号关联"""
        result = self.db.device_account_mappings.delete_one({
            "device_id": device_id,
            "account_id": account_id
        })
        return result.deleted_count > 0
    
    # 账号导入方法
    def import_accounts_from_text(self, text_content, platform_mapping=None, core_service_id="default"):
        """从文本导入账号"""
        if platform_mapping is None:
            platform_mapping = {
                "GG": "youtube",
                "FB": "facebook"
            }
            
        lines = text_content.strip().split("\n")
        imported_count = 0
        errors = []
        
        current_display_name = ""
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否是账号标识行
            if not any(p + "：" in line or p + ":" in line for p in platform_mapping.keys()):
                # 可能是显示名称行
                current_display_name = line
                continue
                
            try:
                # 解析平台和账号信息
                platform_code = None
                for code in platform_mapping.keys():
                    if code + "：" in line or code + ":" in line:
                        platform_code = code
                        break
                        
                if not platform_code:
                    continue
                    
                platform_id = platform_mapping[platform_code]
                
                # 分割账号信息
                account_part = line.split(platform_code + "：")[-1].split(platform_code + ":")[-1]
                parts = account_part.split("----")
                
                username = parts[0].strip()
                
                account_data = {
                    "username": username,
                    "platform_id": platform_id,
                    "core_service_id": core_service_id,
                    "status": "active",
                    "created_at": datetime.datetime.now(),
                    "updated_at": datetime.datetime.now()
                }
                
                # 添加密码
                if len(parts) > 1:
                    account_data["password"] = parts[1].strip()
                    
                # 添加恢复邮箱
                if len(parts) > 2:
                    account_data["recovery_email"] = parts[2].strip()
                    
                # 添加恢复码
                if len(parts) > 3:
                    account_data["recovery_code"] = parts[3].strip()
                    
                # 添加备注/描述
                if len(parts) > 4:
                    account_data["description"] = parts[4].strip()
                    
                # 添加显示名称
                if current_display_name:
                    account_data["display_name"] = current_display_name
                    
                # 检查账号是否已存在
                existing = self.db.social_accounts.find_one({
                    "username": username,
                    "platform_id": platform_id
                })
                
                if existing:
                    # 更新现有账号
                    self.db.social_accounts.update_one(
                        {"_id": existing["_id"]},
                        {"$set": account_data}
                    )
                else:
                    # 创建新账号
                    self.db.social_accounts.insert_one(account_data)
                    
                imported_count += 1
                
            except Exception as e:
                errors.append(f"导入行 '{line}' 失败: {str(e)}")
                
        return {
            "imported_count": imported_count,
            "errors": errors
        }
```

### 1.2 API实现

在`app/api/v1/social_accounts.py`中实现以下API端点：

```python
# 定义API路由
router = APIRouter(
    prefix="/api/v1/social",
    tags=["social"],
    dependencies=[Depends(get_current_user)]
)

# 定义数据模型
class SocialAccount(BaseModel):
    id: Optional[str] = None
    username: str
    password: Optional[str] = None
    recovery_email: Optional[str] = None
    recovery_code: Optional[str] = None
    display_name: Optional[str] = None
    platform_id: str
    core_service_id: str
    status: Optional[str] = "active"
    avatar: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None

# API端点：获取账号列表
@router.get("/accounts", response_model=List[Dict[str, Any]])
async def get_social_accounts(
    platform_id: Optional[str] = None,
    core_service_id: Optional[str] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """获取社交媒体账号列表，支持按平台和Core服务筛选"""
    try:
        accounts = db_service.get_accounts(
            platform_id=platform_id,
            core_service_id=core_service_id,
            status=status,
            skip=skip,
            limit=limit
        )
        
        # 格式化返回数据
        formatted_accounts = []
        for account in accounts:
            if '_id' in account:
                account['id'] = str(account['_id'])
                del account['_id']
            formatted_accounts.append(account)
            
        return formatted_accounts
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取社交媒体账号列表失败: {str(e)}"
        )

# API端点：创建账号
@router.post("/accounts", response_model=Dict[str, Any])
async def create_social_account(
    account: SocialAccount,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """创建社交媒体账号"""
    try:
        # 创建账号
        account_data = account.dict(exclude={"id"})
        account_id = db_service.create_account(account_data)
        
        # 获取创建的账号
        created_account = db_service.get_account(account_id)
        
        # 格式化返回数据
        if '_id' in created_account:
            created_account['id'] = str(created_account['_id'])
            del created_account['_id']
            
        return created_account
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"创建社交媒体账号失败: {str(e)}"
        )
```

## 2. 前端实现

### 2.1 API服务

在`frontend/src/api/social.ts`中实现以下API服务：

```typescript
import request from '@/utils/request'
import type { SocialAccount, SocialPlatform, PlatformApp } from '@/types/social'

// 获取平台列表
export const getPlatforms = (status?: string) => {
  const params = status ? { status } : {}
  return request<SocialPlatform[]>({
    url: '/api/v1/social/platforms',
    method: 'get',
    params
  })
}

// 获取平台应用列表
export const getPlatformApps = (platformId: string, type?: string) => {
  const params = type ? { type } : {}
  return request<PlatformApp[]>({
    url: `/api/v1/social/platforms/${platformId}/apps`,
    method: 'get',
    params
  })
}

// 获取账号列表
export const getAccounts = (params?: {
  platform_id?: string
  core_service_id?: string
  status?: string
  skip?: number
  limit?: number
}) => {
  return request<SocialAccount[]>({
    url: '/api/v1/social/accounts',
    method: 'get',
    params
  })
}

// 创建账号
export const createAccount = (data: Omit<SocialAccount, 'id'>) => {
  return request<SocialAccount>({
    url: '/api/v1/social/accounts',
    method: 'post',
    data
  })
}

// 更新账号
export const updateAccount = (id: string, data: Partial<SocialAccount>) => {
  return request<SocialAccount>({
    url: `/api/v1/social/accounts/${id}`,
    method: 'put',
    data
  })
}

// 删除账号
export const deleteAccount = (id: string) => {
  return request<{ deleted: boolean }>({
    url: `/api/v1/social/accounts/${id}`,
    method: 'delete'
  })
}

// 批量更新账号
export const batchUpdateAccounts = (accountIds: string[], updateData: Partial<SocialAccount>) => {
  return request<{ updated_count: number }>({
    url: '/api/v1/social/accounts/batch_update',
    method: 'post',
    data: {
      account_ids: accountIds,
      update_data: updateData
    }
  })
}

// 批量删除账号
export const batchDeleteAccounts = (accountIds: string[]) => {
  return request<{ deleted_count: number }>({
    url: '/api/v1/social/accounts/batch_delete',
    method: 'post',
    data: {
      account_ids: accountIds
    }
  })
}

// 导入账号
export const importAccounts = (textContent: string, platformMapping?: Record<string, string>, coreServiceId?: string) => {
  return request<{ imported_count: number, errors: string[] }>({
    url: '/api/v1/social/accounts/import',
    method: 'post',
    data: {
      text_content: textContent,
      platform_mapping: platformMapping,
      core_service_id: coreServiceId
    }
  })
}
```
