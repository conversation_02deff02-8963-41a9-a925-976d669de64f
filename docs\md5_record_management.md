# 视频MD5记录管理功能

## 功能概述

视频MD5记录管理功能是文件管理模块的新增功能，用于记录和管理视频文件的MD5哈希值，并支持手动标识发布状态，提供MD5比对和一键删除重复视频的功能。

## 主要功能

### 1. MD5记录采集
- **批量保存MD5记录**：一键扫描当前文件夹中的所有视频文件，计算MD5哈希值并保存到数据库
- **自动提取媒体信息**：同时记录视频时长、分辨率等媒体信息
- **去重检查**：自动跳过已存在的MD5记录，避免重复保存

### 2. 多平台发布状态管理
- **多平台支持**：支持YouTube、TikTok、Instagram、抖音、快手、小红书、微博、B站等多个平台
- **独立发布状态**：每个平台可以有独立的发布状态，一个视频可以在某些平台发布，在其他平台未发布
- **详细发布信息**：记录每个平台的发布日期、发布账号、视频ID、视频链接等详细信息
- **平台备注功能**：支持为每个平台添加独立的备注信息
- **发布统计**：显示已发布平台数量和总平台数量的统计信息

### 3. MD5比对检查
- **重复文件检测**：比对当前文件夹中的视频文件与数据库中的MD5记录
- **智能分析**：显示重复文件的详细信息，包括已有记录的发布状态
- **可视化结果**：清晰展示比对结果，标识重复和唯一文件

### 4. 一键删除功能
- **批量删除重复文件**：选择重复的视频文件，一键删除物理文件
- **安全确认**：删除前提供确认对话框，防止误操作
- **操作反馈**：显示删除结果和错误信息

## 使用方法

### 访问MD5记录管理
1. 进入文件管理页面
2. 点击工具栏中的 "🔐 MD5记录管理" 按钮
3. 打开MD5记录管理对话框

### 批量保存MD5记录
1. 在文件管理页面导航到目标文件夹
2. 点击 "🔐 MD5记录管理" 按钮
3. 在MD5管理对话框中点击 "💾 批量保存当前文件夹MD5" 按钮
4. 系统会自动扫描文件夹中的视频文件并保存MD5记录

### 编辑发布状态
1. 在MD5记录列表中找到目标记录
2. 点击 "编辑" 按钮
3. 在编辑对话框中：
   - 切换发布状态开关
   - 选择发布日期（如果已发布）
   - 选择发布平台
   - 输入发布账号名称
   - 添加备注信息
4. 点击 "保存" 按钮

### MD5比对检查
1. 在MD5管理对话框中点击 "🔍 MD5比对检查" 按钮
2. 系统会自动比对当前文件夹中的视频文件
3. 查看比对结果：
   - 绿色标签：唯一文件
   - 红色标签：重复文件
   - 显示已有记录的详细信息

### 删除重复文件
1. 在MD5比对结果中选择要删除的重复文件
2. 点击 "一键删除选中的重复文件" 按钮
3. 确认删除操作
4. 系统会删除选中的物理文件

## 数据库设计

### video_md5_records 集合
```javascript
{
  "_id": ObjectId,
  "file_path": String,        // 文件路径
  "file_name": String,        // 文件名
  "md5_hash": String,         // MD5哈希值（唯一索引）
  "file_size": Number,        // 文件大小（字节）
  "duration": Number,         // 视频时长（秒）
  "resolution": String,       // 分辨率（如：1920x1080）
  "platform_records": [      // 各平台发布记录数组
    {
      "platform": String,    // 平台名称（youtube, tiktok, instagram, douyin等）
      "is_published": Boolean, // 该平台是否已发布
      "publish_date": String, // 发布日期
      "publish_account": String, // 发布账号
      "video_id": String,     // 平台上的视频ID
      "video_url": String,    // 视频链接
      "notes": String         // 平台备注
    }
  ],
  "notes": String,           // 全局备注
  "created_at": String,      // 创建时间
  "updated_at": String       // 更新时间
}
```

## API接口

### 后端API

#### 基础MD5记录管理
- `POST /api/v1/filesystem/md5-records` - 创建MD5记录
- `PUT /api/v1/filesystem/md5-records/{md5_hash}` - 更新MD5记录基本信息
- `GET /api/v1/filesystem/md5-records` - 获取MD5记录列表
- `GET /api/v1/filesystem/md5-records/{md5_hash}` - 根据MD5获取记录
- `DELETE /api/v1/filesystem/md5-records/{md5_hash}` - 删除MD5记录

#### 批量操作
- `POST /api/v1/filesystem/md5-records/batch-save` - 批量保存文件夹MD5记录
- `POST /api/v1/filesystem/md5-records/compare` - MD5比对
- `POST /api/v1/filesystem/md5-records/batch-delete` - 批量删除文件
- `POST /api/v1/filesystem/md5-records/batch-check-platforms` - 批量检查多个平台发布状态

#### 多平台发布状态管理
- `PUT /api/v1/filesystem/md5-records/{md5_hash}/platform/{platform}` - 更新特定平台发布状态
- `GET /api/v1/filesystem/md5-records/{md5_hash}/platform/{platform}` - 获取特定平台发布状态
- `GET /api/v1/filesystem/md5-records/{md5_hash}/platform/{platform}/published` - 检查平台是否已发布
- `GET /api/v1/filesystem/md5-records/{md5_hash}/platforms` - 获取所有平台发布状态

### 前端API

#### 基础MD5记录管理
- `createVideoMD5Record()` - 创建MD5记录
- `updateVideoMD5Record()` - 更新MD5记录基本信息
- `getVideoMD5Records()` - 获取MD5记录列表
- `getVideoMD5RecordByHash()` - 根据MD5获取记录
- `deleteVideoMD5Record()` - 删除MD5记录

#### 批量操作
- `batchSaveFolderMD5Records()` - 批量保存MD5记录
- `compareFolderMD5Records()` - MD5比对
- `batchDeleteFiles()` - 批量删除文件
- `batchCheckPlatformPublished()` - 批量检查多个平台发布状态

#### 多平台发布状态管理
- `updatePlatformPublishStatus()` - 更新特定平台发布状态
- `getPlatformPublishStatus()` - 获取特定平台发布状态
- `checkPlatformPublished()` - 检查平台是否已发布
- `getAllPlatformPublishStatus()` - 获取所有平台发布状态

## 注意事项

1. **文件路径**：MD5记录基于文件的绝对路径，移动文件后需要重新保存记录
2. **权限要求**：删除文件需要相应的文件系统权限
3. **性能考虑**：大文件夹的MD5计算可能需要较长时间
4. **数据安全**：删除操作不可恢复，请谨慎操作
5. **支持格式**：目前支持常见的视频格式（mp4, avi, mov, mkv, wmv, flv, m4v）

## 故障排除

### 常见问题
1. **MD5计算失败**：检查文件是否存在且有读取权限
2. **保存记录失败**：检查数据库连接和权限
3. **删除文件失败**：检查文件是否被其他程序占用
4. **比对结果不准确**：确保文件没有被修改

### 错误处理
- 所有操作都有错误处理和用户友好的错误提示
- 批量操作会显示成功和失败的详细统计
- 网络错误会自动重试（部分操作）

## 更新日志

### v1.0.0 (2025-01-16)
- 初始版本发布
- 支持MD5记录的增删改查
- 支持发布状态管理
- 支持MD5比对和重复文件删除
- 提供完整的用户界面和API接口
