"""
设备抽象接口
定义设备控制的通用接口
"""

import enum
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

class DeviceStatus(enum.Enum):
    """设备状态枚举"""
    UNKNOWN = "unknown"
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"

@dataclass
class DeviceInfo:
    """设备信息数据类"""
    device_id: str
    name: str
    status: DeviceStatus
    device_type: str
    window_info: Dict[str, Any] = None
    process_info: Dict[str, Any] = None
    display_info: Dict[str, Any] = None
    network_info: Dict[str, Any] = None
    system_info: Dict[str, Any] = None
    hardware_info: Dict[str, Any] = None

class DeviceInterface:
    """设备控制接口"""
    
    async def start(self) -> bool:
        """启动设备
        
        Returns:
            是否启动成功
        """
        raise NotImplementedError("子类必须实现start方法")
    
    async def stop(self) -> bool:
        """停止设备
        
        Returns:
            是否停止成功
        """
        raise NotImplementedError("子类必须实现stop方法")
    
    async def restart(self) -> bool:
        """重启设备
        
        Returns:
            是否重启成功
        """
        raise NotImplementedError("子类必须实现restart方法")
    
    async def get_status(self) -> DeviceStatus:
        """获取设备状态
        
        Returns:
            设备状态
        """
        raise NotImplementedError("子类必须实现get_status方法")
    
    async def execute_command(self, command: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行设备命令
        
        Args:
            command: 命令名称
            params: 命令参数
            
        Returns:
            命令执行结果
        """
        raise NotImplementedError("子类必须实现execute_command方法")
    
    async def get_info(self) -> DeviceInfo:
        """获取设备信息
        
        Returns:
            设备信息
        """
        raise NotImplementedError("子类必须实现get_info方法")

class DeviceManager:
    """设备管理器基类"""
    
    async def initialize(self) -> None:
        """初始化管理器"""
        raise NotImplementedError
    
    async def shutdown(self) -> None:
        """关闭管理器"""
        raise NotImplementedError
    
    async def get_all_devices(self) -> List[DeviceInfo]:
        """获取所有设备信息"""
        raise NotImplementedError
    
    async def get_device_info(self, device_id: str) -> DeviceInfo:
        """获取设备详细信息"""
        raise NotImplementedError
    
    async def start_device(self, device_id: str) -> bool:
        """启动设备"""
        raise NotImplementedError
    
    async def stop_device(self, device_id: str) -> bool:
        """停止设备"""
        raise NotImplementedError
    
    async def restart_device(self, device_id: str) -> bool:
        """重启设备"""
        raise NotImplementedError


