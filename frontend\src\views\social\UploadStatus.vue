<template>
  <div class="upload-status">
    <div class="status-header">
      <h2>📊 YouTube上传状态</h2>
      <div class="header-actions">
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text=""
          @change="toggleAutoRefresh"
        />
        <el-button type="primary" size="small" @click="refreshTasks">刷新</el-button>
      </div>
    </div>

    <div class="status-content">
      <el-card class="table-card" shadow="never">

      <el-table
        :data="tasks"
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="folderPath" label="文件夹路径" />
        <el-table-column prop="videoName" label="视频名称" />
        <el-table-column prop="account" label="账户" />
        <el-table-column label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进度">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress || 0"
              :status="row.progress === 100 ? 'success' : ''"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewLogs(row)"
              :disabled="!row.id"
            >
              查看日志
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      </el-card>
    </div>

    <!-- 日志模态框 -->
    <el-dialog
      v-model="logDialogVisible"
      title="📝 上传日志"
      width="70%"
      destroy-on-close
    >
      <div class="log-content" v-loading="logLoading">
        <pre v-if="logs.length">{{ logs.join('\n') }}</pre>
        <el-empty v-else description="暂无日志" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { getUploadTasks, getTaskLogs } from '@/api/social'
import { ElMessage } from 'element-plus'
import { io, Socket } from 'socket.io-client'

const tasks = ref<any[]>([])
const loading = ref(false)
const logs = ref<string[]>([])
const logDialogVisible = ref(false)
const logLoading = ref(false)
const currentTaskId = ref('')
const socket = ref<Socket | null>(null)
const socketConnected = ref(false)
const autoRefresh = ref(false)
const refreshInterval = ref<number | null>(null)

// 获取任务列表
const fetchTasks = async () => {
  try {
    loading.value = true
    const res = await getUploadTasks()
    tasks.value = res.data || []
    console.log('获取到的上传任务:', tasks.value)
  } catch (err: any) {
    console.error('获取上传任务列表失败:', err)
    ElMessage.error('获取上传任务列表失败: ' + (err.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 刷新任务
const refreshTasks = () => {
  fetchTasks()
}

// 切换自动刷新
const toggleAutoRefresh = (value: boolean) => {
  if (value) {
    // 启动自动刷新，每30秒刷新一次
    refreshInterval.value = window.setInterval(() => {
      console.log('自动刷新任务列表')
      fetchTasks()
    }, 30000) // 30秒
    ElMessage.success('已开启自动刷新（每30秒）')
  } else {
    // 停止自动刷新
    if (refreshInterval.value !== null) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
    ElMessage.info('已关闭自动刷新')
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'failed':
      return 'danger'
    case 'processing':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '待处理'
    case 'processing':
      return '上传中'
    case 'completed':
      return '已完成'
    case 'failed':
      return '失败'
    default:
      return '未知'
  }
}

// 查看日志
const viewLogs = async (row: any) => {
  if (!row.id) return

  try {
    currentTaskId.value = row.id
    logDialogVisible.value = true
    logLoading.value = true

    const res = await getTaskLogs(row.id)
    logs.value = res.data || []
  } catch (err: any) {
    console.error('获取任务日志失败:', err)
    ElMessage.error('获取任务日志失败: ' + (err.message || '未知错误'))
    logs.value = []
  } finally {
    logLoading.value = false
  }
}

// 初始化WebSocket连接
const initWebSocket = () => {
  try {
    // 连接到WebSocket服务器
    socket.value = io('/device-socket.io')

    // 连接成功事件
    socket.value.on('connect', () => {
      console.log('WebSocket连接成功')
      socketConnected.value = true
    })

    // 连接错误事件
    socket.value.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error)
      socketConnected.value = false
    })

    // 监听上传状态更新事件
    socket.value.on('device_status_update', (data) => {
      console.log('收到上传状态更新:', data)

      // 如果是YouTube上传任务的状态更新
      if (data.type === 'youtube_upload') {
        // 查找任务并更新
        const taskIndex = tasks.value.findIndex(task => task.id === data.taskId)

        if (taskIndex !== -1) {
          // 更新现有任务
          tasks.value[taskIndex] = {
            ...tasks.value[taskIndex],
            status: data.status,
            progress: data.progress || tasks.value[taskIndex].progress
          }
        } else if (data.taskDetails) {
          // 添加新任务
          tasks.value.push(data.taskDetails)
        }
      }
    })
  } catch (error) {
    console.error('初始化WebSocket失败:', error)
  }
}

// 关闭WebSocket连接
const closeWebSocket = () => {
  if (socket.value) {
    socket.value.disconnect()
    socket.value = null
    socketConnected.value = false
    console.log('WebSocket连接已关闭')
  }
}

onMounted(() => {
  console.log('上传状态组件已挂载')
  fetchTasks()
  initWebSocket()
})

onUnmounted(() => {
  closeWebSocket()

  // 清除自动刷新定时器
  if (refreshInterval.value !== null) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
})
</script>

<style scoped>
.upload-status {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 10px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.status-header h2 {
  margin: 0;
  color: #409EFF;
  font-size: 1.5rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-content {
  flex: 1;
  overflow: hidden;
  padding: 20px;
  min-height: 0;
}

.table-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.table-card :deep(.el-card__body) {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.table-card :deep(.el-table) {
  height: 100%;
}

.table-card :deep(.el-table__body-wrapper) {
  overflow-y: auto;
}

.log-content {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

/* 自定义滚动条样式 */
.log-content::-webkit-scrollbar {
  width: 8px;
}

.log-content::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 4px;
}

.log-content::-webkit-scrollbar-track {
  background-color: #f2f6fc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-header {
    padding: 15px 15px 10px 15px;
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .status-header h2 {
    text-align: center;
  }

  .header-actions {
    justify-content: center;
  }

  .status-content {
    padding: 15px;
  }
}
</style>