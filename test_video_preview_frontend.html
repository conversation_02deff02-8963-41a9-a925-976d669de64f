<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频预览功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .video-container {
            margin: 20px 0;
        }
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
        }
    </style>
</head>
<body>
    <h1>视频预览功能测试</h1>
    
    <div class="test-section">
        <h2>1. 服务器连接测试</h2>
        <button onclick="testServerConnection()">测试Core服务连接</button>
        <button onclick="testFileServer()">测试文件服务器</button>
        <div id="server-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 缩略图生成测试</h2>
        <input type="text" id="video-path" placeholder="输入视频文件路径" style="width: 400px; padding: 8px;">
        <button onclick="testThumbnailGeneration()">生成缩略图</button>
        <div id="thumbnail-result" class="test-result"></div>
        <div id="thumbnail-display"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 视频预览信息测试</h2>
        <button onclick="testVideoPreviewInfo()">获取视频预览信息</button>
        <div id="preview-info-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 视频播放测试</h2>
        <div class="info">
            注意：由于浏览器安全限制，本地视频文件无法直接在浏览器中播放。
            这是正常现象，实际应用中需要通过HTTP服务器提供视频文件访问。
        </div>
        <button onclick="testVideoPlayback()">测试视频播放</button>
        <div id="video-result" class="test-result"></div>
        <div id="video-display" class="video-container"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        const FILE_SERVER_BASE = 'http://localhost:8001';
        
        async function testServerConnection() {
            const resultDiv = document.getElementById('server-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch(`${API_BASE}/filesystem/folder-list`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        path: 'C:\\',
                        include_md5: false,
                        include_media_info: false
                    })
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✓ Backend API 连接成功</div>';
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ Backend API 连接失败: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Backend API 连接失败: ${error.message}</div>`;
            }
        }
        
        async function testFileServer() {
            const resultDiv = document.getElementById('server-result');
            
            try {
                const response = await fetch(`${FILE_SERVER_BASE}/health`);
                
                if (response.ok) {
                    resultDiv.innerHTML += '<div class="success">✓ 文件服务器连接成功</div>';
                } else {
                    resultDiv.innerHTML += `<div class="error">✗ 文件服务器连接失败: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">✗ 文件服务器连接失败: ${error.message}</div>`;
            }
        }
        
        async function testThumbnailGeneration() {
            const videoPath = document.getElementById('video-path').value;
            const resultDiv = document.getElementById('thumbnail-result');
            const displayDiv = document.getElementById('thumbnail-display');
            
            if (!videoPath) {
                resultDiv.innerHTML = '<div class="error">请输入视频文件路径</div>';
                return;
            }
            
            resultDiv.innerHTML = '生成缩略图中...';
            displayDiv.innerHTML = '';
            
            try {
                const response = await fetch(`${API_BASE}/filesystem/video/thumbnail`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_path: videoPath,
                        max_width: 320,
                        max_height: 180,
                        quality: 85
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✓ 缩略图生成成功<br>
                            路径: ${data.thumbnail_path}<br>
                            URL: ${data.thumbnail_url}<br>
                            尺寸: ${data.actual_width}x${data.actual_height}<br>
                            大小: ${data.thumbnail_size} 字节
                        </div>
                    `;
                    
                    if (data.thumbnail_url) {
                        displayDiv.innerHTML = `<img src="${data.thumbnail_url}" alt="视频缩略图" onerror="this.style.display='none'; this.nextSibling.style.display='block';">
                                               <div style="display:none; color:red;">缩略图加载失败</div>`;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ 缩略图生成失败: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ 缩略图生成失败: ${error.message}</div>`;
            }
        }
        
        async function testVideoPreviewInfo() {
            const videoPath = document.getElementById('video-path').value;
            const resultDiv = document.getElementById('preview-info-result');
            
            if (!videoPath) {
                resultDiv.innerHTML = '<div class="error">请输入视频文件路径</div>';
                return;
            }
            
            resultDiv.innerHTML = '获取视频信息中...';
            
            try {
                const response = await fetch(`${API_BASE}/filesystem/video/preview-info`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_path: videoPath,
                        include_thumbnail: true,
                        include_detailed_metadata: false
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const mediaInfo = data.media_info || {};
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✓ 视频信息获取成功<br>
                            时长: ${mediaInfo.duration || 0} 秒<br>
                            分辨率: ${mediaInfo.resolution || '未知'}<br>
                            视频编码: ${mediaInfo.video_codec || '未知'}<br>
                            音频编码: ${mediaInfo.audio_codec || '未知'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ 视频信息获取失败: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ 视频信息获取失败: ${error.message}</div>`;
            }
        }
        
        function testVideoPlayback() {
            const videoPath = document.getElementById('video-path').value;
            const resultDiv = document.getElementById('video-result');
            const displayDiv = document.getElementById('video-display');
            
            if (!videoPath) {
                resultDiv.innerHTML = '<div class="error">请输入视频文件路径</div>';
                return;
            }
            
            // 尝试构建HTTP URL（这通常不会工作，因为需要文件服务器支持）
            const httpUrl = `${FILE_SERVER_BASE}/files/${videoPath.replace(/\\/g, '/').replace(/^[A-Z]:/, '')}`;
            
            resultDiv.innerHTML = `
                <div class="info">
                    尝试播放视频: ${httpUrl}<br>
                    注意：这通常不会成功，因为需要配置文件服务器来提供视频文件访问。
                </div>
            `;
            
            displayDiv.innerHTML = `
                <video controls>
                    <source src="${httpUrl}" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            `;
        }
        
        // 页面加载时自动测试服务器连接
        window.onload = function() {
            testServerConnection();
            testFileServer();
        };
    </script>
</body>
</html>
