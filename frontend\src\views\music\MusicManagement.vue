<template>
  <div class="music-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>🎵 音乐库管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="showAddDialog = true">
              <el-icon><Plus /></el-icon>
              添加音乐
            </el-button>
            <el-button @click="initSampleData" :loading="initLoading">
              <el-icon><Download /></el-icon>
              初始化示例数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索音乐标题或ID"
              @input="handleSearch"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="selectedCategory" placeholder="选择分类" @change="loadMusicList" clearable>
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedTags"
              placeholder="选择标签"
              multiple
              @change="loadMusicList"
              clearable
            >
              <el-option
                v-for="tag in tags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="selectedPlatform" placeholder="选择平台" @change="loadMusicList">
              <el-option label="YouTube" value="youtube" />
              <el-option label="TikTok" value="tiktok" />
              <el-option label="Instagram" value="instagram" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="loadMusicList" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="总音乐数" :value="total" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="分类数" :value="categories.length" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="标签数" :value="tags.length" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="当前页数量" :value="musicList.length" />
          </el-col>
        </el-row>
      </div>

      <!-- 音乐列表 -->
      <div class="music-table">
        <el-table
          :data="musicList"
          v-loading="loading"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="music_id" label="音乐ID" width="120" sortable />
          <el-table-column prop="title" label="标题" min-width="150" sortable />
          <el-table-column prop="duration" label="时长" width="80" />
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="platform" label="平台" width="100" />
          <el-table-column prop="tags" label="标签" min-width="200">
            <template #default="scope">
              <el-tag
                v-for="tag in scope.row.tags"
                :key="tag"
                size="small"
                style="margin-right: 5px; margin-bottom: 2px;"
              >
                {{ tag }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="editMusic(scope.row)">
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="deleteMusic(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 添加/编辑音乐对话框 -->
    <el-dialog v-model="showAddDialog" :title="editingMusic ? '编辑音乐' : '添加音乐'" width="600px">
      <el-form :model="musicForm" :rules="musicRules" ref="musicFormRef" label-width="80px">
        <el-form-item label="音乐ID" prop="music_id">
          <el-input v-model="musicForm.music_id" placeholder="如：KSA012316484" />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="musicForm.title" placeholder="如：Last Time" />
        </el-form-item>
        <el-form-item label="时长" prop="duration">
          <el-input v-model="musicForm.duration" placeholder="如：2:30" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="musicForm.category" placeholder="如：背景音乐" />
        </el-form-item>
        <el-form-item label="平台" prop="platform">
          <el-select v-model="musicForm.platform" style="width: 100%">
            <el-option label="YouTube" value="youtube" />
            <el-option label="TikTok" value="tiktok" />
            <el-option label="Instagram" value="instagram" />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-select
            v-model="musicForm.tags"
            multiple
            filterable
            allow-create
            placeholder="输入标签，如：平静、梦幻"
            style="width: 100%"
          >
            <el-option
              v-for="tag in tags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelEdit">取消</el-button>
        <el-button type="primary" @click="saveMusic" :loading="saving">
          {{ editingMusic ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, Search, Refresh } from '@element-plus/icons-vue'
import { musicApi } from '@/api/music'

// 响应式数据
const musicList = ref([])
const categories = ref([])
const tags = ref([])
const selectedMusic = ref([])
const searchKeyword = ref('')
const selectedCategory = ref('')
const selectedTags = ref([])
const selectedPlatform = ref('youtube')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)

// 对话框相关
const showAddDialog = ref(false)
const editingMusic = ref(null)
const saving = ref(false)
const initLoading = ref(false)
const musicFormRef = ref()

// 表单数据
const musicForm = reactive({
  music_id: '',
  title: '',
  duration: '',
  category: '背景音乐',
  platform: 'youtube',
  tags: []
})

// 表单验证规则
const musicRules = {
  music_id: [
    { required: true, message: '请输入音乐ID', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入音乐标题', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ]
}

// 示例数据
const sampleData = [
  {
    music_id: "KSA012316484",
    title: "Last Time",
    tags: ["平静", "梦幻"],
    duration: "2:30",
    category: "背景音乐",
    platform: "youtube"
  },
  {
    music_id: "BGM001",
    title: "Morning Light",
    tags: ["清新", "早晨", "阳光"],
    duration: "3:15",
    category: "背景音乐",
    platform: "youtube"
  },
  {
    music_id: "BGM002",
    title: "Peaceful Moments",
    tags: ["平静", "放松", "冥想"],
    duration: "4:20",
    category: "放松音乐",
    platform: "youtube"
  }
]

// 方法
const loadMusicList = async () => {
  loading.value = true
  try {
    const params = {
      platform: selectedPlatform.value,
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    }
    
    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }
    if (selectedCategory.value) {
      params.category = selectedCategory.value
    }
    if (selectedTags.value.length > 0) {
      params.tags = selectedTags.value.join(',')
    }

    const response = await musicApi.getMusicLibrary(params)
    if (response.code === 200) {
      musicList.value = response.data.items
      total.value = response.data.total
    }
  } catch (error) {
    ElMessage.error('加载音乐库失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await musicApi.getCategories(selectedPlatform.value)
    if (response.code === 200) {
      categories.value = response.data.categories
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const loadTags = async () => {
  try {
    const response = await musicApi.getTags(selectedPlatform.value)
    if (response.code === 200) {
      tags.value = response.data.tags
    }
  } catch (error) {
    console.error('加载标签失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadMusicList()
}

const handleSelectionChange = (selection) => {
  selectedMusic.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadMusicList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadMusicList()
}

const editMusic = (music) => {
  editingMusic.value = music
  Object.assign(musicForm, music)
  showAddDialog.value = true
}

const cancelEdit = () => {
  showAddDialog.value = false
  editingMusic.value = null
  resetForm()
}

const resetForm = () => {
  Object.assign(musicForm, {
    music_id: '',
    title: '',
    duration: '',
    category: '背景音乐',
    platform: 'youtube',
    tags: []
  })
}

const saveMusic = async () => {
  if (!musicFormRef.value) return
  
  await musicFormRef.value.validate(async (valid) => {
    if (valid) {
      saving.value = true
      try {
        const response = await musicApi.createMusic(musicForm)
        if (response.code === 200) {
          ElMessage.success(editingMusic.value ? '更新音乐成功' : '添加音乐成功')
          showAddDialog.value = false
          resetForm()
          editingMusic.value = null
          loadMusicList()
          loadTags()
          loadCategories()
        }
      } catch (error) {
        ElMessage.error(editingMusic.value ? '更新音乐失败' : '添加音乐失败')
      } finally {
        saving.value = false
      }
    }
  })
}

const deleteMusic = async (music) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除音乐 "${music.title}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // TODO: 实现删除API
    ElMessage.success('删除成功')
    loadMusicList()
  } catch {
    // 用户取消删除
  }
}

const initSampleData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要初始化示例数据吗？这将添加一些测试音乐到数据库。',
      '确认初始化',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    )
    
    initLoading.value = true
    
    for (const music of sampleData) {
      try {
        await musicApi.createMusic(music)
      } catch (error) {
        console.error('添加示例数据失败:', error)
      }
    }
    
    ElMessage.success('示例数据初始化完成')
    loadMusicList()
    loadTags()
    loadCategories()
    
  } catch {
    // 用户取消
  } finally {
    initLoading.value = false
  }
}

// 初始化
onMounted(() => {
  loadMusicList()
  loadCategories()
  loadTags()
})
</script>

<style scoped>
.music-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-section {
  margin-bottom: 20px;
}

.stats-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.music-table {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
