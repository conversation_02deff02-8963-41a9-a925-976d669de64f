global:
  checkNewVersion: true
  sendAnonymousUsage: false
entryPoints:
  web:
    address: ":8000"
providers:
  consulCatalog:
    endpoint:
      address: "consul:8500"
      scheme: "http"
    exposedByDefault: false
    prefix: "traefik"
  docker:
    exposedByDefault: false
    endpoint: "unix:///var/run/docker.sock"  # 明确指定 Docker socket
    network: "thunderhub"  # 指定网络
  file:
    filename: "/etc/traefik/dynamic.yml"
api:
  dashboard: true
  insecure: true