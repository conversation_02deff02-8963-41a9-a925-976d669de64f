# Vue.js 编码规范

## 1. 命名规范

### 组件名
- 使用帕斯卡命名法（PascalCase）如 `UserProfile.vue`
- 多词组合，避免单个词如 `TodoItem` 而非 `Item`

### 文件和目录
- 组件文件名与组件名一致，如 `UserProfile.vue`
- 目录使用小写加连字符（kebab-case）如 `user-profile/`

### Props 和事件
- Props 使用驼峰命名法（camelCase）如 `userId`
- 自定义事件使用 kebab-case 如 `update:user-id`
- 变量和方法使用 camelCase 如 `fetchData`

## 2. 代码格式化

### 缩进
- 使用 2 个空格

### 行长度
- 每行最多 100 字符，长行换行并对齐

### 模板（Template）
```vue
<MyComponent
  prop-one="value1"
  prop-two="value2"
/>
```

- 自闭合标签使用 `/>` 如 `<img />`

### 脚本（Script）
- 使用 setup 语法糖（Vue 3）或 Composition API
- 逻辑块之间空 1 行

### 样式（Style）
```vue
<style scoped>
.container {
  padding: 20px;
}
</style>
```

- 使用 CSS 变量或预处理器时保持命名一致

## 3. 组件设计

### 单一职责
- 每个组件专注单一功能
- 提取可复用逻辑到组合式函数（composables）

### Props
```javascript
defineProps({
  userId: {
    type: Number,
    default: 0
  }
})
```

- 避免直接修改 Props，使用事件通知父组件

### 事件
```javascript
const emit = defineEmits(['update:modelValue'])
emit('update:modelValue', newValue)
```

- 事件名使用 kebab-case

### 插槽（Slots）
```vue
<slot name="header"></slot>
```

## 4. 代码组织

### 文件结构
```
src/
├── assets/           # 静态资源
├── components/       # 通用组件
├── composables/      # 组合式函数
├── views/            # 页面组件
├── router/           # 路由配置
├── store/            # 状态管理
└── utils/            # 工具函数
```

### 组件结构
```vue
<template>
  <div>{{ message }}</div>
</template>

<script setup>
import { ref } from 'vue'
const message = ref('Hello, Vue!')
</script>

<style scoped>
div {
  color: blue;
}
</style>
```

## 5. 状态管理

### Composition API
- 优先使用，避免 option API

### 状态管理库
```javascript
export const useUserStore = defineStore('user', {
  state: () => ({
    userId: null
  })
})
```

- 通过 actions 或 mutations 修改状态

## 6. 注释和文档

### 模板注释
```vue
<!-- Display user info if authenticated -->
<div v-if="isAuthenticated">{{ user.name }}</div>
```

### 脚本注释
```javascript
/**
 * Fetch user data by ID
 * @param {number} id - User ID
 * @returns {Promise<object>} User data
 */
async function fetchUser(id) {
  // ...
}
```

## 7. 工具和检查

### 格式化
- 使用 Prettier

### Lint 检查
```javascript
module.exports = {
  extends: ['plugin:vue/vue3-recommended', 'plugin:typescript/recommended'],
  rules: {
    'vue/multi-word-component-names': 'off'
  }
}
```

### 测试
- 使用 Vitest 或 Jest

## 8. 性能和安全

### 性能
```javascript
const myComponent = defineAsyncComponent(() => import('./MyComponent.vue'))
```

### 安全
- 使用 `v-html` 时确保内容经过消毒
- 验证用户输入
- 使用环境变量存储敏感信息

## 9. 路由

### 路由配置
```javascript
{
  path: '/user/:id',
  name: 'UserProfile',
  component: UserProfile
}
```

### 懒加载
```javascript
{
  path: '/about',
  component: () => import('../views/About.vue')
}
