/**
 * 文件系统API接口
 */

import request from '@/utils/request'

// 数据类型定义
export interface MediaInfo {
  duration: number
  resolution: string
  video_codec: string
  audio_codec: string
  frame_rate: number
  bitrate: number
  video?: {
    codec: string
    width: number
    height: number
    fps: number
  }
  audio?: {
    codec: string
    sample_rate: number
    channels: number
  }
}

export interface FileInfo {
  name: string
  path: string
  size: number
  is_directory: boolean
  extension?: string
  last_modified: string
  md5_hash?: string
  is_uploaded?: boolean
  media_info?: MediaInfo
}

export interface FolderListResponse {
  path: string
  files: FileInfo[]
  parent_path?: string
}

// 获取文件夹内容
export const getFolderContents = (params: {
  path: string
  filter_extensions?: string[]
  include_md5?: boolean
  include_media_info?: boolean
}) => {
  return request<FolderListResponse>({
    url: '/api/v1/filesystem/list',
    method: 'post',
    data: {
      path: params.path
    },
    params: {
      filter_extensions: params.filter_extensions,
      include_md5: params.include_md5,
      include_media_info: params.include_media_info
    }
  })
}

// 获取视频文件列表（从指定文件夹）
export const getVideoFiles = (folderPath: string) => {
  return getFolderContents({
    path: folderPath,
    filter_extensions: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
    include_md5: true,
    include_media_info: true
  })
}

// 获取文件路径配置
export const getFilePaths = () => {
  return request({
    url: '/api/v1/filesystem/paths',
    method: 'get'
  })
}

// 检查路径是否存在
export const checkPathExists = (path: string) => {
  return request({
    url: '/api/v1/filesystem/check-path',
    method: 'post',
    data: { path }
  })
}

// 验证视频文件夹
export const validateVideoFolder = (path: string) => {
  return request({
    url: '/api/v1/filesystem/validate-video-folder',
    method: 'post',
    data: { path }
  })
}

// 获取视频预览信息
export const getVideoPreviewInfo = (videoPath: string, includeThumbnail = false) => {
  return request({
    url: '/api/v1/filesystem/video-preview-info',
    method: 'post',
    data: {
      video_path: videoPath,
      include_thumbnail: includeThumbnail,
      include_detailed_metadata: true
    }
  })
}
