/**
 * 视频采集相关API
 */

import request from '@/utils/request'

// 视频采集任务接口
export interface VideoCollectTask {
  account_id: string
  account_name: string
  account_url: string
  platform: string
  collect_config: {
    mode: 'basic_collect' | 'full_collect' | 'collect_and_download'
    max_videos: number
    use_month_filter: boolean
    target_months: Array<{
      year: string
      month: string
      display: string
    }>
    video_quality: 'high' | 'medium' | 'low'
    include_metadata: boolean
    skip_existing: boolean
  }
  download_path: string
}

// 视频信息接口
export interface VideoInfo {
  video_id: string
  title: string
  video_url: string
  real_video_url?: string
  cover_url: string
  like_count: string
  view_count?: string
  year: string
  month: string
  platform: string
  account_id: string
  account_name: string
  download_status: 'pending' | 'downloading' | 'completed' | 'failed'
  download_path?: string
  file_size?: number
  duration?: number
  created_at: string
  updated_at: string
}

// 视频统计信息接口
export interface VideoStats {
  total: number
  with_real_url: number
  downloaded: number
  failed: number
  total_size: number
  last_collect_time?: string
  status: 'pending' | 'collecting' | 'completed' | 'failed' | 'partial'
}

// 启动视频采集任务
export function startVideoCollectTask(taskData: VideoCollectTask) {
  return request({
    url: '/api/video-collect/start',
    method: 'post',
    data: taskData
  })
}

// 获取采集任务状态
export function getCollectTaskStatus(taskId: string) {
  return request({
    url: `/api/video-collect/status/${taskId}`,
    method: 'get'
  })
}

// 取消采集任务
export function cancelCollectTask(taskId: string) {
  return request({
    url: `/api/video-collect/cancel/${taskId}`,
    method: 'post'
  })
}

// 获取账号可用的发布月份
export function getAccountMonths(accountUrl: string) {
  return request({
    url: '/api/video-collect/months',
    method: 'get',
    params: {
      account_url: accountUrl
    }
  })
}

// 获取视频统计信息
export function getVideoStatistics(accountId?: string) {
  return request({
    url: '/api/video-collect/statistics',
    method: 'get',
    params: accountId ? { account_id: accountId } : {}
  })
}

// 获取指定账号的视频列表
export function getVideosByAccount(accountId: string, params?: {
  page?: number
  limit?: number
  search?: string
  month?: string
  status?: string
}) {
  return request({
    url: `/api/video-collect/videos/${accountId}`,
    method: 'get',
    params
  })
}

// 获取指定账号指定月份的视频
export function getVideosByMonth(accountId: string, year: string, month: string) {
  return request({
    url: `/api/video-collect/videos/${accountId}/${year}/${month}`,
    method: 'get'
  })
}

// 启动视频下载任务（从数据库）
export function startVideoDownloadTask(taskData: {
  task_id: string
  account_id: string
  download_path?: string
  max_downloads?: number
}) {
  return request({
    url: '/api/video-collect/download',
    method: 'post',
    data: taskData
  })
}

// 下载单个视频
export function downloadSingleVideo(videoId: string) {
  return request({
    url: `/api/video-collect/download/${videoId}`,
    method: 'post'
  })
}

// 批量下载视频
export function batchDownloadVideos(videoIds: string[]) {
  return request({
    url: '/api/video-collect/download/batch',
    method: 'post',
    data: {
      video_ids: videoIds
    }
  })
}

// 删除视频记录
export function deleteVideo(videoId: string) {
  return request({
    url: `/api/video-collect/videos/${videoId}`,
    method: 'delete'
  })
}

// 批量删除视频记录
export function batchDeleteVideos(videoIds: string[]) {
  return request({
    url: '/api/video-collect/videos/batch',
    method: 'delete',
    data: {
      video_ids: videoIds
    }
  })
}

// 更新视频信息
export function updateVideo(videoId: string, data: Partial<VideoInfo>) {
  return request({
    url: `/api/video-collect/videos/${videoId}`,
    method: 'put',
    data
  })
}

// 获取下载任务列表
export function getDownloadTasks(params?: {
  page?: number
  limit?: number
  status?: string
  account_id?: string
}) {
  return request({
    url: '/api/video-collect/download/tasks',
    method: 'get',
    params
  })
}

// 获取下载任务状态
export function getDownloadTaskStatus(taskId: string) {
  return request({
    url: `/api/video-collect/download/status/${taskId}`,
    method: 'get'
  })
}

// 取消下载任务
export function cancelDownloadTask(taskId: string) {
  return request({
    url: `/api/video-collect/download/cancel/${taskId}`,
    method: 'post'
  })
}

// 重新采集账号视频
export function recollectAccountVideos(accountId: string, collectConfig: VideoCollectTask['collect_config']) {
  return request({
    url: `/api/video-collect/recollect/${accountId}`,
    method: 'post',
    data: {
      collect_config: collectConfig
    }
  })
}

// 获取采集进度
export function getCollectProgress(taskId: string) {
  return request({
    url: `/api/video-collect/progress/${taskId}`,
    method: 'get'
  })
}

// 获取下载进度
export function getDownloadProgress(taskId: string) {
  return request({
    url: `/api/video-collect/download/progress/${taskId}`,
    method: 'get'
  })
}

// 清理失败的任务
export function cleanupFailedTasks() {
  return request({
    url: '/api/video-collect/cleanup',
    method: 'post'
  })
}

// 获取系统配置
export function getCollectConfig() {
  return request({
    url: '/api/video-collect/config',
    method: 'get'
  })
}

// 更新系统配置
export function updateCollectConfig(config: any) {
  return request({
    url: '/api/video-collect/config',
    method: 'put',
    data: config
  })
}

// 导出视频列表
export function exportVideoList(accountId: string, format: 'csv' | 'json' | 'excel' = 'csv') {
  return request({
    url: `/api/video-collect/export/${accountId}`,
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}

// 导入视频列表
export function importVideoList(accountId: string, file: File) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('account_id', accountId)
  
  return request({
    url: '/api/video-collect/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取热门视频
export function getPopularVideos(accountId: string, limit: number = 10) {
  return request({
    url: `/api/video-collect/popular/${accountId}`,
    method: 'get',
    params: { limit }
  })
}

// 获取视频趋势分析
export function getVideoTrends(accountId: string, timeRange: '7d' | '30d' | '90d' = '30d') {
  return request({
    url: `/api/video-collect/trends/${accountId}`,
    method: 'get',
    params: { time_range: timeRange }
  })
}

// 内容采集相关API
// 获取所有采集内容
export function getAllCollectedContent(params?: {
  page?: number
  limit?: number
  search?: string
  platform?: string
  account_id?: string
  status?: string
  date_range?: [string, string]
}) {
  return request({
    url: '/api/content-collect/all',
    method: 'get',
    params
  })
}

// 获取内容统计信息
export function getContentStatistics() {
  return request({
    url: '/api/content-collect/statistics',
    method: 'get'
  })
}

// 获取可用账号列表
export function getAvailableAccounts() {
  return request({
    url: '/api/content-collect/accounts',
    method: 'get'
  })
}

// 创建批量下载任务
export function createBatchDownloadTask(taskData: {
  content_ids: string[]
  download_config: any
}) {
  return request({
    url: '/api/content-collect/download/batch',
    method: 'post',
    data: taskData
  })
}

// 更新内容信息
export function updateContentInfo(contentId: string, data: any) {
  return request({
    url: `/api/content-collect/content/${contentId}`,
    method: 'put',
    data
  })
}

// 删除内容
export function deleteContent(contentId: string) {
  return request({
    url: `/api/content-collect/content/${contentId}`,
    method: 'delete'
  })
}

// 批量删除内容
export function batchDeleteContent(contentIds: string[]) {
  return request({
    url: '/api/content-collect/content/batch',
    method: 'delete',
    data: { content_ids: contentIds }
  })
}

// 导出内容数据
export function exportContentData(params?: {
  platform?: string
  account_id?: string
  format?: 'csv' | 'json' | 'excel'
}) {
  return request({
    url: '/api/content-collect/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
