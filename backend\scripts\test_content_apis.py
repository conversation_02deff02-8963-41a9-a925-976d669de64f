#!/usr/bin/env python3
"""
测试文件管理模块API
"""

import os
import sys
import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000"

# 测试用的认证token（需要先登录获取）
TEST_TOKEN = None


def get_auth_token():
    """获取认证token"""
    global TEST_TOKEN
    
    if TEST_TOKEN:
        return TEST_TOKEN
    
    # 登录获取token
    login_data = {
        "username": "test",
        "password": "test123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/token", data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            TEST_TOKEN = token_data["access_token"]
            print(f"✅ 获取认证token成功")
            return TEST_TOKEN
        else:
            print(f"❌ 获取认证token失败: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ 登录请求失败: {str(e)}")
        return None


def make_request(method, endpoint, data=None, params=None):
    """发送API请求"""
    token = get_auth_token()
    if not token:
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            print(f"❌ 不支持的HTTP方法: {method}")
            return None
        
        return response
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return None


def test_categories_api():
    """测试分类管理API"""
    print("\n🧪 测试分类管理API...")
    
    # 1. 获取分类树
    print("1. 获取分类树...")
    response = make_request("GET", "/api/v1/categories/tree")
    if response and response.status_code == 200:
        categories = response.json()
        print(f"✅ 获取分类树成功，共 {len(categories)} 个根分类")
        for cat in categories:
            print(f"   - {cat['name']} (内容数量: {cat['content_count']})")
    else:
        print(f"❌ 获取分类树失败: {response.status_code if response else 'No response'}")
    
    # 2. 创建测试分类
    print("2. 创建测试分类...")
    test_category = {
        "name": f"测试分类_{datetime.now().strftime('%H%M%S')}",
        "description": "这是一个测试分类",
        "color": "#FF5722",
        "icon": "test",
        "sort_order": 10
    }
    
    response = make_request("POST", "/api/v1/categories", test_category)
    if response and response.status_code == 200:
        result = response.json()
        category_id = result["category_id"]
        print(f"✅ 创建分类成功，ID: {category_id}")
        
        # 3. 更新分类
        print("3. 更新分类...")
        update_data = {
            "description": "更新后的描述",
            "color": "#2196F3"
        }
        
        response = make_request("PUT", f"/api/v1/categories/{category_id}", update_data)
        if response and response.status_code == 200:
            print("✅ 更新分类成功")
        else:
            print(f"❌ 更新分类失败: {response.status_code if response else 'No response'}")
        
        # 4. 删除分类
        print("4. 删除测试分类...")
        response = make_request("DELETE", f"/api/v1/categories/{category_id}")
        if response and response.status_code == 200:
            print("✅ 删除分类成功")
        else:
            print(f"❌ 删除分类失败: {response.status_code if response else 'No response'}")
    else:
        print(f"❌ 创建分类失败: {response.status_code if response else 'No response'}")


def test_content_api():
    """测试内容管理API"""
    print("\n🧪 测试内容管理API...")
    
    # 1. 获取内容列表
    print("1. 获取内容列表...")
    params = {
        "page": 1,
        "limit": 10
    }
    
    response = make_request("GET", "/api/v1/content/list", params=params)
    if response and response.status_code == 200:
        content_list = response.json()
        print(f"✅ 获取内容列表成功，共 {content_list['total']} 个内容")
        print(f"   当前页: {content_list['page']}, 每页: {content_list['limit']}")
        print(f"   是否有下一页: {content_list['has_next']}")
    else:
        print(f"❌ 获取内容列表失败: {response.status_code if response else 'No response'}")
    
    # 2. 获取内容统计
    print("2. 获取内容统计...")
    response = make_request("GET", "/api/v1/content/stats/summary")
    if response and response.status_code == 200:
        stats = response.json()
        print(f"✅ 获取内容统计成功")
        print(f"   总内容数: {stats['total_count']}")
        print(f"   最近7天新增: {stats['recent_count']}")
        if stats['platform_stats']:
            print("   平台统计:")
            for platform in stats['platform_stats'][:3]:
                print(f"     - {platform['_id']}: {platform['count']}")
    else:
        print(f"❌ 获取内容统计失败: {response.status_code if response else 'No response'}")


def test_download_tasks_api():
    """测试下载任务API"""
    print("\n🧪 测试下载任务API...")
    
    # 1. 获取下载任务列表
    print("1. 获取下载任务列表...")
    params = {
        "page": 1,
        "limit": 10
    }
    
    response = make_request("GET", "/api/v1/download/tasks", params=params)
    if response and response.status_code == 200:
        tasks = response.json()
        print(f"✅ 获取下载任务列表成功，共 {len(tasks)} 个任务")
        for task in tasks:
            print(f"   - {task['task_name']} ({task['status']})")
    else:
        print(f"❌ 获取下载任务列表失败: {response.status_code if response else 'No response'}")
    
    # 2. 创建测试下载任务
    print("2. 创建测试下载任务...")
    test_task = {
        "task_name": f"测试下载任务_{datetime.now().strftime('%H%M%S')}",
        "task_type": "single",
        "source_urls": ["https://example.com/test-video"],
        "target_platform": "youtube",
        "download_config": {
            "quality": "best",
            "format": "mp4",
            "include_subtitles": False,
            "include_thumbnail": True,
            "include_metadata": True
        }
    }
    
    response = make_request("POST", "/api/v1/download/tasks", test_task)
    if response and response.status_code == 200:
        result = response.json()
        task_id = result["task_id"]
        print(f"✅ 创建下载任务成功，ID: {task_id}")
        
        # 3. 获取任务详情
        print("3. 获取任务详情...")
        response = make_request("GET", f"/api/v1/download/tasks/{task_id}")
        if response and response.status_code == 200:
            task_detail = response.json()
            print(f"✅ 获取任务详情成功")
            print(f"   任务名称: {task_detail['task_name']}")
            print(f"   任务状态: {task_detail['status']}")
            print(f"   进度: {task_detail['progress']['percentage']:.1f}%")
        else:
            print(f"❌ 获取任务详情失败: {response.status_code if response else 'No response'}")
        
        # 4. 取消任务
        print("4. 取消测试任务...")
        response = make_request("POST", f"/api/v1/download/tasks/{task_id}/cancel")
        if response and response.status_code == 200:
            print("✅ 取消任务成功")
        else:
            print(f"❌ 取消任务失败: {response.status_code if response else 'No response'}")
    else:
        print(f"❌ 创建下载任务失败: {response.status_code if response else 'No response'}")


def test_server_connection():
    """测试服务器连接"""
    print("🔗 测试服务器连接...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        print(f"请确保服务器正在运行在 {BASE_URL}")
        return False


def main():
    """主函数"""
    print("🚀 开始测试文件管理模块API...")
    print(f"服务器地址: {BASE_URL}")
    
    # 测试服务器连接
    if not test_server_connection():
        print("\n❌ 服务器连接失败，请检查服务器是否正在运行")
        sys.exit(1)
    
    # 运行各项测试
    try:
        test_categories_api()
        test_content_api()
        test_download_tasks_api()
        
        print("\n✅ 所有API测试完成！")
        print("\n📝 测试总结:")
        print("- 分类管理API: 创建、获取、更新、删除功能正常")
        print("- 内容管理API: 列表获取、统计功能正常")
        print("- 下载任务API: 创建、获取、取消功能正常")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")


if __name__ == "__main__":
    main()
