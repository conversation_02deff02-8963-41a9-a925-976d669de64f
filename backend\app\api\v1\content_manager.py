"""
内容管理API
用于管理竞品内容的CRUD操作
"""

import os
import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from pymongo.database import Database
from bson import ObjectId
from datetime import datetime

from app.core.security import get_current_user
from app.core.schemas.content_models import (
    CompetitorContent, ContentListRequest, ContentListResponse,
    ContentCategory, CategoryTreeNode
)

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/content",
    tags=["content-manager"],
    dependencies=[Depends(get_current_user)]
)


def get_db(request: Request) -> Database:
    """获取数据库连接"""
    return request.app.state.mongo_db


@router.get("/list", response_model=ContentListResponse)
async def get_content_list(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    platform: Optional[str] = Query(None, description="平台过滤"),
    category: Optional[str] = Query(None, description="分类过滤"),
    tags: Optional[str] = Query(None, description="标签过滤，逗号分隔"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    content_type: Optional[str] = Query(None, description="内容类型过滤"),
    db: Database = Depends(get_db)
):
    """
    获取竞品内容列表
    
    支持多种过滤条件：
    - platform: 平台过滤 (youtube, tiktok, instagram等)
    - category: 分类过滤
    - tags: 标签过滤，多个标签用逗号分隔
    - search: 在标题和描述中搜索关键词
    - content_type: 内容类型过滤 (video, image, audio, text)
    """
    try:
        logger.info(f"获取内容列表: page={page}, limit={limit}, platform={platform}, category={category}")
        
        # 构建查询条件
        query = {}
        
        if platform:
            query["platform"] = platform
            
        if category:
            query["metadata.category"] = category
            
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
            if tag_list:
                query["metadata.tags"] = {"$in": tag_list}
                
        if content_type:
            query["content_type"] = content_type
            
        if search:
            query["$or"] = [
                {"title": {"$regex": search, "$options": "i"}},
                {"description": {"$regex": search, "$options": "i"}}
            ]
        
        # 计算跳过的文档数
        skip = (page - 1) * limit
        
        # 查询总数
        total = await db.competitor_content.count_documents(query)

        # 查询内容列表
        cursor = db.competitor_content.find(query).skip(skip).limit(limit).sort("created_at", -1)
        contents = await cursor.to_list(length=limit)
        
        # 转换ObjectId为字符串
        for content in contents:
            content["_id"] = str(content["_id"])
            if "metadata" in content and "category_id" in content["metadata"]:
                if isinstance(content["metadata"]["category_id"], ObjectId):
                    content["metadata"]["category_id"] = str(content["metadata"]["category_id"])
        
        # 计算是否有下一页
        has_next = skip + limit < total
        
        return ContentListResponse(
            items=contents,
            total=total,
            page=page,
            limit=limit,
            has_next=has_next
        )
        
    except Exception as e:
        logger.error(f"获取内容列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取内容列表失败: {str(e)}")


@router.get("/{content_id}")
async def get_content_detail(
    content_id: str,
    db: Database = Depends(get_db)
):
    """获取内容详情"""
    try:
        logger.info(f"获取内容详情: {content_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(content_id):
            raise HTTPException(status_code=400, detail="无效的内容ID格式")
        
        content = await db.competitor_content.find_one({"_id": ObjectId(content_id)})
        
        if not content:
            raise HTTPException(status_code=404, detail="内容不存在")
        
        # 转换ObjectId为字符串
        content["_id"] = str(content["_id"])
        
        return content
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取内容详情失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取内容详情失败: {str(e)}")


@router.put("/{content_id}")
async def update_content(
    content_id: str,
    update_data: Dict[str, Any],
    db: Database = Depends(get_db)
):
    """更新内容信息"""
    try:
        logger.info(f"更新内容: {content_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(content_id):
            raise HTTPException(status_code=400, detail="无效的内容ID格式")
        
        # 添加更新时间
        update_data["updated_at"] = datetime.now()
        
        result = await db.competitor_content.update_one(
            {"_id": ObjectId(content_id)},
            {"$set": update_data}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="内容不存在")
        
        return {"message": "内容更新成功", "modified_count": result.modified_count}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新内容失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新内容失败: {str(e)}")


@router.delete("/{content_id}")
async def delete_content(
    content_id: str,
    db: Database = Depends(get_db)
):
    """删除内容"""
    try:
        logger.info(f"删除内容: {content_id}")
        
        # 验证ObjectId格式
        if not ObjectId.is_valid(content_id):
            raise HTTPException(status_code=400, detail="无效的内容ID格式")
        
        # 先获取内容信息，用于删除文件
        content = await db.competitor_content.find_one({"_id": ObjectId(content_id)})
        if not content:
            raise HTTPException(status_code=404, detail="内容不存在")
        
        # 删除数据库记录
        result = await db.competitor_content.delete_one({"_id": ObjectId(content_id)})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="内容不存在")
        
        # 尝试删除本地文件
        try:
            if "file_info" in content and "local_path" in content["file_info"]:
                local_path = content["file_info"]["local_path"]
                if os.path.exists(local_path):
                    os.remove(local_path)
                    logger.info(f"已删除本地文件: {local_path}")
        except Exception as file_error:
            logger.warning(f"删除本地文件失败: {str(file_error)}")
        
        return {"message": "内容删除成功", "deleted_count": result.deleted_count}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除内容失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除内容失败: {str(e)}")


@router.post("/batch")
async def batch_operation(
    operation_data: Dict[str, Any],
    db: Database = Depends(get_db)
):
    """批量操作内容"""
    try:
        action = operation_data.get("action")
        content_ids = operation_data.get("content_ids", [])
        data = operation_data.get("data", {})
        
        if not action or not content_ids:
            raise HTTPException(status_code=400, detail="缺少必要参数")
        
        logger.info(f"批量操作: action={action}, count={len(content_ids)}")
        
        # 验证所有ID格式
        object_ids = []
        for content_id in content_ids:
            if not ObjectId.is_valid(content_id):
                raise HTTPException(status_code=400, detail=f"无效的内容ID格式: {content_id}")
            object_ids.append(ObjectId(content_id))
        
        if action == "delete":
            # 批量删除
            result = await db.competitor_content.delete_many({"_id": {"$in": object_ids}})
            return {"message": f"批量删除成功", "deleted_count": result.deleted_count}
            
        elif action == "categorize":
            # 批量分类
            category = data.get("category")
            if not category:
                raise HTTPException(status_code=400, detail="缺少分类信息")
            
            update_data = {
                "metadata.category": category,
                "updated_at": datetime.now()
            }
            result = await db.competitor_content.update_many(
                {"_id": {"$in": object_ids}},
                {"$set": update_data}
            )
            return {"message": f"批量分类成功", "modified_count": result.modified_count}
            
        elif action == "tag":
            # 批量标签
            tags = data.get("tags", [])
            if not tags:
                raise HTTPException(status_code=400, detail="缺少标签信息")
            
            result = await db.competitor_content.update_many(
                {"_id": {"$in": object_ids}},
                {
                    "$addToSet": {"metadata.tags": {"$each": tags}},
                    "$set": {"updated_at": datetime.now()}
                }
            )
            return {"message": f"批量标签成功", "modified_count": result.modified_count}
            
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {action}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量操作失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量操作失败: {str(e)}")


@router.get("/stats/summary")
async def get_content_stats(
    db: Database = Depends(get_db)
):
    """获取内容统计信息"""
    try:
        logger.info("获取内容统计信息")
        
        # 总内容数
        total_count = await db.competitor_content.count_documents({})

        # 按平台统计
        platform_stats = []
        async for doc in db.competitor_content.aggregate([
            {"$group": {"_id": "$platform", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]):
            platform_stats.append(doc)

        # 按内容类型统计
        type_stats = []
        async for doc in db.competitor_content.aggregate([
            {"$group": {"_id": "$content_type", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]):
            type_stats.append(doc)

        # 按分类统计
        category_stats = []
        async for doc in db.competitor_content.aggregate([
            {"$group": {"_id": "$metadata.category", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": 10}
        ]):
            category_stats.append(doc)

        # 最近7天新增内容
        from datetime import timedelta
        seven_days_ago = datetime.now() - timedelta(days=7)
        recent_count = await db.competitor_content.count_documents({
            "created_at": {"$gte": seven_days_ago}
        })
        
        return {
            "total_count": total_count,
            "platform_stats": platform_stats,
            "type_stats": type_stats,
            "category_stats": category_stats,
            "recent_count": recent_count
        }
        
    except Exception as e:
        logger.error(f"获取内容统计失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取内容统计失败: {str(e)}")
