syntax = "proto3";

package device;

// 设备服务
service DeviceService {
  // 获取设备列表
  rpc GetDeviceList(DeviceListRequest) returns (DeviceListResponse) {}
  
  // 启动设备
  rpc StartDevice(DeviceRequest) returns (DeviceResponse) {}
  
  // 停止设备
  rpc StopDevice(DeviceRequest) returns (DeviceResponse) {}
  
  // 重启设备
  rpc RestartDevice(DeviceRequest) returns (DeviceResponse) {}
  
  // 获取设备信息
  rpc GetDeviceInfo(DeviceRequest) returns (DeviceInfoResponse) {}
  
  // 创建新设备
  rpc CreateDevice(CreateDeviceRequest) returns (CreateDeviceResponse) {}
  
  // 删除设备
  rpc RemoveDevice(DeviceRequest) returns (DeviceResponse) {}
  
  // 执行设备命令
  rpc ExecuteCommand(CommandRequest) returns (CommandResponse) {}
}

// 设备列表请求
message DeviceListRequest {
  // 可选的过滤条件
  string filter = 1;
}

// 设备列表响应
message DeviceListResponse {
  // 设备列表
  repeated Device devices = 1;
}

// 设备请求
message DeviceRequest {
  // 设备ID
  string device_id = 1;
}

// 设备响应
message DeviceResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
}

// 设备信息响应
message DeviceInfoResponse {
  // 设备信息
  Device device = 1;
}

// 创建设备请求
message CreateDeviceRequest {
  // 设备名称
  string name = 1;
  // 分辨率
  string resolution = 2;
  // CPU核心数
  int32 cpu_count = 3;
  // 内存大小(MB)
  int32 memory_size = 4;
  // 制造商
  string manufacturer = 5;
  // 型号
  string model = 6;
}

// 创建设备响应
message CreateDeviceResponse {
  // 是否成功
  bool success = 1;
  // 设备ID
  string device_id = 2;
  // 错误信息
  string error = 3;
}

// 命令请求
message CommandRequest {
  // 设备ID
  string device_id = 1;
  // 命令名称
  string command = 2;
  // 命令参数
  map<string, string> params = 3;
}

// 命令响应
message CommandResponse {
  // 是否成功
  bool success = 1;
  // 输出结果
  string output = 2;
  // 错误信息
  string error = 3;
}

// 设备信息
message Device {
  // 设备ID
  string device_id = 1;
  // 设备名称
  string name = 2;
  // 设备状态
  string status = 3;
  // 设备类型
  string device_type = 4;
  // 窗口信息
  map<string, string> window_info = 5;
  // 进程信息
  map<string, string> process_info = 6;
  // 显示信息
  map<string, string> display_info = 7;
}
