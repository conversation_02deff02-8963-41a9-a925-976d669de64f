{"platforms": [{"id": "youtube", "name": "YouTube", "icon": "youtube-icon.png", "website": "https://www.youtube.com", "status": "active", "features": ["video", "live", "comment"], "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}], "platform_apps": [{"id": "youtube_android", "platform_id": "youtube", "name": "YouTube Android App", "type": "android", "status": "active", "version": "18.20.38", "app_info": {"package_name": "com.google.android.youtube", "main_activity": "com.google.android.youtube.HomeActivity", "min_android_version": "8.0", "download_url": "https://play.google.com/store/apps/details?id=com.google.android.youtube"}, "automation": {"type": "appium", "selectors": {"login_button": "//android.widget.Button[@resource-id='com.google.android.youtube:id/sign_in_button']", "post_button": "//android.widget.ImageView[@content-desc='创建']", "comment_button": "//android.widget.ImageView[@content-desc='评论']", "like_button": "//android.widget.ImageView[@content-desc='顶']", "share_button": "//android.widget.ImageView[@content-desc='分享']"}, "actions": {"login": [{"action": "click", "selector": "//android.widget.Button[@resource-id='com.google.android.youtube:id/sign_in_button']", "value": ""}, {"action": "input", "selector": "//android.widget.EditText[@resource-id='identifierId']", "value": "{username}"}], "post": [{"action": "click", "selector": "//android.widget.ImageView[@content-desc='创建']", "value": ""}, {"action": "click", "selector": "//android.widget.TextView[@text='上传视频']", "value": ""}]}, "commands": {"start_app": "adb shell am start -n com.google.android.youtube/com.google.android.youtube.HomeActivity", "stop_app": "adb shell am force-stop com.google.android.youtube", "clear_data": "adb shell pm clear com.google.android.youtube"}}, "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}, {"id": "youtube_web", "platform_id": "youtube", "name": "YouTube Web", "type": "web", "status": "active", "version": "latest", "app_info": {"download_url": "https://www.youtube.com"}, "automation": {"type": "selenium", "selectors": {"login_button": "//a[contains(@href, 'signin')]", "post_button": "//a[contains(@href, 'upload')]", "comment_button": "//div[@id='comment-section']", "like_button": "//button[@aria-label='Like']", "share_button": "//button[@aria-label='Share']"}}, "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}], "accounts": [{"id": "acc_youtube_EveryKucharski402", "username": "<EMAIL>", "password": "Longer889614$", "recovery_email": "<PERSON><EMAIL>", "recovery_code": "Kkoazf465k", "display_name": "A-HK-0-1-00", "platform_id": "youtube", "platform_name": "YouTube", "core_service_id": "default", "status": "active", "description": "mn", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}, {"id": "acc_youtube_zzy773454", "username": "<EMAIL>", "password": "ug0aewb1w1s7geg7", "recovery_email": "<EMAIL>", "display_name": "A-HK-0-1-01", "platform_id": "youtube", "platform_name": "YouTube", "core_service_id": "default", "status": "active", "description": "观察团", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}, {"id": "acc_youtube_liangjiang44444", "username": "<EMAIL>", "password": "enhvyt846z6qv6pg", "recovery_email": "<EMAIL>", "recovery_code": "***********", "display_name": "A-HK-0-1-02", "platform_id": "youtube", "platform_name": "YouTube", "core_service_id": "default", "status": "active", "description": "jhlx h3ls twhb ehzr 5ph7 zdyp yxyf d2oa", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}, {"id": "acc_youtube_Aed950049", "username": "<EMAIL>", "password": "zjvRDSHgrEZq", "recovery_email": "<EMAIL>", "display_name": "A-HK-0-1-02-AU01", "platform_id": "youtube", "platform_name": "YouTube", "core_service_id": "default", "status": "active", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}, {"id": "acc_youtube_linm72224", "username": "<EMAIL>", "password": "goyzhd51k9e4m44r", "recovery_code": "8525270 5364", "display_name": "A-HK-0-2-3", "platform_id": "youtube", "platform_name": "YouTube", "core_service_id": "default", "status": "active", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}], "device_account_mappings": [{"device_id": "1", "account_id": "acc_youtube_EveryKucharski402", "platform_id": "youtube", "app_id": "youtube_android", "status": "active", "core_service_id": "default", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00", "last_used": "2023-08-15T00:00:00"}, {"device_id": "2", "account_id": "acc_youtube_zzy773454", "platform_id": "youtube", "app_id": "youtube_android", "status": "active", "core_service_id": "default", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00", "last_used": "2023-08-15T00:00:00"}, {"device_id": "3", "account_id": "acc_youtube_liangjiang44444", "platform_id": "youtube", "app_id": "youtube_android", "status": "active", "core_service_id": "default", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00", "last_used": "2023-08-15T00:00:00"}, {"device_id": "4", "account_id": "acc_youtube_Aed950049", "platform_id": "youtube", "app_id": "youtube_android", "status": "active", "core_service_id": "default", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00", "last_used": "2023-08-15T00:00:00"}, {"device_id": "5", "account_id": "acc_youtube_linm72224", "platform_id": "youtube", "app_id": "youtube_android", "status": "active", "core_service_id": "default", "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00", "last_used": "2023-08-15T00:00:00"}], "export_time": "2023-08-15T00:00:00"}