#!/usr/bin/env python3
"""
视频旋转功能测试脚本
"""

import asyncio
import os
import sys
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.video_rotation_service import VideoRotationService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_video_rotation():
    """测试视频旋转功能"""
    
    # 创建视频旋转服务实例
    rotation_service = VideoRotationService()
    
    # 测试参数验证
    print("=== 测试参数验证 ===")
    
    # 测试空文件列表
    try:
        result = await rotation_service.rotate_videos([], 90)
        print(f"空文件列表测试: {result['success']}")
    except Exception as e:
        print(f"空文件列表测试异常: {e}")
    
    # 测试无效旋转角度
    try:
        result = await rotation_service.rotate_videos(['test.mp4'], 45)
        print(f"无效角度测试: {result['success']}")
    except Exception as e:
        print(f"无效角度测试异常: {e}")
    
    # 测试旋转滤镜生成
    print("\n=== 测试旋转滤镜生成 ===")
    try:
        filter_90 = rotation_service.get_rotation_filter(90)
        print(f"90度旋转滤镜: {filter_90}")
        
        filter_neg90 = rotation_service.get_rotation_filter(-90)
        print(f"-90度旋转滤镜: {filter_neg90}")
        
        filter_180 = rotation_service.get_rotation_filter(180)
        print(f"180度旋转滤镜: {filter_180}")
    except Exception as e:
        print(f"滤镜生成测试异常: {e}")
    
    # 测试输出路径生成
    print("\n=== 测试输出路径生成 ===")
    test_cases = [
        ("C:/test/video.mp4", False, "_rotated"),
        ("C:/test/video.mp4", True, "_rotated"),
        ("/home/<USER>/video.avi", False, "_turned"),
    ]
    
    for input_path, overwrite, suffix in test_cases:
        output_path = rotation_service.get_output_path(input_path, overwrite, suffix)
        print(f"输入: {input_path}, 覆盖: {overwrite}, 后缀: {suffix} -> 输出: {output_path}")
    
    # 测试视频文件检测
    print("\n=== 测试视频文件检测 ===")
    test_files = [
        "video.mp4",
        "video.avi", 
        "video.mov",
        "image.jpg",
        "document.txt",
        "video.mkv"
    ]
    
    for file_path in test_files:
        # 创建临时文件用于测试
        temp_file = f"/tmp/{file_path}" if os.name != 'nt' else f"C:\\temp\\{file_path}"
        try:
            os.makedirs(os.path.dirname(temp_file), exist_ok=True)
            with open(temp_file, 'w') as f:
                f.write("test")
            
            is_video = rotation_service.is_video_file(temp_file)
            print(f"{file_path}: {'是视频文件' if is_video else '不是视频文件'}")
            
            # 清理临时文件
            os.remove(temp_file)
        except Exception as e:
            print(f"测试文件 {file_path} 时出错: {e}")
    
    print("\n=== 视频旋转功能测试完成 ===")
    print("注意：实际的视频旋转需要ffmpeg和真实的视频文件")

if __name__ == "__main__":
    asyncio.run(test_video_rotation())
