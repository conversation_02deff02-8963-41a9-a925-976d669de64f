# 抖音视频采集配置

# 采集模式配置
collect_modes:
  # 模式1: 只采集基础信息到数据库
  basic_collect:
    name: "基础信息采集"
    description: "只采集视频基础信息（标题、链接、封面等）到数据库"
    get_real_urls: false
    save_to_database: true
    download_files: false
    
  # 模式2: 采集信息并获取真实下载地址
  full_collect:
    name: "完整信息采集"
    description: "采集视频基础信息并获取真实下载地址"
    get_real_urls: true
    save_to_database: true
    download_files: false
    
  # 模式3: 采集并下载视频文件
  collect_and_download:
    name: "采集并下载"
    description: "采集视频信息并下载视频文件"
    get_real_urls: true
    save_to_database: true
    download_files: true

# 月份筛选配置
month_filter:
  enabled: true
  # 指定要采集的月份（为空则采集所有可用月份）
  target_months: []
  # 示例：指定特定月份
  # target_months:
  #   - year: "2025"
  #     month: "04"
  #   - year: "2025"
  #     month: "05"

# 采集限制
limits:
  max_videos_per_month: 100  # 每个月份最大采集数量
  max_total_videos: 500      # 总最大采集数量
  max_concurrent_requests: 3  # 最大并发请求数
  request_delay: 2           # 请求间隔（秒）

# 数据库配置
database:
  collection_name: "douyin_videos"
  indexes:
    - fields: {"video_id": 1}
      unique: true
    - fields: {"account_id": 1, "year": 1, "month": 1}
    - fields: {"download_status": 1}
    - fields: {"created_at": -1}

# 文件下载配置
download:
  chunk_size: 8192
  timeout: 30
  retry_count: 3
  verify_file_size: true
  
# 视频质量选择
quality:
  preferred: "high"  # high, medium, low
  fallback: true     # 如果首选质量不可用，是否使用其他质量

# 文件命名规则
naming:
  pattern: "{video_id}_{timestamp}.mp4"
  # 可用变量：
  # {video_id} - 视频ID
  # {title} - 视频标题（会清理特殊字符）
  # {timestamp} - 时间戳
  # {year} - 年份
  # {month} - 月份
  # {like_count} - 点赞数

# 目录结构
directory_structure:
  base: "{download_path}"
  videos: "{base}/videos"
  metadata: "{base}/metadata"
  covers: "{base}/covers"
  logs: "{base}/logs"
  
  # 按月份组织
  monthly_structure:
    enabled: true
    pattern: "{base}/{year}/{month}"

# 元数据保存
metadata:
  save_json: true
  save_cover: true
  include_account_info: true
  include_statistics: true

# 错误处理
error_handling:
  continue_on_error: true
  max_consecutive_errors: 5
  error_log_detail: true
  
# 进度报告
progress:
  log_interval: 10  # 每处理多少个视频记录一次进度
  save_checkpoint: true
  checkpoint_interval: 50

# 工作流步骤定义
workflow_steps:
  1:
    name: "初始化浏览器"
    description: "启动Playwright浏览器"
    required: true
    
  2:
    name: "访问账号页面"
    description: "访问抖音账号主页"
    required: true
    
  3:
    name: "获取账号信息"
    description: "提取账号基础信息"
    required: true
    
  4:
    name: "检测月份筛选"
    description: "检查是否支持按月份筛选"
    required: false
    
  5:
    name: "采集视频列表"
    description: "获取视频基础信息列表"
    required: true
    
  6:
    name: "保存到数据库"
    description: "将视频信息保存到数据库"
    required: true
    condition: "save_to_database"
    
  7:
    name: "获取真实地址"
    description: "获取视频真实下载地址"
    required: false
    condition: "get_real_urls"
    
  8:
    name: "下载视频文件"
    description: "下载视频文件到本地"
    required: false
    condition: "download_files"
    
  9:
    name: "生成报告"
    description: "生成采集和下载报告"
    required: true

# 监控和统计
monitoring:
  track_performance: true
  track_success_rate: true
  track_error_types: true
  
  # 统计指标
  metrics:
    - name: "videos_collected"
      description: "采集的视频数量"
    - name: "real_urls_obtained"
      description: "获取真实地址的数量"
    - name: "files_downloaded"
      description: "下载的文件数量"
    - name: "total_file_size"
      description: "下载文件总大小"
    - name: "average_download_speed"
      description: "平均下载速度"

# 调试配置
debug:
  enabled: false
  save_screenshots: true
  save_page_source: false
  verbose_logging: true
  browser_headless: false  # 调试时显示浏览器界面
