@echo off
echo ========================================
echo ThunderHub 服务启动脚本
echo ========================================
echo.

echo 正在启动服务...
echo.

echo [1/3] 启动 Backend 服务 (端口 8000)...
start "ThunderHub Backend" cmd /k "cd /d %~dp0backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"
timeout /t 3 /nobreak >nul

echo [2/3] 启动 Core 服务 (端口 50051 和 8001)...
start "ThunderHub Core" cmd /k "cd /d %~dp0core && python src/run.py"
timeout /t 3 /nobreak >nul

echo [3/3] 启动 Frontend 服务 (端口 3000)...
start "ThunderHub Frontend" cmd /k "cd /d %~dp0frontend && npm run dev"
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo.
echo 请等待几秒钟让服务完全启动，然后访问：
echo.
echo Frontend:  http://localhost:3000
echo Backend:   http://localhost:8000/docs
echo File Server: http://localhost:8001/health
echo.
echo 如果遇到视频加载问题，请检查：
echo 1. 所有服务是否正常启动
echo 2. 防火墙是否阻止了端口访问
echo 3. 视频文件路径是否正确
echo.
echo 按任意键关闭此窗口...
pause >nul
