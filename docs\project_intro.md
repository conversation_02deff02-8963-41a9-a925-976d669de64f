# ThunderHub 项目简介

## 项目概述
ThunderHub 是一个基于 Appium 和 LDPlayer 的 Android 应用自动化测试框架，支持多实例并行执行(MCP)。项目采用模块化设计，提供完整的自动化测试解决方案。

## 核心功能
- 多实例并行测试能力
- Appium 驱动封装
- LDPlayer 模拟器管理
- Allure 测试报告生成
- 完善的日志记录系统
- 模块化架构，易于扩展

## 技术栈
- **测试框架**: Appium + Pytest
- **模拟器管理**: LDPlayer
- **并行控制**: MCP 协议
- **报告系统**: Allure
- **前端**: Vue.js
- **后端**: FastAPI

## 适用场景
- 移动应用自动化测试
- 多设备并行测试
- 持续集成/持续部署(CI/CD)
- 性能测试
- 兼容性测试