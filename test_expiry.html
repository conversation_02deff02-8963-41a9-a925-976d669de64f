<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号过期状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        /* 整行背景色样式 */
        .test-item.row-expired {
            background-color: #fef0f0;
            border-color: #f56c6c;
        }

        .test-item.row-warning-1 {
            background-color: #fdf6ec;
            border-color: #e6a23c;
        }

        .test-item.row-warning-2 {
            background-color: #fef5f5;
            border-color: #f78989;
        }

        .test-item.row-warning-3 {
            background-color: #fffbf0;
            border-color: #b88230;
        }
        .status-tag {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .date-input {
            margin: 20px 0;
        }
        .date-input input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-left: 10px;
        }
        .date-input button {
            padding: 8px 16px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            margin-left: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>账号过期状态测试</h1>
        
        <div class="date-input">
            <label>测试日期:</label>
            <input type="date" id="testDate" />
            <button onclick="testDate()">测试</button>
        </div>
        
        <div id="testResults">
            <!-- 测试结果将显示在这里 -->
        </div>
        
        <h2>预设测试案例</h2>
        <div id="presetTests">
            <!-- 预设测试案例将显示在这里 -->
        </div>
    </div>

    <script>
        // 过期状态枚举
        const ExpiryStatus = {
            NORMAL: 'normal',
            WARNING_3: 'warning_3',
            WARNING_2: 'warning_2',
            WARNING_1: 'warning_1',
            EXPIRED: 'expired'
        };

        // 计算账号过期状态
        function calculateExpiryStatus(lastUpdatedDate) {
            if (!lastUpdatedDate) {
                return {
                    status: ExpiryStatus.NORMAL,
                    daysRemaining: 0,
                    message: '未设置更新日期',
                    color: '#909399',
                    bgColor: '#f4f4f5'
                };
            }

            const now = new Date();
            const updateDate = new Date(lastUpdatedDate);
            
            const timeDiff = updateDate.getTime() - now.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

            if (daysDiff < 0) {
                return {
                    status: ExpiryStatus.EXPIRED,
                    daysRemaining: daysDiff,
                    message: `已过期 ${Math.abs(daysDiff)} 天`,
                    color: '#ffffff',
                    bgColor: '#f56c6c'
                };
            } else if (daysDiff === 0) {
                return {
                    status: ExpiryStatus.WARNING_1,
                    daysRemaining: daysDiff,
                    message: '今天过期',
                    color: '#ffffff',
                    bgColor: '#e6a23c'
                };
            } else if (daysDiff === 1) {
                return {
                    status: ExpiryStatus.WARNING_1,
                    daysRemaining: daysDiff,
                    message: '1天后过期',
                    color: '#ffffff',
                    bgColor: '#e6a23c'
                };
            } else if (daysDiff === 2) {
                return {
                    status: ExpiryStatus.WARNING_2,
                    daysRemaining: daysDiff,
                    message: '2天后过期',
                    color: '#ffffff',
                    bgColor: '#f78989'
                };
            } else if (daysDiff === 3) {
                return {
                    status: ExpiryStatus.WARNING_3,
                    daysRemaining: daysDiff,
                    message: '3天后过期',
                    color: '#606266',
                    bgColor: '#fdf6ec'
                };
            } else {
                return {
                    status: ExpiryStatus.NORMAL,
                    daysRemaining: daysDiff,
                    message: `${daysDiff}天后过期`,
                    color: '#67c23a',
                    bgColor: '#f0f9ff'
                };
            }
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        }

        // 获取行类名
        function getRowClassName(status) {
            switch (status) {
                case 'expired':
                    return 'row-expired';
                case 'warning_1':
                    return 'row-warning-1';
                case 'warning_2':
                    return 'row-warning-2';
                case 'warning_3':
                    return 'row-warning-3';
                default:
                    return '';
            }
        }

        // 创建测试项
        function createTestItem(label, date) {
            const expiryInfo = calculateExpiryStatus(date);
            const rowClass = getRowClassName(expiryInfo.status);

            return `
                <div class="test-item ${rowClass}">
                    <div>
                        <strong>${label}</strong><br>
                        <small>更新日期: ${formatDate(date)}</small>
                    </div>
                    <div style="color: ${expiryInfo.color}; font-weight: 600;">
                        ${expiryInfo.message}
                    </div>
                </div>
            `;
        }

        // 测试指定日期
        function testDate() {
            const dateInput = document.getElementById('testDate');
            const testResults = document.getElementById('testResults');
            
            if (dateInput.value) {
                testResults.innerHTML = createTestItem('自定义测试', dateInput.value);
            } else {
                testResults.innerHTML = '<p style="color: red;">请选择一个日期</p>';
            }
        }

        // 初始化预设测试
        function initPresetTests() {
            const today = new Date();
            const presetTests = document.getElementById('presetTests');
            
            const testCases = [
                { label: '已过期5天', days: -5 },
                { label: '已过期1天', days: -1 },
                { label: '今天过期', days: 0 },
                { label: '1天后过期', days: 1 },
                { label: '2天后过期', days: 2 },
                { label: '3天后过期', days: 3 },
                { label: '7天后过期', days: 7 },
                { label: '未设置日期', days: null }
            ];
            
            let html = '';
            testCases.forEach(testCase => {
                let testDate = null;
                if (testCase.days !== null) {
                    testDate = new Date(today);
                    testDate.setDate(today.getDate() + testCase.days);
                    testDate = testDate.toISOString().split('T')[0];
                }
                html += createTestItem(testCase.label, testDate);
            });
            
            presetTests.innerHTML = html;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPresetTests);
    </script>
</body>
</html>
