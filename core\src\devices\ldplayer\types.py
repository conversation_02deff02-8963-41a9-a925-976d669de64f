"""
雷电模拟器类型定义
"""

from typing import Dict, List, Any, Optional, TypedDict

class LDPlayerConfig(TypedDict, total=False):
    """雷电模拟器配置类型"""
    device_id: str  # 设备ID
    name: str  # 设备名称
    ldconsole_path: str  # ldconsole.exe路径
    resolution: str  # 分辨率，如"1280,720,320"
    cpu_count: int  # CPU核心数
    memory_size: int  # 内存大小(MB)
    manufacturer: str  # 制造商
    model: str  # 型号
    pnumber: str  # 手机号码
    imei: str  # IMEI号
    mac_address: str  # MAC地址
    android_id: str  # Android ID
    auto_start: bool  # 是否自动启动
