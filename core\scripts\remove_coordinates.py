#!/usr/bin/env python3
"""
移除元素配置文件中的所有坐标操作
"""

import yaml
import sys
import os

def remove_coordinate_operations(config_path):
    """移除配置文件中的坐标操作"""
    
    # 读取配置文件
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    if 'elements' not in config:
        print("配置文件中没有找到elements节点")
        return
    
    removed_count = 0
    
    # 遍历所有元素
    for element_name, element_config in config['elements'].items():
        if not isinstance(element_config, list):
            continue
            
        # 过滤掉坐标操作
        original_count = len(element_config)
        filtered_config = []
        
        for method in element_config:
            if isinstance(method, dict) and method.get('type') in ['coordinate', 'coordinate_relative']:
                print(f"移除 {element_name} 的坐标操作: {method.get('description', 'N/A')}")
                removed_count += 1
            else:
                filtered_config.append(method)
        
        # 更新配置
        config['elements'][element_name] = filtered_config
        
        if len(filtered_config) != original_count:
            print(f"✅ {element_name}: {original_count} -> {len(filtered_config)} 个定位方法")
    
    # 写回配置文件
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    print(f"\n🎉 完成！总共移除了 {removed_count} 个坐标操作")

if __name__ == "__main__":
    config_path = "core/config/platforms/youtube/elements.yaml"
    
    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        sys.exit(1)
    
    # 备份原文件
    backup_path = config_path + ".backup"
    with open(config_path, 'r', encoding='utf-8') as src, open(backup_path, 'w', encoding='utf-8') as dst:
        dst.write(src.read())
    print(f"📋 已备份原文件到: {backup_path}")
    
    # 移除坐标操作
    remove_coordinate_operations(config_path)
