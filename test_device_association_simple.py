#!/usr/bin/env python3
"""
简单测试IP管理模块的关联设备功能
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from motor.motor_asyncio import AsyncIOMotorClient
from datetime import datetime
from backend.app.core.schemas.proxy_models import ProxyIPService

# 数据库配置
MONGODB_URL = "mongodb://***************:27017"
MONGODB_NAME = "thunderhub"

async def test_device_association():
    """测试设备关联功能"""
    print("开始测试IP管理模块的关联设备功能...")
    
    # 连接数据库
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[MONGODB_NAME]
    
    try:
        # 创建服务实例
        proxy_service = ProxyIPService(db)
        
        # 1. 创建测试设备数据
        print("\n1. 创建测试设备数据...")
        test_devices = [
            {
                "_id": "test_device_1",
                "name": "测试设备1",
                "status": "running",
                "type": "雷电模拟器",
                "core_id": "test_core",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "_id": "test_device_2", 
                "name": "测试设备2",
                "status": "starting",
                "type": "雷电模拟器",
                "core_id": "test_core",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "_id": "test_device_3",
                "name": "测试设备3", 
                "status": "stopped",
                "type": "雷电模拟器",
                "core_id": "test_core",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        ]
        
        # 清理旧的测试数据
        await db.devices.delete_many({"_id": {"$in": ["test_device_1", "test_device_2", "test_device_3"]}})
        
        # 插入测试设备
        await db.devices.insert_many(test_devices)
        print("✓ 测试设备数据创建成功")
        
        # 2. 测试获取活跃设备列表
        print("\n2. 测试获取活跃设备列表...")
        active_devices = await proxy_service.get_active_devices()
        print(f"✓ 获取到 {len(active_devices)} 个活跃设备")
        
        for device in active_devices:
            if device["id"] in ["test_device_1", "test_device_2", "test_device_3"]:
                print(f"  - {device['name']} ({device['id']}) - {device['status']}")
        
        # 验证只返回活跃设备
        active_test_devices = [d for d in active_devices if d["id"] in ["test_device_1", "test_device_2", "test_device_3"]]
        if len(active_test_devices) == 2:  # 应该只有running和starting的设备
            print("✓ 活跃设备过滤正常，只返回running和starting状态的设备")
        else:
            print(f"✗ 活跃设备过滤异常，期望2个，实际{len(active_test_devices)}个")
        
        # 3. 创建测试代理IP
        print("\n3. 创建测试代理IP...")
        from backend.app.core.schemas.proxy_models import ProxyIPCreateRequest
        
        proxy_data = ProxyIPCreateRequest(
            region="HK",
            ip_address="*************", 
            port=8080,
            proxy_type="vless",
            v2ray_config="vless://test-config",
            provider="测试提供商",
            notes="测试代理IP"
        )
        
        proxy_id = await proxy_service.create_proxy_ip(proxy_data)
        print(f"✓ 测试代理IP创建成功，ID: {proxy_id}")
        
        # 4. 测试设备关联
        print("\n4. 测试设备关联...")
        mapping_id1 = await proxy_service.associate_device("test_device_1", proxy_id)
        mapping_id2 = await proxy_service.associate_device("test_device_2", proxy_id)
        print(f"✓ 设备关联成功，映射ID: {mapping_id1}, {mapping_id2}")
        
        # 5. 验证关联结果
        print("\n5. 验证关联结果...")
        proxy_info = await proxy_service.get_proxy_ip_by_id(proxy_id)
        if proxy_info:
            associated_devices = proxy_info.associated_devices
            print(f"✓ 代理IP关联了 {len(associated_devices)} 个设备")
            for device in associated_devices:
                print(f"  - 设备: {device['device_name']} ({device['device_id']})")
                print(f"    状态: {device['device_status']}, 类型: {device['device_type']}")
        else:
            print("✗ 获取代理IP信息失败")
        
        # 6. 测试取消关联
        print("\n6. 测试取消关联...")
        success = await proxy_service.disassociate_device("test_device_1", proxy_id)
        if success:
            print("✓ 取消关联成功")
        else:
            print("✗ 取消关联失败")
        
        # 7. 清理测试数据
        print("\n7. 清理测试数据...")
        await db.proxy_ips.delete_one({"_id": proxy_service.collection.find_one({"_id": proxy_id})})
        await db.device_proxy_mappings.delete_many({"proxy_id": proxy_id})
        await db.devices.delete_many({"_id": {"$in": ["test_device_1", "test_device_2", "test_device_3"]}})
        print("✓ 测试数据清理完成")
        
        print("\n✓ 所有测试完成！功能正常工作。")
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        client.close()

if __name__ == "__main__":
    print("ThunderHub IP管理模块关联设备功能测试")
    print("=" * 50)
    
    # 运行测试
    asyncio.run(test_device_association())
