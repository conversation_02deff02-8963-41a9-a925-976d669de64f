syntax = "proto3";

package file;

// 文件服务
service FileService {
  // 获取文件路径配置
  rpc GetFilePaths(FilePathsRequest) returns (FilePathsResponse) {}

  // 列出目录内容
  rpc ListDirectory(ListDirectoryRequest) returns (ListDirectoryResponse) {}

  // 检查路径是否存在
  rpc CheckPathExists(PathExistsRequest) returns (PathExistsResponse) {}

  // 删除文件
  rpc DeleteFiles(DeleteFilesRequest) returns (DeleteFilesResponse) {}

  // 移动文件
  rpc MoveFiles(MoveFilesRequest) returns (MoveFilesResponse) {}

  // 创建文件夹
  rpc CreateDirectory(CreateDirectoryRequest) returns (CreateDirectoryResponse) {}

  // 归档已发布文件
  rpc ArchivePublishedFiles(ArchivePublishedFilesRequest) returns (ArchivePublishedFilesResponse) {}

  // 创建三拼视频
  rpc CreateTripleVideo(CreateTripleVideoRequest) returns (CreateTripleVideoResponse) {}

  // 合并视频
  rpc MergeVideos(MergeVideosRequest) returns (MergeVideosResponse) {}

  // 检测视频水印
  rpc DetectWatermark(DetectWatermarkRequest) returns (DetectWatermarkResponse) {}

  // 清除视频水印
  rpc RemoveWatermark(RemoveWatermarkRequest) returns (RemoveWatermarkResponse) {}

  // 批量处理视频水印
  rpc BatchProcessWatermark(BatchProcessWatermarkRequest) returns (BatchProcessWatermarkResponse) {}

  // 旋转视频
  rpc RotateVideos(RotateVideosRequest) returns (RotateVideosResponse) {}

  // 生成视频缩略图
  rpc GenerateVideoThumbnail(GenerateVideoThumbnailRequest) returns (GenerateVideoThumbnailResponse) {}

  // 获取视频预览信息
  rpc GetVideoPreviewInfo(GetVideoPreviewInfoRequest) returns (GetVideoPreviewInfoResponse) {}

  // 生成视频预览片段
  rpc GenerateVideoPreviewClip(GenerateVideoPreviewClipRequest) returns (GenerateVideoPreviewClipResponse) {}

  // 批量视频加速
  rpc AccelerateVideos(AccelerateVideosRequest) returns (AccelerateVideosResponse) {}
}

// 文件路径请求
message FilePathsRequest {
  // 可选的平台ID
  string platform_id = 1;
}

// 文件路径响应
message FilePathsResponse {
  // 文件根路径
  string file_root_path = 1;
  // 平台路径模板
  string platform_path_template = 2;
  // 设备路径模板
  string device_path_template = 3;
  // 内容路径模板
  string content_path_template = 4;
}

// 列出目录请求
message ListDirectoryRequest {
  // 目录路径
  string path = 1;
  // 过滤扩展名列表（如 ["mp4", "avi"]）
  repeated string filter_extensions = 2;
  // 是否计算文件MD5哈希值
  bool include_md5 = 3;
  // 是否获取媒体文件信息
  bool include_media_info = 4;
}

// 列出目录响应
message ListDirectoryResponse {
  // 目录内容
  repeated FileInfo files = 1;
}

// 检查路径是否存在请求
message PathExistsRequest {
  // 路径
  string path = 1;
}

// 检查路径是否存在响应
message PathExistsResponse {
  // 是否存在
  bool exists = 1;
  // 是否是目录
  bool is_directory = 2;
}

// 删除文件请求
message DeleteFilesRequest {
  // 要删除的文件路径列表
  repeated string file_paths = 1;
}

// 删除文件响应
message DeleteFilesResponse {
  // 是否成功
  bool success = 1;
  // 成功删除的文件数量
  int32 deleted_count = 2;
  // 总文件数量
  int32 total_count = 3;
  // 错误信息列表
  repeated string errors = 4;
}

// 移动文件请求
message MoveFilesRequest {
  // 文件移动操作列表
  repeated FileMoveOperation operations = 1;
}

// 文件移动操作
message FileMoveOperation {
  // 源文件路径
  string source_path = 1;
  // 目标文件路径
  string target_path = 2;
}

// 移动文件响应
message MoveFilesResponse {
  // 是否成功
  bool success = 1;
  // 成功移动的文件数量
  int32 moved_count = 2;
  // 总文件数量
  int32 total_count = 3;
  // 错误信息列表
  repeated string errors = 4;
}

// 创建文件夹请求
message CreateDirectoryRequest {
  // 文件夹路径
  string directory_path = 1;
  // 是否创建父目录
  bool create_parents = 2;
}

// 创建文件夹响应
message CreateDirectoryResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 创建的文件夹路径
  string created_path = 3;
}

// 归档已发布文件请求
message ArchivePublishedFilesRequest {
  // 文件夹路径
  string folder_path = 1;
  // 归档文件夹名称
  string archive_folder_name = 2;
  // 要检查的平台列表（可选）
  repeated string platforms = 3;
  // MD5哈希值到发布状态的映射
  map<string, bool> published_status = 4;
}

// 归档已发布文件响应
message ArchivePublishedFilesResponse {
  // 是否成功
  bool success = 1;
  // 归档文件数量
  int32 archived_count = 2;
  // 跳过文件数量
  int32 skipped_count = 3;
  // 归档文件夹路径
  string archive_folder = 4;
  // 归档的文件列表
  repeated string archived_files = 5;
  // 错误信息列表
  repeated string errors = 6;
}

// 文件信息
message FileInfo {
  // 文件名
  string name = 1;
  // 完整路径
  string path = 2;
  // 是否是目录
  bool is_directory = 3;
  // 文件大小（字节）
  int64 size = 4;
  // 最后修改时间（Unix时间戳）
  int64 modified_time = 5;
  // 文件MD5哈希值（可选）
  string md5_hash = 6;
  // 媒体文件信息（可选）
  MediaInfo media_info = 7;
}

// 媒体文件信息
message MediaInfo {
  // 时长（秒）
  int32 duration = 1;
  // 分辨率（如 "1920x1080"）
  string resolution = 2;
  // 视频编码
  string video_codec = 3;
  // 音频编码
  string audio_codec = 4;
  // 帧率
  float frame_rate = 5;
  // 比特率（kbps）
  int32 bitrate = 6;
}

// 创建三拼视频请求
message CreateTripleVideoRequest {
  // 文件夹路径
  string folder_path = 1;
  // 输出文件路径
  string output_path = 2;
  // 每个视频片段播放时长（秒）
  int32 video_duration_per_segment = 3;
  // 转场时长（秒）
  float transition_duration = 4;
  // 输出质量
  string output_quality = 5;
}

// 创建三拼视频响应
message CreateTripleVideoResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 输出文件路径
  string output_file = 3;
  // 处理的视频数量
  int32 processed_videos = 4;
}

// 合并视频请求
message MergeVideosRequest {
  // 文件夹路径
  string folder_path = 1;
  // 目标时长最小值（秒）
  int32 target_duration_min = 2;
  // 目标时长最大值（秒）
  int32 target_duration_max = 3;
  // 是否启用转场特效
  bool enable_transitions = 4;
  // 输出质量
  string output_quality = 5;
  // 每个合并视频最多包含的原视频数量
  int32 max_videos_per_merge = 6;
}

// 合并视频响应
message MergeVideosResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 输出文件列表
  repeated string output_files = 3;
  // 处理的视频数量
  int32 processed_videos = 4;
  // 成功合并的组数
  int32 successful_merges = 5;
}

// 检测水印请求
message DetectWatermarkRequest {
  // 视频文件路径
  string video_path = 1;
  // 检测模式：auto(自动), template(模板匹配), region(区域检测)
  string detection_mode = 2;
  // 水印模板路径（模板匹配模式使用）
  string template_path = 3;
  // 检测区域（格式：x,y,width,height）
  string detection_region = 4;
  // 检测敏感度（0.0-1.0）
  float sensitivity = 5;
  // 是否保存检测结果图片
  bool save_detection_result = 6;
}

// 检测水印响应
message DetectWatermarkResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 是否检测到水印
  bool watermark_detected = 3;
  // 检测到的水印信息
  repeated WatermarkInfo watermarks = 4;
  // 检测结果图片路径
  string detection_result_path = 5;
  // 检测耗时（毫秒）
  int64 detection_time_ms = 6;
}

// 清除水印请求
message RemoveWatermarkRequest {
  // 输入视频文件路径
  string input_video_path = 1;
  // 输出视频文件路径
  string output_video_path = 2;
  // 清除模式：auto(自动), manual(手动指定区域), inpaint(修复算法)
  string removal_mode = 3;
  // 水印区域列表（格式：x,y,width,height）
  repeated string watermark_regions = 4;
  // 修复算法：blur(模糊), median(中值滤波), inpaint(图像修复)
  string inpaint_method = 5;
  // 输出质量：high, medium, low
  string output_quality = 6;
  // 是否保持原始编码
  bool preserve_encoding = 7;
}

// 清除水印响应
message RemoveWatermarkResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 输出文件路径
  string output_file_path = 3;
  // 处理耗时（毫秒）
  int64 processing_time_ms = 4;
  // 原始文件大小（字节）
  int64 original_file_size = 5;
  // 输出文件大小（字节）
  int64 output_file_size = 6;
  // 清除的水印数量
  int32 removed_watermarks_count = 7;
}

// 批量处理水印请求
message BatchProcessWatermarkRequest {
  // 输入文件夹路径
  string input_folder_path = 1;
  // 输出文件夹路径
  string output_folder_path = 2;
  // 处理模式：detect_only(仅检测), remove_only(仅清除), detect_and_remove(检测并清除)
  string process_mode = 3;
  // 检测配置
  DetectWatermarkRequest detection_config = 4;
  // 清除配置
  RemoveWatermarkRequest removal_config = 5;
  // 文件过滤器（支持通配符，如*.mp4）
  repeated string file_filters = 6;
  // 是否递归处理子目录
  bool recursive = 7;
  // 最大并发处理数
  int32 max_concurrent = 8;
}

// 批量处理水印响应
message BatchProcessWatermarkResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 处理结果列表
  repeated BatchProcessResult results = 3;
  // 总处理文件数
  int32 total_files = 4;
  // 成功处理文件数
  int32 successful_files = 5;
  // 失败处理文件数
  int32 failed_files = 6;
  // 总处理耗时（毫秒）
  int64 total_processing_time_ms = 7;
}

// 水印信息
message WatermarkInfo {
  // 水印类型：logo, text, timestamp, platform_mark
  string watermark_type = 1;
  // 水印位置（x,y,width,height）
  string position = 2;
  // 置信度（0.0-1.0）
  float confidence = 3;
  // 水印描述
  string description = 4;
  // 检测到的时间范围（开始时间,结束时间，单位秒）
  string time_range = 5;
}

// 批量处理结果
message BatchProcessResult {
  // 文件路径
  string file_path = 1;
  // 处理状态：success, failed, skipped
  string status = 2;
  // 错误信息（如果失败）
  string error_message = 3;
  // 检测结果（如果进行了检测）
  DetectWatermarkResponse detection_result = 4;
  // 清除结果（如果进行了清除）
  RemoveWatermarkResponse removal_result = 5;
  // 处理耗时（毫秒）
  int64 processing_time_ms = 6;
}

// 旋转视频请求
message RotateVideosRequest {
  // 要旋转的视频文件路径列表
  repeated string video_paths = 1;
  // 旋转角度：90(向右90度), -90(向左90度), 180(180度)
  int32 rotation_angle = 2;
  // 输出质量：high, medium, low
  string output_quality = 3;
  // 是否覆盖原文件（false则在原文件名后添加后缀）
  bool overwrite_original = 4;
  // 输出文件名后缀（当overwrite_original为false时使用）
  string output_suffix = 5;
}

// 旋转视频响应
message RotateVideosResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 旋转结果列表
  repeated VideoRotationResult results = 3;
  // 成功旋转的视频数量
  int32 successful_count = 4;
  // 失败的视频数量
  int32 failed_count = 5;
  // 总处理耗时（毫秒）
  int64 total_processing_time_ms = 6;
}

// 视频旋转结果
message VideoRotationResult {
  // 原始文件路径
  string original_path = 1;
  // 输出文件路径
  string output_path = 2;
  // 是否成功
  bool success = 3;
  // 错误信息（如果失败）
  string error_message = 4;
  // 处理耗时（毫秒）
  int64 processing_time_ms = 5;
  // 原始文件大小（字节）
  int64 original_file_size = 6;
  // 输出文件大小（字节）
  int64 output_file_size = 7;
}

// 生成视频缩略图请求
message GenerateVideoThumbnailRequest {
  // 视频文件路径
  string video_path = 1;
  // 缩略图输出路径（可选，不指定则自动生成）
  string thumbnail_path = 2;
  // 缩略图时间点（秒，默认为视频中间位置）
  float timestamp = 3;
  // 缩略图宽度（像素，默认320）
  int32 width = 4;
  // 缩略图高度（像素，默认180）
  int32 height = 5;
  // 图片质量（1-100，默认85）
  int32 quality = 6;
  // 是否强制重新生成（忽略缓存）
  bool force_regenerate = 7;
}

// 生成视频缩略图响应
message GenerateVideoThumbnailResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 缩略图文件路径
  string thumbnail_path = 3;
  // 缩略图HTTP URL
  string thumbnail_url = 4;
  // 缩略图文件大小（字节）
  int64 thumbnail_size = 5;
  // 生成耗时（毫秒）
  int64 generation_time_ms = 6;
  // 是否来自缓存
  bool from_cache = 7;
}

// 获取视频预览信息请求
message GetVideoPreviewInfoRequest {
  // 视频文件路径
  string video_path = 1;
  // 是否包含缩略图信息
  bool include_thumbnail = 2;
  // 是否包含详细元数据
  bool include_detailed_metadata = 3;
}

// 获取视频预览信息响应
message GetVideoPreviewInfoResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 基本媒体信息
  MediaInfo media_info = 3;
  // 缩略图信息
  VideoThumbnailInfo thumbnail_info = 4;
  // 详细元数据
  VideoDetailedMetadata detailed_metadata = 5;
}

// 生成视频预览片段请求
message GenerateVideoPreviewClipRequest {
  // 视频文件路径
  string video_path = 1;
  // 预览片段输出路径（可选，不指定则自动生成）
  string preview_clip_path = 2;
  // 预览开始时间（秒）
  float start_time = 3;
  // 预览时长（秒，默认30秒）
  float duration = 4;
  // 输出质量：high, medium, low
  string output_quality = 5;
  // 是否强制重新生成（忽略缓存）
  bool force_regenerate = 6;
}

// 生成视频预览片段响应
message GenerateVideoPreviewClipResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 预览片段文件路径
  string preview_clip_path = 3;
  // 预览片段文件大小（字节）
  int64 preview_clip_size = 4;
  // 生成耗时（毫秒）
  int64 generation_time_ms = 5;
  // 是否来自缓存
  bool from_cache = 6;
}

// 视频缩略图信息
message VideoThumbnailInfo {
  // 缩略图文件路径
  string thumbnail_path = 1;
  // 缩略图文件大小（字节）
  int64 thumbnail_size = 2;
  // 缩略图宽度
  int32 width = 3;
  // 缩略图高度
  int32 height = 4;
  // 缩略图生成时间戳
  int64 generated_time = 5;
  // 是否存在
  bool exists = 6;
}

// 视频详细元数据
message VideoDetailedMetadata {
  // 文件格式
  string format = 1;
  // 视频流信息
  repeated VideoStreamInfo video_streams = 2;
  // 音频流信息
  repeated AudioStreamInfo audio_streams = 3;
  // 字幕流信息
  repeated SubtitleStreamInfo subtitle_streams = 4;
  // 创建时间
  string creation_time = 5;
  // 标题
  string title = 6;
  // 作者
  string author = 7;
  // 描述
  string description = 8;
  // 标签
  repeated string tags = 9;
}

// 视频流信息
message VideoStreamInfo {
  // 流索引
  int32 index = 1;
  // 编码器
  string codec = 2;
  // 分辨率
  string resolution = 3;
  // 帧率
  float frame_rate = 4;
  // 比特率
  int32 bitrate = 5;
  // 像素格式
  string pixel_format = 6;
  // 色彩空间
  string color_space = 7;
}

// 音频流信息
message AudioStreamInfo {
  // 流索引
  int32 index = 1;
  // 编码器
  string codec = 2;
  // 采样率
  int32 sample_rate = 3;
  // 声道数
  int32 channels = 4;
  // 比特率
  int32 bitrate = 5;
  // 声道布局
  string channel_layout = 6;
}

// 字幕流信息
message SubtitleStreamInfo {
  // 流索引
  int32 index = 1;
  // 编码器
  string codec = 2;
  // 语言
  string language = 3;
  // 标题
  string title = 4;
}

// 批量视频加速请求
message AccelerateVideosRequest {
  // 视频文件路径列表
  repeated string video_paths = 1;
  // 目标时长（秒）
  int32 target_duration = 2;
  // 输出质量
  string output_quality = 3;
  // 是否覆盖原文件
  bool overwrite_original = 4;
  // 输出文件后缀（仅在不覆盖模式下使用）
  string output_suffix = 5;
}

// 批量视频加速响应
message AccelerateVideosResponse {
  // 是否成功
  bool success = 1;
  // 错误信息
  string error = 2;
  // 处理结果列表
  repeated VideoAccelerationResult results = 3;
  // 总处理时间（毫秒）
  int64 total_processing_time_ms = 4;
  // 成功处理的视频数量
  int32 successful_count = 5;
  // 失败处理的视频数量
  int32 failed_count = 6;
}

// 单个视频加速结果
message VideoAccelerationResult {
  // 原始文件路径
  string original_path = 1;
  // 输出文件路径
  string output_path = 2;
  // 是否成功
  bool success = 3;
  // 错误信息
  string error_message = 4;
  // 处理时间（毫秒）
  int64 processing_time_ms = 5;
  // 原始文件大小（字节）
  int64 original_file_size = 6;
  // 输出文件大小（字节）
  int64 output_file_size = 7;
  // 原始时长（秒）
  float original_duration = 8;
  // 输出时长（秒）
  float output_duration = 9;
  // 加速倍率
  float speed_factor = 10;
}
