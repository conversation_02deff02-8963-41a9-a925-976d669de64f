<template>
  <div class="device-list">
    <div class="header-container">
      <h1>设备列表</h1>
      <div class="filter-container">
        <el-select
          v-model="selectedCoreId"
          placeholder="选择Core服务"
          clearable
          @change="handleCoreChange"
          style="width: 180px;"
        >
          <el-option
            v-for="core in coreServices"
            :key="core.id"
            :label="core.name"
            :value="core.id"
          />
        </el-select>
        <el-button type="primary" @click="refreshDevices" style="margin-left: 10px;">刷新</el-button>
      </div>
    </div>
    <el-table :data="paginatedDevices" style="width: 100%">
      <el-table-column prop="name" label="设备名称" />
      <el-table-column prop="type" label="类别" />
      <el-table-column prop="status" label="状态" />
      <el-table-column prop="resolution" label="分辨率" />
      <el-table-column prop="cpu" label="CPU" />
      <el-table-column prop="memory" label="内存" />
      <el-table-column prop="network" label="网络" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" @click="handleControl(scope.row)">
            设置
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="fetchDevices"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'

const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

const typeMap: Record<string, string> = {
  'ldplayer': '雷电模拟器',
  'android': '安卓设备',
  'ios': 'iOS设备',
  'windows': 'Windows设备'
}

const getTypeName = (type?: string) => {
  return type ? typeMap[type] || type : '未知'
}
import { useRouter } from 'vue-router'
import { getDevices, getCoreServices } from '@/api/rest/device'
import { ElMessage, ElPagination } from 'element-plus'

interface CoreService {
  id: string
  name: string
}

const router = useRouter()
const devices = ref<Device[]>([])
const coreServices = ref<CoreService[]>([])
const selectedCoreId = ref<string | null>(null)

const paginatedDevices = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return devices.value.slice(start, end)
})

// 获取设备列表
const fetchDevices = async () => {
  try {
    console.log('开始获取设备数据...')

    // 如果没有选择Core服务，则不显示任何设备
    if (!selectedCoreId.value) {
      devices.value = []
      pagination.value.total = 0
      return
    }

    const res = await getDevices({
      include_config: true,
      core_id: selectedCoreId.value
    })
    console.log('API响应:', res)

    if (!res) {
      console.warn('API返回空响应，使用空数组')
      devices.value = []
      pagination.value.total = 0
      return
    }

    if (!Array.isArray(res)) {
      console.warn(`无效的设备数据格式，期望数组，实际得到: ${typeof res}，使用空数组`)
      devices.value = []
      pagination.value.total = 0
      return
    }

    // 安全地处理设备数据
    devices.value = res.map(device => {
      // 确保每个字段都有默认值
      const deviceId = device.id || 'unknown'

      // 调试输出设备数据
      console.debug('设备数据:', device)

      // 处理显示信息
      let resolution = '未知'
      if (device.display_info) {
        const width = device.display_info.width || 0
        const height = device.display_info.height || 0
        if (width > 0 && height > 0) {
          resolution = `${width}x${height}`
        }
      }

      // 处理状态信息
      let statusText = '未知'
      if (device.status === 'running') {
        statusText = '运行中'
      } else if (device.status === 'stopped') {
        statusText = '已停止'
      } else if (device.status === 'starting') {
        statusText = '启动中'
      } else if (device.status === 'stopping') {
        statusText = '停止中'
      } else {
        statusText = device.status || '未知'
      }

      return {
        id: deviceId,
        name: device.name || `设备-${deviceId}`,
        type: getTypeName(device.type),
        status: statusText,
        resolution: resolution,
        cpu: device.cpu || '0%',
        memory: device.memory || '0%',
        network: device.network || '未知'
      }
    })

    pagination.value.total = res.length
    console.log(`成功获取 ${res.length} 个设备`)
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败，请检查网络连接和后端服务')
    // 设置为空数组而不是保持之前的状态
    devices.value = []
    pagination.value.total = 0
  }
}

// 获取Core服务列表
const fetchCoreServices = async () => {
  try {
    const cores = await getCoreServices()
    coreServices.value = cores
    console.log('获取到Core服务列表:', cores)
  } catch (error) {
    console.error('获取Core服务列表失败:', error)
    ElMessage.error('获取Core服务列表失败')
  }
}

// 处理Core服务筛选变更
const handleCoreChange = () => {
  console.log('选择的Core服务ID:', selectedCoreId.value)
  pagination.value.page = 1 // 重置到第一页
  fetchDevices()
}

onMounted(() => {
  fetchCoreServices()
  fetchDevices()
})

const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  pagination.value.page = 1
  fetchDevices()
}

const handleControl = (device: any) => {
  router.push({ name: 'DeviceControl', params: { id: device.name } })
}
</script>

<style scoped>
.device-list {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  align-items: center;
}

.table-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.el-table {
  margin-top: 20px;
}

.el-table :deep(.el-table__header) th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: bold;
}

.el-table :deep(.el-table__row) td {
  padding: 12px 0;
  text-align: center !important;
}

/* 表头居中 */
.el-table :deep(.el-table__header) th {
  text-align: center;
}

.el-table :deep(.el-button) {
  padding: 7px 15px;
}
</style>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
  background: #fff;
  border-top: 1px solid #ebeef5;
}

.el-pagination {
  padding: 0 10px;
}
</style>