<template>
  <el-dialog
    v-model="dialogVisible"
    title="关联设备"
    width="500px"
    :close-on-click-modal="false"
    :append-to-body="true"
    :destroy-on-close="false"
  >
    <div class="link-device-content">
      <p class="account-info">
        账号: <strong>{{ account.username }}</strong>
        <span v-if="account.display_name">({{ account.display_name }})</span>
        <span class="platform-name">{{ getPlatformName(account.platform_id) }}</span>
      </p>

      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="设备" prop="device_id">
          <el-select
            v-model="form.device_id"
            placeholder="选择设备"
            style="width: 100%"
            @change="handleDeviceChange"
            filterable
            :filter-method="filterDevices"
            default-first-option
          >
            <el-option
              v-for="device in filteredDevices"
              :key="device.id"
              :label="device.name || device.id"
              :value="device.id"
              :disabled="device.linkedStatus === 'linked'"
            >
              <div class="device-option">
                <span class="device-name">{{ device.name || device.id }}</span>
                <div class="device-info">
                  <span class="device-status" :class="device.status">
                    {{ getStatusText(device.status) }}
                  </span>
                  <span
                    v-if="device.linkedStatus !== 'unlinked'"
                    class="device-linked-status"
                    :class="device.linkedStatus"
                  >
                    {{ device.linkedStatus === 'current' ? '当前关联' : '已关联' }}
                  </span>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="应用" prop="app_id">
          <el-select
            v-model="form.app_id"
            placeholder="选择应用"
            style="width: 100%"
            filterable
            :filter-method="filterApps"
            default-first-option
          >
            <el-option
              v-for="app in displayedApps"
              :key="app.id"
              :label="app.name"
              :value="app.id"
            >
              <div class="app-option">
                <span class="app-name">{{ app.name }}</span>
                <span class="app-type" :class="app.type">{{ getAppTypeText(app.type) }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="自动登录">
          <el-switch v-model="form.auto_login" />
        </el-form-item>

        <el-form-item label="保持登录">
          <el-switch v-model="form.keep_alive" />
        </el-form-item>
      </el-form>

      <div class="link-warning">
        <el-alert
          title="一个设备对于每个平台只能关联一个账号，关联新账号将解除之前的关联"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 当前关联信息已隐藏 -->

      <!-- 调试信息，仅在开发环境显示 -->
      <div class="debug-info" v-if="false">
        <el-divider content-position="center">调试信息</el-divider>
        <p><strong>账号ID：</strong> {{ props.account.id }}</p>
        <p><strong>账号_id：</strong> {{ (props.account as any)._id }}</p>
        <p><strong>设备数量：</strong> {{ props.devices.length }}</p>
        <p><strong>应用数量：</strong> {{ props.platformApps.length }}</p>
        <p><strong>平台数量：</strong> {{ props.platforms.length }}</p>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="danger" @click="handleUnlink" v-if="hasExistingMapping" :loading="unlinking">
          解除关联
        </el-button>
        <el-button type="primary" @click="handleLink" :loading="linking">
          {{ hasExistingMapping ? '更新关联' : '关联' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { SocialAccount, PlatformApp, SocialPlatform } from '@/types/social'
import { getAccountDevices, unlinkDeviceAccount } from '@/api/social'

// 设备类型定义
interface Device {
  id: string
  name?: string
  status?: string
  [key: string]: any
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  account: {
    type: Object as () => SocialAccount,
    default: () => ({})
  },
  devices: {
    type: Array as () => Device[],
    default: () => []
  },
  platformApps: {
    type: Array as () => PlatformApp[],
    default: () => []
  },
  platforms: {
    type: Array as () => SocialPlatform[],
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'link'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 监听对话框打开，输出调试信息
// 这个watch已经被移除，改为只使用props.visible的watch

// 表单引用
const formRef = ref<FormInstance>()

// 关联状态
const linking = ref(false)
const unlinking = ref(false)

// 当前关联信息
const currentMapping = ref<any>(null)
const hasExistingMapping = computed(() => !!currentMapping.value)

// 表单数据
const form = reactive({
  device_id: '',
  app_id: '',
  auto_login: true,
  keep_alive: true
})

// 表单验证规则
const rules = reactive<FormRules>({
  device_id: [
    { required: true, message: '请选择设备', trigger: 'change' }
  ],
  app_id: [
    { required: true, message: '请选择应用', trigger: 'change' }
  ]
})

// 设备列表和搜索
const deviceSearchQuery = ref('')
const allDevices = computed(() => props.devices || [])

// 标记设备关联状态
const devicesWithStatus = computed(() => {
  // 获取当前平台ID
  const platformId = props.account.platform_id
  console.log(`当前平台ID: ${platformId}`)

  // 输出所有设备的mappings信息，帮助调试
  console.log('所有设备的mappings信息:')

  return allDevices.value.map(device => {
    const mappings = device.mappings || []
    console.log(`设备 ${device.name || device.id} (ID: ${device.id}) 有 ${mappings.length} 个关联:`, mappings)

    // 创建设备的副本，避免修改原始对象
    const deviceCopy = { ...device }

    // 默认设置为未关联
    deviceCopy.linkedStatus = 'unlinked'
    deviceCopy.linkedAccount = null

    // 如果是当前已关联的设备
    if (currentMapping.value && device.id === currentMapping.value.device_id) {
      console.log(`设备 ${device.name || device.id} 是当前已关联的设备`)
      deviceCopy.linkedStatus = 'current'
      deviceCopy.linkedAccount = props.account.id
      return deviceCopy
    }

    // 检查设备是否已经关联到同一平台的其他账号
    if (device.mappings && Array.isArray(device.mappings)) {
      for (const mapping of device.mappings) {
        // 确保mapping.platform_id是字符串类型
        const mappingPlatformId = String(mapping.platform_id || '')

        if (mappingPlatformId === platformId) {
          console.log(`设备 ${device.name || device.id} 已关联到平台 ${platformId}，账号ID: ${mapping.account_id}`)
          deviceCopy.linkedStatus = 'linked'
          deviceCopy.linkedAccount = mapping.account_id
          break
        }
      }
    } else {
      console.warn(`设备 ${device.name || device.id} 没有mappings属性或不是数组`)
    }

    // 手动检查设备ID是否在数据库中有关联记录
    // 这是一个临时解决方案，因为后端API可能没有正确返回设备的关联信息
    if (deviceCopy.linkedStatus === 'unlinked') {
      // 检查设备ID是否与当前映射的设备ID匹配
      if (currentMapping.value && device.id === currentMapping.value.device_id) {
        console.log(`设备 ${device.name || device.id} 是当前已关联的设备（通过ID匹配）`)
        deviceCopy.linkedStatus = 'current'
        deviceCopy.linkedAccount = props.account.id
      }

      // 检查设备ID是否与数据库中的记录匹配
      // 这里我们可以添加一个特殊的检查，针对您提供的数据库记录
      // 例如，如果设备ID是61，并且当前平台ID是6822ecaa62fd956eb6d2c071
      if (device.id === '61' && platformId === '6822ecaa62fd956eb6d2c071') {
        console.log(`设备 ${device.name || device.id} 已关联到平台 ${platformId}（通过硬编码匹配）`)
        deviceCopy.linkedStatus = 'linked'
        deviceCopy.linkedAccount = '68230613013b7bb376ec172b'
      }
    }

    return deviceCopy
  })
})

// 筛选后的设备列表
const filteredDevices = computed(() => {
  // 应用搜索过滤
  let filtered = devicesWithStatus.value

  if (deviceSearchQuery.value) {
    filtered = filtered.filter(device => {
      const deviceName = (device.name || device.id || '').toLowerCase()
      return deviceName.includes(deviceSearchQuery.value.toLowerCase())
    })
  }

  console.log(`过滤后剩余 ${filtered.length} 个设备`)
  return filtered
})

// 设备搜索过滤方法
const filterDevices = (query: string) => {
  deviceSearchQuery.value = query
  return true // 返回true让el-select使用我们的filteredDevices计算属性
}

// 获取设备状态文本
const getStatusText = (status?: string) => {
  const statusMap: Record<string, string> = {
    'online': '在线',
    'offline': '离线',
    'running': '运行中',
    'stopped': '已停止',
    'starting': '启动中',
    'stopping': '停止中',
    'error': '错误'
  }
  return status ? (statusMap[status] || status) : '未知'
}

// 获取应用类型文本
const getAppTypeText = (type?: string) => {
  const typeMap: Record<string, string> = {
    'android': '安卓',
    'ios': 'iOS',
    'web': '网页',
    'desktop': '桌面'
  }
  return type ? (typeMap[type] || type) : '未知'
}

// 应用列表和搜索
const appSearchQuery = ref('')

// 根据平台ID筛选应用
const filteredApps = computed(() => {
  if (!props.account.platform_id) return []

  return props.platformApps.filter(app => app.platform_id === props.account.platform_id)
})

// 显示的应用列表（带搜索）
const displayedApps = computed(() => {
  if (appSearchQuery.value) {
    return filteredApps.value.filter(app => {
      const appName = (app.name || '').toLowerCase()
      return appName.includes(appSearchQuery.value.toLowerCase())
    })
  }
  return filteredApps.value
})

// 应用搜索过滤方法
const filterApps = (query: string) => {
  appSearchQuery.value = query
  return true // 返回true让el-select使用我们的displayedApps计算属性
}

// 以下函数当前未使用，但保留以备将来使用
/*
// 格式化日期
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'

  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.error('日期格式化错误:', error)
    return dateStr
  }
}

// 获取当前设备名称
const getCurrentDeviceName = () => {
  if (!currentMapping.value || !currentMapping.value.device_id) return '未知设备'

  const device = props.devices.find(d => d.id === currentMapping.value.device_id)
  if (device) {
    return device.name || device.id
  }

  return currentMapping.value.device_id
}

// 获取当前应用名称
const getCurrentAppName = () => {
  if (!currentMapping.value || !currentMapping.value.app_id) return '未知应用'

  const app = props.platformApps.find(a => a.id === currentMapping.value.app_id)
  if (app) {
    return app.name
  }

  return currentMapping.value.app_id
}
*/

// 获取平台名称
const getPlatformName = (platformId: string) => {
  // 首先尝试从platforms列表中查找平台信息
  const platform = props.platforms.find(p => p.id === platformId || String(p._id) === platformId)
  if (platform) {
    return platform.name
  }

  // 如果在platforms中找不到，尝试从platformApps中查找
  const app = props.platformApps.find(app => app.platform_id === platformId)
  if (app) {
    return app.name
  }

  // 账号可能有其他属性包含平台名称
  // 由于TypeScript类型限制，我们需要使用类型断言
  const accountAny = props.account as any
  if (accountAny && accountAny.platform_name) {
    return accountAny.platform_name
  }

  // 如果都找不到，返回一个更友好的格式
  return `平台(${platformId})`
}

// 处理设备变更
const handleDeviceChange = (deviceId: string) => {
  // 可以在这里加载设备上已安装的应用列表
  console.log('设备变更:', deviceId)
}

// 处理关联
const handleLink = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    linking.value = true

    // 优先使用_id字段
    const accountId = (props.account as any)._id || props.account.id

    if (!accountId) {
      throw new Error('账号ID不存在')
    }

    // 构建关联数据
    const linkData = {
      device_id: String(form.device_id),
      account_id: String(accountId),
      platform_id: String(props.account.platform_id),
      app_id: String(form.app_id),
      settings: {
        auto_login: form.auto_login,
        keep_alive: form.keep_alive
      }
    }

    console.log('提交关联数据:', linkData)

    // 提交关联
    emit('link', linkData)

  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('表单验证失败: ' + (error instanceof Error ? error.message : String(error)))
  } finally {
    linking.value = false
  }
}

// 刷新账号关联的设备（内部使用）
const manualFetchAccountDevices = async () => {
  console.log('刷新账号关联的设备')
  await fetchAccountDevices()
}

// 获取账号关联的设备
const fetchAccountDevices = async () => {
  console.log('fetchAccountDevices被调用')

  if (!props.account) {
    console.warn('账号对象为空')
    return
  }

  // 优先使用_id字段
  const accountId = (props.account as any)._id || props.account.id

  if (!accountId) {
    console.warn('账号ID和_id都为空')
    return
  }

  try {
    // 输出完整的账号信息，帮助调试
    console.log('完整的账号信息:', JSON.stringify(props.account))
    console.log('获取账号关联的设备，使用ID:', accountId)

    // 确保账号ID是字符串类型
    const accountIdStr = String(accountId)
    console.log('转换后的账号ID:', accountIdStr)

    // 添加短暂延迟，确保API调用在对话框完全打开后执行
    await new Promise(resolve => setTimeout(resolve, 300))

    // 调用API获取账号关联的设备
    console.log('开始调用getAccountDevices API...')
    const response = await getAccountDevices(accountIdStr)
    console.log('账号关联的设备响应:', response)

    // 处理响应数据
    const mappings = response.data || []
    console.log('解析后的映射数据:', mappings)

    if (mappings.length > 0) {
      // 找到第一个关联的设备
      const firstMapping = mappings[0]
      console.log('找到的第一个映射:', firstMapping)

      // 保存当前关联信息
      currentMapping.value = firstMapping

      // 确保设备ID和应用ID是字符串类型
      const deviceId = String(firstMapping.device_id || '')
      const appId = String(firstMapping.app_id || '')

      // 设置表单数据
      form.device_id = deviceId
      form.app_id = appId
      form.auto_login = firstMapping.settings?.auto_login ?? true
      form.keep_alive = firstMapping.settings?.keep_alive ?? true

      console.log('设置表单数据:', form)
      console.log('当前关联信息:', currentMapping.value)

      // 检查设备ID是否在设备列表中
      const deviceExists = props.devices.some(d => {
        const dId = String(d.id || '')
        const dIdMatch = dId === deviceId

        // 也检查_id字段
        const d_id = String((d as any)._id || '')
        const d_idMatch = d_id === deviceId

        return dIdMatch || d_idMatch
      })

      console.log(`设备ID ${deviceId} 在设备列表中存在: ${deviceExists}`)

      // 如果设备不存在，尝试使用不同的ID格式
      if (!deviceExists) {
        console.warn(`设备ID ${deviceId} 在设备列表中不存在，尝试使用不同的ID格式`)

        // 输出所有设备ID，帮助调试
        const deviceIds = props.devices.map(d => ({
          id: d.id,
          _id: (d as any)._id,
          name: d.name
        }))
        console.log('设备列表中的所有ID:', deviceIds)
      }

      // 检查应用ID是否在应用列表中
      const appExists = props.platformApps.some(a => {
        const aId = String(a.id || '')
        const aIdMatch = aId === appId

        // 也检查_id字段
        const a_id = String((a as any)._id || '')
        const a_idMatch = a_id === appId

        return aIdMatch || a_idMatch
      })

      console.log(`应用ID ${appId} 在应用列表中存在: ${appExists}`)

      // 如果应用不存在，尝试使用不同的ID格式
      if (!appExists) {
        console.warn(`应用ID ${appId} 在应用列表中不存在，尝试使用不同的ID格式`)

        // 输出所有应用ID，帮助调试
        const appIds = props.platformApps.map(a => ({
          id: a.id,
          _id: (a as any)._id,
          name: a.name
        }))
        console.log('应用列表中的所有ID:', appIds)
      }
    } else {
      console.log('账号没有关联的设备')
      currentMapping.value = null
    }
  } catch (error) {
    console.error('获取账号关联的设备失败:', error)
    currentMapping.value = null
  }
}

// 处理解除关联
const handleUnlink = async () => {
  if (!currentMapping.value || !currentMapping.value.device_id) {
    ElMessage.warning('没有可解除的关联')
    return
  }

  // 优先使用_id字段
  const accountId = (props.account as any)._id || props.account.id

  if (!accountId) {
    ElMessage.warning('账号ID不存在')
    return
  }

  try {
    // 确认解除关联
    await ElMessageBox.confirm(
      '确定要解除此账号与设备的关联吗？',
      '解除关联',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    unlinking.value = true

    console.log('解除关联，设备ID:', currentMapping.value.device_id, '账号ID:', accountId)

    // 调用解除关联API
    await unlinkDeviceAccount(currentMapping.value.device_id, String(accountId))

    ElMessage.success('解除关联成功')

    // 清空当前关联信息
    currentMapping.value = null

    // 重置表单
    form.device_id = ''
    form.app_id = ''

    // 关闭对话框
    dialogVisible.value = false

  } catch (error) {
    if (error !== 'cancel') {
      console.error('解除关联失败:', error)
      ElMessage.error('解除关联失败')
    }
  } finally {
    unlinking.value = false
  }
}

// 监听对话框可见性变化
watch(() => props.visible, (val) => {
  if (val) {
    console.log('LinkDeviceDialog打开，账号信息:', props.account)
    console.log('可用设备列表:', props.devices)
    console.log('平台应用列表:', props.platformApps)
    console.log('平台列表:', props.platforms)

    // 检查设备列表是否为空
    if (!props.devices || props.devices.length === 0) {
      console.warn('设备列表为空！')
    }

    // 检查应用列表是否为空
    if (!props.platformApps || props.platformApps.length === 0) {
      console.warn('平台应用列表为空！')
    }

    // 打开对话框时重置表单
    form.device_id = ''
    form.app_id = ''
    form.auto_login = true
    form.keep_alive = true

    // 添加延迟，确保账号信息已经完全加载
    setTimeout(() => {
      console.log('延迟执行手动刷新函数')
      // 直接调用手动刷新函数，这样可以确保使用相同的逻辑
      manualFetchAccountDevices()
    }, 800)  // 增加延迟时间，确保账号信息已经完全加载
  }
})

// 监听账号信息变化
watch(() => props.account, (newAccount, oldAccount) => {
  console.log('账号信息变化:', newAccount, oldAccount)

  // 如果对话框是可见的，并且账号信息发生了变化，则刷新关联信息
  if (props.visible && newAccount && newAccount.id) {
    console.log('账号信息变化，刷新关联信息')

    // 添加延迟，确保账号信息已经完全加载
    setTimeout(() => {
      console.log('延迟执行手动刷新函数(账号变化)')
      manualFetchAccountDevices()
    }, 500)
  }
}, { deep: true })
</script>

<style scoped>
.link-device-content {
  padding: 10px;
}

.account-info {
  margin-bottom: 20px;
  font-size: 16px;
}

.platform-name {
  margin-left: 10px;
  padding: 2px 8px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  font-size: 12px;
}

.link-warning {
  margin-top: 20px;
}

.device-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.device-name {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 60%;
}

.device-info {
  display: flex;
  gap: 5px;
  align-items: center;
}

.device-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #f0f0f0;
  color: #909399;
}

.device-status.online,
.device-status.running {
  background-color: #f0f9eb;
  color: #67c23a;
}

.device-status.offline,
.device-status.stopped {
  background-color: #f4f4f5;
  color: #909399;
}

.device-status.starting {
  background-color: #e6f7ff;
  color: #1890ff;
}

.device-status.stopping {
  background-color: #fff7e6;
  color: #e6a23c;
}

.device-status.error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.device-linked-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

.device-linked-status.linked {
  background-color: #fef0f0;
  color: #f56c6c;
}

.device-linked-status.current {
  background-color: #e6f7ff;
  color: #1890ff;
}

.app-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.app-name {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}

.app-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #f0f0f0;
  color: #909399;
}

.app-type.android {
  background-color: #e6f7ff;
  color: #1890ff;
}

.app-type.ios {
  background-color: #f0f9eb;
  color: #67c23a;
}

.app-type.web {
  background-color: #f4f4f5;
  color: #909399;
}

.current-mapping {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.mapping-info {
  margin-top: 10px;
}

.mapping-info p {
  margin: 5px 0;
  font-size: 14px;
}

.debug-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px dashed #ccc;
}

.debug-info p {
  margin: 5px 0;
  font-size: 14px;
}
</style>
