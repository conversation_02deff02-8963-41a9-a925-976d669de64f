"""
文件管理模块的数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Annotated
from datetime import datetime
try:
    from bson import ObjectId
except ImportError:
    # 如果没有安装pymongo，使用字符串代替
    ObjectId = str


class AuthorInfo(BaseModel):
    """作者信息"""
    name: str = Field(..., description="作者名称")
    channel_id: Optional[str] = Field(None, description="频道ID")
    avatar_url: Optional[str] = Field(None, description="头像URL")


class FileInfo(BaseModel):
    """文件信息"""
    local_path: str = Field(..., description="本地存储路径")
    file_size: int = Field(..., description="文件大小(字节)")
    file_format: str = Field(..., description="文件格式")
    duration: Optional[int] = Field(None, description="视频/音频时长(秒)")
    resolution: Optional[str] = Field(None, description="分辨率")
    hash: str = Field(..., description="文件哈希值，用于去重")


class ContentMetadata(BaseModel):
    """内容元数据"""
    tags: List[str] = Field(default_factory=list, description="标签")
    category: Optional[str] = Field(None, description="分类")
    language: Optional[str] = Field(None, description="语言")
    publish_date: Optional[datetime] = Field(None, description="原始发布时间")
    view_count: Optional[int] = Field(None, description="观看数")
    like_count: Optional[int] = Field(None, description="点赞数")
    comment_count: Optional[int] = Field(None, description="评论数")


class DownloadInfo(BaseModel):
    """下载信息"""
    download_date: datetime = Field(default_factory=datetime.now, description="下载时间")
    download_source: str = Field(..., description="下载来源工具")
    quality: Optional[str] = Field(None, description="下载质量")
    status: str = Field(default="downloaded", description="状态: downloaded, processing, failed")


class ContentAnalysis(BaseModel):
    """内容分析数据"""
    sentiment: Optional[str] = Field(None, description="情感分析")
    keywords: List[str] = Field(default_factory=list, description="关键词")
    topics: List[str] = Field(default_factory=list, description="主题")
    engagement_rate: Optional[float] = Field(None, description="互动率")


class CompetitorContent(BaseModel):
    """竞品内容模型"""
    id: Optional[str] = Field(default=None, alias="_id", description="内容ID")
    title: str = Field(..., description="内容标题")
    description: Optional[str] = Field(None, description="内容描述")
    platform: str = Field(..., description="来源平台")
    original_url: str = Field(..., description="原始链接")
    author: AuthorInfo = Field(..., description="作者信息")
    content_type: str = Field(..., description="内容类型")
    file_info: FileInfo = Field(..., description="文件信息")
    metadata: ContentMetadata = Field(default_factory=ContentMetadata, description="元数据")
    download_info: DownloadInfo = Field(..., description="下载信息")
    analysis: Optional[ContentAnalysis] = Field(None, description="分析数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    created_by: str = Field(..., description="创建用户")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


class ContentCategory(BaseModel):
    """内容分类模型"""
    id: Optional[str] = Field(default=None, alias="_id", description="分类ID")
    name: str = Field(..., description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")
    parent_id: Optional[str] = Field(None, description="父分类ID")
    color: Optional[str] = Field(None, description="分类颜色标识")
    icon: Optional[str] = Field(None, description="分类图标")
    sort_order: int = Field(default=0, description="排序")
    is_active: bool = Field(default=True, description="是否启用")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


class DownloadConfig(BaseModel):
    """下载配置"""
    quality: str = Field(default="best", description="质量设置")
    format: str = Field(default="mp4", description="格式设置")
    include_subtitles: bool = Field(default=False, description="包含字幕")
    include_thumbnail: bool = Field(default=True, description="包含缩略图")
    include_metadata: bool = Field(default=True, description="包含元数据")


class FilterConfig(BaseModel):
    """过滤配置"""
    date_range: Optional[Dict[str, datetime]] = Field(None, description="日期范围")
    min_duration: Optional[int] = Field(None, description="最小时长")
    max_duration: Optional[int] = Field(None, description="最大时长")
    keywords: List[str] = Field(default_factory=list, description="关键词")
    exclude_keywords: List[str] = Field(default_factory=list, description="排除关键词")


class TaskProgress(BaseModel):
    """任务进度"""
    total_items: int = Field(default=0, description="总项目数")
    completed_items: int = Field(default=0, description="已完成项目数")
    failed_items: int = Field(default=0, description="失败项目数")
    current_item: Optional[str] = Field(None, description="当前项目")
    percentage: float = Field(default=0.0, description="完成百分比")


class TaskResult(BaseModel):
    """任务结果"""
    success_count: int = Field(default=0, description="成功数量")
    failed_count: int = Field(default=0, description="失败数量")
    total_size: int = Field(default=0, description="总大小")
    failed_urls: List[str] = Field(default_factory=list, description="失败URL列表")


class DownloadTask(BaseModel):
    """下载任务模型"""
    id: Optional[str] = Field(default=None, alias="_id", description="任务ID")
    task_name: str = Field(..., description="任务名称")
    task_type: str = Field(..., description="任务类型")
    source_urls: List[str] = Field(..., description="源URL列表")
    target_platform: str = Field(..., description="目标平台")
    download_config: DownloadConfig = Field(default_factory=DownloadConfig, description="下载配置")
    filter_config: Optional[FilterConfig] = Field(None, description="过滤配置")
    progress: TaskProgress = Field(default_factory=TaskProgress, description="进度信息")
    status: str = Field(default="pending", description="状态")
    result: Optional[TaskResult] = Field(None, description="结果统计")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    created_by: str = Field(..., description="创建用户")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


# API请求/响应模型
class ContentListRequest(BaseModel):
    """内容列表请求"""
    page: int = Field(default=1, ge=1, description="页码")
    limit: int = Field(default=20, ge=1, le=100, description="每页数量")
    platform: Optional[str] = Field(None, description="平台过滤")
    category: Optional[str] = Field(None, description="分类过滤")
    tags: Optional[List[str]] = Field(None, description="标签过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    content_type: Optional[str] = Field(None, description="内容类型过滤")


class ContentListResponse(BaseModel):
    """内容列表响应"""
    items: List[CompetitorContent] = Field(..., description="内容列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    limit: int = Field(..., description="每页数量")
    has_next: bool = Field(..., description="是否有下一页")


class CreateDownloadTaskRequest(BaseModel):
    """创建下载任务请求"""
    task_name: str = Field(..., description="任务名称")
    task_type: str = Field(..., description="任务类型")
    source_urls: List[str] = Field(..., description="源URL列表")
    target_platform: str = Field(..., description="目标平台")
    download_config: Optional[DownloadConfig] = Field(None, description="下载配置")
    filter_config: Optional[FilterConfig] = Field(None, description="过滤配置")


class CategoryTreeNode(BaseModel):
    """分类树节点"""
    id: str = Field(..., description="分类ID")
    name: str = Field(..., description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")
    color: Optional[str] = Field(None, description="颜色")
    icon: Optional[str] = Field(None, description="图标")
    children: List['CategoryTreeNode'] = Field(default_factory=list, description="子分类")
    content_count: int = Field(default=0, description="内容数量")


# 对标账号相关模型
class BenchmarkAccountData(BaseModel):
    """对标账号数据"""
    followers: Optional[int] = Field(None, description="粉丝数")
    following: Optional[int] = Field(None, description="关注数")
    posts_count: Optional[int] = Field(None, description="发布数")
    avg_views: Optional[int] = Field(None, description="平均观看数")
    avg_likes: Optional[int] = Field(None, description="平均点赞数")
    engagement_rate: Optional[float] = Field(None, description="互动率")
    last_post_date: Optional[datetime] = Field(None, description="最后发布时间")
    growth_rate: Optional[float] = Field(None, description="增长率")


class OurAccountInfo(BaseModel):
    """关联的我们的账号信息"""
    id: str = Field(..., description="账号ID")
    username: str = Field(..., description="用户名")
    display_name: str = Field(..., description="显示名称")
    platform_id: str = Field(..., description="平台ID")
    status: str = Field(..., description="账号状态")


class BenchmarkAccount(BaseModel):
    """对标账号模型"""
    id: Optional[str] = Field(default=None, alias="_id", description="对标账号ID")
    our_account_id: str = Field(..., description="我们的账号ID")
    our_account_info: Optional[OurAccountInfo] = Field(None, description="关联的我们的账号信息")
    platform: str = Field(..., description="平台名称")
    account_name: str = Field(..., description="对标账号名称")
    account_url: str = Field(..., description="对标账号链接")
    benchmark_type: str = Field(..., description="对标类型: original(原创), recreate(二创), repost(搬运)")
    description: Optional[str] = Field(None, description="账号描述")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    account_data: BenchmarkAccountData = Field(default_factory=BenchmarkAccountData, description="账号数据")
    tags: List[str] = Field(default_factory=list, description="标签")
    priority: int = Field(default=1, description="优先级 1-5")
    status: str = Field(default="active", description="状态: active, inactive, monitoring")
    notes: Optional[str] = Field(None, description="备注")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    created_by: str = Field(..., description="创建用户")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


class BenchmarkAccountGroup(BaseModel):
    """对标账号分组模型"""
    id: Optional[str] = Field(default=None, alias="_id", description="分组ID")
    name: str = Field(..., description="分组名称")
    description: Optional[str] = Field(None, description="分组描述")
    our_account_id: str = Field(..., description="我们的账号ID")
    benchmark_account_ids: List[str] = Field(default_factory=list, description="对标账号ID列表")
    color: Optional[str] = Field(None, description="分组颜色")
    sort_order: int = Field(default=0, description="排序")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


# API请求/响应模型
class CreateBenchmarkAccountRequest(BaseModel):
    """创建对标账号请求"""
    our_account_id: str = Field(..., description="我们的账号ID")
    platform: str = Field(..., description="平台名称")
    account_name: str = Field(..., description="对标账号名称")
    account_url: str = Field(..., description="对标账号链接")
    benchmark_type: str = Field(..., description="对标类型")
    description: Optional[str] = Field(None, description="账号描述")
    tags: List[str] = Field(default_factory=list, description="标签")
    priority: int = Field(default=1, description="优先级")


class BenchmarkAccountListRequest(BaseModel):
    """对标账号列表请求"""
    page: int = Field(default=1, ge=1, description="页码")
    limit: int = Field(default=20, ge=1, le=100, description="每页数量")
    our_account_id: Optional[str] = Field(None, description="我们的账号ID过滤")
    platform: Optional[str] = Field(None, description="平台过滤")
    benchmark_type: Optional[str] = Field(None, description="对标类型过滤")
    status: Optional[str] = Field(None, description="状态过滤")
    search: Optional[str] = Field(None, description="搜索关键词")


class BenchmarkAccountListResponse(BaseModel):
    """对标账号列表响应"""
    items: List[BenchmarkAccount] = Field(..., description="对标账号列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    limit: int = Field(..., description="每页数量")
    has_next: bool = Field(..., description="是否有下一页")


class BenchmarkAccountStats(BaseModel):
    """对标账号统计"""
    total_count: int = Field(..., description="总对标账号数")
    by_platform: Dict[str, int] = Field(..., description="按平台统计")
    by_type: Dict[str, int] = Field(..., description="按类型统计")
    by_status: Dict[str, int] = Field(..., description="按状态统计")
    top_performers: List[BenchmarkAccount] = Field(..., description="表现最好的账号")


# 更新前向引用
CategoryTreeNode.model_rebuild()
