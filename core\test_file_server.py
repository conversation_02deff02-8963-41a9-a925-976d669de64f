import asyncio
import sys
import os
from pathlib import Path
sys.path.append('src')

from src.services.file_server import CoreFileServer

async def test_file_server():
    print("开始测试文件服务器...")
    
    # 创建文件服务器实例
    server = CoreFileServer(host="0.0.0.0", port=8002)  # 使用不同端口避免冲突
    
    print(f"安全根目录: {[str(root) for root in server.safe_roots]}")
    
    # 检查缩略图文件是否在安全根目录中
    thumbnail_file = "bf42ba3af0698ce9d2dafdf554da91e1_mid_120x68_q75.jpg"
    thumbnail_path = ".video_cache/thumbnails/" + thumbnail_file
    
    print(f"要查找的文件路径: {thumbnail_path}")
    
    # 测试路径解析
    test_resolve_paths = [
        ".video_cache/thumbnails/" + thumbnail_file,
        "thumbnails/" + thumbnail_file
    ]

    for test_resolve_path in test_resolve_paths:
        actual_path = server._resolve_file_path(test_resolve_path)
        print(f"解析路径 '{test_resolve_path}' -> {actual_path}")

        if actual_path:
            print(f"  文件是否存在: {actual_path.exists()}")
            if actual_path.exists():
                print(f"  文件大小: {actual_path.stat().st_size} 字节")
    
    # 测试文件URL生成
    test_file_path = str(Path.cwd() / ".video_cache" / "thumbnails" / thumbnail_file)
    url = server.get_file_url(test_file_path)
    print(f"生成的URL: {url}")

    # 测试不同的路径格式
    test_paths = [
        str(Path.cwd() / ".video_cache" / "thumbnails" / thumbnail_file),
        ".video_cache/thumbnails/" + thumbnail_file,
        "thumbnails/" + thumbnail_file
    ]

    for test_path in test_paths:
        url = server.get_file_url(test_path)
        print(f"路径 '{test_path}' -> URL: {url}")

if __name__ == "__main__":
    asyncio.run(test_file_server())
