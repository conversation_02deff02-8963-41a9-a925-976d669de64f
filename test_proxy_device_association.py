#!/usr/bin/env python3
"""
测试IP管理模块的关联设备功能
"""

import asyncio
import httpx
import json
from datetime import datetime

# 测试配置
BACKEND_URL = "http://localhost:8000"
TEST_PROXY_DATA = {
    "region": "HK",
    "ip_address": "*************",
    "port": 8080,
    "proxy_type": "vless",
    "v2ray_config": "vless://test-config",
    "provider": "测试提供商",
    "notes": "测试代理IP"
}

TEST_DEVICE_DATA = {
    "name": "测试设备1",
    "status": "running",
    "type": "雷电模拟器",
    "core_id": "test_core",
    "created_at": datetime.now().isoformat(),
    "updated_at": datetime.now().isoformat()
}

async def test_proxy_device_association():
    """测试代理IP和设备关联功能"""
    print("开始测试IP管理模块的关联设备功能...")
    
    async with httpx.AsyncClient() as client:
        try:
            # 1. 创建测试代理IP
            print("\n1. 创建测试代理IP...")
            response = await client.post(
                f"{BACKEND_URL}/api/v1/proxy/create",
                json=TEST_PROXY_DATA
            )
            if response.status_code == 200:
                proxy_result = response.json()
                proxy_id = proxy_result["proxy_id"]
                print(f"✓ 代理IP创建成功，ID: {proxy_id}")
            else:
                print(f"✗ 代理IP创建失败: {response.text}")
                return
            
            # 2. 创建测试设备（模拟设备数据）
            print("\n2. 创建测试设备...")
            # 注意：这里需要直接插入到MongoDB，因为设备通常由Core服务管理
            # 为了测试，我们假设设备已存在
            device_id = "test_device_1"
            print(f"✓ 使用测试设备ID: {device_id}")
            
            # 3. 测试获取活跃设备列表
            print("\n3. 测试获取活跃设备列表...")
            response = await client.get(f"{BACKEND_URL}/api/v1/proxy/devices/active")
            if response.status_code == 200:
                active_devices = response.json()
                print(f"✓ 获取到 {len(active_devices)} 个活跃设备")
                for device in active_devices:
                    print(f"  - {device['name']} ({device['id']}) - {device['status']}")
            else:
                print(f"✗ 获取活跃设备失败: {response.text}")
            
            # 4. 测试关联设备和代理IP
            print(f"\n4. 测试关联设备 {device_id} 和代理IP {proxy_id}...")
            response = await client.post(
                f"{BACKEND_URL}/api/v1/proxy/{proxy_id}/associate/{device_id}"
            )
            if response.status_code == 200:
                association_result = response.json()
                mapping_id = association_result["mapping_id"]
                print(f"✓ 设备关联成功，映射ID: {mapping_id}")
            else:
                print(f"✗ 设备关联失败: {response.text}")
                return
            
            # 5. 验证关联结果
            print(f"\n5. 验证关联结果...")
            response = await client.get(f"{BACKEND_URL}/api/v1/proxy/{proxy_id}")
            if response.status_code == 200:
                proxy_info = response.json()
                associated_devices = proxy_info.get("associated_devices", [])
                print(f"✓ 代理IP关联了 {len(associated_devices)} 个设备")
                for device in associated_devices:
                    print(f"  - 设备ID: {device['device_id']}")
                    print(f"    设备名称: {device.get('device_name', 'N/A')}")
                    print(f"    设备状态: {device.get('device_status', 'N/A')}")
                    print(f"    设备类型: {device.get('device_type', 'N/A')}")
            else:
                print(f"✗ 获取代理IP信息失败: {response.text}")
            
            # 6. 测试取消关联
            print(f"\n6. 测试取消关联...")
            response = await client.delete(
                f"{BACKEND_URL}/api/v1/proxy/{proxy_id}/disassociate/{device_id}"
            )
            if response.status_code == 200:
                print("✓ 取消关联成功")
            else:
                print(f"✗ 取消关联失败: {response.text}")
            
            # 7. 清理测试数据
            print(f"\n7. 清理测试数据...")
            response = await client.delete(f"{BACKEND_URL}/api/v1/proxy/{proxy_id}")
            if response.status_code == 200:
                print("✓ 测试代理IP删除成功")
            else:
                print(f"✗ 删除测试代理IP失败: {response.text}")
            
            print("\n✓ 所有测试完成！")
            
        except Exception as e:
            print(f"\n✗ 测试过程中发生错误: {str(e)}")

async def test_active_devices_filter():
    """测试活跃设备过滤功能"""
    print("\n开始测试活跃设备过滤功能...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BACKEND_URL}/api/v1/proxy/devices/active")
            if response.status_code == 200:
                active_devices = response.json()
                print(f"✓ 获取到 {len(active_devices)} 个活跃设备")
                
                # 验证所有设备都是活跃状态
                active_statuses = ["running", "starting"]
                for device in active_devices:
                    if device["status"] not in active_statuses:
                        print(f"✗ 发现非活跃设备: {device['name']} - {device['status']}")
                        return False
                
                print("✓ 所有返回的设备都处于活跃状态")
                return True
            else:
                print(f"✗ 获取活跃设备失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"✗ 测试活跃设备过滤时发生错误: {str(e)}")
            return False

if __name__ == "__main__":
    print("ThunderHub IP管理模块关联设备功能测试")
    print("=" * 50)
    
    # 运行测试
    asyncio.run(test_active_devices_filter())
    asyncio.run(test_proxy_device_association())
