#!/usr/bin/env python3
"""
诊断视频加载失败问题的脚本
"""

import requests
import json
import os
import sys
from pathlib import Path

def check_service_status():
    """检查服务状态"""
    print("=== 检查服务状态 ===")
    
    services = [
        ("Backend API", "http://localhost:8000/docs"),
        ("Core文件服务器", "http://localhost:8001/health"),
    ]
    
    for name, url in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✓ {name}: 运行正常 ({url})")
            else:
                print(f"✗ {name}: 响应异常 {response.status_code} ({url})")
        except requests.exceptions.ConnectionError:
            print(f"✗ {name}: 连接失败 ({url})")
        except requests.exceptions.Timeout:
            print(f"✗ {name}: 连接超时 ({url})")
        except Exception as e:
            print(f"✗ {name}: 错误 {str(e)} ({url})")

def test_video_api():
    """测试视频相关API"""
    print("\n=== 测试视频API ===")
    
    # 测试视频文件路径（请根据实际情况修改）
    test_paths = [
        r"C:\Users\<USER>\Videos\Sample Videos\Wildlife.wmv",  # Windows示例视频
        r"H:\PublishSystem\test.mp4",  # 自定义路径
        r"D:\Videos\test.mp4",  # 另一个可能的路径
    ]
    
    # 找到一个存在的视频文件
    test_video_path = None
    for path in test_paths:
        if os.path.exists(path):
            test_video_path = path
            print(f"找到测试视频: {path}")
            break
    
    if not test_video_path:
        print("⚠ 未找到测试视频文件，请手动指定一个视频文件路径")
        test_video_path = input("请输入视频文件路径: ").strip()
        if not test_video_path or not os.path.exists(test_video_path):
            print("✗ 无效的视频文件路径，跳过API测试")
            return
    
    # 测试生成缩略图API
    print(f"\n测试缩略图生成API...")
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/filesystem/video/thumbnail",
            json={
                "video_path": test_video_path,
                "max_width": 320,
                "max_height": 180,
                "quality": 85
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"✓ 缩略图生成成功")
                print(f"  路径: {data.get('thumbnail_path', 'N/A')}")
                print(f"  URL: {data.get('thumbnail_url', 'N/A')}")
                print(f"  尺寸: {data.get('actual_width', 0)}x{data.get('actual_height', 0)}")
                
                # 测试缩略图URL访问
                thumbnail_url = data.get('thumbnail_url')
                if thumbnail_url:
                    try:
                        thumb_response = requests.get(thumbnail_url, timeout=10)
                        if thumb_response.status_code == 200:
                            print(f"✓ 缩略图URL可访问")
                        else:
                            print(f"✗ 缩略图URL访问失败: {thumb_response.status_code}")
                    except Exception as e:
                        print(f"✗ 缩略图URL访问错误: {str(e)}")
            else:
                print(f"✗ 缩略图生成失败: {data.get('error', '未知错误')}")
        else:
            print(f"✗ 缩略图API请求失败: {response.status_code}")
            print(f"  响应: {response.text}")
    except Exception as e:
        print(f"✗ 缩略图API请求异常: {str(e)}")
    
    # 测试视频预览信息API
    print(f"\n测试视频预览信息API...")
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/filesystem/video/preview-info",
            json={
                "video_path": test_video_path,
                "include_thumbnail": True,
                "include_detailed_metadata": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"✓ 视频预览信息获取成功")
                media_info = data.get("media_info", {})
                print(f"  时长: {media_info.get('duration', 0)} 秒")
                print(f"  分辨率: {media_info.get('resolution', 'N/A')}")
                print(f"  视频编码: {media_info.get('video_codec', 'N/A')}")
            else:
                print(f"✗ 视频预览信息获取失败: {data.get('error', '未知错误')}")
        else:
            print(f"✗ 视频预览信息API请求失败: {response.status_code}")
            print(f"  响应: {response.text}")
    except Exception as e:
        print(f"✗ 视频预览信息API请求异常: {str(e)}")

def check_file_permissions():
    """检查文件权限"""
    print("\n=== 检查文件权限 ===")
    
    # 检查常见的视频目录
    video_dirs = [
        r"C:\Users\<USER>\Videos",
        r"H:\PublishSystem",
        r"D:\Videos",
        Path.cwd() / "test_videos"
    ]
    
    for dir_path in video_dirs:
        if os.path.exists(dir_path):
            try:
                # 尝试列出目录内容
                files = list(os.listdir(dir_path))
                video_files = [f for f in files if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv'))]
                print(f"✓ {dir_path}: 可访问，包含 {len(video_files)} 个视频文件")
                if video_files:
                    print(f"  示例: {video_files[0]}")
            except PermissionError:
                print(f"✗ {dir_path}: 权限不足")
            except Exception as e:
                print(f"✗ {dir_path}: 错误 {str(e)}")
        else:
            print(f"- {dir_path}: 不存在")

def check_browser_console_errors():
    """提供浏览器控制台检查指导"""
    print("\n=== 浏览器控制台检查指导 ===")
    print("请在浏览器中打开开发者工具（F12），查看以下内容：")
    print("1. Console标签页中是否有错误信息")
    print("2. Network标签页中视频相关请求的状态")
    print("3. 特别关注以下请求：")
    print("   - /api/v1/filesystem/video/thumbnail")
    print("   - /api/v1/filesystem/video/preview-info")
    print("   - http://localhost:8001/files/...")
    print("4. 检查请求的响应状态码和错误信息")

def main():
    """主函数"""
    print("视频加载失败诊断工具")
    print("=" * 50)
    
    check_service_status()
    test_video_api()
    check_file_permissions()
    check_browser_console_errors()
    
    print("\n=== 诊断完成 ===")
    print("如果发现问题，请根据上述信息进行修复。")
    print("常见解决方案：")
    print("1. 确保Backend服务运行在端口8000")
    print("2. 确保Core服务运行在端口50051，文件服务器运行在端口8001")
    print("3. 检查视频文件路径是否正确")
    print("4. 检查防火墙设置是否阻止了端口访问")

if __name__ == "__main__":
    main()
