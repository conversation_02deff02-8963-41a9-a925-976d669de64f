# ThunderHub 系统架构设计文档

**版本**: v1.4.4  
**最后更新**: 2025/05/03  

## 1. 技术架构

### 1.1 前端技术栈

- **框架**: Vue3 + Composition API
- **构建工具**: Vite (支持代码分割、懒加载)
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **测试框架**:
  - Vitest (单元测试)
  - Cypress (E2E测试)
- **语言**: TypeScript
- **可访问性**: axe-core (a11y测试)
- **部署**: 运行在Docker容器中（基于Node.js镜像）

### 1.2 后端技术栈

- **框架**: FastAPI (Python 3.10+)
- **数据库**: MongoDB 5.0+（支持分片、时间序列集合）
- **缓存**: Redis 7.0（实时状态、发布/订阅）
- **接口规范**:
  - RESTful API
  - GraphQL（复杂查询）
- **测试框架**: pytest
- **依赖管理**: Poetry（确保依赖一致性）
- **API文档**:
  - Swagger（OpenAPI）
  - Redoc
- **服务发现**: Consul（动态注册/管理多个Core）
- **负载均衡**: Traefik（支持动态分配设备或指定设备）
- **部署**: 运行在Docker容器中（基于Python镜像）

### 1.3 核心引擎技术栈

部署位置：Core 服务直接运行在负责设备管理的 Windows 主机上（与物理设备或模拟器集群紧密相连）

设备控制：

- Appium Server (v2.0+)
- ADB (Android Debug Bridge)
- ldconsole（雷电模拟器控制协议）
- XCTest (iOS 设备)

高级设备管理：

- Frida：
  - 运行时应用行为监控
  - 动态行为修改（函数拦截、参数修改）
  - 代码注入（安全限制）
  - 设备状态深度分析

通信协议：

- ldconsole（雷电模拟器，基于命令行封装）
- gRPC (高性能设备控制)
- WebSocket (实时通信，启用 permessage-deflate 压缩)
- REST (调试和管理接口)

关键组件：

- 设备连接池（支持连接复用、健康检查）
- 多进程管理（Windows 进程隔离）
- 异步 IO（asyncio）

多 Core 管理：

- 后端通过 Consul 服务发现动态注册/管理多个 Core
- Traefik 负载均衡根据任务需求分配设备：
  - 动态分配：随机或基于负载选择设备
  - 指定设备：通过任务参数（如设备 ID）分配特定设备
- 安全：Frida 操作受权限控制，Core 不直接联系前端

### 1.4 架构特点

- 前后端分离，运行在 Docker 容器中
- 多 Core 架构，Core 直接部署在 Windows 设备主机上
- 高可用性（负载均衡 + 服务副本）
- 轻量级数据库 + 分布式存储
- 支持快速迭代与动态扩展
- Core 与前端通信必须通过后端中转
- 集成分布式追踪与监控

### 1.5 开发环境要求

- 前端：Node.js v18+（Docker 镜像）
- 后端：Python 3.10+（Poetry 管理依赖，Docker 镜像）
- Core：Python 3.10+（Windows 主机，Poetry 管理依赖）

统一环境：

- 前后端：Docker Compose 管理开发环境
- Core：Windows 主机手动配置虚拟环境（venv）
- 环境变量：.env 文件（使用 python-dotenv 和 vite-env）
- 自动化：提供 Windows 初始化脚本（scripts/setup.ps1）

## 2. 功能模块

### 2.1 认证与用户管理 已完成

功能：

- 登录/登出
- JWT 认证（支持刷新令牌）
- 角色权限管理（RBAC）
- 多因素认证（MFA，敏感操作）
- 安全：敏感数据加密存储，日志脱敏

### 2.2 设备管理 (核心模块)

功能：

- 模拟器生命周期管理（启动/停止/重启，基于 ldconsole）
- 状态监控（CPU、内存、电池状态）
- 配置采集与批量操作（支持事务性）
- 设备指纹浏览器集成（反检测机制）

Frida 高级管理：

- 运行时监控（应用行为、内存使用）
- 动态修改（函数拦截、参数调整）
- 设备状态深度分析

优化：

- 实时告警（WebSocket 推送，经后端中转）
- 设备分组与批量导入/导出
- 多 Core 分布式管理（Consul + Traefik）

### 2.3 任务调度 (开发中)

核心功能：

- 定时任务编排（支持优先级）
- 任务执行历史与实时日志
- 分布式任务队列（Celery）

扩展设计：

- 标准化任务接口（JSON Schema）
- 可插拔流程引擎
- AI 决策扩展（ONNX 模型）

```python
class TaskPlugin:
    def pre_execute(task): pass
    def post_execute(result): pass
```

### 2.4 报表中心 (规划中)

功能：

- 测试数据全景看板（ECharts）
- 自定义报告生成（PDF/Excel 导出）
- 历史数据对比分析

优化：MongoDB 聚合管道

### 2.5 社媒应用管理 (规划中)

功能：

- 多平台账号管理（API 合规）
- 内容发布接口
- 互动数据分析（情感分析模型）
- 安全：账号数据 AES 加密

### 2.6 文件管理系统 (规划中)

功能：

- 文件浏览器（MinIO 分布式存储）
- 全文检索（Elasticsearch）
- 分类与版本管理
- 优化：高并发文件访问

### 3. 前后端及 Core 项目结构

```目录结构
frontend/
  src/
    api/          # API 请求（Axios 拦截器）
    components/   # 公共组件
    views/        # 页面视图
    store/        # Pinia 状态管理
    hooks/        # 自定义 Vue 钩子
    assets/       # 静态资源
  vite.config.ts  # Vite 配置
  Dockerfile      # 前端 Docker 镜像

backend/
├── app/                  # 应用核心容器
│   ├── __init__.py
│   ├── main.py           # FastAPI 入口
│   ├── config/           # 配置管理
│   │   ├── settings.py   # 应用设置
│   │   └── database.py   # 数据库配置
│   ├── api/              # API 路由
│   │   ├── v1/           # 版本化路由
│   │   └── internal/     # 内部 API
│   ├── core/             # 核心业务逻辑
│   │   ├── services/     # 按领域拆分（设备、任务）
│   │   ├── schemas/      # 数据模型
│   │   └── models/       # 业务模型
│   ├── dependencies/     # 依赖管理
│   ├── middleware/       # 中间件（限流、认证）
│   ├── exceptions/       # 自定义异常
│   └── utils/            # 工具函数
├── scripts/              # 管理脚本
├── tests/                # 单元测试
├── migrations/           # 数据库迁移
├── run_server.py         # 启动入口
├── .env                  # 环境变量
├── Dockerfile            # 后端 Docker 镜像
└── docker-compose.yml    # 开发环境配置

core/  # 部署在 Windows 主机
  ├── api/                    # 接口服务
  │   ├── grpc_handler.py     # gRPC 接口
  │   └── rest_handler.py     # REST 接口
  ├── devices/                # 设备控制
  │   ├── base.py             # 设备抽象接口
  │   ├── ldplayer/           # 雷电模拟器
  │   │   ├── controller.py   # ldconsole 控制器
  │   │   ├── manager.py      # 生命周期管理
  │   │   └── types.py        # 类型定义
  │   ├── android/            # 安卓设备
  │   │   └── adb.py          # ADB 封装
  │   └── ios/                # iOS 设备
  │       └── xctest.py       # XCTest 管理
  ├── services/               # 核心服务
  │   ├── consul.py           # 注册Core到Consul
  │   ├── device_factory.py   # 设备工厂（对象池）
  │   ├── device_sync.py      # 设备状态同步
  │   ├── appium/            # Appium 服务
  │   │   ├── driver.py       # 驱动封装
  │   │   └── service.py      # 服务管理
  │   ├── frida/             # Frida 服务
  │   │   ├── injector.py    # 代码注入
  │   │   ├── monitor.py     # 运行时监控
  │   │   ├── modifier.py    # 动态行为修改
  │   │   └── manager.py     # Frida 管理
  │   └── ldconsole/         # ldconsole 协议服务
  │       ├── controller.py   # 协议控制器
  │       └── server.py       # 协议服务端
  ├── main_service.py         # 主流程控制
  ├── main.py                 # 服务入口
  ├── config/                 # 设备配置
  ├── requirements.txt        # 依赖清单
  └── scripts/                # Windows 启动脚本（setup.ps1）
```

### 4. 模块架构关系

``` 目录结构
                    +---------------+
                    |   frontend/   | ← Docker 容器
                    +-------┬-------+
                            ↑ HTTPS/WebSocket (TLS)
                    +-------┴-------+
                    |   backend/    | ← Docker 容器
                    | (业务逻辑控制) |
                    +-------┬-------+
                            ↑ gRPC/REST
                    +-------┴-------+
                    |    core/      | ← Windows 主机（多实例）
                    | (设备操作封装) |
                    +-------┬-------+
                            ↓ ADB/Appium/ldconsole/XCTest/Frida
                    物理设备/模拟器集群
```

### 4.1 核心服务 (v1.4.4 更新)

**部署**：Core 直接运行在 Windows 主机上，靠近设备

**多 Core 管理**：

- **Consul 服务发现**：
- 每个 Core 启动时向 Consul 注册（包含设备 ID、IP、端口等元数据）

- 后端通过 Consul 查询可用 Core 实例

**Traefik 负载均衡**：

- **动态分配**：根据设备负载（CPU、内存）或任务优先级选择 Core/设备

- **指定设备**：任务请求中携带设备 ID（如 `device_id: "device-123"`），Traefik 路由到对应 Core

- **配置**：Traefik 使用 Consul 动态更新路由规则

**ldconsole 协议**：
封装雷电模拟器命令行工具（如 ldconsole.exe）

- 支持启动、停止、配置修改

- **配置文件**：`core/config/ldplayer_config.yaml`

**Frida 服务**：

- **运行时监控**：跟踪应用行为、内存使用

- **动态修改**：函数拦截、参数调整

- **代码注入**：安全限制，权限控制

- **状态分析**：设备运行时数据采集

#### 4.1.1 设备同步服务

**功能**：

- 定时同步设备配置（ldconsole）

- 维护状态一致性

配置变更通知（通过后端 WebSocket 推送）

**实现**：

- 文件：`core/services/device_sync.py`

- 依赖：ldconsole Controller、MongoDB（事务支持）

配置：同步间隔默认 3600 秒

异常处理：
自动重试（3 次）

- 错误日志脱敏

### 4.2 服务接口规范

#### 4.2.1 接口协议

- **主协议**：gRPC（高性能）
- **备协议**：REST/JSON（调试）
- **实时控制**：WebSocket（TLS 加密，消息压缩）

#### 4.2.2 服务端点

- 设备管理：`/api/v1/devices`
- 任务控制：`/api/v1/tasks`
- 状态监控：`/api/v1/status`
- GraphQL：`/graphql`

#### 4.2.3 调用约束

- **路由**：API 网关（Traefik）
- **服务发现**：Consul
- **追踪**：X-Request-ID（OpenTelemetry）
- **限流**：FastAPI Rate Limiter

### 4.3 数据库设计

#### 技术选型

- **主数据库**：MongoDB 5.0+（分片、时间序列集合）
- **缓存**：Redis 7.0（状态存储、发布/订阅）
- **备份**：每日 mongodump
- **一致性**：变更数据捕获（CDC）

#### 数据流向

- Core 将设备状态（如 CPU、内存、Frida 监控数据）推送到 Redis
- 后端从 Redis 获取实时状态，通过 WebSocket 推送至前端
- 后端定时将 Redis 数据快照写入 MongoDB
- 前端通过后端 REST/GraphQL 接口查询历史数据
- Core 不直接联系前端，所有通信经后端中转

```mermaid
graph LR
    A[Core: 设备控制] -->|实时状态| B[Redis]
    B -->|WebSocket 推送| C[后端: FastAPI]
    C -->|WebSocket| D[前端]
    B -->|定时快照| E[(MongoDB)]
    E -->|历史查询| C
    C -->|REST/GraphQL| D
```

分层存储
热数据：实时状态 → Redis

温数据：近期配置 → MongoDB 内存映射

冷数据：历史记录 → MongoDB 磁盘存储

索引：常用字段索引

### 4.4 通信协议

#### 双 WebSocket 架构

- **认证通道** (`/auth-socket.io`)：
  - 用途：认证、权限变更
  - 特点：低频、心跳 25 秒、3 次重连

- **设备通道** (`/device-socket.io`)：
  - 用途：设备状态、控制指令
  - 特点：高频（>10 msg/s）、5 次重连（3-5 秒）、消息压缩
  - 指标：延迟 <500ms，丢包率 <0.1%，支持 100+ 设备

#### 健康管理

- 双向心跳（Ping/Pong）
- 无效连接清理（5 分钟）
- TLS 加密

#### 协议实现

- **REST**：HTTP/1.1，JSON，JWT 认证
- **WebSocket**：Socket.IO，事件规范（auth_、device_），JSON Schema
- **gRPC**：Protocol Buffers，服务端流式传输
- **ldconsole**：命令行封装，配置文件驱动
- **Frida**：运行时接口，权限控制

## 5. 系统入口

### 5.1 后端服务入口

**文件**：`backend/run_server.py`

**功能**：

- 自动检测端口
- 启动 FastAPI（热重载）
- 集成 OpenTelemetry

**启动方式**：

```bash
docker-compose up backend
```

**访问地址**：`http://0.0.0.0:8000`

**环境变量**：`.env`（`ENV=development`, `PORT=8000`）

### 5.2 前端服务入口

**文件**：`frontend/vite.config.js`

**启动方式**：

```bash
docker-compose up frontend
```

**访问地址**：`https://0.0.0.0:5713`

### 5.3 Core 服务入口

**文件**：`core/main.py`

**部署**：Windows 主机

**启动方式**：

```powershell
# Windows PowerShell
cd core
.\venv\Scripts\activate
poetry install
python main.py
```

**服务发现**：注册到 Consul

**配置**：`core/config/ldplayer_config.yaml`

### 5.4 数据库启动

**MongoDB**：

```powershell
# 停止服务
net stop MongoDB

# 检查端口占用
netstat -a -n -o | findstr "27017"

# 启动服务
net start MongoDB

# Docker方式启动
docker-compose up mongodb
```

**Redis**：

- Docker Compose 启动
- 每日 mongodump 备份

### 5.5 一键启动（前端+后端+数据库）

**文件**：`docker-compose.yml`

**命令**：

```bash
docker-compose up -d
```

**包含服务**：

- 前端
- 后端
- MongoDB
- Redis
- Consul

## 6. 部署与运维

### 6.1 部署

- **前端/后端**：Docker + Kubernetes
- **Core**：Windows 主机（手动部署）
- **负载均衡**：Traefik（多 Core 路由）
- **CI/CD**：GitHub Actions

### 6.2 可观测性

- **监控**：Prometheus + Grafana
- **日志**：Loki + Grafana
- **追踪**：OpenTelemetry + Jaeger
- **告警**：Sentry

### 6.3 安全性

- **通信**：HTTPS，WebSocket TLS
- **认证**：JWT + MFA
- **防护**：FastAPI 限流，Cloudflare DDoS 防护
- **Frida**：权限控制，限制非授权操作
- **日志**：敏感数据脱敏

## 7. 模块架构关系图

```mermaid
graph TD
    A[前端: Vue3] -->|HTTPS/WebSocket| B[后端: FastAPI]
    B -->|gRPC/REST| C[Core 1: Windows 主机]
    B -->|gRPC/REST| D[Core 2: Windows 主机]
    B -->|gRPC/REST| E[Core N: Windows 主机]
    C -->|ADB/Appium/ldconsole/Frida| F[设备集群]
    D -->|ADB/Appium/ldconsole/Frida| F
    E -->|ADB/Appium/ldconsole/Frida| F
    C -->|实时状态| G[Redis]
    D -->|实时状态| G
    E -->|实时状态| G
    G -->|WebSocket| B
    B -->|WebSocket| A
    G -->|定时快照| H[(MongoDB)]
    H -->|历史查询| B
    B -->|REST/GraphQL| A
    B -->|监控| I[Prometheus/Grafana]
    B -->|追踪| J[Jaeger]
    B -->|告警| K[Sentry]
```

## 8. 优化亮点

- **多 Core 管理**：
  - Consul 服务发现动态注册 Core，包含设备元数据
  - Traefik 负载均衡支持动态分配或指定设备

- **ldconsole 协议**：
  - 高效封装雷电模拟器命令行
  - 支持批量操作与状态同步

- **Frida 高级管理**：
  - 运行时监控、动态修改、状态分析
  - 权限控制确保安全

- **通信架构**：
  - Core 不直接联系前端，所有状态通过后端中转
  - WebSocket 推送实时状态，REST/GraphQL 查询历史数据

- **部署架构**：
  - 前端/后端 Docker 容器化
  - Core 运行在 Windows 主机，贴近设备

- **性能与安全**：
  - WebSocket 消息压缩
  - TLS 加密、MFA、限流

- **可观测性**：
  - OpenTelemetry 分布式追踪
  - Prometheus/Sentry 实时监控

## 9. 示例配置

### 9.1 Docker Compose（前端+后端+数据库）

```yaml
version: '3.8'
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "5713:5713"
    environment:
      - VITE_API_URL=https://backend:8000
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENV=development
      - MONGODB_URL=mongodb://mongodb:27017
      - REDIS_URL=redis://redis:6379
      - CONSUL_URL=consul:8500
    depends_on:
      - mongodb
      - redis
      - consul

  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:7.0
    ports:
      - "6379:6379"

  consul:
    image: consul:latest
    ports:
      - "8500:8500"

volumes:
  mongodb_data:
```

### 9.2 Core Windows 启动脚本（scripts/setup.ps1）

```powershell
# PowerShell 脚本，用于初始化 Core 环境
$ErrorActionPreference = "Stop"

# 创建虚拟环境
python -m venv venv
.\venv\Scripts\activate

# 安装依赖
poetry install

# 配置环境变量
$env:CONSUL_URL = "consul:8500"
$env:LDCONSOLE_CONFIG = "config/ldplayer_config.yaml"
$env:FRIDA_CONFIG = "config/frida_config.yaml"

# 启动 Core
python main.py
```

### 9.3 Frida 配置示例（config/frida_config.yaml）

```yaml
frida:
  enabled: true
  permissions:
    monitor: true      # 运行时监控
    modify: true       # 动态行为修改
    inject: true       # 代码注入
    analyze: true      # 状态分析
  restrictions:
    max_injections: 5  # 最大注入次数
    allowed_apps:      # 允许操作的应用
      - com.example.app
    log_level: info    # 日志级别
```

### 9.4 Traefik 配置示例（traefik.yml）

```yaml
http:
  routers:
    core-router:
      rule: "PathPrefix(`/api/v1/devices`)"
      service: core-service
      middlewares:
        - rate-limit
  services:
    core-service:
      loadBalancer:
        serversTransport: insecureTransport
        servers:
          # 动态从 Consul 获取 Core 实例
          - url: "http://core-1:5000"
          - url: "http://core-2:5000"
  middlewares:
    rate-limit:
      rateLimit:
        average: 100
        burst: 200
```

### 9.5 Consul Core 注册示例（core/services/consul.py）

```python
import consul

class ConsulClient:
    def __init__(self, host="consul", port=8500):
        self.client = consul.Consul(host=host, port=port)

    def register(self, core_id, address, port, device_ids):
        """注册 Core 到 Consul"""
        self.client.agent.service.register(
            name="core-service",
            service_id=f"core-{core_id}",
            address=address,
            port=port,
            tags=[f"device-{id}" for id in device_ids],
            check={
                "http": f"http://{address}:/health",
                "interval": "10s",
                "timeout": "5s"
            }
        )

    def deregister(self, core_id):
        """注销 Core"""
        self.client.agent.service.deregister(f"core-{core_id}")
```
