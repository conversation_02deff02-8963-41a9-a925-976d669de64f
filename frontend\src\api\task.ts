import request from '@/utils/request'

// 任务相关类型定义
export interface Task {
  id: string
  platform_id: string
  platform_name: string
  account_id: string
  account_name: string
  device_id: string
  content_path: string
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'canceled'
  progress: number
  task_type: 'main' | 'subtask' | 'single'
  workflow_name?: string
  content_type?: string
  video_file?: string
  created_at: string
  updated_at: string
  start_time?: string
  end_time?: string
  estimated_end_time?: string
  workflow_id?: string
  params?: Record<string, any>
  // 主任务特有字段
  total_subtasks?: number
  completed_subtasks?: number
  // 子任务特有字段
  parent_task_id?: string
  subtask_index?: number
  // 树形结构
  children?: Task[]
}

export interface TaskListResponse {
  tasks: Task[]
  total: number
  pagination?: {
    limit: number
    offset: number
    has_more: boolean
  }
}

export interface TaskDetailResponse {
  task: Task
}

export interface TaskStatusResponse {
  status: string
  progress: number
  message?: string
}

export interface TaskLogEntry {
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'success'
  message: string
}

export interface TaskLogsResponse {
  logs: TaskLogEntry[]
}

export interface TaskStatsResponse {
  running: number
  pending: number
  paused: number
  completed: number
  failed: number
  canceled: number
  total: number
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 任务相关接口

// 获取任务列表
export const getTaskList = (params?: {
  status?: string
  platform_id?: string
  limit?: number
  offset?: number
}): Promise<ApiResponse<TaskListResponse>> => {
  return request({
    url: '/api/tasks/',
    method: 'get',
    params
  })
}

// 获取任务详情
export const getTaskDetail = (taskId: string): Promise<ApiResponse<TaskDetailResponse>> => {
  return request({
    url: `/api/task/detail/${taskId}`,
    method: 'get'
  })
}

// 获取任务状态
export const getTaskStatus = (taskId: string): Promise<ApiResponse<TaskStatusResponse>> => {
  return request({
    url: `/api/tasks/${taskId}/status`,
    method: 'get'
  })
}

// 获取任务日志
export const getTaskLogs = (taskId: string): Promise<ApiResponse<TaskLogsResponse>> => {
  return request({
    url: `/api/tasks/detail/${taskId}/logs`,
    method: 'get'
  })
}

// 获取运行中的任务
export const getRunningTasks = (): Promise<ApiResponse<TaskListResponse>> => {
  return request({
    url: '/api/tasks/running',
    method: 'get'
  })
}

// 启动任务
export const startTask = (taskId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/start`,
    method: 'post'
  })
}

// 暂停任务
export const pauseTask = (taskId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/pause`,
    method: 'post'
  })
}

// 取消任务
export const cancelTask = (taskId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}/cancel`,
    method: 'post'
  })
}

// 获取任务结果
export const getTaskResult = (taskId: string): Promise<ApiResponse<any>> => {
  return request({
    url: `/api/tasks/${taskId}/result`,
    method: 'get'
  })
}

// 删除任务
export const deleteTask = (taskId: string): Promise<ApiResponse<{ message: string }>> => {
  return request({
    url: `/api/tasks/${taskId}`,
    method: 'delete'
  })
}

// 批量操作任务
export const batchOperateTasks = (taskIds: string[], operation: string): Promise<ApiResponse<{ message: string; affected_count: number }>> => {
  return request({
    url: '/api/tasks/batch',
    method: 'post',
    data: {
      task_ids: taskIds,
      operation
    }
  })
}

// 获取任务统计
export const getTaskStats = (): Promise<ApiResponse<TaskStatsResponse>> => {
  return request({
    url: '/api/tasks/stats',
    method: 'get'
  })
}

// 获取任务历史
export const getTaskHistory = (params?: {
  start_date?: string
  end_date?: string
  status?: string
  platform_id?: string
  limit?: number
  offset?: number
}): Promise<ApiResponse<TaskListResponse>> => {
  return request({
    url: '/api/tasks/history',
    method: 'get',
    params
  })
}

// 清理任务历史
export const cleanTaskHistory = (params: {
  before_date?: string
  status?: string
  keep_count?: number
  clean_invalid?: boolean  // 新增：清理无效任务
}): Promise<ApiResponse<{ deleted_count: number; message: string }>> => {
  return request({
    url: '/api/tasks/history/clean',
    method: 'post',
    data: params
  })
}

// 导出任务数据
export const exportTasks = (params?: {
  start_date?: string
  end_date?: string
  status?: string
  format?: 'csv' | 'excel'
}): Promise<Blob> => {
  return request({
    url: '/api/tasks/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
