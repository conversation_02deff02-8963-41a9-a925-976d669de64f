<template>
  <div class="device-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>设备管理</span>
          <div style="display: flex; gap: 10px; align-items: center">
            <el-button-group>
              <el-button type="primary" @click="refreshDevices">刷新</el-button>
              <el-button type="success" @click="startSelected">启动</el-button>
              <el-button type="danger" @click="stopSelected">停止</el-button>
            </el-button-group>

            <!-- Core服务筛选下拉框 -->
            <el-select
              v-model="selectedCoreId"
              placeholder="选择Core服务"
              clearable
              @change="handleCoreChange"
              style="margin-left: 20px; width: 180px;"
            >
              <el-option
                v-for="core in coreServices"
                :key="core.id"
                :label="core.name"
                :value="core.id"
              />
            </el-select>

            <el-button
              type="warning"
              @click="handleLogout"
              style="margin-left: auto"
            >
              退出登录
            </el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="card">
        <!-- 设备列表标签页 -->
        <el-tab-pane label="设备列表" name="list">
          <el-table
            :data="devices"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            v-loading="loading"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="设备名称" />
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cpu" label="CPU占用" />
            <el-table-column prop="memory" label="内存占用" />
            <el-table-column prop="network" label="网络状态" />
          </el-table>
        </el-tab-pane>

        <!-- 状态监控标签页 -->
        <el-tab-pane label="状态监控" name="monitor">
          <div class="status-chart">
            <h3>设备状态监控</h3>
            <div ref="chart" style="width: 100%; height: 100%"></div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { getDevices, startDevice, stopDevice } from '@/api/device'
import { getCoreServices } from '@/api/rest/device'
import { io } from 'socket.io-client'
import { logout } from '@/api/auth'
import { useRouter } from 'vue-router'

interface Device {
  id: string
  name: string
  status: string
  cpu: string
  memory: string
  network: string
  core_id?: string
}

interface CoreService {
  id: string
  name: string
}

const activeTab = ref('list')
const loading = ref(false)
const devices = ref<Device[]>([])
const selectedDevices = ref<Device[]>([])
const chart = ref<HTMLElement | null>(null)
const socket = ref<any>(null)
const coreServices = ref<CoreService[]>([])
const selectedCoreId = ref<string | null>(null)

const getStatusType = (status: string) => {
  return status === '运行中' ? 'success' : status === '已停止' ? 'danger' : 'warning'
}

const handleSelectionChange = (val: Device[]) => {
  selectedDevices.value = val
}

const refreshDevices = async () => {
  try {
    loading.value = true

    // 如果没有选择Core服务，则不显示任何设备
    if (!selectedCoreId.value) {
      devices.value = []
      updateChart()
      loading.value = false
      return
    }

    const res = await getDevices({
      core_id: selectedCoreId.value
    })
    devices.value = res.data
    updateChart()
  } catch (error) {
    ElMessage.error('获取设备列表失败')
  } finally {
    loading.value = false
  }
}

const fetchCoreServices = async () => {
  try {
    const cores = await getCoreServices()
    coreServices.value = cores
    if (cores.length > 0 && !selectedCoreId.value) {
      // 默认不选择任何Core，显示所有设备
      selectedCoreId.value = null
    }
  } catch (error) {
    console.error('获取Core服务列表失败', error)
    ElMessage.error('获取Core服务列表失败')
  }
}

const handleCoreChange = () => {
  refreshDevices()
}

const startSelected = async () => {
  if (selectedDevices.value.length === 0) {
    ElMessage.warning('请选择设备')
    return
  }
  try {
    loading.value = true
    await Promise.all(selectedDevices.value.map(device =>
      startDevice(device.id)
    ))
    await refreshDevices()
    ElMessage.success('启动成功')
  } catch (error) {
    ElMessage.error('启动设备失败')
  } finally {
    loading.value = false
  }
}

const stopSelected = async () => {
  if (selectedDevices.value.length === 0) {
    ElMessage.warning('请选择设备')
    return
  }
  try {
    loading.value = true
    await Promise.all(selectedDevices.value.map(device =>
      stopDevice(device.id)
    ))
    await refreshDevices()
    ElMessage.success('停止成功')
  } catch (error) {
    ElMessage.error('停止设备失败')
  } finally {
    loading.value = false
  }
}

const updateChart = () => {
  if (!chart.value) return

  const myChart = echarts.init(chart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['CPU占用', '内存占用']
    },
    xAxis: {
      type: 'category',
      data: devices.value.map(d => d.name)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'CPU占用',
        type: 'bar',
        data: devices.value.map(d => parseFloat(d.cpu))
      },
      {
        name: '内存占用',
        type: 'bar',
        data: devices.value.map(d => parseFloat(d.memory))
      }
    ]
  }
  myChart.setOption(option)
  myChart.resize()
}

import { watch } from 'vue'

onMounted(() => {
  // 初始化WebSocket连接
  const host = window.location.hostname
  socket.value = io(`ws://${host}:8000`, {
    path: '/socket.io',
    transports: ['websocket']
  })

  // 监听设备状态更新事件
  socket.value.on('device_status_update', (updatedDevice: Device) => {
    // 如果没有选择Core服务，则不显示任何设备
    if (!selectedCoreId.value) {
      return
    }

    // 根据Core服务ID筛选设备
    if (updatedDevice.core_id !== selectedCoreId.value) {
      return
    }

    const index = devices.value.findIndex(d => d.id === updatedDevice.id)
    if (index !== -1) {
      devices.value[index] = updatedDevice
      updateChart()
    }
  })

  // 监听设备列表更新事件
  socket.value.on('devices_update', (newDevices: Device[]) => {
    // 如果没有选择Core服务，则不显示任何设备
    if (!selectedCoreId.value) {
      devices.value = []
      updateChart()
      return
    }

    // 根据Core服务ID筛选设备
    newDevices = newDevices.filter(d => d.core_id === selectedCoreId.value)

    devices.value = newDevices
    updateChart()
  })

  // 获取Core服务列表
  fetchCoreServices()

  refreshDevices()
  window.addEventListener('resize', updateChart)
})

onBeforeUnmount(() => {
  if (socket.value) {
    socket.value.disconnect()
  }
  window.removeEventListener('resize', updateChart)
})

watch(activeTab, (newVal) => {
  if (newVal === 'monitor') {
    nextTick(() => {
      updateChart()
    })
  }
})

const router = useRouter()

const handleLogout = async () => {
  try {
    await logout()
    router.push('/login')
  } catch (error) {
    ElMessage.error('退出失败')
  }
}
</script>

<style scoped>
.device-management {
  padding: 20px;
  height: 100%;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.status-chart {
  position: relative;
  height: calc(100vh - 200px);
  padding: 20px;
}
.status-chart h3 {
  margin: 0 0 16px 0;
}
.status-chart div {
  width: 100%;
  height: calc(100% - 36px);
}
</style>