"""
YouTube下载器
基于yt-dlp的YouTube内容下载器
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
import subprocess

from .base_downloader import BaseDownloader

logger = logging.getLogger(__name__)


class YouTubeDownloader(BaseDownloader):
    """YouTube下载器类"""

    def __init__(self, download_path: str):
        """初始化YouTube下载器
        
        Args:
            download_path: 下载路径
        """
        super().__init__("youtube", download_path)
        
        # 检查yt-dlp是否可用
        self.yt_dlp_available = self._check_yt_dlp()
        if not self.yt_dlp_available:
            self.add_log("警告: yt-dlp不可用，请安装 pip install yt-dlp")

    def _check_yt_dlp(self) -> bool:
        """检查yt-dlp是否可用"""
        try:
            result = subprocess.run(['yt-dlp', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception:
            return False

    def extract_channel_id(self, account_url: str) -> str:
        """从URL提取频道ID
        
        Args:
            account_url: 账号URL
            
        Returns:
            str: 频道ID
        """
        try:
            # 支持多种URL格式
            # https://www.youtube.com/channel/UCxxxxxx
            # https://www.youtube.com/c/channelname
            # https://www.youtube.com/@username
            
            if '/channel/' in account_url:
                channel_id = account_url.split('/channel/')[-1].split('?')[0]
                return channel_id
            elif '/c/' in account_url:
                # 需要通过yt-dlp解析
                return account_url
            elif '/@' in account_url:
                # 新格式的用户名
                return account_url
            else:
                return account_url
                
        except Exception as e:
            logger.error(f"提取频道ID失败: {str(e)}")
            return account_url

    async def get_account_info(self, account_url: str) -> Dict[str, Any]:
        """获取账号信息
        
        Args:
            account_url: 账号URL
            
        Returns:
            Dict: 账号信息
        """
        if not self.yt_dlp_available:
            self.add_log("yt-dlp不可用，无法获取账号信息")
            return {}

        try:
            self.add_log(f"获取YouTube账号信息: {account_url}")
            
            # 使用yt-dlp获取频道信息
            cmd = [
                'yt-dlp',
                '--dump-json',
                '--playlist-end', '1',  # 只获取第一个视频来获取频道信息
                account_url
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 解析JSON输出
                lines = stdout.decode('utf-8').strip().split('\n')
                for line in lines:
                    if line.strip():
                        try:
                            data = json.loads(line)
                            
                            account_info = {
                                'channel_id': data.get('channel_id', ''),
                                'channel_name': data.get('channel', ''),
                                'uploader': data.get('uploader', ''),
                                'subscriber_count': data.get('channel_follower_count', 0),
                                'video_count': data.get('playlist_count', 0),
                                'description': data.get('description', ''),
                                'channel_url': data.get('channel_url', ''),
                                'avatar_url': data.get('thumbnail', ''),
                            }
                            
                            self.add_log(f"账号信息获取成功: {account_info['channel_name']}")
                            return account_info
                        except json.JSONDecodeError:
                            continue
                
                raise Exception("无法解析yt-dlp输出")
            else:
                error_msg = stderr.decode('utf-8')
                raise Exception(f"yt-dlp执行失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"获取YouTube账号信息失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return {}

    async def get_video_list(
        self, 
        account_url: str, 
        max_count: int = 50,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """获取视频列表
        
        Args:
            account_url: 账号URL
            max_count: 最大数量
            filters: 过滤条件
            
        Returns:
            List: 视频列表
        """
        if not self.yt_dlp_available:
            self.add_log("yt-dlp不可用，无法获取视频列表")
            return []

        try:
            self.add_log(f"获取YouTube视频列表: {account_url}, 最大数量: {max_count}")
            
            # 构建yt-dlp命令
            cmd = [
                'yt-dlp',
                '--dump-json',
                '--playlist-end', str(max_count),
                '--no-download',
                account_url
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                videos = []
                lines = stdout.decode('utf-8').strip().split('\n')
                
                for line in lines:
                    if line.strip():
                        try:
                            data = json.loads(line)
                            video_info = self._parse_video_info(data)
                            
                            # 应用过滤条件
                            if self._apply_filters(video_info, filters):
                                videos.append(video_info)
                                
                        except json.JSONDecodeError:
                            continue
                
                self.add_log(f"获取到 {len(videos)} 个视频")
                return videos
            else:
                error_msg = stderr.decode('utf-8')
                raise Exception(f"yt-dlp执行失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"获取YouTube视频列表失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return []

    def _parse_video_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """解析视频信息
        
        Args:
            data: yt-dlp返回的视频数据
            
        Returns:
            Dict: 解析后的视频信息
        """
        try:
            video_info = {
                'id': data.get('id', ''),
                'title': data.get('title', ''),
                'description': data.get('description', ''),
                'upload_date': data.get('upload_date', ''),
                'duration': data.get('duration', 0),
                'view_count': data.get('view_count', 0),
                'like_count': data.get('like_count', 0),
                'comment_count': data.get('comment_count', 0),
                'video_url': data.get('webpage_url', ''),
                'thumbnail_url': data.get('thumbnail', ''),
                'uploader': data.get('uploader', ''),
                'channel_id': data.get('channel_id', ''),
                'channel_url': data.get('channel_url', ''),
                'categories': data.get('categories', []),
                'tags': data.get('tags', []),
                'formats': data.get('formats', [])
            }
            
            return video_info
            
        except Exception as e:
            logger.error(f"解析YouTube视频信息失败: {str(e)}")
            return {}

    def _apply_filters(self, video_info: Dict[str, Any], filters: Optional[Dict[str, Any]]) -> bool:
        """应用过滤条件
        
        Args:
            video_info: 视频信息
            filters: 过滤条件
            
        Returns:
            bool: 是否通过过滤
        """
        if not filters:
            return True
        
        try:
            # 最小播放量过滤
            min_views = filters.get('min_views')
            if min_views and video_info.get('view_count', 0) < min_views:
                return False
            
            # 最小点赞数过滤
            min_likes = filters.get('min_likes')
            if min_likes and video_info.get('like_count', 0) < min_likes:
                return False
            
            # 关键词过滤
            keywords = filters.get('keywords', [])
            if keywords:
                title = video_info.get('title', '').lower()
                description = video_info.get('description', '').lower()
                if not any(keyword.lower() in title or keyword.lower() in description 
                          for keyword in keywords):
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"应用过滤条件失败: {str(e)}")
            return True

    async def download_video(
        self, 
        video_info: Dict[str, Any], 
        save_path: str
    ) -> bool:
        """下载单个视频
        
        Args:
            video_info: 视频信息
            save_path: 保存路径
            
        Returns:
            bool: 是否下载成功
        """
        if not self.yt_dlp_available:
            self.add_log("yt-dlp不可用，无法下载视频")
            return False

        try:
            video_url = video_info.get('video_url')
            if not video_url:
                raise ValueError("视频URL为空")
            
            self.add_log(f"开始下载YouTube视频: {video_info.get('title', 'Unknown')}")
            
            # 创建目录
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 构建yt-dlp下载命令
            cmd = [
                'yt-dlp',
                '-o', save_path,
                '--format', 'best[ext=mp4]',  # 下载最佳质量的mp4格式
                video_url
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 验证文件
                if os.path.exists(save_path) and os.path.getsize(save_path) > 0:
                    self.add_log(f"YouTube视频下载成功: {save_path}")
                    return True
                else:
                    raise Exception("下载的文件为空或不存在")
            else:
                error_msg = stderr.decode('utf-8')
                raise Exception(f"yt-dlp下载失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"下载YouTube视频失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return False

    async def download_account_content(
        self, 
        account_url: str, 
        account_name: str,
        download_config: Dict[str, Any]
    ) -> bool:
        """下载账号内容
        
        Args:
            account_url: 账号URL
            account_name: 账号名称
            download_config: 下载配置
            
        Returns:
            bool: 是否下载成功
        """
        if not self.yt_dlp_available:
            self.set_status("error", "yt-dlp不可用，请安装 pip install yt-dlp")
            return False

        try:
            self.set_status("downloading", f"开始下载 {account_name} 的YouTube内容")
            self.update_progress(0, "初始化YouTube下载器")
            
            # 获取账号信息
            account_info = await self.get_account_info(account_url)
            if not account_info:
                raise Exception("获取YouTube账号信息失败")
            
            self.update_progress(10, "获取视频列表")
            
            # 获取视频列表
            max_videos = download_config.get('max_videos', 50)
            filters = {
                'min_views': download_config.get('min_views'),
                'min_likes': download_config.get('min_likes'),
                'keywords': download_config.get('keywords', [])
            }
            
            videos = await self.get_video_list(account_url, max_videos, filters)
            if not videos:
                raise Exception("未获取到YouTube视频列表")
            
            self.total_count = len(videos)
            self.update_progress(20, f"找到 {self.total_count} 个视频")
            
            # 创建下载目录结构
            videos_dir = os.path.join(self.download_path, "videos")
            metadata_dir = os.path.join(self.download_path, "metadata")
            os.makedirs(videos_dir, exist_ok=True)
            os.makedirs(metadata_dir, exist_ok=True)
            
            # 保存账号信息
            account_info_path = os.path.join(self.download_path, "account_info.json")
            with open(account_info_path, 'w', encoding='utf-8') as f:
                json.dump(account_info, f, ensure_ascii=False, indent=2)
            
            # 下载视频
            naming_rule = download_config.get('naming_rule', 'timestamp')
            include_metadata = download_config.get('include_metadata', True)
            skip_existing = download_config.get('skip_existing', True)
            
            for i, video_info in enumerate(videos):
                try:
                    # 生成文件名
                    filename = self.generate_filename(video_info, naming_rule)
                    video_path = os.path.join(videos_dir, filename)
                    
                    # 检查是否跳过已存在的文件
                    if skip_existing and self.is_file_exists(video_path):
                        self.add_log(f"跳过已存在的文件: {filename}")
                        self.downloaded_count += 1
                        continue
                    
                    # 下载视频
                    success = await self.download_video(video_info, video_path)
                    if success:
                        self.downloaded_count += 1
                        
                        # 保存元数据
                        if include_metadata:
                            self.save_metadata(video_info, video_path)
                    
                    # 更新进度
                    progress = 20 + int((i + 1) / len(videos) * 70)
                    self.update_progress(progress, f"已下载 {self.downloaded_count}/{self.total_count}")
                    
                    # 避免请求过快
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.add_log(f"下载视频失败: {video_info.get('title', 'Unknown')} - {str(e)}")
                    continue
            
            # 生成下载报告
            self._generate_download_report(account_info, videos)
            
            self.update_progress(100, f"下载完成，成功下载 {self.downloaded_count} 个视频")
            self.set_status("completed", "YouTube下载任务完成")
            
            return True
            
        except Exception as e:
            error_msg = f"下载YouTube账号内容失败: {str(e)}"
            self.set_status("error", error_msg)
            self.error_message = error_msg
            logger.error(error_msg)
            return False

    def _generate_download_report(self, account_info: Dict[str, Any], videos: List[Dict[str, Any]]):
        """生成下载报告
        
        Args:
            account_info: 账号信息
            videos: 视频列表
        """
        try:
            report = {
                'download_time': datetime.now().isoformat(),
                'account_info': account_info,
                'total_videos': len(videos),
                'downloaded_count': self.downloaded_count,
                'success_rate': f"{(self.downloaded_count / len(videos) * 100):.1f}%" if videos else "0%",
                'download_path': self.download_path,
                'platform': self.platform,
                'logs': self.logs[-50:]  # 最近50条日志
            }
            
            report_path = os.path.join(self.download_path, "download_report.json")
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.add_log(f"YouTube下载报告已生成: {report_path}")
            
        except Exception as e:
            self.add_log(f"生成YouTube下载报告失败: {str(e)}")
