import { defineStore } from 'pinia'
import { login, logout, getCurrentUser } from '@/api/auth'
import type { LoginParams } from '@/api/auth'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(null)
  const user = ref<any>(null)

  const isAuthenticated = computed(() => !!token.value)

  const loginUser = async (credentials: LoginParams) => {
    try {
      const response = await login(credentials)
      if (!response?.token) {
        throw new Error('登录失败: 无效的token')
      }
      
      token.value = response.token
      user.value = response.user
      return response
    } catch (error) {
      token.value = null
      user.value = null
      throw error
    }
  }

  const logoutUser = async () => {
    await logout()
    token.value = null
    user.value = null
  }

  const checkAuth = async () => {
    // 先从localStorage获取token
    const storedToken = localStorage.getItem('token')
    if (!storedToken) {
      token.value = null
      user.value = null
      return false
    }
    
    token.value = storedToken
    
    try {
      const response = await getCurrentUser()
      user.value = response.data
      return true
    } catch {
      token.value = null
      user.value = null
      localStorage.removeItem('token')
      return false
    }
  }

  return {
    token,
    user,
    isAuthenticated,
    loginUser,
    logoutUser,
    checkAuth
  }
})