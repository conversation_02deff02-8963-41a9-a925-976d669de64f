<template>
  <el-dialog
    v-model="dialogVisible"
    title="分类管理"
    width="800px"
    :before-close="handleClose"
  >
    <div class="category-manager">
      <!-- 工具栏 -->
      <div class="toolbar">
        <el-button type="primary" @click="showCreateForm = true">
          <el-icon><Plus /></el-icon>
          新建分类
        </el-button>
        <el-button @click="loadCategories">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>

      <!-- 分类列表 -->
      <div class="category-list" v-loading="loading">
        <el-table
          :data="flatCategories"
          row-key="id"
          :tree-props="{ children: 'children' }"
          style="width: 100%"
        >
          <el-table-column label="分类名称" min-width="200">
            <template #default="{ row }">
              <div class="category-name">
                <el-icon 
                  v-if="row.icon" 
                  :style="{ color: row.color }"
                  class="category-icon"
                >
                  <component :is="getIconComponent(row.icon)" />
                </el-icon>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="描述" prop="description" min-width="150">
            <template #default="{ row }">
              <span class="description">{{ row.description || '-' }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="内容数量" width="100" align="center">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.content_count || 0 }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="排序" width="80" align="center">
            <template #default="{ row }">
              {{ row.sort_order }}
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
                {{ row.is_active ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" text @click="editCategory(row)">
                编辑
              </el-button>
              <el-button size="small" text @click="addSubCategory(row)">
                添加子分类
              </el-button>
              <el-button 
                size="small" 
                text 
                type="danger" 
                @click="deleteCategory(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 创建/编辑表单 -->
      <el-drawer
        v-model="showCreateForm"
        :title="editingCategory ? '编辑分类' : '新建分类'"
        direction="rtl"
        size="400px"
      >
        <el-form
          ref="formRef"
          :model="categoryForm"
          :rules="formRules"
          label-width="80px"
          @submit.prevent
        >
          <el-form-item label="分类名称" prop="name">
            <el-input
              v-model="categoryForm.name"
              placeholder="请输入分类名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="父分类" prop="parent_id">
            <el-tree-select
              v-model="categoryForm.parent_id"
              :data="categoryTreeData"
              :props="{ label: 'name', value: 'id' }"
              placeholder="请选择父分类（可选）"
              clearable
              check-strictly
              :render-after-expand="false"
            />
          </el-form-item>

          <el-form-item label="描述">
            <el-input
              v-model="categoryForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入分类描述（可选）"
            />
          </el-form-item>

          <el-form-item label="图标">
            <el-select v-model="categoryForm.icon" placeholder="选择图标" clearable>
              <el-option
                v-for="icon in iconOptions"
                :key="icon.value"
                :label="icon.label"
                :value="icon.value"
              >
                <div class="icon-option">
                  <el-icon><component :is="icon.component" /></el-icon>
                  <span>{{ icon.label }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="颜色">
            <el-color-picker v-model="categoryForm.color" />
          </el-form-item>

          <el-form-item label="排序">
            <el-input-number
              v-model="categoryForm.sort_order"
              :min="0"
              :max="999"
              controls-position="right"
            />
          </el-form-item>

          <el-form-item label="状态">
            <el-switch
              v-model="categoryForm.is_active"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>

          <div class="form-actions">
            <el-button @click="cancelEdit">取消</el-button>
            <el-button type="primary" @click="saveCategory" :loading="saving">
              {{ editingCategory ? '更新' : '创建' }}
            </el-button>
          </div>
        </el-form>
      </el-drawer>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { 
  Plus, 
  Refresh,
  Folder,
  VideoPlay,
  Picture,
  Headphones,
  Document,
  Star,
  Tag
} from '@element-plus/icons-vue'
import {
  getCategoryTree,
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory as deleteCategoryApi,
  type ContentCategory,
  type CategoryTreeNode
} from '@/api/content'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showCreateForm = ref(false)
const categories = ref<ContentCategory[]>([])
const categoryTree = ref<CategoryTreeNode[]>([])
const editingCategory = ref<ContentCategory | null>(null)
const formRef = ref<FormInstance>()

// 表单数据
const categoryForm = reactive({
  name: '',
  parent_id: '',
  description: '',
  icon: '',
  color: '#409EFF',
  sort_order: 0,
  is_active: true
})

// 图标选项
const iconOptions = [
  { label: '文件夹', value: 'folder', component: Folder },
  { label: '视频', value: 'video', component: VideoPlay },
  { label: '图片', value: 'image', component: Picture },
  { label: '音频', value: 'audio', component: Headphones },
  { label: '文档', value: 'text', component: Document },
  { label: '星标', value: 'fire', component: Star },
  { label: '标签', value: 'tag', component: Tag }
]

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 20, message: '分类名称长度在 1 到 20 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 扁平化分类数据用于表格显示
const flatCategories = computed(() => {
  const flatten = (nodes: CategoryTreeNode[], level = 0): any[] => {
    const result: any[] = []
    for (const node of nodes) {
      result.push({
        ...node,
        level,
        content_count: node.content_count
      })
      if (node.children && node.children.length > 0) {
        result.push(...flatten(node.children, level + 1))
      }
    }
    return result
  }
  return flatten(categoryTree.value)
})

// 分类树数据用于选择父分类
const categoryTreeData = computed(() => {
  const buildTreeData = (nodes: CategoryTreeNode[]): any[] => {
    return nodes.map(node => ({
      id: node.id,
      name: node.name,
      children: node.children ? buildTreeData(node.children) : []
    }))
  }
  return buildTreeData(categoryTree.value)
})

// 方法
const getIconComponent = (iconName?: string) => {
  const iconMap = {
    folder: Folder,
    video: VideoPlay,
    image: Picture,
    audio: Headphones,
    text: Document,
    fire: Star,
    tag: Tag
  }
  return iconMap[iconName as keyof typeof iconMap] || Folder
}

const loadCategories = async () => {
  loading.value = true
  try {
    const [treeData, listData] = await Promise.all([
      getCategoryTree(true),
      getCategories()
    ])
    categoryTree.value = treeData
    categories.value = listData
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  categoryForm.name = ''
  categoryForm.parent_id = ''
  categoryForm.description = ''
  categoryForm.icon = ''
  categoryForm.color = '#409EFF'
  categoryForm.sort_order = 0
  categoryForm.is_active = true
  editingCategory.value = null
  formRef.value?.clearValidate()
}

const editCategory = (category: any) => {
  editingCategory.value = category
  categoryForm.name = category.name
  categoryForm.parent_id = category.parent_id || ''
  categoryForm.description = category.description || ''
  categoryForm.icon = category.icon || ''
  categoryForm.color = category.color || '#409EFF'
  categoryForm.sort_order = category.sort_order || 0
  categoryForm.is_active = category.is_active !== false
  showCreateForm.value = true
}

const addSubCategory = (parentCategory: any) => {
  resetForm()
  categoryForm.parent_id = parentCategory.id
  showCreateForm.value = true
}

const saveCategory = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    saving.value = true
    
    const categoryData = {
      name: categoryForm.name,
      parent_id: categoryForm.parent_id || undefined,
      description: categoryForm.description || undefined,
      icon: categoryForm.icon || undefined,
      color: categoryForm.color,
      sort_order: categoryForm.sort_order,
      is_active: categoryForm.is_active
    }
    
    if (editingCategory.value) {
      await updateCategory(editingCategory.value.id, categoryData)
      ElMessage.success('分类更新成功')
    } else {
      await createCategory(categoryData)
      ElMessage.success('分类创建成功')
    }
    
    showCreateForm.value = false
    resetForm()
    loadCategories()
    emit('success')
    
  } catch (error) {
    console.error('保存分类失败:', error)
    ElMessage.error('保存分类失败')
  } finally {
    saving.value = false
  }
}

const deleteCategory = async (category: any) => {
  try {
    const hasChildren = category.children && category.children.length > 0
    const hasContent = category.content_count > 0
    
    let message = '确定要删除这个分类吗？'
    if (hasChildren || hasContent) {
      message = `该分类${hasChildren ? '包含子分类' : ''}${hasChildren && hasContent ? '和' : ''}${hasContent ? '包含内容' : ''}，确定要强制删除吗？`
    }
    
    await ElMessageBox.confirm(message, '确认删除', {
      type: 'warning',
      confirmButtonText: hasChildren || hasContent ? '强制删除' : '删除',
      confirmButtonClass: 'el-button--danger'
    })
    
    await deleteCategoryApi(category.id, hasChildren || hasContent)
    ElMessage.success('分类删除成功')
    loadCategories()
    emit('success')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除分类失败')
    }
  }
}

const cancelEdit = () => {
  showCreateForm.value = false
  resetForm()
}

const handleClose = () => {
  resetForm()
  showCreateForm.value = false
  dialogVisible.value = false
}

// 生命周期
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.category-manager {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.category-list {
  flex: 1;
  overflow-y: auto;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-icon {
  font-size: 16px;
}

.description {
  color: #606266;
  font-size: 13px;
}

.icon-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-actions {
  margin-top: 24px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-drawer__body) {
  padding: 20px;
}
</style>
