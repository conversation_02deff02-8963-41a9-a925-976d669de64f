"""
设备定时同步服务
负责定时同步设备配置，维护状态一致性
基于雷电模拟器API优化，支持批量操作和高效同步
"""

import os
import logging
import asyncio
import time
import json
import subprocess
import re
from typing import Dict, List, Any, Optional, Tuple, Set
import aiohttp
import redis.asyncio as redis

from src.devices.ldplayer.manager import LDPlayerManager
from src.devices.base import DeviceInfo, DeviceStatus

logger = logging.getLogger(__name__)

class DeviceSyncService:
    """设备定时同步服务类"""

    def __init__(self,
                 ldplayer_manager: LDPlayerManager,
                 redis_url: Optional[str] = None,
                 backend_url: Optional[str] = None,
                 sync_interval: int = 300,  # 默认同步间隔改为300秒（5分钟）
                 core_id: Optional[str] = None):  # 添加Core服务ID参数
        """初始化设备同步服务

        Args:
            ldplayer_manager: 雷电模拟器管理器
            redis_url: Redis服务URL，用于推送设备状态
            backend_url: 后端服务URL，用于推送状态变更（备用方案）
            sync_interval: 同步间隔（秒），默认300秒
            core_id: Core服务ID，如果为None则从配置中获取
        """
        self.ldplayer_manager = ldplayer_manager
        self.redis_url = redis_url
        self.backend_url = backend_url
        self.sync_interval = sync_interval
        self.core_id = core_id  # 保存Core服务ID
        self.is_running = False
        self.sync_task = None
        self.last_device_states = {}  # 上次同步的设备状态
        self.redis_client = None

        # 缓存机制
        self._running_devices_cache = set()  # 运行中设备的缓存
        self._cache_timestamp = 0  # 缓存时间戳
        self._cache_ttl = 10  # 缓存有效期（秒）

        # 如果没有提供Core服务ID，尝试从配置中获取
        if not self.core_id:
            try:
                from src.config.settings import CoreSettings
                settings = CoreSettings()
                self.core_id = settings.core_id
                logger.info(f"从配置中获取Core服务ID: {self.core_id}")
            except Exception as e:
                logger.error(f"获取Core服务ID异常: {str(e)}")
                self.core_id = "default"

        # 获取ldconsole路径
        self.ldconsole_path = self.ldplayer_manager.ldconsole_path
        if not os.path.exists(self.ldconsole_path):
            logger.error(f"ldconsole.exe不存在: {self.ldconsole_path}")
            raise FileNotFoundError(f"ldconsole.exe不存在: {self.ldconsole_path}")

        if redis_url:
            logger.info(f"设备同步服务将使用Redis: {redis_url}")
        elif backend_url:
            logger.info(f"设备同步服务将使用后端API: {backend_url}")
        else:
            logger.warning("未配置Redis或后端URL，设备状态将不会被推送")

        logger.info(f"设备同步服务初始化，同步间隔: {sync_interval}秒")

    async def start(self) -> None:
        """启动同步服务"""
        if self.is_running:
            logger.warning("同步服务已在运行中")
            return

        # 初始化Redis连接
        if self.redis_url:
            try:
                self.redis_client = redis.Redis.from_url(self.redis_url)
                # 测试连接
                await self.redis_client.ping()
                logger.info("Redis连接成功")
            except Exception as e:
                logger.error(f"Redis连接失败: {str(e)}", exc_info=True)
                self.redis_client = None

                if not self.backend_url:
                    logger.warning("Redis连接失败且未配置后端URL，设备状态将不会被推送")

        self.is_running = True
        self.sync_task = asyncio.create_task(self._sync_loop())
        logger.info("设备同步服务已启动")

    async def stop(self) -> None:
        """停止同步服务"""
        if not self.is_running:
            return

        self.is_running = False
        if self.sync_task:
            self.sync_task.cancel()
            try:
                await self.sync_task
            except asyncio.CancelledError:
                pass
            self.sync_task = None

        # 关闭Redis连接
        if self.redis_client:
            try:
                await self.redis_client.close()
                logger.info("Redis连接已关闭")
            except Exception as e:
                logger.error(f"关闭Redis连接异常: {str(e)}", exc_info=True)
            self.redis_client = None

        logger.info("设备同步服务已停止")

    async def _sync_loop(self) -> None:
        """同步循环"""
        try:
            # 启动后立即执行一次同步
            await self._sync_devices()

            # 然后按照间隔定时执行
            while self.is_running:
                await asyncio.sleep(self.sync_interval)
                if self.is_running:  # 再次检查，避免在sleep期间被停止
                    await self._sync_devices()
        except asyncio.CancelledError:
            logger.info("同步循环被取消")
            raise
        except Exception as e:
            logger.error(f"同步循环异常: {str(e)}", exc_info=True)
            # 出现异常后尝试重启同步任务
            if self.is_running:
                logger.info("尝试重启同步任务")
                self.sync_task = asyncio.create_task(self._sync_loop())

    async def _get_all_devices_info(self) -> List[Dict[str, Any]]:
        """使用list2命令一次性获取所有设备的详细信息

        Returns:
            设备信息列表
        """
        cmd = f'"{self.ldconsole_path}" list2'
        logger.info(f"执行命令: {cmd}")

        try:
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"获取设备列表失败: {stderr.decode('gbk', errors='ignore')}")
                return []

            # 解析输出 - 使用GBK编码而不是UTF-8
            try:
                # 首先尝试使用GBK解码
                output = stdout.decode('gbk', errors='ignore').strip()

                # 尝试解析为JSON
                try:
                    device_list = json.loads(output)
                    logger.info(f"成功解析JSON格式设备列表，共{len(device_list)}个设备")
                    return device_list
                except json.JSONDecodeError:
                    # 如果JSON解析失败，尝试解析为CSV格式
                    logger.info("JSON解析失败，尝试解析为CSV格式")

                    device_list = []
                    lines = output.split('\n')
                    for line in lines:
                        if not line.strip():
                            continue

                        parts = line.split(',')
                        if len(parts) >= 7:  # list2命令返回7个字段
                            device_id = parts[0].strip()
                            name = parts[1].strip()
                            top_window = parts[2].strip()
                            bound_window = parts[3].strip()
                            android_started = parts[4].strip() == '1'
                            pid = parts[5].strip()
                            vbox_pid = parts[6].strip()

                            device_info = {
                                'index': device_id,
                                'name': name,
                                'top_window': top_window,
                                'bound_window': bound_window,
                                'android_started': android_started,
                                'pid': pid,
                                'vbox_pid': vbox_pid,
                                # 根据进程PID判断设备状态 - 修复PID为-1的情况
                                'status': 'running' if pid and pid != '0' and pid != '-1' and int(pid) > 0 else 'stopped'
                            }

                            device_list.append(device_info)

                    logger.info(f"成功解析CSV格式设备列表，共{len(device_list)}个设备")
                    return device_list
            except Exception as e:
                logger.error(f"解析设备列表失败: {e}")
                logger.debug(f"原始输出: {stdout}")
                return []

        except Exception as e:
            logger.error(f"获取设备列表异常: {str(e)}", exc_info=True)
            return []

    async def _get_running_devices(self, force_refresh: bool = False) -> Set[str]:
        """使用runninglist命令获取所有正在运行的设备ID

        使用缓存机制减少命令执行次数

        Args:
            force_refresh: 是否强制刷新缓存

        Returns:
            正在运行的设备ID集合
        """
        current_time = time.time()

        # 如果缓存有效且不强制刷新，直接返回缓存结果
        if not force_refresh and (current_time - self._cache_timestamp) < self._cache_ttl:
            logger.debug(f"使用缓存的运行设备列表，共{len(self._running_devices_cache)}个设备")
            return self._running_devices_cache

        cmd = f'"{self.ldconsole_path}" runninglist'
        logger.info(f"执行命令: {cmd}")

        try:
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"获取运行中设备列表失败: {stderr.decode('gbk', errors='ignore')}")
                return self._running_devices_cache  # 出错时返回缓存结果

            # 解析输出
            output = stdout.decode('gbk', errors='ignore').strip()
            if not output:
                # 更新缓存
                self._running_devices_cache = set()
                self._cache_timestamp = current_time
                return set()

            # 输出格式为空格分隔的设备ID列表
            device_ids = set(output.split())
            logger.info(f"获取到{len(device_ids)}个运行中的设备: {device_ids}")

            # 更新缓存
            self._running_devices_cache = device_ids
            self._cache_timestamp = current_time

            return device_ids

        except Exception as e:
            logger.error(f"获取运行中设备列表异常: {str(e)}", exc_info=True)
            return self._running_devices_cache  # 出错时返回缓存结果

    async def _get_device_hardware_info(self, device_id: str) -> Dict[str, Any]:
        """获取设备硬件信息（CPU、内存、网络等）

        使用ld.exe命令获取设备的硬件信息

        Args:
            device_id: 设备ID

        Returns:
            包含硬件信息的字典
        """
        # 默认硬件信息
        hardware_info = {
            'cpu_cores': 2,
            'cpu_usage': '0%',
            'memory_size': 2048,  # MB
            'memory_usage': '0%',
            'network_status': '未知'
        }

        # 如果设备未运行，直接返回默认值
        # 使用缓存的运行设备列表，避免重复执行runninglist命令
        if device_id not in self._running_devices_cache:
            logger.debug(f"设备{device_id}未运行，使用默认硬件信息")
            return hardware_info

        try:
            # 获取CPU核心数
            ld_path = os.path.join(os.path.dirname(self.ldconsole_path), "ld.exe")
            if not os.path.exists(ld_path):
                logger.warning(f"ld.exe不存在: {ld_path}，使用默认硬件信息")
                return hardware_info

            # 获取CPU信息
            cmd = f'"{ld_path}" -s {device_id} getprop ro.product.cpu.abi'
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                cpu_info = stdout.decode('gbk', errors='ignore').strip()
                # 根据CPU架构判断核心数
                if 'x86_64' in cpu_info:
                    hardware_info['cpu_cores'] = 4
                elif 'x86' in cpu_info:
                    hardware_info['cpu_cores'] = 2

            # 获取内存大小
            cmd = f'"{ld_path}" -s {device_id} getprop ro.product.memory'
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                memory_info = stdout.decode('gbk', errors='ignore').strip()
                try:
                    # 尝试解析内存大小
                    if memory_info and memory_info.isdigit():
                        hardware_info['memory_size'] = int(memory_info)
                except ValueError:
                    pass

            # 获取内存使用情况
            cmd = f'"{ld_path}" -s {device_id} cat /proc/meminfo'
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                meminfo = stdout.decode('gbk', errors='ignore').strip()
                # 解析内存信息
                mem_total = 0
                mem_free = 0

                for line in meminfo.split('\n'):
                    if 'MemTotal' in line:
                        match = re.search(r'(\d+)', line)
                        if match:
                            mem_total = int(match.group(1)) // 1024  # 转换为MB
                    elif 'MemFree' in line or 'MemAvailable' in line:
                        match = re.search(r'(\d+)', line)
                        if match:
                            mem_free = int(match.group(1)) // 1024  # 转换为MB
                            break  # 优先使用MemAvailable

                if mem_total > 0 and mem_free > 0:
                    used_percent = (mem_total - mem_free) / mem_total * 100
                    hardware_info['memory_usage'] = f"{used_percent:.1f}%"
                    hardware_info['memory_size'] = mem_total

            # 获取CPU使用情况
            cmd = f'"{ld_path}" -s {device_id} cat /proc/stat'
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                cpu_stat = stdout.decode('gbk', errors='ignore').strip()
                # 简单估算CPU使用率
                if cpu_stat:
                    first_line = cpu_stat.split('\n')[0]
                    if first_line.startswith('cpu '):
                        values = first_line.split()[1:]
                        if len(values) >= 4:
                            try:
                                user = int(values[0])
                                nice = int(values[1])
                                system = int(values[2])
                                idle = int(values[3])

                                total = user + nice + system + idle
                                if total > 0:
                                    usage = (total - idle) / total * 100
                                    hardware_info['cpu_usage'] = f"{usage:.1f}%"
                            except (ValueError, IndexError):
                                pass

            # 获取网络状态
            cmd = f'"{ld_path}" -s {device_id} settings get global airplane_mode_on'
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                airplane_mode = stdout.decode('gbk', errors='ignore').strip()
                if airplane_mode == '0':
                    # 检查网络连接
                    cmd = f'"{ld_path}" -s {device_id} ping -c 1 -W 1 *******'
                    process = await asyncio.create_subprocess_shell(
                        cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    stdout, stderr = await process.communicate()

                    if process.returncode == 0:
                        hardware_info['network_status'] = '已连接'
                    else:
                        hardware_info['network_status'] = '未连接'
                else:
                    hardware_info['network_status'] = '飞行模式'

            return hardware_info

        except Exception as e:
            logger.error(f"获取设备{device_id}硬件信息异常: {str(e)}", exc_info=True)
            return hardware_info

    async def _sync_devices(self) -> None:
        """同步所有设备状态

        使用优化的批量操作方式，减少单个设备查询次数
        1. 使用list2命令一次性获取所有设备的详细信息
        2. 使用runninglist命令获取所有正在运行的设备列表
        3. 合并信息构建完整的设备状态
        """
        try:
            logger.info("开始同步设备状态")
            start_time = time.time()

            # 1. 使用list2命令一次性获取所有设备的详细信息
            device_list = await self._get_all_devices_info()

            if not device_list:
                logger.warning("没有找到设备，尝试重新初始化设备管理器")
                success = await self.ldplayer_manager.initialize()

                if not success:
                    logger.error("重新初始化设备管理器失败")
                    return

                # 重新获取设备列表
                device_list = await self._get_all_devices_info()

                if not device_list:
                    logger.warning("重新初始化后仍然没有找到设备，同步跳过")
                    return

            # 2. 使用runninglist命令获取所有正在运行的设备列表
            # 强制刷新缓存，确保获取最新状态
            running_devices = await self._get_running_devices(force_refresh=True)

            # 3. 合并信息构建完整的设备状态
            current_states = {}
            changes = []
            devices = []

            for device_info in device_list:
                device_id = device_info.get('index')
                if not device_id:
                    continue

                name = device_info.get('name', f'模拟器-{device_id}')

                # 确定设备状态
                # 优先使用runninglist命令的结果，更准确
                if device_id in running_devices:
                    status = DeviceStatus.RUNNING.value
                else:
                    # 如果不在running_devices中，但list2中显示有PID，可能是刚启动或正在关闭
                    pid = device_info.get('pid')
                    # 修复PID为-1的情况，确保只有有效的PID才认为设备在运行
                    if pid and pid != '0' and pid != '-1' and int(pid) > 0:
                        # 检查是否正在启动Android
                        android_started = device_info.get('android_started', False)
                        status = DeviceStatus.RUNNING.value if android_started else DeviceStatus.STARTING.value
                    else:
                        # PID无效，设备应该是停止状态
                        status = DeviceStatus.STOPPED.value
                        logger.debug(f"设备{device_id}的PID为{pid}，判定为停止状态")

                # 获取设备硬件信息
                hardware_info = await self._get_device_hardware_info(device_id)

                # 创建设备信息对象
                device = DeviceInfo(
                    device_id=device_id,
                    name=name,  # 使用list2命令获取的真实设备名称
                    status=DeviceStatus(status),
                    device_type='雷电模拟器',  # 统一设置为"雷电模拟器"
                    window_info={
                        'top_window': device_info.get('top_window', ''),
                        'bound_window': device_info.get('bound_window', '')
                    },
                    process_info={
                        'pid': device_info.get('pid', '0'),
                        'vbox_pid': device_info.get('vbox_pid', '0')
                    },
                    hardware_info=hardware_info,  # 添加硬件信息
                    network_info={
                        'status': hardware_info.get('network_status', '未知')
                    },
                    system_info={
                        'cpu_cores': hardware_info.get('cpu_cores', 2)
                    }
                )

                devices.append(device)

                # 构建当前状态
                current_states[device_id] = {
                    'device_id': device_id,
                    'name': name,  # 使用list2命令获取的真实设备名称
                    'status': status,
                    'device_type': '雷电模拟器',  # 统一设置为"雷电模拟器"
                    'window_info': {
                        'top_window': device_info.get('top_window', ''),
                        'bound_window': device_info.get('bound_window', '')
                    },
                    'process_info': {
                        'pid': device_info.get('pid', '0'),
                        'vbox_pid': device_info.get('vbox_pid', '0')
                    },
                    'hardware_info': hardware_info,  # 添加硬件信息
                    'cpu_usage': hardware_info.get('cpu_usage', '0%'),  # 直接添加CPU使用率
                    'memory_usage': hardware_info.get('memory_usage', '0%'),  # 直接添加内存使用率
                    'network_status': hardware_info.get('network_status', '未知'),  # 直接添加网络状态
                    'last_sync': int(time.time())
                }

                # 检查状态是否变化
                if device_id in self.last_device_states:
                    old_state = self.last_device_states[device_id]
                    old_status = old_state.get('status', 'unknown')

                    if old_status != status:
                        changes.append({
                            'device_id': device_id,
                            'name': name,
                            'old_status': old_status,
                            'new_status': status,
                            'timestamp': int(time.time())
                        })
                        logger.info(f"设备{device_id}状态变化: {old_status} -> {status}")
                else:
                    # 新设备，记录状态变化
                    changes.append({
                        'device_id': device_id,
                        'name': name,
                        'old_status': 'unknown',
                        'new_status': status,
                        'timestamp': int(time.time())
                    })
                    logger.info(f"新设备{device_id}状态: {status}")

            # 更新上次状态
            self.last_device_states = current_states

            # 推送设备状态到Redis
            if self.redis_client:
                await self._push_to_redis(current_states)

                # 如果有变化，也推送变更记录
                if changes:
                    await self._push_changes_to_redis(changes)

                logger.info(f"设备状态已推送到Redis，共{len(devices)}个设备，{len(changes)}个状态变化")
            # 备用方案：如果Redis不可用，推送到后端API
            elif changes and self.backend_url:
                await self._push_changes_to_backend(changes)
                logger.info(f"设备状态变更已推送到后端API，共{len(changes)}个状态变化")

            # 更新设备管理器中的设备状态
            # 这样其他组件可以通过设备管理器获取最新状态
            for device in devices:
                controller = await self.ldplayer_manager.get_device(device.device_id)
                if controller:
                    controller.status = device.status

            end_time = time.time()
            logger.info(f"设备同步完成，共{len(devices)}个设备，{len(changes)}个状态变化，耗时: {end_time - start_time:.2f}秒")

        except Exception as e:
            logger.error(f"同步设备状态异常: {str(e)}", exc_info=True)
            # 重试逻辑
            await self._retry_sync()

    async def _retry_sync(self, max_retries: int = 3) -> None:
        """重试同步

        Args:
            max_retries: 最大重试次数
        """
        for i in range(max_retries):
            try:
                logger.info(f"尝试重新同步 (第{i+1}次)")
                await asyncio.sleep(5 * (i + 1))  # 指数退避
                await self._sync_devices()
                logger.info("重新同步成功")
                return
            except Exception as e:
                logger.error(f"重新同步失败 (第{i+1}次): {str(e)}", exc_info=True)

        logger.error(f"达到最大重试次数({max_retries})，放弃同步")

    async def _push_to_redis(self, device_states: Dict[str, Dict[str, Any]]) -> None:
        """推送设备状态到Redis

        优化版本：
        1. 使用管道批量操作，减少网络往返
        2. 使用哈希表存储设备状态，减少内存使用
        3. 使用集合存储设备分类，便于快速查询
        4. 设置合理的过期时间，避免内存泄漏

        Args:
            device_states: 设备状态字典
        """
        if not self.redis_client:
            logger.warning("Redis客户端未初始化，无法推送设备状态")
            return

        try:
            # 获取Core服务ID
            core_id = getattr(self, 'core_id', None)
            if not core_id:
                # 尝试从配置中获取Core服务ID
                try:
                    from src.config.settings import CoreSettings
                    settings = CoreSettings()
                    core_id = settings.core_id
                    logger.info(f"从配置中获取Core服务ID: {core_id}")
                except Exception as e:
                    logger.error(f"获取Core服务ID异常: {str(e)}")
                    core_id = "default"

            logger.info(f"使用Core服务ID: {core_id}")

            # 使用管道批量操作，提高性能
            pipeline = self.redis_client.pipeline()

            # 清理旧的状态集合
            status_sets = ["running", "stopped", "starting", "stopping", "error", "unknown"]
            for status in status_sets:
                pipeline.delete(f"devices:status:{status}")

            # 注册Core服务ID到Redis
            pipeline.sadd("cores:all", core_id)

            # 清理该Core服务的设备集合
            pipeline.delete(f"core:{core_id}:devices")

            # 设备状态存储在hash中，键名为"device:{core_id}:{device_id}:state"
            for device_id, state in device_states.items():
                # 添加Core服务ID到设备状态
                state['core_id'] = core_id

                # 使用新的键名格式，包含Core服务ID
                redis_key = f"device:{core_id}:{device_id}:state"

                # 获取设备状态
                status = state.get('status', 'unknown')

                # 将状态序列化为JSON
                state_json = json.dumps(state)
                # 存储到Redis
                pipeline.set(redis_key, state_json)
                # 设置过期时间（1天）
                pipeline.expire(redis_key, 86400)

                # 同时在设备列表中添加设备ID
                pipeline.sadd("devices:all", device_id)

                # 将设备ID添加到该Core服务的设备集合
                pipeline.sadd(f"core:{core_id}:devices", device_id)

                # 根据设备状态添加到不同的集合中
                pipeline.sadd(f"devices:status:{status}", device_id)
                pipeline.sadd(f"core:{core_id}:devices:status:{status}", device_id)

                # 根据设备类型添加到不同的集合中
                device_type = state.get('device_type', 'unknown')
                pipeline.sadd(f"devices:type:{device_type}", device_id)
                pipeline.sadd(f"core:{core_id}:devices:type:{device_type}", device_id)

                # 设置设备最后更新时间
                pipeline.hset("devices:last_update", device_id, int(time.time()))

                # 存储设备硬件信息到单独的哈希表，便于快速查询
                hardware_key = f"device:{device_id}:hardware"
                hardware_info = state.get('hardware_info', {})
                if hardware_info:
                    # 将硬件信息存储为哈希表
                    for key, value in hardware_info.items():
                        pipeline.hset(hardware_key, key, str(value))
                    pipeline.expire(hardware_key, 86400)  # 1天过期

                # 存储CPU和内存使用率到单独的哈希表，便于监控
                pipeline.hset("devices:cpu_usage", device_id, state.get('cpu_usage', '0%'))
                pipeline.hset("devices:memory_usage", device_id, state.get('memory_usage', '0%'))
                pipeline.hset("devices:network_status", device_id, state.get('network_status', '未知'))

            # 设置设备列表过期时间（7天）
            pipeline.expire("devices:all", 604800)
            pipeline.expire("devices:last_update", 604800)

            # 为每个状态集合设置过期时间（1天）
            for status in status_sets:
                pipeline.expire(f"devices:status:{status}", 86400)

            # 执行管道操作
            await pipeline.execute()
            logger.debug(f"已将{len(device_states)}个设备状态推送到Redis")

        except Exception as e:
            logger.error(f"推送设备状态到Redis异常: {str(e)}", exc_info=True)

    async def _push_changes_to_redis(self, changes: List[Dict[str, Any]]) -> None:
        """推送设备状态变更到Redis

        优化版本：
        1. 批量处理变更记录
        2. 使用管道减少网络往返
        3. 优化发布/订阅通知

        Args:
            changes: 变更列表
        """
        if not self.redis_client:
            logger.warning("Redis客户端未初始化，无法推送设备状态变更")
            return

        try:
            # 使用管道批量操作，提高性能
            pipeline = self.redis_client.pipeline()

            # 变更记录存储在列表中，键名为"device:changes"
            redis_key = "device:changes"

            # 批量序列化变更记录
            change_jsons = []
            for change in changes:
                # 确保状态是字符串
                old_status = change.get('old_status', 'unknown')
                new_status = change.get('new_status', 'unknown')

                serializable_change = {
                    'device_id': change.get('device_id', ''),
                    'name': change.get('name', ''),
                    'old_status': old_status,
                    'new_status': new_status,
                    'timestamp': change.get('timestamp', int(time.time()))
                }

                change_jsons.append(json.dumps(serializable_change))

            # 批量添加到Redis列表
            if change_jsons:
                pipeline.lpush(redis_key, *change_jsons)

            # 限制列表长度，避免无限增长
            pipeline.ltrim(redis_key, 0, 999)  # 保留最近1000条变更记录

            # 设置过期时间（7天）
            pipeline.expire(redis_key, 604800)

            # 为每个设备记录最后一次状态变更
            for change in changes:
                device_id = change.get('device_id')
                if device_id:
                    # 记录设备最后一次状态变更
                    last_change_key = f"device:{device_id}:last_change"
                    pipeline.set(last_change_key, json.dumps({
                        'old_status': change.get('old_status', 'unknown'),
                        'new_status': change.get('new_status', 'unknown'),
                        'timestamp': change.get('timestamp', int(time.time()))
                    }))
                    pipeline.expire(last_change_key, 604800)  # 7天过期

            # 执行管道操作
            await pipeline.execute()
            logger.debug(f"已将{len(changes)}个设备状态变更推送到Redis")

            # 发布变更通知 - 使用单一全局通知减少网络流量
            if changes:
                # 构建批量变更通知
                serializable_changes = []
                device_specific_changes = {}

                for change in changes:
                    device_id = change.get('device_id', '')

                    # 添加到全局变更列表
                    serializable_changes.append({
                        'device_id': device_id,
                        'name': change.get('name', ''),
                        'old_status': change.get('old_status', 'unknown'),
                        'new_status': change.get('new_status', 'unknown'),
                        'timestamp': change.get('timestamp', int(time.time()))
                    })

                    # 添加到设备特定变更
                    if device_id:
                        device_specific_changes[device_id] = {
                            'device_id': device_id,
                            'name': change.get('name', ''),
                            'old_status': change.get('old_status', 'unknown'),
                            'new_status': change.get('new_status', 'unknown'),
                            'timestamp': change.get('timestamp', int(time.time()))
                        }

                # 发布到全局变更频道
                await self.redis_client.publish("device:all:changes", json.dumps({
                    "changes": serializable_changes,
                    "timestamp": int(time.time()),
                    "count": len(serializable_changes)
                }))

                # 发布到设备特定频道 - 只发送一次每个设备的最新变更
                for device_id, change_data in device_specific_changes.items():
                    channel = f"device:{device_id}:changes"
                    await self.redis_client.publish(channel, json.dumps(change_data))

        except Exception as e:
            logger.error(f"推送设备状态变更到Redis异常: {str(e)}", exc_info=True)

    async def _push_changes_to_backend(self, changes: List[Dict[str, Any]]) -> None:
        """推送变更到后端API（备用方案）

        优化版本：
        1. 添加重试机制
        2. 添加超时控制
        3. 批量处理变更

        Args:
            changes: 变更列表
        """
        if not self.backend_url:
            logger.warning("未配置后端URL，无法推送变更")
            return

        # 最大重试次数
        max_retries = 3
        # 超时设置（秒）
        timeout = aiohttp.ClientTimeout(total=10)

        # 构建请求数据
        payload = {
            'changes': changes,
            'timestamp': int(time.time()),
            'count': len(changes),
            'source': 'core-sync-service'
        }

        # 敏感数据脱敏处理
        log_payload = json.dumps({
            'count': len(changes),
            'timestamp': payload['timestamp'],
            'source': payload['source']
        })

        url = f"{self.backend_url}/api/v1/devices/sync"
        logger.info(f"推送变更到后端: {url}, 数据摘要: {log_payload}")

        for retry in range(max_retries):
            try:
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.post(url, json=payload, headers={
                        'Content-Type': 'application/json',
                        'X-Source': 'core-sync-service'
                    }) as response:
                        if response.status == 200:
                            logger.info(f"变更推送成功，共{len(changes)}个变更")
                            return
                        else:
                            text = await response.text()
                            logger.error(f"变更推送失败，状态码: {response.status}, 响应: {text}")

                            # 如果是服务器错误，尝试重试
                            if 500 <= response.status < 600 and retry < max_retries - 1:
                                wait_time = 2 ** retry  # 指数退避
                                logger.info(f"将在{wait_time}秒后重试 (第{retry+1}次)")
                                await asyncio.sleep(wait_time)
                                continue
                            else:
                                # 客户端错误或已达到最大重试次数
                                break

            except asyncio.TimeoutError:
                logger.error(f"推送变更超时 (第{retry+1}次)")
                if retry < max_retries - 1:
                    wait_time = 2 ** retry
                    logger.info(f"将在{wait_time}秒后重试")
                    await asyncio.sleep(wait_time)
                    continue

            except Exception as e:
                logger.error(f"推送变更异常 (第{retry+1}次): {str(e)}", exc_info=True)
                if retry < max_retries - 1:
                    wait_time = 2 ** retry
                    logger.info(f"将在{wait_time}秒后重试")
                    await asyncio.sleep(wait_time)
                    continue

        logger.error(f"推送变更失败，已达到最大重试次数({max_retries})")

        # 记录失败的变更，以便后续处理
        try:
            # 将失败的变更写入本地文件，以便后续恢复
            import datetime
            import os

            # 确保目录存在
            os.makedirs('logs/failed_syncs', exist_ok=True)

            # 生成文件名
            filename = f"logs/failed_syncs/sync_failed_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            # 写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(payload, f, ensure_ascii=False, indent=2)

            logger.info(f"已将失败的变更保存到文件: {filename}")

        except Exception as e:
            logger.error(f"保存失败变更异常: {str(e)}", exc_info=True)
