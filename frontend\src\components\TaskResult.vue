<template>
  <el-dialog
    v-model="visible"
    title="🎉 任务执行结果"
    width="700px"
    :close-on-click-modal="false"
  >
    <div v-if="task" class="task-result">
      <!-- 基本信息 -->
      <el-card class="result-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="success-icon"><SuccessFilled /></el-icon>
            <span>任务执行成功</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ task.id }}</el-descriptions-item>
          <el-descriptions-item label="平台">{{ task.platform_name }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ task.account_name }}</el-descriptions-item>
          <el-descriptions-item label="设备">{{ task.device_id }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatTime(task.start_time) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatTime(task.end_time) }}</el-descriptions-item>
          <el-descriptions-item label="执行时长">{{ calculateDuration(task.start_time, task.end_time) }}</el-descriptions-item>
          <el-descriptions-item label="工作流">{{ task.workflow_name || '默认工作流' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 上传结果 -->
      <el-card class="result-card" shadow="never" style="margin-top: 20px;">
        <template #header>
          <div class="card-header">
            <el-icon class="upload-icon"><Upload /></el-icon>
            <span>上传结果</span>
          </div>
        </template>
        
        <div class="upload-results">
          <div v-if="task.result && task.result.upload_url" class="result-item">
            <label>上传链接：</label>
            <el-link :href="task.result.upload_url" target="_blank" type="primary">
              {{ task.result.upload_url }}
            </el-link>
            <el-button 
              size="small" 
              @click="copyToClipboard(task.result.upload_url)"
              style="margin-left: 10px;"
            >
              复制链接
            </el-button>
          </div>
          
          <div v-if="task.result && task.result.video_id" class="result-item">
            <label>视频ID：</label>
            <span>{{ task.result.video_id }}</span>
            <el-button 
              size="small" 
              @click="copyToClipboard(task.result.video_id)"
              style="margin-left: 10px;"
            >
              复制ID
            </el-button>
          </div>
          
          <div class="result-item">
            <label>上传文件：</label>
            <span>{{ getFileName(task.content_path) }}</span>
          </div>
          
          <div v-if="task.result && task.result.file_size" class="result-item">
            <label>文件大小：</label>
            <span>{{ formatFileSize(task.result.file_size) }}</span>
          </div>
          
          <div v-if="task.result && task.result.duration" class="result-item">
            <label>视频时长：</label>
            <span>{{ formatDuration(task.result.duration) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 执行统计 -->
      <el-card class="result-card" shadow="never" style="margin-top: 20px;">
        <template #header>
          <div class="card-header">
            <el-icon class="stats-icon"><DataAnalysis /></el-icon>
            <span>执行统计</span>
          </div>
        </template>
        
        <div class="execution-stats">
          <div class="stat-item">
            <div class="stat-label">总步骤数</div>
            <div class="stat-value">{{ task.result?.total_steps || '-' }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">成功步骤</div>
            <div class="stat-value success">{{ task.result?.success_steps || '-' }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">跳过步骤</div>
            <div class="stat-value warning">{{ task.result?.skipped_steps || '-' }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">重试次数</div>
            <div class="stat-value info">{{ task.result?.retry_count || '0' }}</div>
          </div>
        </div>
      </el-card>

      <!-- 操作建议 -->
      <el-card class="result-card" shadow="never" style="margin-top: 20px;">
        <template #header>
          <div class="card-header">
            <el-icon class="tips-icon"><InfoFilled /></el-icon>
            <span>后续操作建议</span>
          </div>
        </template>
        
        <div class="operation-tips">
          <el-alert
            type="success"
            :closable="false"
            show-icon
          >
            <template #title>
              <div>
                <p><strong>✅ 任务已成功完成！</strong></p>
                <p style="margin-top: 8px;">建议您：</p>
                <ul style="margin: 8px 0; padding-left: 20px;">
                  <li>检查上传的内容是否符合预期</li>
                  <li>如有需要，可以分享上传链接</li>
                  <li>关注内容的播放数据和用户反馈</li>
                  <li v-if="task.platform_name === 'YouTube'">等待YouTube处理完成后检查视频质量</li>
                </ul>
              </div>
            </template>
          </el-alert>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button 
          v-if="task && task.result && task.result.upload_url" 
          type="primary" 
          @click="openUploadUrl"
        >
          查看上传内容
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { SuccessFilled, Upload, DataAnalysis, InfoFilled } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
  task: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  task: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 工具函数
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}

const calculateDuration = (startTime: string, endTime: string) => {
  if (!startTime || !endTime) return '-'
  
  const start = new Date(startTime)
  const end = new Date(endTime)
  const diff = end.getTime() - start.getTime()
  
  const minutes = Math.floor(diff / 60000)
  const seconds = Math.floor((diff % 60000) / 1000)
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  } else {
    return `${seconds}秒`
  }
}

const getFileName = (filePath: string) => {
  if (!filePath) return ''
  return filePath.split(/[/\\]/).pop() || filePath
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '-'
  
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const formatDuration = (seconds: number) => {
  if (!seconds) return '-'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const openUploadUrl = () => {
  if (props.task?.result?.upload_url) {
    window.open(props.task.result.upload_url, '_blank')
  }
}
</script>

<style scoped>
.task-result {
  max-height: 600px;
  overflow-y: auto;
}

.result-card {
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.success-icon {
  color: #67c23a;
}

.upload-icon {
  color: #409eff;
}

.stats-icon {
  color: #e6a23c;
}

.tips-icon {
  color: #909399;
}

.upload-results {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-item label {
  font-weight: 500;
  min-width: 80px;
  color: #606266;
}

.execution-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.warning {
  color: #e6a23c;
}

.stat-value.info {
  color: #409eff;
}

.operation-tips {
  margin-top: 10px;
}
</style>
