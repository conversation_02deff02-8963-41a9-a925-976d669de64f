"""
Core服务API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from typing import List, Dict, Any
import logging

from app.core.security import get_current_user
from app.services.consul_discovery import ConsulDiscovery
from app.config.database import DatabaseConfig
from urllib.parse import urlparse

# 配置日志
logger = logging.getLogger(__name__)

def init_core_routes(fastapi_app):
    """初始化Core服务相关路由

    Args:
        fastapi_app: FastAPI应用实例
    """
    router = APIRouter(
        prefix="/api/v1/cores",
        tags=["cores"],
        dependencies=[Depends(get_current_user)]
    )

    @router.get("/", response_model=List[Dict[str, Any]])
    async def get_core_services():
        """获取所有Core服务列表

        Returns:
            List[Dict[str, Any]]: Core服务列表，包含id和name
        """
        try:
            # 获取数据库配置
            db_config = DatabaseConfig()

            # 解析Consul URL
            consul_url = urlparse(db_config.consul_url)
            consul_host = consul_url.hostname or "localhost"
            consul_port = consul_url.port or 8500

            # 从Consul获取Core服务列表
            consul_discovery = ConsulDiscovery(
                consul_host=consul_host,
                consul_port=consul_port
            )

            # 获取所有Core服务实例
            services = consul_discovery.get_all_services("thunderhub-core")

            if not services:
                # 如果没有找到服务，返回默认服务
                logger.warning("无法从Consul获取Core服务列表，返回默认服务")
                return [
                    {
                        "id": "default",
                        "name": "默认Core服务",
                        "host": "localhost",
                        "port": 50051
                    }
                ]

            # 格式化服务列表
            result = []
            for service_id, service_info in services.items():
                result.append({
                    "id": service_id,
                    "name": service_info.get("name", f"Core-{service_id}"),
                    "host": service_info.get("host"),
                    "port": service_info.get("port")
                })

            return result

        except Exception as e:
            logger.error(f"获取Core服务列表异常: {str(e)}", exc_info=True)
            # 返回默认服务，避免前端崩溃
            return [
                {
                    "id": "default",
                    "name": "默认Core服务"
                }
            ]

    # 注册路由
    fastapi_app.include_router(router)
