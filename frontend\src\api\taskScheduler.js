/**
 * 任务调度器API
 */
import request from '@/utils/request'

export const taskSchedulerApi = {
  /**
   * 获取调度任务列表
   */
  getTasks() {
    return request({
      url: '/api/v1/task-scheduler/list',
      method: 'get'
    })
  },

  /**
   * 获取单个调度任务详情
   * @param {string} taskId - 任务ID
   */
  getTask(taskId) {
    return request({
      url: `/api/v1/task-scheduler/${taskId}`,
      method: 'get'
    })
  },

  /**
   * 创建调度任务
   * @param {Object} taskData - 任务数据
   */
  createTask(taskData) {
    return request({
      url: '/api/v1/task-scheduler/create',
      method: 'post',
      data: taskData
    })
  },

  /**
   * 更新调度任务
   * @param {string} taskId - 任务ID
   * @param {Object} taskData - 任务数据
   */
  updateTask(taskId, taskData) {
    return request({
      url: `/api/v1/task-scheduler/${taskId}`,
      method: 'put',
      data: taskData
    })
  },

  /**
   * 暂停调度任务
   * @param {string} taskId - 任务ID
   */
  pauseTask(taskId) {
    return request({
      url: `/api/v1/task-scheduler/${taskId}/pause`,
      method: 'post'
    })
  },

  /**
   * 恢复调度任务
   * @param {string} taskId - 任务ID
   */
  resumeTask(taskId) {
    return request({
      url: `/api/v1/task-scheduler/${taskId}/resume`,
      method: 'post'
    })
  },

  /**
   * 删除调度任务
   * @param {string} taskId - 任务ID
   */
  deleteTask(taskId) {
    return request({
      url: `/api/v1/task-scheduler/${taskId}`,
      method: 'delete'
    })
  }
}
