"""
Core服务日志配置
支持按日期和类型分文件存储
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path


class DateFolderFileHandler(logging.handlers.TimedRotatingFileHandler):
    """按日期文件夹组织的文件处理器"""

    def __init__(self, base_log_dir: str, log_type: str, when='midnight', interval=1, backupCount=30):
        self.base_log_dir = Path(base_log_dir)
        self.log_type = log_type

        # 确保基础日志目录存在
        self.base_log_dir.mkdir(parents=True, exist_ok=True)

        # 生成当前日期的文件夹和文件名
        current_date = datetime.now().strftime('%Y-%m-%d')
        self.date_dir = self.base_log_dir / current_date
        self.date_dir.mkdir(parents=True, exist_ok=True)

        filename = self.date_dir / f"{log_type}.log"

        super().__init__(
            filename=str(filename),
            when=when,
            interval=interval,
            backupCount=backupCount,
            encoding='utf-8'
        )

    def rotation_filename(self, default_name):
        """自定义轮转文件名"""
        # 获取新的日期
        new_date = datetime.now().strftime('%Y-%m-%d')
        new_date_dir = self.base_log_dir / new_date
        new_date_dir.mkdir(parents=True, exist_ok=True)

        return str(new_date_dir / f"{self.log_type}.log")


def setup_core_logging(log_dir: str = "logs"):
    """
    设置Core服务日志配置
    
    Args:
        log_dir: 日志目录路径
    """
    
    # 创建日志目录
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置根日志级别
    root_logger.setLevel(logging.DEBUG)
    
    # 定义日志格式
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 1. 控制台处理器 - 只显示INFO及以上级别
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    root_logger.addHandler(console_handler)
    
    # 2. 全量日志文件 - 记录所有日志
    all_handler = DateFolderFileHandler(log_dir, "all", when='midnight', backupCount=30)
    all_handler.setLevel(logging.DEBUG)
    all_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(all_handler)

    # 3. 错误日志文件 - 只记录ERROR及以上级别
    error_handler = DateFolderFileHandler(log_dir, "error", when='midnight', backupCount=90)
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)

    # 4. 任务执行日志文件 - 记录任务执行相关日志
    task_handler = DateFolderFileHandler(log_dir, "task", when='midnight', backupCount=30)
    task_handler.setLevel(logging.DEBUG)
    task_handler.setFormatter(detailed_formatter)
    
    task_loggers = [
        'src.services.task_executor',
        'src.services.task_manager',
        'src.api.task_service'
    ]
    
    for logger_name in task_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(task_handler)
        logger.propagate = True
    
    # 5. 设备管理日志文件 - 记录设备操作日志
    device_handler = DateFolderFileHandler(log_dir, "device", when='midnight', backupCount=30)
    device_handler.setLevel(logging.DEBUG)
    device_handler.setFormatter(detailed_formatter)

    device_loggers = [
        'src.devices',
        'src.devices.ldplayer',
        'src.devices.ldplayer.controller',
        'src.devices.device_manager'
    ]

    for logger_name in device_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(device_handler)
        logger.propagate = True

    # 6. 下载器日志文件 - 记录下载任务日志
    download_handler = DateFolderFileHandler(log_dir, "download", when='midnight', backupCount=30)
    download_handler.setLevel(logging.DEBUG)
    download_handler.setFormatter(detailed_formatter)

    download_loggers = [
        'src.downloaders',
        'src.downloaders.download_task_executor',
        'src.downloaders.douyin_downloader',
        'src.downloaders.youtube_downloader'
    ]

    for logger_name in download_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(download_handler)
        logger.propagate = True

    # 7. 上传器日志文件 - 记录上传任务日志
    upload_handler = DateFolderFileHandler(log_dir, "upload", when='midnight', backupCount=30)
    upload_handler.setLevel(logging.DEBUG)
    upload_handler.setFormatter(detailed_formatter)
    
    upload_loggers = [
        'src.uploaders',
        'src.uploaders.youtube_uploader',
        'src.uploaders.douyin_uploader',
        'src.uploaders.tiktok_uploader'
    ]
    
    for logger_name in upload_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(upload_handler)
        logger.propagate = True
    
    # 8. gRPC服务日志文件 - 记录gRPC通信日志
    grpc_handler = DateFolderFileHandler(log_dir, "grpc", when='midnight', backupCount=30)
    grpc_handler.setLevel(logging.INFO)
    grpc_handler.setFormatter(detailed_formatter)

    grpc_loggers = [
        'src.api',
        'src.api.task_service',
        'src.api.device_service',
        'grpc'
    ]

    for logger_name in grpc_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(grpc_handler)
        logger.propagate = True

    # 9. 主服务日志文件 - 记录主服务相关日志
    main_handler = DateFolderFileHandler(log_dir, "main", when='midnight', backupCount=30)
    main_handler.setLevel(logging.DEBUG)
    main_handler.setFormatter(detailed_formatter)
    
    main_loggers = [
        'src.main_service',
        'src.config',
        '__main__'
    ]
    
    for logger_name in main_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(main_handler)
        logger.propagate = True
    
    # 设置第三方库的日志级别
    logging.getLogger('grpc').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('appium').setLevel(logging.WARNING)
    logging.getLogger('selenium').setLevel(logging.WARNING)
    
    # 记录日志配置完成
    logger = logging.getLogger(__name__)
    logger.info(f"Core服务日志配置完成，日志目录: {log_path.absolute()}")
    logger.info("日志文件组织结构:")
    logger.info("  logs/")
    logger.info("  ├── YYYY-MM-DD/")
    logger.info("  │   ├── all.log: 全量日志")
    logger.info("  │   ├── error.log: 错误日志")
    logger.info("  │   ├── task.log: 任务执行日志")
    logger.info("  │   ├── device.log: 设备管理日志")
    logger.info("  │   ├── download.log: 下载任务日志")
    logger.info("  │   ├── upload.log: 上传任务日志")
    logger.info("  │   ├── grpc.log: gRPC通信日志")
    logger.info("  │   └── main.log: 主服务日志")
    logger.info("  └── YYYY-MM-DD/ (其他日期的日志)")


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的logger
    
    Args:
        name: logger名称
        
    Returns:
        logging.Logger: 配置好的logger实例
    """
    return logging.getLogger(name)


# 便捷的logger获取函数
def get_task_logger() -> logging.Logger:
    """获取任务日志记录器"""
    return logging.getLogger('src.services.task_executor')


def get_device_logger() -> logging.Logger:
    """获取设备日志记录器"""
    return logging.getLogger('src.devices')


def get_download_logger() -> logging.Logger:
    """获取下载日志记录器"""
    return logging.getLogger('src.downloaders')


def get_upload_logger() -> logging.Logger:
    """获取上传日志记录器"""
    return logging.getLogger('src.uploaders')


def get_main_logger() -> logging.Logger:
    """获取主服务日志记录器"""
    return logging.getLogger('src.main_service')
