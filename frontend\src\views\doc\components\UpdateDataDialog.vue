<template>
  <el-dialog
    v-model="dialogVisible"
    title="更新账号数据"
    width="500px"
    @close="handleClose"
  >
    <div v-if="account" class="account-header">
      <div class="account-info">
        <h3>{{ account.account_name }}</h3>
        <p>{{ account.platform }} - {{ getBenchmarkTypeText(account.benchmark_type) }}</p>
      </div>
    </div>

    <el-form
      ref="formRef"
      :model="form"
      label-width="120px"
      v-if="account"
    >
      <el-form-item label="粉丝数">
        <el-input-number
          v-model="form.followers"
          :min="0"
          :max="*********"
          placeholder="请输入粉丝数"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="关注数">
        <el-input-number
          v-model="form.following"
          :min="0"
          :max="*********"
          placeholder="请输入关注数"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="发布数">
        <el-input-number
          v-model="form.posts_count"
          :min="0"
          :max="*********"
          placeholder="请输入发布数"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="平均观看数">
        <el-input-number
          v-model="form.avg_views"
          :min="0"
          :max="*********"
          placeholder="请输入平均观看数"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="平均点赞数">
        <el-input-number
          v-model="form.avg_likes"
          :min="0"
          :max="*********"
          placeholder="请输入平均点赞数"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="互动率">
        <el-input-number
          v-model="form.engagement_rate"
          :min="0"
          :max="1"
          :step="0.001"
          :precision="3"
          placeholder="请输入互动率（0-1）"
          style="width: 100%"
        />
        <div class="form-tip">
          互动率 = (点赞数 + 评论数 + 分享数) / 观看数
        </div>
      </el-form-item>

      <el-form-item label="增长率">
        <el-input-number
          v-model="form.growth_rate"
          :min="-1"
          :max="10"
          :step="0.001"
          :precision="3"
          placeholder="请输入增长率"
          style="width: 100%"
        />
        <div class="form-tip">
          月增长率，正数表示增长，负数表示下降
        </div>
      </el-form-item>

      <el-form-item label="最后发布时间">
        <el-date-picker
          v-model="form.last_post_date"
          type="datetime"
          placeholder="请选择最后发布时间"
          style="width: 100%"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
    </el-form>

    <div class="data-tips">
      <el-alert
        title="数据更新提示"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <ul>
            <li>建议定期更新账号数据以保持准确性</li>
            <li>可以通过爬虫工具自动获取最新数据</li>
            <li>数据将用于分析对标账号的表现趋势</li>
          </ul>
        </template>
      </el-alert>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="autoFetchData" :loading="fetching">
          自动获取
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { updateBenchmarkAccountData, type BenchmarkAccount } from '@/api/content'

interface Props {
  modelValue: boolean
  account: BenchmarkAccount | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const fetching = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  followers: null as number | null,
  following: null as number | null,
  posts_count: null as number | null,
  avg_views: null as number | null,
  avg_likes: null as number | null,
  engagement_rate: null as number | null,
  growth_rate: null as number | null,
  last_post_date: null as string | null
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  }
})

// 监听账号数据变化，填充表单
watch(() => props.account, (account) => {
  if (account && account.account_data) {
    const data = account.account_data
    Object.assign(form, {
      followers: data.followers || null,
      following: data.following || null,
      posts_count: data.posts_count || null,
      avg_views: data.avg_views || null,
      avg_likes: data.avg_likes || null,
      engagement_rate: data.engagement_rate || null,
      growth_rate: data.growth_rate || null,
      last_post_date: data.last_post_date || null
    })
  }
}, { immediate: true })

// 方法
const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const resetForm = () => {
  Object.assign(form, {
    followers: null,
    following: null,
    posts_count: null,
    avg_views: null,
    avg_likes: null,
    engagement_rate: null,
    growth_rate: null,
    last_post_date: null
  })
}

const autoFetchData = async () => {
  if (!props.account) return

  fetching.value = true
  try {
    // TODO: 实现自动获取数据的功能
    // 这里可以调用爬虫API或第三方数据API
    ElMessage.info('自动获取数据功能开发中...')
    
    // 模拟获取数据
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟填充一些数据
    form.followers = Math.floor(Math.random() * 100000) + 10000
    form.following = Math.floor(Math.random() * 1000) + 100
    form.posts_count = Math.floor(Math.random() * 500) + 50
    form.avg_views = Math.floor(Math.random() * 10000) + 1000
    form.avg_likes = Math.floor(Math.random() * 1000) + 100
    form.engagement_rate = Math.random() * 0.1 + 0.01
    form.last_post_date = new Date().toISOString().slice(0, 19).replace('T', ' ')
    
    ElMessage.success('数据获取成功（模拟）')
  } catch (error) {
    console.error('自动获取数据失败:', error)
    ElMessage.error('自动获取数据失败')
  } finally {
    fetching.value = false
  }
}

const handleSubmit = async () => {
  if (!props.account) return

  submitting.value = true
  try {
    // 过滤掉null值
    const updateData = Object.fromEntries(
      Object.entries(form).filter(([_, value]) => value !== null)
    )

    await updateBenchmarkAccountData(props.account._id!, updateData)

    ElMessage.success('账号数据更新成功')
    emit('success')
  } catch (error) {
    console.error('更新账号数据失败:', error)
    ElMessage.error('更新账号数据失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.account-header {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.account-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.account-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.data-tips {
  margin: 20px 0;
}

.data-tips ul {
  margin: 0;
  padding-left: 20px;
}

.data-tips li {
  font-size: 12px;
  line-height: 1.6;
  margin-bottom: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
