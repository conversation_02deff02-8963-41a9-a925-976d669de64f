# 📚 ThunderHub 文档中心

---

## 1. 项目概述

- [📝 项目简介](project_intro)
  - [📋 前端代码规范](.codestyle/vue)
  - [📋 后端代码规范](.codestyle/python)

---

## 2. 需求文档

### 2.1 YouTube相关

- [📋 YouTube上传需求](1_demand/youtube-upload/3_youtube-upload-1.0)
---

## 3. 架构设计

### 3.1 系统架构

- [🏗️ 系统架构](2_architecture/architecture)
- [🗃️ 数据库设计](2_architecture/database_design)
- [📂 文件管理设计](2_architecture/file_manager_design)
- [⚙️ MCP服务设计](2_architecture/mcp_service_design)

### 3.2 YouTube上传

- [🎥 YouTube上传设计](2_architecture/youtube-upload/youtube-upload-design)

---

## 4. 开发文档

- [📅 开发计划](3_development/dev_plan)
- [👨‍💻 YouTube上传开发](3_development/youtube_upload.csv)

## 5. 测试文档

## 6. 部署文档
