# 账号管理功能重新设计

## 1. 功能概述

账号管理模块是系统的核心功能之一，负责管理各社交媒体平台的账号信息，支持账号的添加、编辑、删除、筛选和与设备的关联管理。

## 2. 数据模型

### 2.1 核心数据集合

#### social_platforms 集合 (社交媒体平台)

```javascript
{
  "_id": ObjectId,       // MongoDB自动生成的ID
  "id": String,          // 平台唯一标识符，如"youtube", "tiktok"
  "name": String,        // 平台名称，如"YouTube", "TikTok"
  "icon": String,        // 平台图标文件名
  "website": String,     // 平台官方网站，如"https://www.youtube.com"
  "status": String,      // 平台状态："active", "inactive", "maintenance"
  "features": [String],  // 平台支持的功能，如["video", "live", "comment"]
  "created_at": Date,    // 创建时间
  "updated_at": Date     // 更新时间
}
```

#### platform_apps 集合 (平台应用)

```javascript
{
  "_id": ObjectId,       // MongoDB自动生成的ID
  "id": String,          // 应用唯一标识符，如"youtube_android"
  "platform_id": String, // 关联的平台ID
  "name": String,        // 应用名称，如"YouTube Android"
  "type": String,        // 应用类型："android", "ios", "web", "desktop"
  "package_name": String,// 应用包名，如"com.google.android.youtube"
  "main_activity": String,// 主Activity，如"com.google.android.youtube.HomeActivity"
  "version": String,     // 应用版本，如"17.36.3"
  "status": String,      // 应用状态："active", "inactive", "deprecated"
  "ui_elements": {       // UI元素选择器
    "login_button": String, // 登录按钮选择器
    "username_field": String, // 用户名输入框选择器
    "password_field": String  // 密码输入框选择器
  },
  "actions": {           // 预定义操作
    "login": [Object],   // 登录操作步骤
    "post": [Object]     // 发布内容操作步骤
  },
  "adb_commands": {      // ADB命令
    "start_app": String, // 启动应用命令
    "stop_app": String,  // 停止应用命令
    "clear_data": String // 清除数据命令
  },
  "created_at": Date,    // 创建时间
  "updated_at": Date     // 更新时间
}
```

#### social_accounts 集合 (社交媒体账号)

```javascript
{
  "_id": ObjectId,       // MongoDB自动生成的ID
  "id": String,          // 账号唯一标识符，用于API引用
  "username": String,    // 账号用户名，如"thunderhub_official"
  "password": String,    // 加密存储的密码
  "recovery_email": String, // 恢复邮箱
  "recovery_code": String,  // 恢复码
  "display_name": String,// 显示名称，如"雷电中心官方"
  "platform_id": String, // 关联的平台ID，如"youtube", "tiktok", "douyin"
  "status": String,      // 账号状态："active", "inactive", "suspended"
  "avatar": String,      // 头像URL，账号头像图片地址
  "description": String, // 账号描述，账号简介
  "followers": Number,   // 粉丝数量，定期更新
  "following": Number,   // 关注数量，定期更新
  "posts_count": Number, // 发布内容数量，定期更新
  "last_login": Date,    // 最后登录时间，记录账号最后一次登录时间
  "created_at": Date,    // 创建时间，账号添加到系统的时间
  "updated_at": Date,    // 更新时间，账号信息最后更新时间
  "auth_data": {         // 认证数据，包含token、cookie等认证信息，应加密存储
    "token": String,     // 加密的访问令牌
    "cookies": String,   // 加密的cookie数据
    "expires_at": Date   // 令牌过期时间
  },
  "settings": {          // 账号设置，账号特定的设置项
    "auto_reply": Boolean,   // 是否启用自动回复
    "notification": Boolean, // 是否启用通知
    "privacy_level": String  // 隐私级别："public", "private", "friends"
  },
  "tags": [String],      // 标签，用于分类和筛选账号
  "core_service_id": String // Core服务ID，用于区分不同Core服务管理的账号
}
```

#### device_account_mappings 集合 (设备账号关联)

```javascript
{
  "_id": ObjectId,       // MongoDB自动生成的ID
  "device_id": String,   // 设备ID，关联到devices表的id字段
  "account_id": String,  // 账号ID，关联到social_accounts表的id字段
  "platform_id": String, // 平台ID，关联到social_platforms表的id字段
  "app_id": String,      // 应用ID，关联到platform_apps表的id字段
  "status": String,      // 关联状态："active", "inactive"
  "created_at": Date,    // 创建时间
  "updated_at": Date,    // 更新时间
  "last_used": Date,     // 最后使用时间
  "core_service_id": String, // Core服务ID
  "settings": {          // 设备特定的账号设置
    "auto_login": Boolean,   // 是否自动登录
    "keep_alive": Boolean,   // 是否保持登录状态
    "notification": Boolean  // 是否启用通知
  }
}
```

## 3. API设计

### 3.1 平台相关API

#### 获取平台列表
- 路径: `/api/v1/social/platforms`
- 方法: GET
- 参数: 
  - `status`: 可选，平台状态筛选
- 响应: 平台列表

#### 获取平台应用列表
- 路径: `/api/v1/social/platforms/{platform_id}/apps`
- 方法: GET
- 参数:
  - `platform_id`: 平台ID
  - `type`: 可选，应用类型筛选
- 响应: 应用列表

### 3.2 账号相关API

#### 获取账号列表
- 路径: `/api/v1/social/accounts`
- 方法: GET
- 参数:
  - `platform_id`: 可选，平台ID筛选
  - `core_service_id`: 可选，Core服务ID筛选
  - `status`: 可选，账号状态筛选
  - `skip`: 可选，分页起始位置
  - `limit`: 可选，分页大小
- 响应: 账号列表

#### 创建账号
- 路径: `/api/v1/social/accounts`
- 方法: POST
- 参数: 账号信息对象
- 响应: 创建的账号信息

#### 更新账号
- 路径: `/api/v1/social/accounts/{account_id}`
- 方法: PUT
- 参数: 
  - `account_id`: 账号ID
  - 请求体: 更新的账号信息
- 响应: 更新后的账号信息

#### 删除账号
- 路径: `/api/v1/social/accounts/{account_id}`
- 方法: DELETE
- 参数: 
  - `account_id`: 账号ID
- 响应: 删除结果

#### 批量更新账号
- 路径: `/api/v1/social/accounts/batch_update`
- 方法: POST
- 参数: 
  - `account_ids`: 账号ID列表
  - `update_data`: 更新数据
- 响应: 更新结果

#### 批量删除账号
- 路径: `/api/v1/social/accounts/batch_delete`
- 方法: POST
- 参数: 
  - `account_ids`: 账号ID列表
- 响应: 删除结果

### 3.3 设备账号关联API

#### 获取设备关联的账号
- 路径: `/api/v1/social/devices/{device_id}/accounts`
- 方法: GET
- 参数:
  - `device_id`: 设备ID
  - `platform_id`: 可选，平台ID筛选
- 响应: 关联的账号列表

#### 关联设备和账号
- 路径: `/api/v1/social/devices/{device_id}/accounts`
- 方法: POST
- 参数:
  - `device_id`: 设备ID
  - `account_id`: 账号ID
  - `platform_id`: 平台ID
  - `app_id`: 应用ID
- 响应: 关联结果

#### 解除设备和账号关联
- 路径: `/api/v1/social/devices/{device_id}/accounts/{account_id}`
- 方法: DELETE
- 参数:
  - `device_id`: 设备ID
  - `account_id`: 账号ID
- 响应: 解除关联结果

## 4. 前端组件设计

### 4.1 账号管理页面

账号管理页面是账号管理模块的主界面，包含以下功能：

1. **筛选区域**
   - 平台筛选下拉框
   - Core服务筛选下拉框
   - 状态筛选下拉框
   - 关键词搜索框

2. **操作区域**
   - 添加账号按钮
   - 批量操作按钮（删除、更新状态等）
   - 导入账号按钮

3. **账号列表**
   - 账号基本信息（用户名、显示名称、平台）
   - 账号状态标签
   - 关联设备信息
   - 操作按钮（编辑、删除、关联设备）

### 4.2 添加/编辑账号对话框

1. **基本信息区域**
   - 平台选择下拉框
   - 用户名输入框
   - 密码输入框
   - 恢复邮箱/恢复码输入框
   - 显示名称输入框
   - 描述输入框

2. **高级设置区域**
   - 标签选择/添加
   - 账号状态选择
   - Core服务选择

3. **设备关联区域**
   - 设备选择下拉框
   - 应用选择下拉框

### 4.3 账号导入功能

支持从文本文件导入账号信息，特别是支持从`模拟.txt`格式的文件导入：

1. **文件上传区域**
   - 文件选择/拖拽上传
   - 支持格式说明

2. **导入预览区域**
   - 解析结果预览
   - 字段映射设置
   - 错误/警告提示

3. **导入选项**
   - 平台选择（默认为YouTube，标记为GG）
   - Core服务选择
   - 冲突处理策略选择

## 5. 实现要点

### 5.1 账号数据安全

1. **密码加密存储**
   - 使用安全的加密算法存储密码
   - 避免明文存储敏感信息

2. **访问控制**
   - 实现基于角色的访问控制
   - 记录账号操作日志

### 5.2 账号导入功能

1. **格式解析**
   - 支持解析`模拟.txt`格式的文件
   - 识别GG（YouTube）、FB（Facebook）等平台标记
   - 解析账号格式：`用户名----密码----恢复邮箱----恢复码----备注`

2. **数据验证**
   - 验证必填字段
   - 检查账号格式
   - 检查账号是否已存在

### 5.3 设备关联管理

1. **关联约束**
   - 确保一个设备对于每个平台只能关联一个账号
   - 维护设备和账号之间的一对一关系

2. **状态同步**
   - 同步设备和账号的状态变更
   - 处理设备离线情况

## 6. 数据迁移策略

1. **现有数据迁移**
   - 将现有账号数据迁移到新的数据结构
   - 保留账号历史记录

2. **数据完整性检查**
   - 检查并修复数据不一致
   - 补充缺失的必要字段
