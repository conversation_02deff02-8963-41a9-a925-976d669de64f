"""
设备服务工厂
用于创建与Core服务通信的客户端
"""

import logging
from typing import Optional
from app.core.client import DeviceServiceClient
from app.services.consul_discovery import ConsulDiscovery
from app.config.settings import get_settings

logger = logging.getLogger(__name__)

class DeviceServiceFactory:
    """设备服务工厂类"""
    
    @staticmethod
    async def create_client() -> Optional[DeviceServiceClient]:
        """创建设备服务客户端
        
        Returns:
            设备服务客户端实例，如果创建失败则返回None
        """
        try:
            settings = get_settings()
            
            # 从Consul获取Core服务地址
            consul_discovery = ConsulDiscovery(
                consul_host=settings.consul_host,
                consul_port=settings.consul_port
            )
            
            # 获取Core服务实例
            service = consul_discovery.get_service("thunderhub-core")
            
            if not service:
                # 使用默认地址
                logger.warning("无法从Consul获取Core服务，使用默认地址")
                host = settings.core_default_host
                port = settings.core_default_port
            else:
                host, port = service
            
            # 创建客户端
            client = DeviceServiceClient(host=host, port=port)
            return client
            
        except Exception as e:
            logger.error(f"创建设备服务客户端异常: {str(e)}", exc_info=True)
            return None