# YouTube普通视频上传工作流配置
workflow:
  name: "YouTube视频上传"
  description: "完整的YouTube普通视频上传流程"
  version: "1.0"
  
  # 工作流步骤
  steps:
    - name: "点击创建按钮"
      id: "click_create"
      description: "点击YouTube主界面的创建按钮（+按钮）"
      action: "click"
      element: "create_button"
      wait_after: 2
      required: true
      
    - name: "选择上传视频选项"
      id: "select_upload"
      description: "在创建选项中选择上传视频"
      action: "click"
      element: "video_upload_option"
      wait_after: 2
      required: true
      
    - name: "选择视频文件"
      id: "select_video"
      description: "选择要上传的视频文件"
      action: "click"
      element: "video_file"
      wait_after: 3
      required: true
      parameters:
        - name: "filename"
          type: "string"
          description: "视频文件名"
          
    - name: "输入视频标题"
      id: "input_title"
      description: "输入视频标题"
      action: "input_text"
      element: "title_input"
      wait_after: 1
      required: true
      parameters:
        - name: "title"
          type: "string"
          description: "视频标题"
          
    - name: "输入视频描述"
      id: "input_description"
      description: "输入视频描述"
      action: "input_text"
      element: "description_input"
      wait_after: 1
      required: false
      parameters:
        - name: "description"
          type: "string"
          description: "视频描述"
          
    - name: "设置隐私选项"
      id: "set_privacy"
      description: "设置视频的隐私选项"
      action: "select_option"
      element: "privacy_button"
      wait_after: 1
      required: true
      parameters:
        - name: "privacy"
          type: "string"
          description: "隐私设置"
          options: ["public", "unlisted", "private"]
          
    - name: "开始上传"
      id: "start_upload"
      description: "点击上传按钮开始上传视频"
      action: "click"
      element: "upload_button"
      wait_after: 1
      required: true
      
    - name: "等待上传完成"
      id: "wait_upload"
      description: "等待视频上传完成"
      action: "wait_for_completion"
      timeout: 600  # 10分钟超时
      required: true
      success_indicators:
        - type: "xpath"
          value: "//android.widget.TextView[contains(@text, '上传成功') or contains(@text, 'Upload successful')]"
        - type: "xpath"
          value: "//android.widget.TextView[contains(@text, '发布成功')]"
      error_indicators:
        - type: "xpath"
          value: "//android.widget.TextView[contains(@text, '失败') or contains(@text, 'failed') or contains(@text, 'error')]"
      progress_indicator:
        type: "id"
        value: "com.google.android.youtube:id/upload_progress_text"

# 工作流配置
config:
  # 全局超时设置
  global_timeout: 1800  # 30分钟
  
  # 重试配置
  retry:
    max_retries: 3
    retry_delay: 2
    retry_on_failure: true
    
  # 错误处理
  error_handling:
    continue_on_optional_failure: true
    screenshot_on_error: true
    log_level: "INFO"
    
  # 进度报告
  progress:
    report_interval: 5  # 每5秒报告一次进度
    detailed_logging: true
