<template>
    <div class="device-control" ref="container">
    <el-row :gutter="20" class="scrollable-container">
      <el-col :span="16">
        <el-card class="control-panel">
          <template #header>
            <div class="card-header">
              <span>控制面板</span>
              <div class="control-actions">
                <el-select
                  v-model="selectedCoreId"
                  placeholder="选择Core服务"
                  clearable
                  @change="handleCoreChange"
                  style="width: 180px; margin-right: 15px;"
                >
                  <el-option
                    v-for="core in coreServices"
                    :key="core.id"
                    :label="core.name"
                    :value="core.id"
                  />
                </el-select>
                <el-button-group>
                  <el-button type="primary" @click="refreshStatus">刷新状态</el-button>
                  <el-button type="warning" @click="autoLayout">自动布局</el-button>
                  <el-button type="success" @click="startAll">全部启动</el-button>
                  <el-button type="danger" @click="stopAll">全部停止</el-button>
                </el-button-group>
              </div>
            </div>
          </template>

          <el-table
            :data="devices"
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
            row-key="name"
            :tree-props="{children: 'children', hasChildren: (row) => row.type === 'group'}"
            :expand-row-keys="expandedRows"
            @expand-change="handleExpandChange"
          >
            <el-table-column
              type="selection"
              width="55"
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="name"
              label="设备名称"
              min-width="180"
              header-align="center"
              align="center"
            >
              <template #default="{row}">
                <div style="text-align: left">
                  <span v-if="row.type === 'group'" style="font-weight: bold">
                    {{ row.name }}
                  </span>
                  <span v-else style="display: block; text-align: center">{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              min-width="120"
              header-align="center"
              align="center"
            >
              <template #default="{row}">
                <template v-if="row.type === 'group'">
                  <el-tag :type="getRunningCount(row) > 0 ? 'success' : 'danger'">
                    {{ getRunningCount(row) }}/{{ row.children.length }}运行
                  </el-tag>
                </template>
                <template v-else>
                  <el-tag :type="row.status === '运行中' ? 'success' : 'danger'">
                    {{ row.status }}
                  </el-tag>
                </template>
              </template>
            </el-table-column>
            <el-table-column prop="uptime" label="运行时间" min-width="120" header-align="center" align="center">
              <template #default="{row}">
                <span v-if="row.uptime">{{ row.uptime }}</span>
                <span v-else-if="row.type === 'group'">-</span>
                <span v-else>加载中...</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="180" header-align="center">
              <template #default="{row}">
                <el-button-group>
                  <el-button size="small" @click="startDevice(row)">启动</el-button>
                  <el-button size="small" @click="stopDevice(row)">停止</el-button>
                  <el-button size="small" @click="restartDevice(row)">重启</el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="fetchDevices"
        @size-change="handleSizeChange"
      />
    </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card>
          <template #header>
            <span>操作日志</span>
          </template>
          <div class="log-container">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              [{{ log.time }}] {{ log.message }}
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import 'element-plus/es/components/message-box/style/css'
import { getDeviceSocket } from '@/socket.io'
import { getDevices, getCoreServices } from '@/api/rest/device'
import { ElPagination } from 'element-plus'

interface CoreService {
  id: string
  name: string
}

const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

const socket = ref<any>(null)
const coreServices = ref<CoreService[]>([])
const selectedCoreId = ref<string | null>(null)

onMounted(async () => {
  try {
    const token = localStorage.getItem('token')
    console.log('Current token:', token ? `${token.substring(0,6)}...${token.substring(token.length-4)}` : 'None')
    socket.value = await getDeviceSocket()
    console.log('Socket initialized')

    socket.value.on('connect', () => {
      console.log('Socket connected')
    })

    socket.value.on('disconnect', () => {
      console.log('Socket disconnected')
    })
  } catch (err) {
    console.error('Socket init failed:', err)
    ElMessage.error(`设备连接失败: ${err.message}`)
  }
})

const devices = ref([])

const fetchCoreServices = async () => {
  try {
    const cores = await getCoreServices()
    coreServices.value = cores
    console.log('获取到Core服务列表:', cores)
  } catch (error) {
    console.error('获取Core服务列表失败:', error)
    addLog('获取Core服务列表失败')
  }
}

const handleCoreChange = () => {
  console.log('选择的Core服务ID:', selectedCoreId.value)
  pagination.value.page = 1 // 重置到第一页
  fetchDevices()
}

const fetchDevices = async () => {
  try {
    addLog('开始获取设备数据...')

    // 如果没有选择Core服务，则不显示任何设备
    if (!selectedCoreId.value) {
      devices.value = []
      pagination.value.total = 0
      addLog('未选择Core服务，不显示设备')
      return
    }

    const res = await getDevices({
      include_config: true,
      core_id: selectedCoreId.value
    })
    if (Array.isArray(res)) {
      // 按类型分组
      const groups = {}
      res.forEach(device => {
        // 按设备名前4个字符分组
        const groupName = device.name.substring(0, 4) || '默认组'
        if (!groups[groupName]) {
          groups[groupName] = {
            name: groupName,
            type: 'group',
            children: []
          }
        }
        groups[groupName].children.push({
          name: device.name,
          status: device.status === 'running' ? '运行中' : '已停止',
          uptime: device.uptime || '-',
          _id: device._id
        })
      })
      const groupList = Object.values(groups)
      pagination.value.total = groupList.length
      // 按组分页
      const start = (pagination.value.page - 1) * pagination.value.pageSize
      devices.value = groupList.slice(start, start + pagination.value.pageSize)
      //expandedRows.value = devices.value.map(g => g.name) // 只展开当前页的分组
    }
  } catch (error) {
    console.error('获取设备失败:', error)
    addLog(`获取设备失败: ${error.message}`)
  }
}

onMounted(() => {
  fetchCoreServices()
  fetchDevices()
})

const getRunningCount = (group) => {
  return group.children.filter(d => d.status === '运行中').length
}

const selectedDevices = ref([])

const handleSelectionChange = (selection: any[]) => {
  // 过滤掉分组行，只保留设备
  selectedDevices.value = selection.filter(item => !item.type || item.type !== 'group')
}

const expandedRows = ref<string[]>([])
const logs = ref<Array<{time: string, message: string}>>([])

const handleExpandChange = (row: any, expanded: boolean) => {
  if (expanded) {
    expandedRows.value.push(row.name)
  } else {
    const index = expandedRows.value.indexOf(row.name)
    if (index > -1) {
      expandedRows.value.splice(index, 1)
    }
  }
}

const addLog = (message: string) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
  if (logs.value.length > 50) logs.value.pop()
}

const refreshStatus = async () => {
  addLog('开始刷新设备状态...')
  // TODO: 调用API获取最新状态
  addLog('设备状态刷新完成')
}

const startDevice = (device: any) => {
  addLog(`启动设备: ${device.name}`)
  // TODO: 调用启动API
}

const stopDevice = (device: any) => {
  addLog(`停止设备: ${device.name}`)
  // TODO: 调用停止API
}

const restartDevice = (device: any) => {
  addLog(`重启设备: ${device.name}`)
  // TODO: 调用重启API
}

const startAll = async () => {
  const selectedNames = selectedDevices.value.map(d => d.name)
  const message = selectedNames.length > 0
  ? `确定要启动以下设备吗?<br><br><div style="max-height:200px;overflow-y:auto;padding:8px;background:#f8f8f8;border-radius:4px;white-space:normal;">${selectedNames.map(name => `• ${name} `).join('')}</div>`
  : '确定要启动所有设备吗?'

  try {
    await ElMessageBox.confirm(message, '启动确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    })
    addLog('批量启动所有设备')
    // TODO: 调用批量启动API
  } catch {
    addLog('用户取消启动操作')
  }
}
const autoLayout = async () => {
  try {
    addLog('开始自动布局...');

    // 调用WebSocket接口
    if (!socket.value?.connected) {
      throw new Error('Socket未连接')
    }

    const result = await new Promise((resolve, reject) => {
      socket.value.emit('arrange_layout',
        { type: 'grid' },
        (response) => {
          if (response?.status === 'success') {
            resolve(response)
          } else {
            reject(response?.message || '布局失败')
          }
        }
      )
    })

    const deviceCount = result.devices?.length || 0;
    addLog(`自动布局成功: ${deviceCount}台设备已调整`);

  } catch (error) {
    addLog(`自动布局失败: ${error}`);
    ElMessage.error(`自动布局失败: ${error}`);
  }
}

const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  pagination.value.page = 1
  fetchDevices()
}

const stopAll = async () => {
  const selectedNames = selectedDevices.value.map(d => d.name)
  const message = selectedNames.length > 0
    ? `确定要停止以下设备吗?<br><br><div style="max-height:200px;overflow-y:auto;padding:8px;background:#f8f8f8;border-radius:4px;white-space:normal;">${selectedNames.map(name => `• ${name} `).join('')}</div>`
    : '确定要停止所有设备吗?'

  try {
    await ElMessageBox.confirm(message, '停止确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    })
    addLog('批量停止所有设备')
    // TODO: 调用批量停止API
  } catch (e) {
      addLog(`用户取消停止操作`)
  }
}
</script>

<style scoped>
.device-control {
  padding: 20px;
  height: calc(100vh - 180px);
  display: flex;
  flex-direction: column;
}

.scrollable-container {
    flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.control-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.control-panel :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 !important;
}

.control-panel :deep(.el-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.control-panel :deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
}

.pagination-container {
  display: flex;
  position: sticky;
  bottom: 0;
  background: #fff;
  justify-content:flex-end;
  padding: 10px 0;
  border-top: 1px solid #ebeef5;
  z-index: 1;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
.log-container {
  height: 400px;
  overflow-y: auto;
  font-family: monospace;
}
.log-item {
  padding: 4px 0;
  border-bottom: 1px solid #eee;
}
/* 表格样式 */
:deep(.el-table) {
  width: 100% !important;
  min-width: 100%;
}

:deep(.el-table th.el-table__cell),
:deep(.el-table td.el-table__cell) {
  text-align: center;
  vertical-align: middle;
}

:deep(.el-table__inner-wrapper) {
  width: 100% !important;
}

:deep(.el-table__header-wrapper),
:deep(.el-table__body-wrapper) {
  overflow-x: auto;
}

/* 设备名称单元格特别处理 */
:deep(.el-table td.el-table__cell:nth-child(2)) {
  position: relative;
}

:deep(.el-table__expand-icon) {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

:deep(.el-table td.el-table__cell:nth-child(2) > .cell) {
  display: flex;
  align-items: center;
  padding-left: 28px;
  height: 100%;
}

:deep(.el-table td.el-table__cell:nth-child(2) > .cell > div:last-child) {
  flex: 1;
}
/* 折叠按钮样式 */
/* 折叠按钮样式 - 精确调整 */
:deep(.el-table__expand-icon) {
  position: absolute;
  left: 18px;
  top: 45%;
  transform: translateY(-50%);
  padding: 0;
  margin: 0;
}

:deep(.el-table .el-table__cell) {
  position: relative;
  padding-left: 28px !important;
}

/* 精确控制箭头方向 - 修正版本 */
:deep(.el-table__expand-icon .el-icon svg) {
  transition: transform 0.2s;
}

:deep(.el-table__expand-icon--expanded .el-icon svg) {
  transform: rotate(90deg) !important;
}

:deep(.el-table__expand-icon .el-icon) {
  font-size: 16px;
  color: #409eff;
}
</style>