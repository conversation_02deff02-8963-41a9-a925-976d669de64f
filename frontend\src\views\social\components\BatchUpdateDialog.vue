<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量更新账号"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="batch-update-content">
      <p class="selected-count">已选择 {{ selectedCount }} 个账号</p>
      
      <el-form ref="formRef" :model="form" label-width="100px">
        <el-form-item label="状态">
          <el-select v-model="form.status" placeholder="选择状态" clearable style="width: 100%">
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
            <el-option label="已暂停" value="suspended" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-select
            v-model="form.tags"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入标签"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      
      <div class="update-warning">
        <el-alert
          title="批量更新将覆盖所选账号的相应字段，请谨慎操作"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpdate" :loading="updating">
          更新
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import type { SocialAccount } from '@/types/social'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedCount: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:visible', 'update'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 表单引用
const formRef = ref<FormInstance>()

// 更新状态
const updating = ref(false)

// 表单数据
const form = reactive<Partial<SocialAccount>>({
  status: '',
  tags: [],
  description: ''
})

// 处理更新
const handleUpdate = async () => {
  if (props.selectedCount === 0) {
    ElMessage.warning('请先选择要更新的账号')
    return
  }
  
  // 检查是否有字段被修改
  const updateData: Partial<SocialAccount> = {}
  
  if (form.status) {
    updateData.status = form.status
  }
  
  if (form.tags && form.tags.length > 0) {
    updateData.tags = form.tags
  }
  
  if (form.description) {
    updateData.description = form.description
  }
  
  if (Object.keys(updateData).length === 0) {
    ElMessage.warning('请至少修改一个字段')
    return
  }
  
  updating.value = true
  try {
    // 提交更新
    emit('update', updateData)
    
    // 重置表单
    form.status = ''
    form.tags = []
    form.description = ''
    
  } catch (error) {
    console.error('批量更新失败:', error)
    ElMessage.error('批量更新失败')
  } finally {
    updating.value = false
  }
}
</script>

<style scoped>
.batch-update-content {
  padding: 10px;
}

.selected-count {
  margin-bottom: 20px;
  font-weight: bold;
  color: #409eff;
}

.update-warning {
  margin-top: 20px;
}
</style>
