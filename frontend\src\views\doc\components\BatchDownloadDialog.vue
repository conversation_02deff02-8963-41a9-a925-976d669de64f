<template>
  <el-dialog
    v-model="dialogVisible"
    title="📥 批量下载配置"
    width="600px"
    @close="handleClose"
  >
    <div class="batch-download-config">
      <!-- 选中内容概览 -->
      <div class="selected-overview">
        <h4>📋 选中内容概览</h4>
        <div class="overview-stats">
          <div class="stat-item">
            <span class="stat-label">总数量:</span>
            <span class="stat-value">{{ selectedContent.length }} 个</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">可下载:</span>
            <span class="stat-value">{{ downloadableCount }} 个</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">预估大小:</span>
            <span class="stat-value">{{ formatFileSize(totalSize) }}</span>
          </div>
        </div>
      </div>

      <!-- 下载配置 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="margin-top: 20px"
      >
        <el-form-item label="下载路径" prop="download_path">
          <el-input
            v-model="form.download_path"
            placeholder="请输入下载路径"
            style="width: 100%"
          >
            <template #prepend>📁</template>
            <template #append>
              <el-button @click="selectPath">选择</el-button>
            </template>
          </el-input>
          <div class="form-tip">
            文件将保存到此路径下，按账号和日期自动分类
          </div>
        </el-form-item>

        <el-form-item label="下载质量">
          <el-radio-group v-model="form.quality">
            <el-radio value="high">高质量</el-radio>
            <el-radio value="medium">中等质量</el-radio>
            <el-radio value="low">低质量</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="并发数量">
          <el-input-number
            v-model="form.concurrent_downloads"
            :min="1"
            :max="10"
            style="width: 150px"
          />
          <span style="margin-left: 8px; color: #909399;">个同时下载</span>
          <div class="form-tip">
            建议设置为3-5个，过多可能导致网络拥堵
          </div>
        </el-form-item>

        <el-form-item label="下载选项">
          <el-checkbox v-model="form.skip_existing">跳过已存在文件</el-checkbox>
          <el-checkbox v-model="form.create_metadata">生成元数据文件</el-checkbox>
          <el-checkbox v-model="form.organize_by_date">按日期组织文件夹</el-checkbox>
        </el-form-item>

        <el-form-item label="文件命名">
          <el-select v-model="form.naming_rule" style="width: 200px">
            <el-option label="原始名称" value="original" />
            <el-option label="时间戳" value="timestamp" />
            <el-option label="内容ID" value="content_id" />
            <el-option label="自定义格式" value="custom" />
          </el-select>
          <div class="form-tip">
            选择文件命名规则，避免文件名冲突
          </div>
        </el-form-item>

        <el-form-item label="重试设置">
          <div style="display: flex; align-items: center; gap: 16px;">
            <div>
              <span style="margin-right: 8px;">失败重试:</span>
              <el-input-number
                v-model="form.retry_count"
                :min="0"
                :max="5"
                style="width: 100px"
              />
              <span style="margin-left: 4px;">次</span>
            </div>
            <div>
              <span style="margin-right: 8px;">超时时间:</span>
              <el-input-number
                v-model="form.timeout"
                :min="30"
                :max="300"
                style="width: 100px"
              />
              <span style="margin-left: 4px;">秒</span>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <!-- 预估信息 -->
      <div class="estimate-info">
        <el-alert
          :title="getEstimateText()"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          :disabled="downloadableCount === 0"
        >
          开始下载 ({{ downloadableCount }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
  selectedContent: any[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  download_path: 'H:\\PublishSystem\\Downloads\\',
  quality: 'high',
  concurrent_downloads: 3,
  skip_existing: true,
  create_metadata: true,
  organize_by_date: true,
  naming_rule: 'timestamp',
  retry_count: 2,
  timeout: 60
})

// 表单验证规则
const rules: FormRules = {
  download_path: [
    { required: true, message: '请输入下载路径', trigger: 'blur' }
  ]
}

// 计算属性
const downloadableCount = computed(() => {
  return props.selectedContent.filter(item => item.real_content_url).length
})

const totalSize = computed(() => {
  return props.selectedContent
    .filter(item => item.real_content_url && item.file_size)
    .reduce((total, item) => total + (item.file_size || 0), 0)
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  }
})

// 方法
const resetForm = () => {
  Object.assign(form, {
    download_path: 'H:\\PublishSystem\\Downloads\\',
    quality: 'high',
    concurrent_downloads: 3,
    skip_existing: true,
    create_metadata: true,
    organize_by_date: true,
    naming_rule: 'timestamp',
    retry_count: 2,
    timeout: 60
  })
}

const selectPath = () => {
  // TODO: 实现路径选择功能
  ElMessage.info('路径选择功能开发中...')
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getEstimateText = () => {
  const count = downloadableCount.value
  const concurrent = form.concurrent_downloads
  
  if (count === 0) {
    return '没有可下载的内容'
  }
  
  // 简单估算下载时间（假设每个文件平均2分钟）
  const estimateMinutes = Math.ceil((count * 2) / concurrent)
  const hours = Math.floor(estimateMinutes / 60)
  const minutes = estimateMinutes % 60
  
  let timeText = ''
  if (hours > 0) {
    timeText = `约 ${hours} 小时 ${minutes} 分钟`
  } else {
    timeText = `约 ${minutes} 分钟`
  }
  
  return `预计下载 ${count} 个文件，${timeText}完成`
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 构建下载任务数据
    const downloadTask = {
      content_ids: props.selectedContent
        .filter(item => item.real_content_url)
        .map(item => item.content_id),
      download_config: {
        download_path: form.download_path,
        quality: form.quality,
        concurrent_downloads: form.concurrent_downloads,
        skip_existing: form.skip_existing,
        create_metadata: form.create_metadata,
        organize_by_date: form.organize_by_date,
        naming_rule: form.naming_rule,
        retry_count: form.retry_count,
        timeout: form.timeout
      }
    }

    console.log('提交批量下载任务:', downloadTask)

    // TODO: 调用实际的API
    // await createBatchDownloadTask(downloadTask)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('批量下载任务已创建！')
    emit('success')
    dialogVisible.value = false

  } catch (error: any) {
    console.error('创建批量下载任务失败:', error)
    ElMessage.error(error.message || '创建批量下载任务失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.batch-download-config {
  max-height: 70vh;
  overflow-y: auto;
}

.selected-overview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #409eff;
}

.selected-overview h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.overview-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-value {
  color: #409eff;
  font-weight: 600;
  font-size: 14px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.estimate-info {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
