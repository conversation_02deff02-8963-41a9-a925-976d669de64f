# 账号导入功能实现

## 1. 导入功能概述

账号导入功能允许用户从文本文件（如`模拟.txt`）中批量导入账号信息，支持多种平台的账号格式解析，并将解析后的数据保存到数据库中。

## 2. 后端实现

### 2.1 API端点

在`app/api/v1/social_accounts.py`中添加导入账号的API端点：

```python
# 导入账号
@router.post("/accounts/import", response_model=Dict[str, Any])
async def import_social_accounts(
    import_data: Dict[str, Any],
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    从文本导入社交媒体账号
    
    - **text_content**: 文本内容
    - **platform_mapping**: 平台映射，如 {"GG": "youtube", "FB": "facebook"}
    - **core_service_id**: Core服务ID
    """
    try:
        text_content = import_data.get("text_content", "")
        platform_mapping = import_data.get("platform_mapping", {
            "GG": "youtube",
            "FB": "facebook"
        })
        core_service_id = import_data.get("core_service_id", "default")
        
        if not text_content:
            raise HTTPException(
                status_code=400,
                detail="文本内容不能为空"
            )
            
        result = db_service.import_accounts_from_text(
            text_content=text_content,
            platform_mapping=platform_mapping,
            core_service_id=core_service_id
        )
        
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"导入账号失败: {str(e)}"
        )
```

### 2.2 导入逻辑实现

在`SocialDatabaseService`类中实现账号导入方法：

```python
def import_accounts_from_text(self, text_content, platform_mapping=None, core_service_id="default"):
    """从文本导入账号"""
    if platform_mapping is None:
        platform_mapping = {
            "GG": "youtube",
            "FB": "facebook"
        }
        
    lines = text_content.strip().split("\n")
    imported_count = 0
    errors = []
    
    current_display_name = ""
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 检查是否是账号标识行
        if not any(p + "：" in line or p + ":" in line for p in platform_mapping.keys()):
            # 可能是显示名称行
            current_display_name = line
            continue
            
        try:
            # 解析平台和账号信息
            platform_code = None
            for code in platform_mapping.keys():
                if code + "：" in line or code + ":" in line:
                    platform_code = code
                    break
                    
            if not platform_code:
                continue
                
            platform_id = platform_mapping[platform_code]
            
            # 分割账号信息
            account_part = line.split(platform_code + "：")[-1].split(platform_code + ":")[-1]
            
            # 处理不同的分隔符
            if "----" in account_part:
                parts = account_part.split("----")
            elif "|" in account_part:
                parts = account_part.split("|")
            else:
                parts = [account_part]
            
            username = parts[0].strip()
            
            account_data = {
                "username": username,
                "platform_id": platform_id,
                "core_service_id": core_service_id,
                "status": "active",
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            }
            
            # 添加密码
            if len(parts) > 1:
                account_data["password"] = parts[1].strip()
                
            # 添加恢复邮箱
            if len(parts) > 2:
                account_data["recovery_email"] = parts[2].strip()
                
            # 添加恢复码
            if len(parts) > 3:
                account_data["recovery_code"] = parts[3].strip()
                
            # 添加备注/描述
            if len(parts) > 4:
                account_data["description"] = parts[4].strip()
                
            # 添加显示名称
            if current_display_name:
                account_data["display_name"] = current_display_name
                
            # 检查账号是否已存在
            existing = self.db.social_accounts.find_one({
                "username": username,
                "platform_id": platform_id
            })
            
            if existing:
                # 更新现有账号
                self.db.social_accounts.update_one(
                    {"_id": existing["_id"]},
                    {"$set": account_data}
                )
            else:
                # 创建新账号
                self.db.social_accounts.insert_one(account_data)
                
            imported_count += 1
            
        except Exception as e:
            errors.append(f"导入行 '{line}' 失败: {str(e)}")
            
    return {
        "imported_count": imported_count,
        "errors": errors
    }
```

## 3. 前端实现

### 3.1 导入对话框组件

创建`frontend/src/views/social/components/ImportAccountsDialog.vue`组件：

```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入账号"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="import-dialog-content">
      <!-- 导入说明 -->
      <div class="import-instructions">
        <h3>导入说明</h3>
        <p>支持从文本文件导入账号，格式如下：</p>
        <ul>
          <li>每行一个账号，格式为：<code>平台代码：用户名----密码----恢复邮箱----恢复码----备注</code></li>
          <li>支持的平台代码：GG (YouTube)、FB (Facebook) 等</li>
          <li>账号前的行将被视为显示名称</li>
        </ul>
        <p>示例：</p>
        <pre>
A-HK-0-1-00
GG：<EMAIL>----Longer889614$----<EMAIL>----Kkoazf465k----mn
FB：<EMAIL>----tirtho----AKAGWLFEE5PBULZF5KWNZ746G5OF4QVI----**************</pre>
      </div>

      <!-- 导入选项 -->
      <div class="import-options">
        <h3>导入选项</h3>
        <el-form :model="importOptions" label-width="120px">
          <el-form-item label="Core服务">
            <el-select v-model="importOptions.coreServiceId" placeholder="选择Core服务">
              <el-option
                v-for="service in coreServices"
                :key="service.id"
                :label="service.name"
                :value="service.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="平台映射">
            <div v-for="(platform, code) in importOptions.platformMapping" :key="code" class="platform-mapping-item">
              <span class="platform-code">{{ code }}</span>
              <el-select v-model="importOptions.platformMapping[code]" placeholder="选择平台">
                <el-option
                  v-for="platform in platforms"
                  :key="platform.id"
                  :label="platform.name"
                  :value="platform.id"
                />
              </el-select>
              <el-button type="danger" icon="Delete" circle @click="removePlatformMapping(code)" />
            </div>
            <div class="add-mapping">
              <el-input v-model="newMapping.code" placeholder="平台代码" style="width: 100px" />
              <el-select v-model="newMapping.platformId" placeholder="选择平台">
                <el-option
                  v-for="platform in platforms"
                  :key="platform.id"
                  :label="platform.name"
                  :value="platform.id"
                />
              </el-select>
              <el-button type="primary" icon="Plus" circle @click="addPlatformMapping" />
            </div>
          </el-form-item>
          
          <el-form-item label="冲突处理">
            <el-radio-group v-model="importOptions.conflictStrategy">
              <el-radio label="update">更新现有账号</el-radio>
              <el-radio label="skip">跳过已存在账号</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 文本输入区域 -->
      <div class="import-text-area">
        <h3>账号数据</h3>
        <el-input
          v-model="importText"
          type="textarea"
          :rows="10"
          placeholder="请粘贴账号数据或拖拽文件到此处"
          @drop.prevent="handleFileDrop"
          @dragover.prevent
        />
        <div class="file-upload">
          <el-upload
            ref="uploadRef"
            action=""
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
          >
            <el-button type="primary">选择文件</el-button>
          </el-upload>
        </div>
      </div>

      <!-- 预览区域 -->
      <div v-if="previewData.length > 0" class="import-preview">
        <h3>导入预览 ({{ previewData.length }} 个账号)</h3>
        <el-table :data="previewData.slice(0, 5)" style="width: 100%" border>
          <el-table-column prop="platform_id" label="平台" width="100" />
          <el-table-column prop="username" label="用户名" min-width="150" />
          <el-table-column prop="display_name" label="显示名称" min-width="120" />
          <el-table-column prop="description" label="备注" min-width="120" />
        </el-table>
        <div v-if="previewData.length > 5" class="more-preview">
          还有 {{ previewData.length - 5 }} 个账号未显示...
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePreview" :disabled="!importText">
          预览
        </el-button>
        <el-button
          type="success"
          @click="handleImport"
          :disabled="!importText || previewData.length === 0"
          :loading="importing"
        >
          导入
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { importAccounts } from '@/api/social'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  platforms: {
    type: Array,
    default: () => []
  },
  coreServices: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'import'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 导入文本
const importText = ref('')
const previewData = ref<any[]>([])
const importing = ref(false)

// 导入选项
const importOptions = ref({
  coreServiceId: '',
  platformMapping: {
    'GG': 'youtube',
    'FB': 'facebook'
  },
  conflictStrategy: 'update'
})

// 新增平台映射
const newMapping = ref({
  code: '',
  platformId: ''
})

// 初始化Core服务ID
watch(() => props.coreServices, (services) => {
  if (services.length > 0 && !importOptions.value.coreServiceId) {
    importOptions.value.coreServiceId = services[0].id
  }
}, { immediate: true })

// 添加平台映射
const addPlatformMapping = () => {
  if (!newMapping.value.code || !newMapping.value.platformId) {
    ElMessage.warning('请输入平台代码和选择平台')
    return
  }
  
  importOptions.value.platformMapping[newMapping.value.code] = newMapping.value.platformId
  newMapping.value.code = ''
  newMapping.value.platformId = ''
}

// 移除平台映射
const removePlatformMapping = (code: string) => {
  delete importOptions.value.platformMapping[code]
}

// 处理文件拖放
const handleFileDrop = (event: DragEvent) => {
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    readFile(files[0])
  }
}

// 处理文件选择
const handleFileChange = (file: any) => {
  readFile(file.raw)
}

// 读取文件内容
const readFile = (file: File) => {
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    importText.value = e.target?.result as string || ''
  }
  reader.readAsText(file)
}

// 解析文本内容
const parseImportText = () => {
  if (!importText.value) return []
  
  const lines = importText.value.split('\n')
  const accounts = []
  let currentDisplayName = ''
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue
    
    // 检查是否是账号标识行
    const isPlatformLine = Object.keys(importOptions.value.platformMapping).some(
      code => line.includes(code + '：') || line.includes(code + ':')
    )
    
    if (!isPlatformLine) {
      // 可能是显示名称行
      currentDisplayName = line
      continue
    }
    
    // 解析平台和账号信息
    let platformCode = ''
    for (const code of Object.keys(importOptions.value.platformMapping)) {
      if (line.includes(code + '：') || line.includes(code + ':')) {
        platformCode = code
        break
      }
    }
    
    if (!platformCode) continue
    
    const platformId = importOptions.value.platformMapping[platformCode]
    
    // 分割账号信息
    let accountPart = ''
    if (line.includes(platformCode + '：')) {
      accountPart = line.split(platformCode + '：')[1]
    } else {
      accountPart = line.split(platformCode + ':')[1]
    }
    
    // 处理不同的分隔符
    let parts = []
    if (accountPart.includes('----')) {
      parts = accountPart.split('----')
    } else if (accountPart.includes('|')) {
      parts = accountPart.split('|')
    } else {
      parts = [accountPart]
    }
    
    const username = parts[0].trim()
    
    const account = {
      username,
      platform_id: platformId,
      core_service_id: importOptions.value.coreServiceId,
      display_name: currentDisplayName,
      status: 'active'
    }
    
    // 添加密码
    if (parts.length > 1) {
      account.password = parts[1].trim()
    }
    
    // 添加恢复邮箱
    if (parts.length > 2) {
      account.recovery_email = parts[2].trim()
    }
    
    // 添加恢复码
    if (parts.length > 3) {
      account.recovery_code = parts[3].trim()
    }
    
    // 添加备注/描述
    if (parts.length > 4) {
      account.description = parts[4].trim()
    }
    
    accounts.push(account)
  }
  
  return accounts
}

// 预览导入数据
const handlePreview = () => {
  previewData.value = parseImportText()
  if (previewData.value.length === 0) {
    ElMessage.warning('未找到有效的账号数据')
  } else {
    ElMessage.success(`找到 ${previewData.value.length} 个账号`)
  }
}

// 执行导入
const handleImport = async () => {
  if (previewData.value.length === 0) {
    handlePreview()
    if (previewData.value.length === 0) return
  }
  
  importing.value = true
  try {
    const result = await importAccounts(
      importText.value,
      importOptions.value.platformMapping,
      importOptions.value.coreServiceId
    )
    
    ElMessage.success(`成功导入 ${result.imported_count} 个账号`)
    
    if (result.errors && result.errors.length > 0) {
      console.error('导入错误:', result.errors)
      ElMessage.warning(`导入过程中有 ${result.errors.length} 个错误，请查看控制台`)
    }
    
    emit('import', result)
    dialogVisible.value = false
  } catch (error) {
    console.error('导入账号失败:', error)
    ElMessage.error('导入账号失败')
  } finally {
    importing.value = false
  }
}
</script>

<style scoped>
.import-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.import-instructions {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
}

.import-instructions pre {
  background-color: #eee;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.platform-mapping-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.platform-code {
  width: 50px;
  font-weight: bold;
}

.add-mapping {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.file-upload {
  margin-top: 10px;
}

.more-preview {
  margin-top: 10px;
  color: #666;
  font-style: italic;
}
</style>
```

### 3.2 API服务

在`frontend/src/api/social.ts`中添加导入账号的API方法：

```typescript
// 导入账号
export const importAccounts = (
  textContent: string,
  platformMapping?: Record<string, string>,
  coreServiceId?: string
) => {
  return request<{ imported_count: number; errors: string[] }>({
    url: '/api/v1/social/accounts/import',
    method: 'post',
    data: {
      text_content: textContent,
      platform_mapping: platformMapping,
      core_service_id: coreServiceId
    }
  })
}
```

## 4. 导入功能处理流程

1. **用户操作流程**
   - 用户点击"导入账号"按钮
   - 在导入对话框中粘贴文本或上传文件
   - 配置导入选项（Core服务、平台映射等）
   - 点击"预览"查看解析结果
   - 点击"导入"执行导入操作

2. **文本解析流程**
   - 按行读取文本内容
   - 识别平台标识行（如"GG："开头的行）
   - 解析账号信息（用户名、密码、恢复邮箱等）
   - 使用非平台标识行作为显示名称

3. **数据导入流程**
   - 检查账号是否已存在
   - 根据冲突策略决定更新或跳过
   - 将账号数据保存到数据库
   - 返回导入结果统计

## 5. 特殊格式处理

### 5.1 模拟.txt格式解析

`模拟.txt`文件包含多种格式的账号信息，需要特殊处理：

1. **平台标识**
   - GG：YouTube账号
   - FB：Facebook账号
   - AWS：Amazon Web Services账号

2. **分隔符处理**
   - 主要使用"----"作为字段分隔符
   - 部分使用"|"作为分隔符
   - 需要兼容处理不同的分隔符

3. **显示名称处理**
   - 账号前的行通常是显示名称或分组标识
   - 如"A-HK-0-1-00"表示一个分组或设备标识
   - 将这些信息作为账号的显示名称

4. **备注处理**
   - 部分账号末尾包含备注信息
   - 如"---观察团"表示账号的用途或特性
   - 将这些信息保存为账号的描述字段
