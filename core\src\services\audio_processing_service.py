"""
音频处理服务
提供批量音频处理功能，包括字幕生成、音频分离、人声提取和音频替换
"""

import os
import asyncio
import logging
import time
import json
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import subprocess

logger = logging.getLogger(__name__)

class AudioProcessingService:
    """音频处理服务类"""

    def __init__(self):
        self.supported_audio_extensions = ['mp3', 'wav', 'aac', 'flac', 'm4a', 'ogg']
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
        
    def is_supported_file(self, file_path: str) -> bool:
        """检查文件是否支持音频处理"""
        extension = Path(file_path).suffix.lower().lstrip('.')
        return extension in self.supported_audio_extensions or extension in self.supported_video_extensions

    async def generate_subtitles_batch(self, folder_path: str, output_format: str = 'srt',
                                     language: str = 'auto', model_size: str = 'base',
                                     max_concurrent: int = 2) -> Dict[str, Any]:
        """批量生成字幕文件
        
        Args:
            folder_path: 输入文件夹路径
            output_format: 输出格式 (srt, vtt, txt)
            language: 语言代码 (auto, zh, en, etc.)
            model_size: Whisper模型大小 (tiny, base, small, medium, large)
            max_concurrent: 最大并发处理数
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始批量生成字幕: {folder_path}")
            
            # 扫描支持的文件
            files = self._scan_media_files(folder_path)
            if not files:
                return {"success": False, "error": "未找到支持的媒体文件", "processed_files": 0}
            
            logger.info(f"找到 {len(files)} 个媒体文件")
            
            # 创建输出目录
            output_dir = os.path.join(folder_path, "subtitles")
            os.makedirs(output_dir, exist_ok=True)
            
            # 批量处理
            results = []
            successful_count = 0
            failed_count = 0
            
            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_single_file(file_path: str):
                async with semaphore:
                    return await self._generate_single_subtitle(
                        file_path, output_dir, output_format, language, model_size
                    )
            
            # 并发处理所有文件
            tasks = [process_single_file(file_path) for file_path in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"处理文件时发生异常: {str(result)}")
                elif result.get('success', False):
                    successful_count += 1
                else:
                    failed_count += 1
            
            return {
                "success": successful_count > 0,
                "processed_files": len(files),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "output_directory": output_dir,
                "results": [r for r in results if not isinstance(r, Exception)]
            }
            
        except Exception as e:
            logger.error(f"批量生成字幕失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e), "processed_files": 0}

    async def _generate_single_subtitle(self, file_path: str, output_dir: str,
                                      output_format: str, language: str, model_size: str) -> Dict[str, Any]:
        """生成单个文件的字幕"""
        start_time = time.time()
        file_name = Path(file_path).stem
        
        try:
            logger.info(f"开始生成字幕: {file_name}")
            
            # 构建输出文件路径
            output_file = os.path.join(output_dir, f"{file_name}.{output_format}")
            
            # 检查是否已存在
            if os.path.exists(output_file):
                logger.info(f"字幕文件已存在，跳过: {output_file}")
                return {
                    "success": True,
                    "input_file": file_path,
                    "output_file": output_file,
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "message": "字幕文件已存在"
                }
            
            # 使用whisper生成字幕
            cmd = ['whisper', file_path, '--output_dir', output_dir, '--output_format', output_format]
            
            if language != 'auto':
                cmd.extend(['--language', language])
            
            cmd.extend(['--model', model_size])
            cmd.extend(['--verbose', 'False'])
            
            logger.debug(f"执行whisper命令: {' '.join(cmd[:5])}...")
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                processing_time = int((time.time() - start_time) * 1000)
                logger.info(f"字幕生成成功: {file_name} ({processing_time}ms)")
                
                return {
                    "success": True,
                    "input_file": file_path,
                    "output_file": output_file,
                    "processing_time_ms": processing_time,
                    "message": "字幕生成成功"
                }
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"字幕生成失败 {file_name}: {error_msg}")
                
                return {
                    "success": False,
                    "input_file": file_path,
                    "output_file": "",
                    "error_message": error_msg,
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"生成字幕异常 {file_name}: {error_msg}")
            
            return {
                "success": False,
                "input_file": file_path,
                "output_file": "",
                "error_message": error_msg,
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }

    async def extract_audio_batch(self, folder_path: str, output_format: str = 'wav',
                                quality: str = 'high', max_concurrent: int = 3) -> Dict[str, Any]:
        """批量分离音频
        
        Args:
            folder_path: 输入文件夹路径
            output_format: 输出音频格式 (wav, mp3, aac)
            quality: 音频质量 (high, medium, low)
            max_concurrent: 最大并发处理数
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始批量分离音频: {folder_path}")
            
            # 扫描视频文件
            files = self._scan_video_files(folder_path)
            if not files:
                return {"success": False, "error": "未找到支持的视频文件", "processed_files": 0}
            
            logger.info(f"找到 {len(files)} 个视频文件")
            
            # 创建输出目录
            output_dir = os.path.join(folder_path, "extracted_audio")
            os.makedirs(output_dir, exist_ok=True)
            
            # 批量处理
            results = []
            successful_count = 0
            failed_count = 0
            
            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_single_file(file_path: str):
                async with semaphore:
                    return await self._extract_single_audio(
                        file_path, output_dir, output_format, quality
                    )
            
            # 并发处理所有文件
            tasks = [process_single_file(file_path) for file_path in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"处理文件时发生异常: {str(result)}")
                elif result.get('success', False):
                    successful_count += 1
                else:
                    failed_count += 1
            
            return {
                "success": successful_count > 0,
                "processed_files": len(files),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "output_directory": output_dir,
                "results": [r for r in results if not isinstance(r, Exception)]
            }
            
        except Exception as e:
            logger.error(f"批量分离音频失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e), "processed_files": 0}

    def _scan_media_files(self, folder_path: str) -> List[str]:
        """扫描媒体文件"""
        files = []
        try:
            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)
                if os.path.isfile(file_path) and self.is_supported_file(file_path):
                    files.append(file_path)
            return sorted(files)
        except Exception as e:
            logger.error(f"扫描媒体文件失败: {str(e)}")
            return []

    def _scan_video_files(self, folder_path: str) -> List[str]:
        """扫描视频文件"""
        files = []
        try:
            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)
                if os.path.isfile(file_path):
                    extension = Path(file_path).suffix.lower().lstrip('.')
                    if extension in self.supported_video_extensions:
                        files.append(file_path)
            return sorted(files)
        except Exception as e:
            logger.error(f"扫描视频文件失败: {str(e)}")
            return []

    async def _extract_single_audio(self, file_path: str, output_dir: str,
                                  output_format: str, quality: str) -> Dict[str, Any]:
        """分离单个文件的音频"""
        start_time = time.time()
        file_name = Path(file_path).stem

        try:
            logger.info(f"开始分离音频: {file_name}")

            # 构建输出文件路径
            output_file = os.path.join(output_dir, f"{file_name}.{output_format}")

            # 检查是否已存在
            if os.path.exists(output_file):
                logger.info(f"音频文件已存在，跳过: {output_file}")
                return {
                    "success": True,
                    "input_file": file_path,
                    "output_file": output_file,
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "message": "音频文件已存在"
                }

            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y', '-i', file_path]

            # 设置音频质量参数
            if output_format == 'mp3':
                if quality == 'high':
                    cmd.extend(['-b:a', '320k'])
                elif quality == 'medium':
                    cmd.extend(['-b:a', '192k'])
                else:  # low
                    cmd.extend(['-b:a', '128k'])
            elif output_format == 'wav':
                if quality == 'high':
                    cmd.extend(['-ar', '48000', '-ac', '2'])
                elif quality == 'medium':
                    cmd.extend(['-ar', '44100', '-ac', '2'])
                else:  # low
                    cmd.extend(['-ar', '22050', '-ac', '1'])
            elif output_format == 'aac':
                if quality == 'high':
                    cmd.extend(['-b:a', '256k'])
                elif quality == 'medium':
                    cmd.extend(['-b:a', '128k'])
                else:  # low
                    cmd.extend(['-b:a', '96k'])

            # 只提取音频流
            cmd.extend(['-vn', output_file])

            logger.debug(f"执行ffmpeg命令: {' '.join(cmd[:5])}...")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                processing_time = int((time.time() - start_time) * 1000)

                # 获取文件大小
                input_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                output_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0

                logger.info(f"音频分离成功: {file_name} ({processing_time}ms)")

                return {
                    "success": True,
                    "input_file": file_path,
                    "output_file": output_file,
                    "processing_time_ms": processing_time,
                    "input_file_size": input_size,
                    "output_file_size": output_size,
                    "message": "音频分离成功"
                }
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"音频分离失败 {file_name}: {error_msg}")

                return {
                    "success": False,
                    "input_file": file_path,
                    "output_file": "",
                    "error_message": error_msg,
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"分离音频异常 {file_name}: {error_msg}")

            return {
                "success": False,
                "input_file": file_path,
                "output_file": "",
                "error_message": error_msg,
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }

    async def separate_vocals_batch(self, folder_path: str, separation_method: str = 'ffmpeg',
                                  output_quality: str = 'high', max_concurrent: int = 2) -> Dict[str, Any]:
        """批量人声分离（去除背景音乐，保留人声）

        Args:
            folder_path: 输入文件夹路径
            separation_method: 分离方法 (ffmpeg, librosa)
            output_quality: 输出质量 (high, medium, low)
            max_concurrent: 最大并发处理数

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始批量人声分离: {folder_path}")

            # 扫描音频文件
            files = self._scan_audio_files(folder_path)
            if not files:
                return {"success": False, "error": "未找到支持的音频文件", "processed_files": 0}

            logger.info(f"找到 {len(files)} 个音频文件")

            # 创建输出目录
            output_dir = os.path.join(folder_path, "vocals_separated")
            os.makedirs(output_dir, exist_ok=True)

            # 批量处理
            results = []
            successful_count = 0
            failed_count = 0

            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_single_file(file_path: str):
                async with semaphore:
                    return await self._separate_single_vocal(
                        file_path, output_dir, separation_method, output_quality
                    )

            # 并发处理所有文件
            tasks = [process_single_file(file_path) for file_path in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"处理文件时发生异常: {str(result)}")
                elif result.get('success', False):
                    successful_count += 1
                else:
                    failed_count += 1

            return {
                "success": successful_count > 0,
                "processed_files": len(files),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "output_directory": output_dir,
                "results": [r for r in results if not isinstance(r, Exception)]
            }

        except Exception as e:
            logger.error(f"批量人声分离失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e), "processed_files": 0}

    def _scan_audio_files(self, folder_path: str) -> List[str]:
        """扫描音频文件"""
        files = []
        try:
            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)
                if os.path.isfile(file_path):
                    extension = Path(file_path).suffix.lower().lstrip('.')
                    if extension in self.supported_audio_extensions:
                        files.append(file_path)
            return sorted(files)
        except Exception as e:
            logger.error(f"扫描音频文件失败: {str(e)}")
            return []

    async def _separate_single_vocal(self, file_path: str, output_dir: str,
                                   separation_method: str, output_quality: str) -> Dict[str, Any]:
        """分离单个文件的人声"""
        start_time = time.time()
        file_name = Path(file_path).stem

        try:
            logger.info(f"开始人声分离: {file_name}")

            # 构建输出文件路径
            vocals_file = os.path.join(output_dir, f"{file_name}_vocals.wav")
            instrumental_file = os.path.join(output_dir, f"{file_name}_instrumental.wav")

            # 检查是否已存在
            if os.path.exists(vocals_file):
                logger.info(f"人声文件已存在，跳过: {vocals_file}")
                return {
                    "success": True,
                    "input_file": file_path,
                    "vocals_file": vocals_file,
                    "instrumental_file": instrumental_file,
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "message": "人声文件已存在"
                }

            if separation_method == 'ffmpeg':
                # 使用ffmpeg进行简单的人声分离（中央声道提取）
                success = await self._separate_vocals_ffmpeg(
                    file_path, vocals_file, instrumental_file, output_quality
                )
            else:
                # 使用librosa进行更高质量的人声分离
                success = await self._separate_vocals_librosa(
                    file_path, vocals_file, instrumental_file, output_quality
                )

            if success:
                processing_time = int((time.time() - start_time) * 1000)

                # 获取文件大小
                input_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                vocals_size = os.path.getsize(vocals_file) if os.path.exists(vocals_file) else 0
                instrumental_size = os.path.getsize(instrumental_file) if os.path.exists(instrumental_file) else 0

                logger.info(f"人声分离成功: {file_name} ({processing_time}ms)")

                return {
                    "success": True,
                    "input_file": file_path,
                    "vocals_file": vocals_file,
                    "instrumental_file": instrumental_file,
                    "processing_time_ms": processing_time,
                    "input_file_size": input_size,
                    "vocals_file_size": vocals_size,
                    "instrumental_file_size": instrumental_size,
                    "message": "人声分离成功"
                }
            else:
                return {
                    "success": False,
                    "input_file": file_path,
                    "vocals_file": "",
                    "instrumental_file": "",
                    "error_message": "人声分离失败",
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"人声分离异常 {file_name}: {error_msg}")

            return {
                "success": False,
                "input_file": file_path,
                "vocals_file": "",
                "instrumental_file": "",
                "error_message": error_msg,
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }

    async def _separate_vocals_ffmpeg(self, input_file: str, vocals_file: str,
                                    instrumental_file: str, output_quality: str) -> bool:
        """使用ffmpeg进行人声分离"""
        try:
            # 人声提取（中央声道）
            vocals_cmd = [
                'ffmpeg', '-y', '-i', input_file,
                '-af', 'pan=mono|c0=0.5*c0+-0.5*c1',
                vocals_file
            ]

            # 伴奏提取（左右声道差值）
            instrumental_cmd = [
                'ffmpeg', '-y', '-i', input_file,
                '-af', 'pan=mono|c0=0.5*c0+0.5*c1',
                instrumental_file
            ]

            # 执行人声提取
            process = await asyncio.create_subprocess_exec(
                *vocals_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            _, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"人声提取失败: {stderr.decode('utf-8', errors='ignore')}")
                return False

            # 执行伴奏提取
            process = await asyncio.create_subprocess_exec(
                *instrumental_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            _, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"伴奏提取失败: {stderr.decode('utf-8', errors='ignore')}")
                return False

            return True

        except Exception as e:
            logger.error(f"ffmpeg人声分离异常: {str(e)}")
            return False

    async def _separate_vocals_librosa(self, input_file: str, vocals_file: str,
                                     instrumental_file: str, output_quality: str) -> bool:
        """使用librosa进行高质量人声分离"""
        try:
            # 这里可以实现更高质量的人声分离算法
            # 由于librosa的复杂性，暂时使用ffmpeg的方法
            return await self._separate_vocals_ffmpeg(input_file, vocals_file, instrumental_file, output_quality)

        except Exception as e:
            logger.error(f"librosa人声分离异常: {str(e)}")
            return False

    async def replace_audio_batch(self, folder_path: str, new_audio_path: str,
                                audio_volume: float = 1.0, fade_duration: float = 0.5,
                                max_concurrent: int = 3) -> Dict[str, Any]:
        """批量替换音频

        Args:
            folder_path: 输入视频文件夹路径
            new_audio_path: 新音频文件路径
            audio_volume: 音频音量 (0.0-2.0)
            fade_duration: 淡入淡出时长（秒）
            max_concurrent: 最大并发处理数

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始批量替换音频: {folder_path}")

            # 检查新音频文件是否存在
            if not os.path.exists(new_audio_path):
                return {"success": False, "error": "新音频文件不存在", "processed_files": 0}

            # 扫描视频文件
            files = self._scan_video_files(folder_path)
            if not files:
                return {"success": False, "error": "未找到支持的视频文件", "processed_files": 0}

            logger.info(f"找到 {len(files)} 个视频文件")

            # 创建输出目录
            output_dir = os.path.join(folder_path, "audio_replaced")
            os.makedirs(output_dir, exist_ok=True)

            # 批量处理
            results = []
            successful_count = 0
            failed_count = 0

            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_single_file(file_path: str):
                async with semaphore:
                    return await self._replace_single_audio(
                        file_path, new_audio_path, output_dir, audio_volume, fade_duration
                    )

            # 并发处理所有文件
            tasks = [process_single_file(file_path) for file_path in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"处理文件时发生异常: {str(result)}")
                elif result.get('success', False):
                    successful_count += 1
                else:
                    failed_count += 1

            return {
                "success": successful_count > 0,
                "processed_files": len(files),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "output_directory": output_dir,
                "new_audio_file": new_audio_path,
                "results": [r for r in results if not isinstance(r, Exception)]
            }

        except Exception as e:
            logger.error(f"批量替换音频失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e), "processed_files": 0}

    async def _replace_single_audio(self, video_path: str, new_audio_path: str, output_dir: str,
                                  audio_volume: float, fade_duration: float) -> Dict[str, Any]:
        """替换单个视频的音频"""
        start_time = time.time()
        file_name = Path(video_path).stem

        try:
            logger.info(f"开始替换音频: {file_name}")

            # 构建输出文件路径
            output_file = os.path.join(output_dir, f"{file_name}_new_audio.mp4")

            # 检查是否已存在
            if os.path.exists(output_file):
                logger.info(f"音频替换文件已存在，跳过: {output_file}")
                return {
                    "success": True,
                    "input_file": video_path,
                    "output_file": output_file,
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "message": "音频替换文件已存在"
                }

            # 构建ffmpeg命令
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,  # 输入视频
                '-i', new_audio_path,  # 输入新音频
                '-c:v', 'copy',  # 视频流直接复制
                '-c:a', 'aac',  # 音频编码为AAC
                '-b:a', '128k',  # 音频比特率
                '-map', '0:v:0',  # 映射第一个输入的视频流
                '-map', '1:a:0',  # 映射第二个输入的音频流
                '-shortest',  # 以最短的流为准
            ]

            # 添加音量调节
            if audio_volume != 1.0:
                cmd.extend(['-af', f'volume={audio_volume}'])

            # 添加淡入淡出效果
            if fade_duration > 0:
                if audio_volume != 1.0:
                    cmd[-1] += f',afade=t=in:ss=0:d={fade_duration},afade=t=out:st=-{fade_duration}:d={fade_duration}'
                else:
                    cmd.extend(['-af', f'afade=t=in:ss=0:d={fade_duration},afade=t=out:st=-{fade_duration}:d={fade_duration}'])

            cmd.append(output_file)

            logger.debug(f"执行ffmpeg命令: {' '.join(cmd[:8])}...")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            _, stderr = await process.communicate()

            if process.returncode == 0:
                processing_time = int((time.time() - start_time) * 1000)

                # 获取文件大小
                input_size = os.path.getsize(video_path) if os.path.exists(video_path) else 0
                output_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0

                logger.info(f"音频替换成功: {file_name} ({processing_time}ms)")

                return {
                    "success": True,
                    "input_file": video_path,
                    "output_file": output_file,
                    "processing_time_ms": processing_time,
                    "input_file_size": input_size,
                    "output_file_size": output_size,
                    "message": "音频替换成功"
                }
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"音频替换失败 {file_name}: {error_msg}")

                return {
                    "success": False,
                    "input_file": video_path,
                    "output_file": "",
                    "error_message": error_msg,
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"替换音频异常 {file_name}: {error_msg}")

            return {
                "success": False,
                "input_file": video_path,
                "output_file": "",
                "error_message": error_msg,
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }
