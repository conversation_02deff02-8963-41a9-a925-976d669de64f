# 账号管理前端实现

## 1. 类型定义

在`frontend/src/types/social.ts`中定义以下类型：

```typescript
// 社交媒体平台
export interface SocialPlatform {
  id: string
  name: string
  icon: string
  website?: string
  status?: 'active' | 'inactive' | 'maintenance'
  features?: string[]
}

// 平台应用
export interface PlatformApp {
  id: string
  platform_id: string
  name: string
  type: 'android' | 'ios' | 'web' | 'desktop'
  package_name?: string
  main_activity?: string
  version?: string
  status?: 'active' | 'inactive' | 'deprecated'
}

// 社交媒体账号
export interface SocialAccount {
  id: string
  username: string
  password?: string
  recovery_email?: string
  recovery_code?: string
  display_name?: string
  platform_id: string
  platform_name?: string
  core_service_id: string
  status?: 'active' | 'inactive' | 'suspended'
  avatar?: string
  description?: string
  followers?: number
  following?: number
  posts_count?: number
  last_login?: string
  created_at?: string
  updated_at?: string
  tags?: string[]
  device_id?: string
  device_name?: string
}

// 设备账号映射
export interface DeviceAccountMapping {
  id: string
  device_id: string
  account_id: string
  platform_id: string
  app_id: string
  status?: 'active' | 'inactive'
  created_at?: string
  updated_at?: string
  last_used?: string
}
```

## 2. 账号管理组件

### 2.1 账号管理页面

在`frontend/src/views/social/AccountManagement.vue`中实现账号管理页面：

```vue
<template>
  <div class="account-management">
    <h1 class="page-title">账号管理</h1>
    
    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-form :inline="true" class="filter-form">
        <el-form-item label="平台">
          <el-select v-model="filters.platformId" placeholder="选择平台" clearable>
            <el-option
              v-for="platform in platforms"
              :key="platform.id"
              :label="platform.name"
              :value="platform.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="Core服务">
          <el-select v-model="filters.coreServiceId" placeholder="选择Core服务" clearable>
            <el-option
              v-for="service in coreServices"
              :key="service.id"
              :label="service.name"
              :value="service.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
            <el-option label="已暂停" value="suspended" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-input
            v-model="filters.keyword"
            placeholder="搜索账号"
            clearable
            @clear="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 操作区域 -->
    <div class="action-section">
      <el-button type="primary" @click="showAddAccountDialog">添加账号</el-button>
      <el-button type="success" @click="showImportDialog">导入账号</el-button>
      <el-button
        type="danger"
        :disabled="selectedAccountIds.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
      <el-button
        type="warning"
        :disabled="selectedAccountIds.length === 0"
        @click="showBatchUpdateDialog"
      >
        批量更新
      </el-button>
    </div>
    
    <!-- 账号列表 -->
    <el-table
      v-loading="loading"
      :data="accounts"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="username" label="用户名" min-width="150" />
      
      <el-table-column prop="display_name" label="显示名称" min-width="120" />
      
      <el-table-column label="平台" min-width="100">
        <template #default="scope">
          <div class="platform-cell">
            <img
              v-if="getPlatformIcon(scope.row.platform_id)"
              :src="getPlatformIcon(scope.row.platform_id)"
              class="platform-icon"
              alt="平台图标"
            />
            <span>{{ getPlatformName(scope.row.platform_id) }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="关联设备" min-width="120">
        <template #default="scope">
          <span v-if="scope.row.device_name">{{ scope.row.device_name }}</span>
          <el-button v-else size="small" @click="showLinkDeviceDialog(scope.row)">
            关联设备
          </el-button>
        </template>
      </el-table-column>
      
      <el-table-column label="Core服务" min-width="120">
        <template #default="scope">
          {{ getCoreServiceName(scope.row.core_service_id) }}
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" min-width="160">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 添加/编辑账号对话框 -->
    <account-form-dialog
      v-model:visible="accountFormDialog.visible"
      :title="accountFormDialog.title"
      :form-data="accountFormDialog.formData"
      :platforms="platforms"
      :core-services="coreServices"
      @submit="handleAccountFormSubmit"
    />
    
    <!-- 导入账号对话框 -->
    <import-accounts-dialog
      v-model:visible="importDialog.visible"
      :platforms="platforms"
      :core-services="coreServices"
      @import="handleImportAccounts"
    />
    
    <!-- 关联设备对话框 -->
    <link-device-dialog
      v-model:visible="linkDeviceDialog.visible"
      :account="linkDeviceDialog.account"
      :devices="devices"
      :platform-apps="platformApps"
      @link="handleLinkDevice"
    />
    
    <!-- 批量更新对话框 -->
    <batch-update-dialog
      v-model:visible="batchUpdateDialog.visible"
      :selected-count="selectedAccountIds.length"
      @update="handleBatchUpdate"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { format } from 'date-fns'
import {
  getAccounts,
  getPlatforms,
  createAccount,
  updateAccount,
  deleteAccount,
  batchDeleteAccounts,
  batchUpdateAccounts
} from '@/api/social'
import { getDevices } from '@/api/device'
import { getCoreServices } from '@/api/core'
import AccountFormDialog from './components/AccountFormDialog.vue'
import ImportAccountsDialog from './components/ImportAccountsDialog.vue'
import LinkDeviceDialog from './components/LinkDeviceDialog.vue'
import BatchUpdateDialog from './components/BatchUpdateDialog.vue'
import type { SocialAccount, SocialPlatform, PlatformApp } from '@/types/social'

// 状态
const loading = ref(false)
const accounts = ref<SocialAccount[]>([])
const platforms = ref<SocialPlatform[]>([])
const coreServices = ref<any[]>([])
const devices = ref<any[]>([])
const platformApps = ref<PlatformApp[]>([])
const selectedAccountIds = ref<string[]>([])

// 筛选条件
const filters = reactive({
  platformId: '',
  coreServiceId: '',
  status: '',
  keyword: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框状态
const accountFormDialog = reactive({
  visible: false,
  title: '添加账号',
  formData: {} as Partial<SocialAccount>
})

const importDialog = reactive({
  visible: false
})

const linkDeviceDialog = reactive({
  visible: false,
  account: {} as SocialAccount
})

const batchUpdateDialog = reactive({
  visible: false
})

// 获取账号列表
const fetchAccounts = async () => {
  loading.value = true
  try {
    const params = {
      platform_id: filters.platformId || undefined,
      core_service_id: filters.coreServiceId || undefined,
      status: filters.status || undefined,
      skip: (pagination.page - 1) * pagination.pageSize,
      limit: pagination.pageSize
    }
    
    const res = await getAccounts(params)
    accounts.value = Array.isArray(res) ? res : []
    
    // 如果是第一页，更新总数
    if (pagination.page === 1) {
      pagination.total = accounts.value.length
    }
  } catch (error) {
    console.error('获取账号列表失败:', error)
    ElMessage.error('获取账号列表失败')
  } finally {
    loading.value = false
  }
}

// 获取平台列表
const fetchPlatforms = async () => {
  try {
    const res = await getPlatforms()
    platforms.value = Array.isArray(res) ? res : []
  } catch (error) {
    console.error('获取平台列表失败:', error)
  }
}

// 获取Core服务列表
const fetchCoreServices = async () => {
  try {
    const res = await getCoreServices()
    coreServices.value = Array.isArray(res) ? res : []
  } catch (error) {
    console.error('获取Core服务列表失败:', error)
  }
}

// 获取设备列表
const fetchDevices = async () => {
  try {
    const res = await getDevices()
    devices.value = Array.isArray(res) ? res : []
  } catch (error) {
    console.error('获取设备列表失败:', error)
  }
}

// 工具函数
const getPlatformName = (platformId: string) => {
  const platform = platforms.value.find(p => p.id === platformId)
  return platform ? platform.name : platformId
}

const getPlatformIcon = (platformId: string) => {
  const platform = platforms.value.find(p => p.id === platformId)
  return platform ? `/icons/${platform.icon}` : ''
}

const getCoreServiceName = (coreServiceId: string) => {
  const service = coreServices.value.find(s => s.id === coreServiceId)
  return service ? service.name : coreServiceId
}

const getStatusText = (status?: string) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    suspended: '已暂停'
  }
  return status ? statusMap[status] || status : '未知'
}

const getStatusType = (status?: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    suspended: 'warning'
  }
  return status ? typeMap[status] || 'info' : 'info'
}

const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  try {
    return format(new Date(dateStr), 'yyyy-MM-dd HH:mm:ss')
  } catch {
    return dateStr
  }
}

// 事件处理
const handleSearch = () => {
  pagination.page = 1
  fetchAccounts()
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = ''
  })
  handleSearch()
}

const handleSelectionChange = (selection: SocialAccount[]) => {
  selectedAccountIds.value = selection.map(item => item.id)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchAccounts()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchAccounts()
}

const showAddAccountDialog = () => {
  accountFormDialog.title = '添加账号'
  accountFormDialog.formData = {
    status: 'active',
    core_service_id: coreServices.value.length > 0 ? coreServices.value[0].id : ''
  }
  accountFormDialog.visible = true
}

const handleEdit = (account: SocialAccount) => {
  accountFormDialog.title = '编辑账号'
  accountFormDialog.formData = { ...account }
  accountFormDialog.visible = true
}

const handleAccountFormSubmit = async (formData: Partial<SocialAccount>) => {
  try {
    if (formData.id) {
      // 更新账号
      await updateAccount(formData.id, formData)
      ElMessage.success('更新账号成功')
    } else {
      // 创建账号
      await createAccount(formData as Omit<SocialAccount, 'id'>)
      ElMessage.success('创建账号成功')
    }
    accountFormDialog.visible = false
    fetchAccounts()
  } catch (error) {
    console.error('保存账号失败:', error)
    ElMessage.error('保存账号失败')
  }
}

const handleDelete = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除该账号吗？此操作不可恢复。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteAccount(id)
    ElMessage.success('删除账号成功')
    fetchAccounts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除账号失败:', error)
      ElMessage.error('删除账号失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedAccountIds.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedAccountIds.value.length} 个账号吗？此操作不可恢复。`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await batchDeleteAccounts(selectedAccountIds.value)
    ElMessage.success('批量删除账号成功')
    selectedAccountIds.value = []
    fetchAccounts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除账号失败:', error)
      ElMessage.error('批量删除账号失败')
    }
  }
}

const showImportDialog = () => {
  importDialog.visible = true
}

const handleImportAccounts = async (result: any) => {
  ElMessage.success(`成功导入 ${result.imported_count} 个账号`)
  importDialog.visible = false
  fetchAccounts()
}

const showLinkDeviceDialog = (account: SocialAccount) => {
  linkDeviceDialog.account = account
  linkDeviceDialog.visible = true
}

const handleLinkDevice = async (data: any) => {
  try {
    // 调用关联设备API
    // await linkDeviceAccount(data)
    ElMessage.success('关联设备成功')
    linkDeviceDialog.visible = false
    fetchAccounts()
  } catch (error) {
    console.error('关联设备失败:', error)
    ElMessage.error('关联设备失败')
  }
}

const showBatchUpdateDialog = () => {
  if (selectedAccountIds.value.length === 0) return
  batchUpdateDialog.visible = true
}

const handleBatchUpdate = async (updateData: Partial<SocialAccount>) => {
  try {
    const result = await batchUpdateAccounts(selectedAccountIds.value, updateData)
    ElMessage.success(`成功更新 ${result.updated_count} 个账号`)
    batchUpdateDialog.visible = false
    fetchAccounts()
  } catch (error) {
    console.error('批量更新账号失败:', error)
    ElMessage.error('批量更新账号失败')
  }
}

// 生命周期
onMounted(() => {
  fetchPlatforms()
  fetchCoreServices()
  fetchDevices()
  fetchAccounts()
})

// 监听筛选条件变化
watch(
  () => [filters.platformId, filters.coreServiceId, filters.status],
  () => {
    handleSearch()
  }
)
</script>

<style scoped>
.account-management {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  color: #4361ee;
}

.filter-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.action-section {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.platform-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.platform-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
```
