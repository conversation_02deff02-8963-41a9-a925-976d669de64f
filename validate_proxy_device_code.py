#!/usr/bin/env python3
"""
验证IP管理模块关联设备功能的代码正确性
"""

import ast
import os

def validate_python_syntax(file_path):
    """验证Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print(f"✓ {file_path} 语法正确")
        return True
    except SyntaxError as e:
        print(f"✗ {file_path} 语法错误: {e}")
        return False
    except Exception as e:
        print(f"✗ {file_path} 验证失败: {e}")
        return False

def check_api_endpoints():
    """检查API端点定义"""
    proxy_management_file = "backend/app/api/v1/proxy_management.py"
    
    if not os.path.exists(proxy_management_file):
        print(f"✗ 文件不存在: {proxy_management_file}")
        return False
    
    with open(proxy_management_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键API端点
    endpoints_to_check = [
        '@router.get("/devices/active"',
        '@router.post("/{proxy_id}/associate/{device_id}"',
        '@router.delete("/{proxy_id}/disassociate/{device_id}"'
    ]
    
    for endpoint in endpoints_to_check:
        if endpoint in content:
            print(f"✓ 找到API端点: {endpoint}")
        else:
            print(f"✗ 缺少API端点: {endpoint}")
            return False
    
    return True

def check_frontend_api():
    """检查前端API接口"""
    proxy_api_file = "frontend/src/api/proxy.ts"
    
    if not os.path.exists(proxy_api_file):
        print(f"✗ 文件不存在: {proxy_api_file}")
        return False
    
    with open(proxy_api_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键函数
    functions_to_check = [
        'getActiveDevices',
        'associateDeviceProxy', 
        'disassociateDeviceProxy'
    ]
    
    for func in functions_to_check:
        if func in content:
            print(f"✓ 找到前端API函数: {func}")
        else:
            print(f"✗ 缺少前端API函数: {func}")
            return False
    
    return True

def check_vue_component():
    """检查Vue组件"""
    vue_file = "frontend/src/views/device/ProxyManagement.vue"
    
    if not os.path.exists(vue_file):
        print(f"✗ 文件不存在: {vue_file}")
        return False
    
    with open(vue_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键组件
    components_to_check = [
        'addDeviceDialogVisible',
        'showAddDeviceDialog',
        'handleAddDeviceAssociation',
        'handleDeviceSelectionChange',
        'activeDevices'
    ]
    
    for component in components_to_check:
        if component in content:
            print(f"✓ 找到Vue组件元素: {component}")
        else:
            print(f"✗ 缺少Vue组件元素: {component}")
            return False
    
    return True

def main():
    """主验证函数"""
    print("验证IP管理模块关联设备功能代码")
    print("=" * 50)
    
    all_passed = True
    
    # 1. 验证Python文件语法
    print("\n1. 验证Python文件语法...")
    python_files = [
        "backend/app/core/schemas/proxy_models.py",
        "backend/app/api/v1/proxy_management.py"
    ]
    
    for file_path in python_files:
        if os.path.exists(file_path):
            if not validate_python_syntax(file_path):
                all_passed = False
        else:
            print(f"✗ 文件不存在: {file_path}")
            all_passed = False
    
    # 2. 检查API端点
    print("\n2. 检查后端API端点...")
    if not check_api_endpoints():
        all_passed = False
    
    # 3. 检查前端API
    print("\n3. 检查前端API接口...")
    if not check_frontend_api():
        all_passed = False
    
    # 4. 检查Vue组件
    print("\n4. 检查Vue组件...")
    if not check_vue_component():
        all_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ 所有代码验证通过！功能实现完整。")
        print("\n功能说明:")
        print("- 后端API: /api/v1/proxy/devices/active 获取活跃设备")
        print("- 后端API: /api/v1/proxy/{proxy_id}/associate/{device_id} 关联设备")
        print("- 后端API: /api/v1/proxy/{proxy_id}/disassociate/{device_id} 取消关联")
        print("- 前端: 完整的设备选择和关联UI")
        print("- 过滤: 只显示running和starting状态的设备")
    else:
        print("✗ 代码验证失败，存在问题需要修复。")

if __name__ == "__main__":
    main()
