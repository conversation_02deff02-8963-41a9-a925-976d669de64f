[tool.poetry]
name = "thunderhub-core"
version = "0.1.0"
description = "ThunderHub Core Service"
authors = ["Longer <<EMAIL>>"]
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "^0.115.0"
uvicorn = "^0.34.0"
python-consul = "^1.1.0"
pyyaml = "^6.0.0"
aiohttp = "^3.9.0"
grpcio = "^1.71.0"
grpcio-tools = "^1.71.0"
protobuf = "^6.30.2"
asyncio = "^3.4.3"
redis = "^5.0.1"
watchdog = "^6.0.0"
playwright = "^1.52.0"
appium-python-client = "^5.1.1"
croniter = "^3.0.0"
opencv-python = "^4.8.0"
numpy = "^1.24.0"
# 音频处理相关依赖
openai-whisper = "^20231117"
librosa = "^0.10.1"
soundfile = "^0.12.1"
pydub = "^0.25.1"
torch = "^2.1.0"
transformers = "^4.35.0"

[tool.poetry.scripts]
start = "src.run:start"
dev = "hot_reload:main"


[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
