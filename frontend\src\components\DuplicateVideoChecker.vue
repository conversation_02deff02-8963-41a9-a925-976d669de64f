<template>
  <div class="duplicate-checker">
    <!-- 重复检查结果对话框 -->
    <el-dialog
      v-model="showDuplicateDialog"
      title="视频重复检查结果"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="duplicate-results">
        <el-alert
          :title="`检测到 ${duplicateInfo.duplicate_files?.length || 0} 个重复视频`"
          type="warning"
          :closable="false"
          show-icon
          class="mb-4"
        />
        
        <el-table
          :data="duplicateInfo.duplicate_files || []"
          style="width: 100%"
          max-height="400"
        >
          <el-table-column prop="file" label="文件名" width="200" />
          <el-table-column label="重复状态" width="120">
            <template #default="scope">
              <el-tag type="warning">已上传</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="上传历史" min-width="300">
            <template #default="scope">
              <div class="upload-history">
                <div
                  v-for="(record, index) in scope.row.previous_uploads"
                  :key="index"
                  class="history-item"
                >
                  <el-tag size="small" :type="getStatusType(record.status)">
                    {{ record.status }}
                  </el-tag>
                  <span class="upload-time">
                    {{ formatDate(record.upload_time) }}
                  </span>
                  <span class="upload-title">{{ record.title }}</span>
                  <span v-if="record.youtube_video_id" class="video-id">
                    ID: {{ record.youtube_video_id }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDuplicateDialog = false">取消</el-button>
          <el-button type="primary" @click="forceUpload">
            强制上传
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 重复统计信息 -->
    <el-card v-if="showStatistics" class="statistics-card">
      <template #header>
        <div class="card-header">
          <span>视频重复统计</span>
          <el-button type="text" @click="refreshStatistics">刷新</el-button>
        </div>
      </template>
      
      <div class="statistics-content">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.total_unique_videos }}</div>
              <div class="stat-label">唯一视频数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.duplicate_videos }}</div>
              <div class="stat-label">重复视频数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.saved_uploads }}</div>
              <div class="stat-label">节省上传次数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.duplicate_rate }}%</div>
              <div class="stat-label">重复率</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

// Props
interface Props {
  showStatistics?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showStatistics: false
})

// Emits
const emit = defineEmits<{
  forceUpload: []
}>()

// 响应式数据
const showDuplicateDialog = ref(false)
const duplicateInfo = reactive<any>({})
const statistics = reactive({
  total_unique_videos: 0,
  duplicate_videos: 0,
  saved_uploads: 0,
  duplicate_rate: 0
})

// 方法
const checkDuplicates = async (folderPath: string, selectedFiles: string[], accountId: string) => {
  try {
    const response = await request.post('/api/v1/social/youtube/check-duplicates', {
      folderPath,
      selectedFiles,
      accountId
    })
    
    const duplicateFiles = response.data.results.filter((r: any) => r.status === 'duplicate')
    
    if (duplicateFiles.length > 0) {
      // 转换数据格式以匹配对话框
      Object.assign(duplicateInfo, {
        duplicate_files: duplicateFiles.map((file: any) => ({
          file: file.file,
          previous_uploads: file.upload_history || []
        }))
      })
      
      showDuplicateDialog.value = true
      return true // 有重复
    }
    
    return false // 无重复
  } catch (error) {
    console.error('检查重复失败:', error)
    ElMessage.error('检查重复失败')
    return false
  }
}

const forceUpload = () => {
  showDuplicateDialog.value = false
  emit('forceUpload')
}

const refreshStatistics = async () => {
  try {
    const response = await request.get('/api/v1/social/youtube/duplicate-statistics')
    Object.assign(statistics, response.data)
  } catch (error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'success':
      return 'success'
    case 'failed':
      return 'danger'
    case 'pending':
      return 'info'
    default:
      return 'info'
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  if (props.showStatistics) {
    refreshStatistics()
  }
})

// 暴露方法给父组件
defineExpose({
  checkDuplicates
})
</script>

<style scoped>
.duplicate-checker {
  width: 100%;
}

.duplicate-results {
  max-height: 500px;
  overflow-y: auto;
}

.upload-history {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.upload-time {
  font-size: 12px;
  color: #666;
}

.upload-title {
  font-size: 12px;
  color: #333;
  flex: 1;
}

.video-id {
  font-size: 11px;
  color: #999;
  font-family: monospace;
}

.statistics-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics-content {
  padding: 20px 0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
