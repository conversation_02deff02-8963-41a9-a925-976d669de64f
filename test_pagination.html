<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .controls button:hover {
            background: #337ecc;
        }
        .controls select {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .file-list {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .file-item {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid #eee;
            align-items: center;
            transition: all 0.3s ease;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .file-item:hover {
            background-color: #f5f7fa;
        }
        .file-info {
            flex: 1;
            margin-right: 15px;
        }
        .file-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .file-meta {
            color: #666;
            font-size: 12px;
        }
        .duration-info {
            flex: 0 0 120px;
            text-align: center;
            font-weight: 600;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .pagination button {
            padding: 8px 12px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .pagination button:hover {
            background: #337ecc;
        }
        .pagination button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        .pagination .current-page {
            background: #67c23a;
        }
        .pagination-info {
            color: #666;
            font-size: 14px;
        }
        /* 视频时长分类背景色 */
        .file-item.video-very-short {
            background-color: #f0f9ff;
        }
        .file-item.video-short {
            background-color: #fdf6ec;
        }
        .file-item.video-medium {
            background-color: #ecf5ff;
        }
        .file-item.video-long {
            background-color: #fef0f0;
        }
        .file-item.video-very-long {
            background-color: #fef0f0;
            border-left: 4px solid #f56c6c;
        }
        /* 时长文本颜色 */
        .duration-very-short { color: #67c23a; }
        .duration-short { color: #e6a23c; }
        .duration-medium { color: #409eff; }
        .duration-long { color: #f78989; }
        .duration-very-long { color: #f56c6c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>分页功能测试</h1>
        
        <div class="controls">
            <label>每页显示：</label>
            <select id="pageSize" onchange="changePageSize()">
                <option value="5">5条</option>
                <option value="10" selected>10条</option>
                <option value="20">20条</option>
                <option value="50">50条</option>
            </select>
            
            <button onclick="generateTestData(50)">生成50条测试数据</button>
            <button onclick="generateTestData(100)">生成100条测试数据</button>
            <button onclick="generateTestData(200)">生成200条测试数据</button>
            <button onclick="clearData()">清空数据</button>
        </div>
        
        <div class="file-list" id="fileList">
            <!-- 文件列表将在这里动态生成 -->
        </div>
        
        <div class="pagination" id="pagination">
            <!-- 分页控件将在这里动态生成 -->
        </div>
    </div>

    <script>
        // 分页相关变量
        let currentPage = 1;
        let pageSize = 10;
        let totalItems = 0;
        let allFiles = [];

        // 视频时长分类
        function getVideoDurationCategory(duration) {
            if (duration < 35) return { category: 'very_short', message: '极短视频', class: 'video-very-short', textClass: 'duration-very-short' };
            if (duration <= 60) return { category: 'short', message: '短视频', class: 'video-short', textClass: 'duration-short' };
            if (duration <= 180) return { category: 'medium', message: '中等视频', class: 'video-medium', textClass: 'duration-medium' };
            if (duration <= 480) return { category: 'long', message: '长视频', class: 'video-long', textClass: 'duration-long' };
            return { category: 'very_long', message: '超长视频', class: 'video-very-long', textClass: 'duration-very-long' };
        }

        // 格式化时长
        function formatDuration(seconds) {
            const minutes = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }

        // 生成测试数据
        function generateTestData(count) {
            allFiles = [];
            const videoTypes = ['mp4', 'avi', 'mov', 'mkv', 'wmv'];
            const platforms = ['YouTube', 'TikTok', '抖音', 'Instagram', 'Facebook'];
            
            for (let i = 1; i <= count; i++) {
                const duration = Math.floor(Math.random() * 600) + 10; // 10秒到10分钟
                const type = videoTypes[Math.floor(Math.random() * videoTypes.length)];
                const platform = platforms[Math.floor(Math.random() * platforms.length)];
                
                allFiles.push({
                    id: i,
                    name: `测试视频_${i.toString().padStart(3, '0')}.${type}`,
                    platform: platform,
                    duration: duration,
                    size: Math.floor(Math.random() * 100) + 10 // 10-110MB
                });
            }
            
            totalItems = allFiles.length;
            currentPage = 1;
            renderPage();
        }

        // 获取当前页数据
        function getCurrentPageData() {
            const start = (currentPage - 1) * pageSize;
            const end = start + pageSize;
            return allFiles.slice(start, end);
        }

        // 渲染当前页
        function renderPage() {
            const fileListElement = document.getElementById('fileList');
            const currentData = getCurrentPageData();
            
            if (currentData.length === 0) {
                fileListElement.innerHTML = '<div style="padding: 40px; text-align: center; color: #666;">暂无数据</div>';
            } else {
                const html = currentData.map(file => {
                    const durationInfo = getVideoDurationCategory(file.duration);
                    
                    return `
                        <div class="file-item ${durationInfo.class}">
                            <div class="file-info">
                                <div class="file-name">${file.name}</div>
                                <div class="file-meta">
                                    平台: ${file.platform} | 大小: ${file.size}MB
                                </div>
                            </div>
                            <div class="duration-info ${durationInfo.textClass}">
                                ${durationInfo.message}<br>
                                <small>${formatDuration(file.duration)}</small>
                            </div>
                        </div>
                    `;
                }).join('');
                
                fileListElement.innerHTML = html;
            }
            
            renderPagination();
        }

        // 渲染分页控件
        function renderPagination() {
            const paginationElement = document.getElementById('pagination');
            const totalPages = Math.ceil(totalItems / pageSize);
            
            if (totalPages <= 1) {
                paginationElement.innerHTML = `
                    <div class="pagination-info">
                        共 ${totalItems} 条记录
                    </div>
                `;
                return;
            }
            
            let html = `
                <button onclick="goToPage(1)" ${currentPage === 1 ? 'disabled' : ''}>首页</button>
                <button onclick="goToPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>
            `;
            
            // 显示页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <button onclick="goToPage(${i})" ${i === currentPage ? 'class="current-page"' : ''}>
                        ${i}
                    </button>
                `;
            }
            
            html += `
                <button onclick="goToPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>
                <button onclick="goToPage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>末页</button>
                <div class="pagination-info">
                    第 ${currentPage} / ${totalPages} 页，共 ${totalItems} 条记录
                </div>
            `;
            
            paginationElement.innerHTML = html;
        }

        // 跳转到指定页
        function goToPage(page) {
            const totalPages = Math.ceil(totalItems / pageSize);
            if (page < 1 || page > totalPages) return;
            
            currentPage = page;
            renderPage();
        }

        // 改变每页大小
        function changePageSize() {
            const select = document.getElementById('pageSize');
            pageSize = parseInt(select.value);
            currentPage = 1; // 重置到第一页
            renderPage();
        }

        // 清空数据
        function clearData() {
            allFiles = [];
            totalItems = 0;
            currentPage = 1;
            renderPage();
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateTestData(25); // 默认生成25条测试数据
        });
    </script>
</body>
</html>
