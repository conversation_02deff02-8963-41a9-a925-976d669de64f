<template>
  <el-dialog
    v-model="dialogVisible"
    title="账号管理"
    width="800px"
    @close="handleClose"
  >
    <div v-if="account" class="account-management">
      <!-- 账号基本信息 -->
      <el-card class="info-card" style="margin-bottom: 20px;">
        <template #header>
          <span>📋 账号信息</span>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="账号名称">
            {{ account.display_name || account.username }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ account.username }}
          </el-descriptions-item>
          <el-descriptions-item label="平台">
            <el-tag type="primary">{{ account.platform_name }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Core服务">
            <el-tag type="success">{{ account.core_service_name }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getAccountStatusColor(account.status)">
              {{ getAccountStatusText(account.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备">
            <span v-if="account.device_name">{{ account.device_name }}</span>
            <el-tag v-else size="small" type="warning">未关联设备</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 对标账号管理 -->
      <el-card class="info-card">
        <template #header>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>🎯 对标账号管理</span>
            <el-button type="primary" size="small" @click="addBenchmarkAccount">
              添加对标账号
            </el-button>
          </div>
        </template>
        
        <div v-if="account.benchmark_accounts && account.benchmark_accounts.length > 0">
          <el-table :data="account.benchmark_accounts" style="width: 100%">
            <el-table-column label="账号名称" width="200">
              <template #default="{ row }">
                <div class="benchmark-info">
                  <div class="benchmark-name">{{ row.account_name }}</div>
                  <div class="benchmark-url">
                    <el-link :href="row.account_url" target="_blank" type="primary" size="small">
                      查看账号
                    </el-link>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getBenchmarkTypeColor(row.benchmark_type)" size="small">
                  {{ getBenchmarkTypeText(row.benchmark_type) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="优先级" width="100">
              <template #default="{ row }">
                <span class="priority-stars">
                  {{ '★'.repeat(row.priority) }}{{ '☆'.repeat(5 - row.priority) }}
                </span>
              </template>
            </el-table-column>
            
            <el-table-column label="粉丝数" width="100">
              <template #default="{ row }">
                <span v-if="row.account_data?.followers">
                  {{ formatNumber(row.account_data.followers) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button size="small" @click="editBenchmarkAccount(row)">
                  编辑
                </el-button>
                <el-button size="small" type="danger" @click="deleteBenchmarkAccount(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <div v-else class="no-benchmark-accounts">
          <el-empty description="暂无对标账号" :image-size="60">
            <el-button type="primary" @click="addBenchmarkAccount">
              添加第一个对标账号
            </el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="saveChanges">
          保存更改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
  account: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'updated': []
}>()

// 响应式数据
const dialogVisible = ref(false)

// 监听器
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 工具方法
const getAccountStatusText = (status: string) => {
  const textMap = {
    'active': '活跃',
    'inactive': '暂停',
    'suspended': '已停用'
  }
  return textMap[status as keyof typeof textMap] || status
}

const getAccountStatusColor = (status: string) => {
  const colorMap = {
    'active': 'success',
    'inactive': 'warning',
    'suspended': 'danger'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 业务方法
const addBenchmarkAccount = () => {
  ElMessage.info('添加对标账号功能')
  // TODO: 实现添加对标账号功能
}

const editBenchmarkAccount = (benchmarkAccount: any) => {
  ElMessage.info(`编辑对标账号: ${benchmarkAccount.account_name}`)
  // TODO: 实现编辑对标账号功能
}

const deleteBenchmarkAccount = async (benchmarkAccount: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除对标账号 "${benchmarkAccount.account_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('对标账号删除成功')
    // TODO: 实现删除对标账号功能
    emit('updated')
  } catch {
    // 用户取消删除
  }
}

const saveChanges = () => {
  ElMessage.success('更改保存成功')
  emit('updated')
  handleClose()
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.account-management {
  max-height: 600px;
  overflow-y: auto;
}

.info-card {
  border: 1px solid #e4e7ed;
}

.benchmark-info {
  padding: 4px 0;
}

.benchmark-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.benchmark-url {
  font-size: 12px;
}

.priority-stars {
  color: #f39c12;
  font-size: 12px;
}

.no-benchmark-accounts {
  padding: 20px 0;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
