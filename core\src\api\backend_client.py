"""
Backend API客户端
Core服务通过此客户端与Backend服务通信，避免直接操作数据库
"""

import logging
import aiohttp
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class BackendAPIClient:
    """Backend API客户端类"""
    
    def __init__(self, backend_url: str = "http://localhost:8000"):
        """初始化Backend API客户端
        
        Args:
            backend_url: Backend服务地址
        """
        self.backend_url = backend_url.rstrip('/')
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            params: 查询参数
            
        Returns:
            Dict: 响应数据
        """
        if not self.session:
            self.session = aiohttp.ClientSession()
            
        url = f"{self.backend_url}{endpoint}"
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    logger.error(f"Backend API请求失败: {response.status} - {error_text}")
                    return {'success': False, 'error': f'HTTP {response.status}: {error_text}'}
                    
        except Exception as e:
            logger.error(f"Backend API请求异常: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    # 视频相关API
    async def save_video(self, video_data: Dict[str, Any]) -> Optional[str]:
        """保存视频信息到数据库
        
        Args:
            video_data: 视频数据
            
        Returns:
            str: 插入的文档ID，失败返回None
        """
        try:
            response = await self._request('POST', '/api/content/videos', data=video_data)
            if response.get('success'):
                return response.get('video_id')
            else:
                logger.error(f"保存视频失败: {response.get('error')}")
                return None
        except Exception as e:
            logger.error(f"保存视频异常: {str(e)}")
            return None
    
    async def batch_save_videos(self, videos_data: List[Dict[str, Any]]) -> int:
        """批量保存视频信息
        
        Args:
            videos_data: 视频数据列表
            
        Returns:
            int: 成功保存的数量
        """
        try:
            response = await self._request('POST', '/api/content/videos/batch', data={'videos': videos_data})
            if response.get('success'):
                return response.get('saved_count', 0)
            else:
                logger.error(f"批量保存视频失败: {response.get('error')}")
                return 0
        except Exception as e:
            logger.error(f"批量保存视频异常: {str(e)}")
            return 0
    
    async def update_video_real_url(self, video_id: str, real_url: str) -> bool:
        """更新视频的真实下载地址
        
        Args:
            video_id: 视频ID
            real_url: 真实下载地址
            
        Returns:
            bool: 是否更新成功
        """
        try:
            data = {
                'video_id': video_id,
                'real_video_url': real_url
            }
            response = await self._request('PUT', f'/api/content/videos/{video_id}/real-url', data=data)
            return response.get('success', False)
        except Exception as e:
            logger.error(f"更新视频真实地址异常: {str(e)}")
            return False
    
    async def update_download_status(self, video_id: str, status: str, download_path: str = None) -> bool:
        """更新视频下载状态
        
        Args:
            video_id: 视频ID
            status: 下载状态
            download_path: 下载路径
            
        Returns:
            bool: 是否更新成功
        """
        try:
            data = {
                'video_id': video_id,
                'download_status': status
            }
            if download_path:
                data['download_path'] = download_path
                
            response = await self._request('PUT', f'/api/content/videos/{video_id}/status', data=data)
            return response.get('success', False)
        except Exception as e:
            logger.error(f"更新视频下载状态异常: {str(e)}")
            return False
    
    async def get_videos_by_account(self, account_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取指定账号的视频列表
        
        Args:
            account_id: 账号ID
            limit: 限制数量
            
        Returns:
            List: 视频列表
        """
        try:
            params = {'account_id': account_id, 'limit': limit}
            response = await self._request('GET', '/api/content/videos', params=params)
            if response.get('success'):
                return response.get('videos', [])
            else:
                logger.error(f"获取账号视频列表失败: {response.get('error')}")
                return []
        except Exception as e:
            logger.error(f"获取账号视频列表异常: {str(e)}")
            return []
    
    async def get_pending_downloads(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取待下载的视频列表
        
        Args:
            limit: 限制数量
            
        Returns:
            List: 待下载视频列表
        """
        try:
            params = {'status': 'pending', 'limit': limit}
            response = await self._request('GET', '/api/content/videos/pending', params=params)
            if response.get('success'):
                return response.get('videos', [])
            else:
                logger.error(f"获取待下载视频列表失败: {response.get('error')}")
                return []
        except Exception as e:
            logger.error(f"获取待下载视频列表异常: {str(e)}")
            return []
    
    async def get_download_statistics(self, account_id: str = None) -> Dict[str, Any]:
        """获取下载统计信息
        
        Args:
            account_id: 账号ID（可选）
            
        Returns:
            Dict: 统计信息
        """
        try:
            params = {}
            if account_id:
                params['account_id'] = account_id
                
            response = await self._request('GET', '/api/content/videos/statistics', params=params)
            if response.get('success'):
                return response.get('statistics', {})
            else:
                logger.error(f"获取下载统计失败: {response.get('error')}")
                return {}
        except Exception as e:
            logger.error(f"获取下载统计异常: {str(e)}")
            return {}
    
    # 任务相关API
    async def create_collect_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建采集任务
        
        Args:
            task_data: 任务数据
            
        Returns:
            Dict: 创建结果
        """
        try:
            response = await self._request('POST', '/api/tasks/collect', data=task_data)
            return response
        except Exception as e:
            logger.error(f"创建采集任务异常: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    async def update_task_status(self, task_id: str, status: str, progress: int = None, message: str = None) -> bool:
        """更新任务状态
        
        Args:
            task_id: 任务ID
            status: 任务状态
            progress: 进度百分比
            message: 状态消息
            
        Returns:
            bool: 是否更新成功
        """
        try:
            data = {
                'task_id': task_id,
                'status': status
            }
            if progress is not None:
                data['progress'] = progress
            if message:
                data['message'] = message
                
            response = await self._request('PUT', f'/api/tasks/{task_id}/status', data=data)
            return response.get('success', False)
        except Exception as e:
            logger.error(f"更新任务状态异常: {str(e)}")
            return False
    
    async def get_task_info(self, task_id: str) -> Dict[str, Any]:
        """获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务信息
        """
        try:
            response = await self._request('GET', f'/api/tasks/{task_id}')
            if response.get('success'):
                return response.get('task', {})
            else:
                logger.error(f"获取任务信息失败: {response.get('error')}")
                return {}
        except Exception as e:
            logger.error(f"获取任务信息异常: {str(e)}")
            return {}
