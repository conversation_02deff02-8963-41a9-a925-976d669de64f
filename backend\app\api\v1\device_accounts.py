import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from datetime import datetime
from bson import ObjectId

from app.core.security import get_current_user
from app.core.schemas.social_repository import SocialDatabaseService
from app.api.social import get_social_service

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/social",
    tags=["device-accounts"],
    dependencies=[Depends(get_current_user)]
)

# 定义数据模型
class DeviceAccountMapping(BaseModel):
    account_id: str
    platform_id: str
    app_id: str
    settings: Optional[Dict[str, Any]] = None

# API端点：获取设备关联的账号
@router.get("/devices/{device_id}/accounts", response_model=List[Dict[str, Any]])
async def get_device_accounts(
    device_id: str,
    platform_id: Optional[str] = None,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取设备关联的账号列表

    - **device_id**: 设备ID
    - **platform_id**: 可选，平台ID筛选
    """
    try:
        logger.info(f"获取设备 {device_id} 关联的账号，平台筛选: {platform_id}")

        # 构建查询条件
        query = {"device_id": device_id}
        if platform_id:
            query["platform_id"] = platform_id

        # 查询设备账号映射
        cursor = db_service.db.device_account_mappings.find(query)
        mappings = await cursor.to_list(length=None)
        logger.info(f"找到 {len(mappings)} 个设备账号映射")

        # 格式化返回数据
        formatted_mappings = []
        for mapping in mappings:
            # 确保ID字段一致
            if '_id' in mapping:
                mapping['id'] = str(mapping['_id'])
                del mapping['_id']

            formatted_mappings.append(mapping)

        return formatted_mappings
    except Exception as e:
        logger.error(f"获取设备关联账号失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取设备关联账号失败: {str(e)}"
        )

# API端点：获取账号关联的设备
@router.get("/accounts/{account_id}/devices", response_model=List[Dict[str, Any]])
async def get_account_devices(
    account_id: str,
    _id: str = None,  # 添加_id参数
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取账号关联的设备列表

    - **account_id**: 账号ID
    - **_id**: 可选，账号的MongoDB _id
    """
    try:
        logger.info(f"获取账号 {account_id} 关联的设备，_id参数: {_id}")

        # 收集所有可能的ID
        possible_ids = []

        # 添加URL路径中的account_id
        if account_id:
            possible_ids.append(account_id)
            if ObjectId.is_valid(account_id):
                possible_ids.append(ObjectId(account_id))

        # 添加查询参数中的_id
        if _id:
            possible_ids.append(_id)
            if ObjectId.is_valid(_id):
                possible_ids.append(ObjectId(_id))

        # 尝试通过账号的id字段查找对应的MongoDB _id
        try:
            # 查找账号，获取其MongoDB _id
            account_queries = [
                {"id": account_id},
                {"_id": account_id if ObjectId.is_valid(account_id) else None},
                {"username": account_id}
            ]

            account = None
            for query in account_queries:
                if None in query.values():
                    continue
                logger.info(f"尝试查询账号: {query}")
                account = await db_service.db.social_accounts.find_one(query)
                if account:
                    logger.info(f"找到账号: {account}")
                    # 添加账号的MongoDB _id到可能的ID列表
                    if '_id' in account:
                        mongo_id = str(account['_id'])
                        possible_ids.append(mongo_id)
                        if ObjectId.is_valid(mongo_id):
                            possible_ids.append(ObjectId(mongo_id))
                        logger.info(f"添加账号的MongoDB _id: {mongo_id}")
                    break

            if not account:
                logger.warning(f"未找到账号 {account_id}")
        except Exception as e:
            logger.error(f"查询账号失败: {str(e)}")

        # 移除None值和重复值
        possible_ids = list(set([pid for pid in possible_ids if pid is not None]))

        logger.info(f"尝试的可能ID: {possible_ids}")

        # 构建查询条件
        query = {"account_id": {"$in": possible_ids}}
        logger.info(f"查询条件: {query}")

        # 查询账号设备映射
        cursor = db_service.db.device_account_mappings.find(query)
        mappings = await cursor.to_list(length=None)
        logger.info(f"找到 {len(mappings)} 个账号设备映射")

        # 如果没有找到映射，尝试使用_id作为account_id查询
        if len(mappings) == 0 and (_id or account_id):
            logger.info("使用_id作为account_id查询")

            # 构建新的查询条件
            new_query = {"account_id": {"$in": possible_ids}}
            logger.info(f"新的查询条件: {new_query}")

            # 查询账号设备映射
            cursor = db_service.db.device_account_mappings.find(new_query)
            mappings = await cursor.to_list(length=None)
            logger.info(f"找到 {len(mappings)} 个账号设备映射")

        # 如果仍然没有找到映射，尝试直接查询数据库
        if len(mappings) == 0:
            logger.info("未找到映射，尝试直接查询数据库")
            # 列出所有映射，帮助调试
            cursor = db_service.db.device_account_mappings.find({}, {"account_id": 1, "device_id": 1})
            all_mappings = await cursor.to_list(length=None)
            logger.info(f"数据库中的所有映射: {all_mappings}")

            # 尝试查找包含任何可能ID的映射
            for mapping in all_mappings:
                account_id_str = str(mapping.get('account_id', ''))
                for possible_id in possible_ids:
                    possible_id_str = str(possible_id)
                    if account_id_str == possible_id_str:
                        logger.info(f"找到匹配的映射: {mapping}")
                        mappings.append(mapping)

        # 格式化返回数据
        formatted_mappings = []
        for mapping in mappings:
            # 确保ID字段一致
            if '_id' in mapping:
                mapping['id'] = str(mapping['_id'])
                mapping['_id'] = str(mapping['_id'])  # 保留_id字段，但转换为字符串

            # 确保所有字段都是字符串类型
            if 'account_id' in mapping and not isinstance(mapping['account_id'], str):
                mapping['account_id'] = str(mapping['account_id'])

            if 'device_id' in mapping and not isinstance(mapping['device_id'], str):
                mapping['device_id'] = str(mapping['device_id'])

            if 'platform_id' in mapping and not isinstance(mapping['platform_id'], str):
                mapping['platform_id'] = str(mapping['platform_id'])

            if 'app_id' in mapping and not isinstance(mapping['app_id'], str):
                mapping['app_id'] = str(mapping['app_id'])

            logger.info(f"格式化后的映射: {mapping}")
            formatted_mappings.append(mapping)

        return formatted_mappings
    except Exception as e:
        logger.error(f"获取账号关联设备失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取账号关联设备失败: {str(e)}"
        )

# API端点：关联设备和账号
@router.post("/devices/{device_id}/accounts", response_model=Dict[str, Any])
async def link_device_account(
    device_id: str,
    mapping: DeviceAccountMapping,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    关联设备和账号

    - **device_id**: 设备ID
    - **mapping**: 关联信息
    """
    try:
        logger.info(f"关联设备 {device_id} 和账号 {mapping.account_id}")

        # 检查设备是否存在
        device = None
        device_queries = [
            {"id": device_id},
            {"_id": device_id},
            {"_id": ObjectId(device_id) if ObjectId.is_valid(device_id) else None}
        ]

        for query in device_queries:
            if None in query.values():
                continue

            logger.info(f"尝试查询设备: {query}")
            device = await db_service.db.devices.find_one(query)
            if device:
                logger.info(f"找到设备: {device}")
                break

        if not device:
            logger.warning(f"设备 {device_id} 不存在，但仍继续关联操作")

        # 检查账号是否存在
        account = None
        try:
            account = db_service.get_account_by_id(mapping.account_id)
            logger.info(f"找到账号: {account}")
        except Exception as e:
            logger.error(f"获取账号失败: {str(e)}")

        if not account:
            raise HTTPException(status_code=404, detail="账号不存在")

        # 检查是否已存在该平台的关联
        # 使用设备的MongoDB _id或设备ID
        device_mongo_id = None
        if device and '_id' in device:
            device_mongo_id = str(device['_id'])
        else:
            device_mongo_id = str(device_id)

        existing_queries = [
            {"device_id": device_mongo_id, "platform_id": mapping.platform_id},
            {"device_id": device_mongo_id, "platform_id": str(mapping.platform_id)},
            {"device_id": device_id, "platform_id": mapping.platform_id},
            {"device_id": str(device_id), "platform_id": mapping.platform_id},
            {"device_id": device_id, "platform_id": str(mapping.platform_id)},
            {"device_id": str(device_id), "platform_id": str(mapping.platform_id)}
        ]

        existing = None
        for query in existing_queries:
            logger.info(f"尝试查询现有关联: {query}")
            existing = await db_service.db.device_account_mappings.find_one(query)
            if existing:
                logger.info(f"找到现有关联: {existing}")
                break

        now = datetime.now()

        if existing:
            # 更新现有关联
            logger.info(f"更新现有关联: {existing}")

            # 确保account_id是字符串
            account_id = mapping.account_id
            if not isinstance(account_id, str):
                account_id = str(account_id)

            # 确保app_id是字符串
            app_id = mapping.app_id
            if not isinstance(app_id, str):
                app_id = str(app_id)

            result = await db_service.db.device_account_mappings.update_one(
                {"_id": existing["_id"]},
                {
                    "$set": {
                        "account_id": account_id,
                        "app_id": app_id,
                        "settings": mapping.settings,
                        "updated_at": now
                    }
                }
            )

            if result.modified_count > 0:
                logger.info("关联更新成功")

                # 获取更新后的映射
                updated_mapping = await db_service.db.device_account_mappings.find_one({"_id": existing["_id"]})

                # 格式化返回数据
                if '_id' in updated_mapping:
                    updated_mapping['id'] = str(updated_mapping['_id'])
                    del updated_mapping['_id']

                # 确保所有ID字段都是字符串
                for field in ['device_id', 'account_id', 'platform_id', 'app_id']:
                    if field in updated_mapping and not isinstance(updated_mapping[field], str):
                        updated_mapping[field] = str(updated_mapping[field])

                logger.info(f"返回更新后的映射: {updated_mapping}")
                return updated_mapping
            else:
                logger.warning("关联未更新")
                raise HTTPException(status_code=500, detail="关联更新失败")
        else:
            # 创建新关联
            logger.info("创建新关联")

            # 确保所有ID都是字符串
            account_id_str = str(mapping.account_id)
            platform_id_str = str(mapping.platform_id)
            app_id_str = str(mapping.app_id)

            # 使用设备的MongoDB _id而不是设备ID
            device_mongo_id = None
            if device and '_id' in device:
                device_mongo_id = str(device['_id'])
            else:
                # 如果找不到设备的MongoDB _id，使用传入的设备ID
                device_mongo_id = str(device_id)
                logger.warning(f"未找到设备的MongoDB _id，使用传入的设备ID: {device_id}")

            logger.info(f"使用设备MongoDB ID: {device_mongo_id} 进行关联")

            mapping_data = {
                "device_id": device_mongo_id,  # 使用MongoDB _id
                "account_id": account_id_str,
                "platform_id": platform_id_str,
                "app_id": app_id_str,
                "settings": mapping.settings,
                "status": "active",
                "created_at": now,
                "updated_at": now
            }

            # 如果设备有core_id，添加到映射数据中
            if device and "core_id" in device:
                mapping_data["core_service_id"] = str(device["core_id"])

            logger.info(f"插入映射数据: {mapping_data}")
            result = await db_service.db.device_account_mappings.insert_one(mapping_data)

            if result.inserted_id:
                logger.info(f"关联创建成功，ID: {result.inserted_id}")

                # 获取创建的映射
                created_mapping = await db_service.db.device_account_mappings.find_one({"_id": result.inserted_id})

                # 格式化返回数据
                if '_id' in created_mapping:
                    created_mapping['id'] = str(created_mapping['_id'])
                    del created_mapping['_id']

                logger.info(f"返回创建的映射: {created_mapping}")
                return created_mapping
            else:
                logger.warning("关联创建失败")
                raise HTTPException(status_code=500, detail="关联创建失败")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"关联设备和账号失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"关联设备和账号失败: {str(e)}"
        )

# API端点：解除设备和账号关联
@router.delete("/devices/{device_id}/accounts/{account_id}", response_model=Dict[str, bool])
async def unlink_device_account(
    device_id: str,
    account_id: str,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    解除设备和账号关联

    - **device_id**: 设备ID
    - **account_id**: 账号ID
    """
    try:
        logger.info(f"解除设备 {device_id} 和账号 {account_id} 的关联")

        # 尝试查找设备的MongoDB _id
        device = None
        device_queries = [
            {"id": device_id},
            {"_id": device_id},
            {"_id": ObjectId(device_id) if ObjectId.is_valid(device_id) else None}
        ]

        for query in device_queries:
            if None in query.values():
                continue

            logger.info(f"尝试查询设备: {query}")
            device = await db_service.db.devices.find_one(query)
            if device:
                logger.info(f"找到设备: {device}")
                break

        # 尝试不同的ID格式组合
        possible_device_ids = [device_id, str(device_id)]
        possible_account_ids = [account_id, str(account_id)]

        # 如果找到设备，添加设备的MongoDB _id
        if device and '_id' in device:
            device_mongo_id = str(device['_id'])
            possible_device_ids.append(device_mongo_id)
            logger.info(f"添加设备的MongoDB _id: {device_mongo_id}")

        if ObjectId.is_valid(device_id):
            possible_device_ids.append(ObjectId(device_id))

        if ObjectId.is_valid(account_id):
            possible_account_ids.append(ObjectId(account_id))

        logger.info(f"可能的设备ID: {possible_device_ids}")
        logger.info(f"可能的账号ID: {possible_account_ids}")

        # 构建查询条件
        query = {
            "device_id": {"$in": possible_device_ids},
            "account_id": {"$in": possible_account_ids}
        }

        logger.info(f"删除查询条件: {query}")

        # 删除关联
        result = await db_service.db.device_account_mappings.delete_many(query)

        if result.deleted_count > 0:
            logger.info(f"成功删除 {result.deleted_count} 个关联")
            return {"deleted": True, "count": result.deleted_count}
        else:
            # 如果没有找到映射，尝试列出所有映射，帮助调试
            cursor = db_service.db.device_account_mappings.find({}, {"device_id": 1, "account_id": 1})
            all_mappings = await cursor.to_list(length=None)
            logger.warning(f"未找到关联或关联解除失败，数据库中的所有映射: {all_mappings}")
            return {"deleted": False, "count": 0}
    except Exception as e:
        logger.error(f"解除设备和账号关联失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"解除设备和账号关联失败: {str(e)}"
        )
