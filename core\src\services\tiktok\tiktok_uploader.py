"""
TikTok上传器
基于通用基类的TikTok上传功能
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional

from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from ..common.base_uploader import BaseUploader

logger = logging.getLogger(__name__)


class TikTokUploader(BaseUploader):
    """TikTok上传器类"""

    def __init__(self, device_id: str, appium_server: str = 'http://localhost:4723'):
        """初始化TikTok上传器

        Args:
            device_id: 设备ID
            appium_server: Appium服务器地址
        """
        super().__init__(
            device_id=device_id,
            app_package='com.zhiliaoapp.musically',  # TikTok包名
            app_activity='com.ss.android.ugc.aweme.splash.SplashActivity',  # TikTok主活动
            appium_server=appium_server
        )

    def get_app_name(self) -> str:
        """获取应用名称

        Returns:
            str: 应用名称
        """
        return "TikTok"

    async def verify_app_launched(self) -> bool:
        """验证TikTok应用是否已启动

        Returns:
            bool: 是否已启动
        """
        try:
            driver = self.device_manager.get_driver()
            if not driver:
                return False

            # 尝试查找TikTok特有的界面元素
            tiktok_elements = [
                'com.zhiliaoapp.musically:id/main_tab_container',
                'com.zhiliaoapp.musically:id/bottom_tab_bar',
                'com.zhiliaoapp.musically:id/fab_plus'
            ]

            for element_id in tiktok_elements:
                try:
                    WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((AppiumBy.ID, element_id))
                    )
                    logger.info(f"找到TikTok界面元素: {element_id}")
                    return True
                except:
                    continue

            # 检查页面源码
            try:
                page_source = driver.page_source
                if 'tiktok' in page_source.lower() or 'musically' in page_source.lower():
                    logger.info("页面源码中包含TikTok相关内容")
                    return True
            except:
                pass

            return False

        except Exception as e:
            logger.error(f"验证TikTok启动状态异常: {str(e)}")
            return False

    async def execute_upload_task(self, video_path: str, title: str, description: str = "", hashtags: list = None) -> bool:
        """执行TikTok上传任务

        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            hashtags: 标签列表

        Returns:
            bool: 是否上传成功
        """
        try:
            logger.info("===== 开始执行TikTok上传任务 =====")
            logger.info(f"视频路径: {video_path}")
            logger.info(f"视频标题: {title}")
            logger.info(f"标签: {hashtags}")

            # 更新上传状态
            self.set_status("uploading", "正在准备上传...")
            self.update_progress(0, "正在准备上传...")

            # 检查文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                self.set_status("error", f"视频文件不存在: {video_path}")
                return False

            # 确保网络连接正常
            await self.ensure_network_ready()
            self.update_progress(10, "网络连接检查完成")

            # 启动TikTok应用
            if not await self.launch_app():
                return False
            self.update_progress(20, "TikTok应用已启动")

            # 推送视频文件到设备
            if not await self.prepare_media_file(video_path):
                return False
            self.update_progress(30, "视频文件已推送到设备")

            # 执行上传流程
            return await self._perform_tiktok_upload(video_path, title, description, hashtags)

        except Exception as e:
            logger.error(f"执行TikTok上传任务异常: {str(e)}", exc_info=True)
            self.set_status("error", f"执行TikTok上传任务异常: {str(e)}")
            return False

    async def _perform_tiktok_upload(self, video_path: str, title: str, description: str, hashtags: list) -> bool:
        """执行TikTok上传流程

        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            hashtags: 标签列表

        Returns:
            bool: 是否上传成功
        """
        try:
            driver = self.device_manager.get_driver()
            if not driver:
                self.set_status("error", "没有可用的Appium驱动")
                return False

            logger.info("开始执行TikTok上传流程...")
            self.update_progress(35, "正在执行上传流程...")

            # 点击创建按钮（+按钮）
            if not await self._click_create_button(driver):
                return False
            self.update_progress(40, "已点击创建按钮")

            # 选择视频文件
            if not await self._select_video_file(video_path, driver):
                return False
            self.update_progress(55, "已选择视频文件")

            # 填写视频信息
            if not await self._fill_video_info(title, description, hashtags, driver):
                return False
            self.update_progress(75, "已填写视频信息")

            # 发布视频
            if not await self._publish_video(driver):
                return False

            self.set_status("completed", "TikTok视频发布成功")
            self.update_progress(100, "TikTok视频发布成功")
            return True

        except Exception as e:
            logger.error(f"执行TikTok上传流程异常: {str(e)}", exc_info=True)
            self.set_status("error", f"执行TikTok上传流程异常: {str(e)}")
            return False

    async def _click_create_button(self, driver) -> bool:
        """点击创建按钮"""
        try:
            logger.info("点击TikTok创建按钮")
            # TikTok的创建按钮通常是底部的+号
            create_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((AppiumBy.ID, 'com.zhiliaoapp.musically:id/fab_plus'))
            )
            create_button.click()
            await asyncio.sleep(3)
            return True
        except Exception as e:
            logger.error(f"点击TikTok创建按钮异常: {str(e)}")
            self.set_status("error", "无法找到TikTok创建按钮")
            return False

    async def _select_video_file(self, video_path: str, driver) -> bool:
        """选择视频文件"""
        try:
            logger.info("选择TikTok视频文件")
            video_filename = os.path.basename(video_path)
            
            # 使用文件管理器选择文件
            success = await self.file_manager.select_file_in_picker(video_filename)
            if success:
                logger.info(f"已选择视频文件: {video_filename}")
                await asyncio.sleep(3)  # 等待视频加载
                return True
            else:
                logger.warning("文件管理器选择失败")
                self.set_status("error", "选择视频文件失败")
                return False
        except Exception as e:
            logger.error(f"选择TikTok视频文件异常: {str(e)}")
            self.set_status("error", f"选择视频文件失败: {str(e)}")
            return False

    async def _fill_video_info(self, title: str, description: str, hashtags: list, driver) -> bool:
        """填写视频信息"""
        try:
            logger.info("填写TikTok视频信息")

            # 构建完整的描述文本
            full_description = title
            if description:
                full_description += f"\n{description}"
            if hashtags:
                hashtag_text = " ".join([f"#{tag}" for tag in hashtags])
                full_description += f"\n{hashtag_text}"

            # 输入描述
            await self._input_description(full_description, driver)

            return True
        except Exception as e:
            logger.error(f"填写TikTok视频信息异常: {str(e)}")
            self.set_status("error", f"填写视频信息失败: {str(e)}")
            return False

    async def _input_description(self, description: str, driver) -> None:
        """输入描述"""
        try:
            # TikTok的描述输入框
            description_input = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((AppiumBy.ID, 'com.zhiliaoapp.musically:id/desc_edittext'))
            )
            description_input.clear()
            description_input.send_keys(description)
            logger.info("已输入TikTok描述")
        except Exception as e:
            logger.warning(f"输入TikTok描述异常: {str(e)}")
            # 使用ADB输入
            await self.input_text_via_adb(description)

    async def _publish_video(self, driver) -> bool:
        """发布视频"""
        try:
            logger.info("发布TikTok视频")
            # 点击发布按钮
            publish_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((AppiumBy.ID, 'com.zhiliaoapp.musically:id/publish_btn'))
            )
            publish_button.click()
            
            # 等待发布完成
            await asyncio.sleep(5)
            logger.info("TikTok视频发布完成")
            return True
        except Exception as e:
            logger.error(f"发布TikTok视频异常: {str(e)}")
            self.set_status("error", f"发布视频失败: {str(e)}")
            return False
