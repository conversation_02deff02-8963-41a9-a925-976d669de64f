<template>
  <el-dialog
    v-model="dialogVisible"
    title="对标账号详情"
    width="800px"
    @close="handleClose"
  >
    <div v-if="account" class="account-detail">
      <!-- 账号基本信息 -->
      <div class="account-header">
        <div class="account-avatar">
          <img 
            v-if="account.avatar_url" 
            :src="account.avatar_url" 
            @error="handleImageError"
          />
          <div v-else class="avatar-placeholder">
            {{ account.account_name.charAt(0).toUpperCase() }}
          </div>
        </div>
        <div class="account-info">
          <h2>{{ account.account_name }}</h2>
          <div class="account-meta">
            <el-tag>{{ account.platform }}</el-tag>
            <el-tag 
              :type="getBenchmarkTypeColor(account.benchmark_type)" 
              style="margin-left: 8px"
            >
              {{ getBenchmarkTypeText(account.benchmark_type) }}
            </el-tag>
            <el-tag 
              :type="getStatusColor(account.status)" 
              style="margin-left: 8px"
            >
              {{ getStatusText(account.status) }}
            </el-tag>
          </div>
          <div class="account-url">
            <el-link :href="account.account_url" target="_blank" type="primary">
              {{ account.account_url }}
            </el-link>
          </div>
        </div>
        <div class="account-priority">
          <div class="priority-label">优先级</div>
          <el-rate 
            :model-value="account.priority" 
            :max="5" 
            disabled
            size="small"
          />
        </div>
      </div>

      <!-- 账号描述 -->
      <div v-if="account.description" class="account-description">
        <h3>账号描述</h3>
        <p>{{ account.description }}</p>
      </div>

      <!-- 标签 -->
      <div v-if="account.tags.length > 0" class="account-tags">
        <h3>标签</h3>
        <div class="tags-container">
          <el-tag 
            v-for="tag in account.tags" 
            :key="tag"
            style="margin-right: 8px; margin-bottom: 8px"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>

      <!-- 关联账号信息 -->
      <div class="linked-account-section">
        <h3>关联账号信息</h3>
        <div v-if="account.our_account_info && account.our_account_info.status !== 'not_found'" class="linked-account-detail">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="关联平台">
              <el-tag type="primary">
                {{ getPlatformDisplayName(account.our_account_info.platform_id) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="账号名称">
              {{ formatOurAccountName(account.our_account_info) }}
            </el-descriptions-item>
            <el-descriptions-item label="用户名">
              {{ account.our_account_info.username }}
            </el-descriptions-item>
            <el-descriptions-item label="Core服务">
              <el-tag type="success">
                {{ getAccountCoreService(account.our_account_info) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>

          <div class="download-path-info" style="margin-top: 15px;">
            <h4>📁 下载路径预览</h4>
            <div class="path-preview">
              {{ getDownloadPath(account) }}
            </div>
            <div class="path-explanation">
              <small>基础路径/关联账号平台/关联账号名称/对标账号名称/发布月份/</small>
            </div>
          </div>
        </div>
        <div v-else class="unlinked-account-detail">
          <el-alert
            title="未关联账号"
            type="warning"
            description="该对标账号尚未关联我们的账号，无法确定下载路径。请在社媒管理中进行关联设置。"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <!-- 账号数据 -->
      <div class="account-data">
        <h3>账号数据</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="data-card">
              <div class="data-icon">👥</div>
              <div class="data-content">
                <div class="data-value">
                  {{ formatNumber(account.account_data.followers) || '--' }}
                </div>
                <div class="data-label">粉丝数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="data-card">
              <div class="data-icon">📝</div>
              <div class="data-content">
                <div class="data-value">
                  {{ formatNumber(account.account_data.posts_count) || '--' }}
                </div>
                <div class="data-label">发布数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="data-card">
              <div class="data-icon">👁️</div>
              <div class="data-content">
                <div class="data-value">
                  {{ formatNumber(account.account_data.avg_views) || '--' }}
                </div>
                <div class="data-label">平均观看</div>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 16px">
          <el-col :span="8">
            <div class="data-card">
              <div class="data-icon">👍</div>
              <div class="data-content">
                <div class="data-value">
                  {{ formatNumber(account.account_data.avg_likes) || '--' }}
                </div>
                <div class="data-label">平均点赞</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="data-card">
              <div class="data-icon">📈</div>
              <div class="data-content">
                <div class="data-value">
                  {{ formatPercentage(account.account_data.engagement_rate) || '--' }}
                </div>
                <div class="data-label">互动率</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="data-card">
              <div class="data-icon">📊</div>
              <div class="data-content">
                <div class="data-value">
                  {{ formatPercentage(account.account_data.growth_rate) || '--' }}
                </div>
                <div class="data-label">增长率</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 时间信息 -->
      <div class="time-info">
        <h3>时间信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="最后发布">
            {{ formatTime(account.account_data.last_post_date) || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(account.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatTime(account.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建用户">
            {{ account.created_by }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 备注 -->
      <div v-if="account.notes" class="account-notes">
        <h3>备注</h3>
        <div class="notes-content">
          {{ account.notes }}
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button @click="openAccountUrl">
          访问账号
        </el-button>
        <el-button
          type="primary"
          @click="downloadAccount"
          :disabled="!canDownload"
        >
          立即下载
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { type BenchmarkAccount } from '@/api/content'

interface Props {
  modelValue: boolean
  account: BenchmarkAccount | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const dialogVisible = ref(false)

// 计算属性
const canDownload = computed(() => {
  return props.account?.our_account_info && props.account.our_account_info.status !== 'not_found'
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    active: '活跃',
    inactive: '非活跃',
    monitoring: '监控中'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getStatusColor = (status: string) => {
  const colorMap = {
    active: 'success',
    inactive: 'danger',
    monitoring: 'warning'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const formatNumber = (num?: number) => {
  if (!num) return null
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatPercentage = (num?: number) => {
  if (!num) return null
  return (num * 100).toFixed(2) + '%'
}

const formatTime = (timeString?: string) => {
  if (!timeString) return null
  return new Date(timeString).toLocaleString('zh-CN')
}

// 新增的工具方法
const getPlatformDisplayName = (platformId: string) => {
  const platformIdMap = {
    '681efeeecd836bd64b9c2a1e': 'YouTube',
    '681efeeecd836bd64b9c2a20': 'TikTok',
    '681efeeecd836bd64b9c2a22': '抖音',
    '681efeeecd836bd64b9c2a24': 'Instagram'
  }
  return platformIdMap[platformId as keyof typeof platformIdMap] || platformId
}

const formatOurAccountName = (accountInfo: any) => {
  if (!accountInfo) return '未知账号'

  let name = accountInfo.display_name
  if (!name && accountInfo.username) {
    if (accountInfo.username.includes('@')) {
      name = accountInfo.username.split('@')[0]
    } else {
      name = accountInfo.username
    }
  }

  if (!name) {
    name = accountInfo.id ? accountInfo.id.substring(0, 8) : '未知账号'
  }

  return name
}

const getAccountCoreService = (accountInfo: any) => {
  if (!accountInfo || !accountInfo.core_service_id) return '未知'
  return accountInfo.core_service_id
}

const getDownloadPath = (account: any) => {
  const basePath = 'H:\\PublishSystem\\'
  const currentMonth = new Date().toISOString().slice(0, 7)

  const ourAccountInfo = account.our_account_info
  if (ourAccountInfo && ourAccountInfo.status !== 'not_found') {
    const ourPlatformName = getPlatformDisplayName(ourAccountInfo.platform_id)
    const ourAccountName = formatOurAccountName(ourAccountInfo)
    return `${basePath}${ourPlatformName}\\${ourAccountName}\\${account.account_name}\\${currentMonth}\\`
  } else {
    return `${basePath}未关联平台\\未关联账号\\${account.account_name}\\${currentMonth}\\`
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const openAccountUrl = () => {
  if (props.account?.account_url) {
    window.open(props.account.account_url, '_blank')
  }
}

const downloadAccount = () => {
  if (!canDownload.value) {
    ElMessage.warning('该账号未关联我们的账号，无法下载')
    return
  }

  // TODO: 实现下载功能
  ElMessage.info(`开始下载 ${props.account?.account_name} 的内容...`)
  handleClose()
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.account-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.account-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.account-avatar {
  width: 80px;
  height: 80px;
  margin-right: 20px;
  flex-shrink: 0;
}

.account-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
}

.account-info {
  flex: 1;
}

.account-info h2 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 24px;
}

.account-meta {
  margin-bottom: 12px;
}

.account-url {
  font-size: 14px;
}

.account-priority {
  text-align: center;
}

.priority-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.account-description,
.account-tags,
.linked-account-section,
.account-data,
.time-info,
.account-notes {
  margin-bottom: 24px;
}

.account-description h3,
.account-tags h3,
.linked-account-section h3,
.account-data h3,
.time-info h3,
.account-notes h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.linked-account-detail {
  padding: 10px 0;
}

.download-path-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.path-preview {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #606266;
  word-break: break-all;
}

.path-explanation {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}

.unlinked-account-detail {
  padding: 10px 0;
}

.account-description p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
}

.data-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s;
}

.data-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.data-icon {
  font-size: 24px;
  margin-right: 12px;
}

.data-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.data-label {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
}

.notes-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.6;
}

.dialog-footer {
  text-align: right;
}
</style>
