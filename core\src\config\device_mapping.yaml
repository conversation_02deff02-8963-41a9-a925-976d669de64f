# 设备映射配置
# 用于将设备ID映射到ADB设备ID

# 映射规则:
# - 键: 设备ID
# - 值: ADB设备ID

mappings:
  # 示例: 雷电模拟器设备0映射到ADB设备emulator-5554
  "0": "emulator-5554"
  # 示例: 雷电模拟器设备1映射到ADB设备emulator-5556
  "1": "emulator-5556"
  # 示例: 雷电模拟器设备2映射到ADB设备emulator-5558
  "2": "emulator-5558"
  "3": "emulator-5560"
  "4": "emulator-5562"
  "18": "emulator-5590"
  "42": "emulator-5638"
  "85": "emulator-5724"

# 设备选择策略:
# - strict: 严格按照映射关系选择设备，如果映射不存在则失败
# - fallback: 如果映射不存在，尝试使用端口号规则（5554+2*index）
# - first_available: 如果映射不存在，使用第一个可用设备（不推荐）
selection_strategy: "strict"
