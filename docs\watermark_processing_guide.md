# 视频水印检测和清除功能使用指南

## 功能概述

ThunderHub系统现已集成完整的视频水印检测和清除功能，支持：

- **自动水印检测**：智能识别视频中的水印位置
- **模板匹配检测**：基于预定义模板精确检测特定水印
- **区域检测**：在指定区域内检测水印
- **智能水印清除**：使用多种算法清除检测到的水印
- **批量处理**：支持文件夹批量处理，提高效率
- **异步任务**：大批量处理采用后台任务，不阻塞界面

## 架构设计

```
Frontend → Backend (HTTP API) → Core (gRPC) → FFmpeg/OpenCV
```

- **Frontend**: 用户界面，提供操作入口
- **Backend**: HTTP API接口，任务管理和状态跟踪
- **Core**: 核心处理服务，执行实际的视频处理操作
- **FFmpeg/OpenCV**: 底层视频处理和图像分析工具

## API接口说明

### 1. 检测视频水印

**接口**: `POST /api/v1/filesystem/watermark/detect`

**请求参数**:
```json
{
  "video_path": "视频文件路径",
  "detection_mode": "auto",  // auto, template, region
  "template_path": "模板文件路径（可选）",
  "detection_region": "x,y,width,height（可选）",
  "sensitivity": 0.7,  // 0.0-1.0
  "save_detection_result": false
}
```

**响应示例**:
```json
{
  "success": true,
  "watermark_detected": true,
  "watermarks": [
    {
      "watermark_type": "logo",
      "position": "100,50,200,100",
      "confidence": 0.85,
      "description": "右上角检测到可能的水印"
    }
  ],
  "detection_time_ms": 1500
}
```

### 2. 清除视频水印

**接口**: `POST /api/v1/filesystem/watermark/remove`

**请求参数**:
```json
{
  "input_video_path": "输入视频路径",
  "output_video_path": "输出视频路径",
  "removal_mode": "auto",  // auto, manual, inpaint
  "watermark_regions": ["100,50,200,100"],  // 手动指定区域
  "inpaint_method": "blur",  // blur, median, inpaint
  "output_quality": "medium",  // high, medium, low
  "preserve_encoding": false
}
```

**响应示例**:
```json
{
  "success": true,
  "output_file_path": "输出文件路径",
  "processing_time_ms": 45000,
  "original_file_size": 10485760,
  "output_file_size": 10240000,
  "removed_watermarks_count": 1
}
```

### 3. 批量处理水印

**接口**: `POST /api/v1/filesystem/watermark/batch`

**请求参数**:
```json
{
  "input_folder_path": "输入文件夹路径",
  "output_folder_path": "输出文件夹路径",
  "process_mode": "detect_and_remove",  // detect_only, remove_only, detect_and_remove
  "file_filters": ["*.mp4"],
  "recursive": false,
  "max_concurrent": 3,
  "detection_config": {
    "detection_mode": "auto",
    "sensitivity": 0.7
  },
  "removal_config": {
    "removal_mode": "auto",
    "inpaint_method": "blur",
    "output_quality": "medium"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "task_id": "uuid-task-id",
  "message": "批量水印处理任务已启动"
}
```

### 4. 查询批量任务进度

**接口**: `GET /api/v1/filesystem/watermark/batch/{task_id}`

**响应示例**:
```json
{
  "task_id": "uuid-task-id",
  "status": "processing",  // pending, processing, completed, failed
  "progress": 65,
  "current_step": "正在处理第3个文件",
  "total_files": 10,
  "processed_files": 6,
  "successful_files": 5,
  "failed_files": 1
}
```

## 配置说明

水印处理功能的配置文件位于 `core/config/watermark_config.yaml`，主要配置项包括：

### 检测配置
- `default_mode`: 默认检测模式
- `default_sensitivity`: 默认敏感度
- `supported_formats`: 支持的视频格式
- `auto_detection`: 自动检测区域和阈值设置
- `template_matching`: 模板匹配配置
- `frame_sampling`: 帧采样策略

### 清除配置
- `default_mode`: 默认清除模式
- `default_inpaint_method`: 默认修复算法
- `quality_settings`: 不同质量级别的编码参数
- `inpaint_methods`: 各种修复算法的参数
- `encoding`: 视频编码配置

### 性能配置
- `max_memory_mb`: 最大内存使用
- `ffmpeg_threads`: FFmpeg线程数
- `opencv_threads`: OpenCV线程数
- `cleanup_temp_files`: 是否清理临时文件

## 使用示例

### Python代码示例

```python
import requests

# 检测水印
detection_data = {
    "video_path": "/path/to/video.mp4",
    "detection_mode": "auto",
    "sensitivity": 0.7
}

response = requests.post(
    "http://localhost:8000/api/v1/filesystem/watermark/detect",
    json=detection_data
)

result = response.json()
if result["success"] and result["watermark_detected"]:
    print(f"检测到 {len(result['watermarks'])} 个水印")
    
    # 清除水印
    removal_data = {
        "input_video_path": "/path/to/video.mp4",
        "output_video_path": "/path/to/cleaned_video.mp4",
        "removal_mode": "auto",
        "output_quality": "high"
    }
    
    response = requests.post(
        "http://localhost:8000/api/v1/filesystem/watermark/remove",
        json=removal_data
    )
    
    result = response.json()
    if result["success"]:
        print(f"水印清除成功: {result['output_file_path']}")
```

### 批量处理示例

```python
import requests
import time

# 创建批量任务
batch_data = {
    "input_folder_path": "/path/to/videos",
    "output_folder_path": "/path/to/cleaned_videos",
    "process_mode": "detect_and_remove",
    "file_filters": ["*.mp4", "*.avi"],
    "recursive": True,
    "max_concurrent": 3
}

response = requests.post(
    "http://localhost:8000/api/v1/filesystem/watermark/batch",
    json=batch_data
)

task_id = response.json()["task_id"]

# 监控任务进度
while True:
    response = requests.get(
        f"http://localhost:8000/api/v1/filesystem/watermark/batch/{task_id}"
    )
    
    progress = response.json()
    print(f"进度: {progress['progress']}% - {progress['current_step']}")
    
    if progress["status"] in ["completed", "failed"]:
        break
    
    time.sleep(5)
```

## 测试和验证

运行测试脚本验证功能：

```bash
python test_watermark_processing.py
```

测试脚本会：
1. 创建测试视频文件
2. 测试Core服务的直接调用
3. 测试Backend API接口
4. 测试批量处理功能
5. 输出详细的测试结果

## 注意事项

1. **依赖要求**：
   - FFmpeg（用于视频处理）
   - OpenCV（用于图像分析）
   - 足够的磁盘空间存储临时文件和输出文件

2. **性能考虑**：
   - 水印检测和清除是CPU密集型操作
   - 大文件处理可能需要较长时间
   - 建议根据硬件配置调整并发数

3. **质量设置**：
   - `high`质量输出文件较大但质量最好
   - `medium`质量平衡文件大小和质量
   - `low`质量文件最小但可能有质量损失

4. **错误处理**：
   - 系统会自动重试失败的操作
   - 损坏的文件会被跳过
   - 详细的错误信息会记录在日志中

## 故障排除

1. **检测不到水印**：
   - 调整敏感度参数
   - 尝试不同的检测模式
   - 检查视频质量和分辨率

2. **清除效果不佳**：
   - 尝试不同的修复算法
   - 手动指定水印区域
   - 调整输出质量设置

3. **处理速度慢**：
   - 增加FFmpeg线程数
   - 降低输出质量
   - 减少并发处理数

4. **内存不足**：
   - 调整最大内存限制
   - 启用临时文件清理
   - 减少并发处理数
