<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts/core'
// 引入必要的渲染器
import { CanvasRenderer } from 'echarts/renderers'
import { Bar<PERSON>hart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'

// 注册必要的组件
echarts.use([
  CanvasRenderer,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
])

const chartRef = ref(null)
let chart = null
let resizeObserver = null

onMounted(() => {
  // 使用nextTick和setTimeout的组合确保DOM已经完全渲染并有宽高
  nextTick(() => {
    // 先设置明确的尺寸
    if (chartRef.value) {
      chartRef.value.style.width = '100%'
      chartRef.value.style.height = '300px'
    }

    // 然后延迟初始化图表，确保布局已经计算完成
    setTimeout(() => {
      initChart()
    }, 500)
  })
})

onBeforeUnmount(() => {
  // 清理资源
  if (chart) {
    chart.dispose()
    chart = null
  }

  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }

  window.removeEventListener('resize', handleResize)
})

const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

const initChart = () => {
  if (!chartRef.value) {
    console.warn('Chart container not found')
    return
  }

  // 确保容器有宽高
  const container = chartRef.value
  console.log('Chart container dimensions:', container.clientWidth, container.clientHeight)

  if (container.clientWidth === 0 || container.clientHeight === 0) {
    console.log('Setting explicit dimensions for chart container')
    container.style.width = '100%'
    container.style.height = '300px'
    // 强制重新计算布局
    container.offsetHeight
  }

  // 如果已经有chart实例，先销毁
  if (chart) {
    chart.dispose()
  }

  try {
    chart = echarts.init(container)

    const option = {
      title: {
        text: '发布统计'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['发布数量', '成功率']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: [
        {
          type: 'value',
          name: '发布数量'
        },
        {
          type: 'value',
          name: '成功率',
          min: 0,
          max: 100,
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: '发布数量',
          type: 'bar',
          data: [10, 15, 20, 25, 30, 35, 40]
        },
        {
          name: '成功率',
          type: 'line',
          yAxisIndex: 1,
          data: [90, 85, 95, 92, 88, 96, 93]
        }
      ]
    }

    chart.setOption(option)

    // 使用ResizeObserver监听容器大小变化
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        if (chart) {
          chart.resize()
        }
      })
      resizeObserver.observe(container)
    } else {
      // 兼容性处理
      window.addEventListener('resize', handleResize)
    }

    console.log('Chart initialized successfully')
  } catch (error) {
    console.error('Failed to initialize chart:', error)
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 300px;
  min-height: 300px; /* 确保最小高度 */
}
</style>
