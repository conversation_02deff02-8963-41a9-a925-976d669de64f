# 账号显示格式优化

## 改进概述

优化对标账号管理中"我们的账号"下拉框的显示格式，从原来的格式改为更清晰的"平台-名称"格式。

## 显示格式对比

### 改进前
```
youtube - YouTube测试账号
tiktok - TikTok测试账号  
instagram - Instagram测试账号
```

### 改进后
```
YouTube-YouTube测试账号
TikTok-TikTok测试账号
Instagram-Instagram测试账号
```

## 技术实现

### 1. 平台名称映射

创建平台ID到显示名称的映射表：

```javascript
const getPlatformDisplayName = (platformId: string) => {
  const platformMap = {
    youtube: 'YouTube',
    tiktok: 'TikTok', 
    instagram: 'Instagram',
    weibo: '微博',
    xiaohongshu: '小红书',
    douyin: '抖音',
    kuaishou: '快手',
    bilibili: 'B站'
  }
  return platformMap[platformId as keyof typeof platformMap] || platformId
}
```

### 2. 账号标签格式化

创建统一的账号标签格式化函数：

```javascript
const formatAccountLabel = (account: any) => {
  const platform = getPlatformDisplayName(account.platform_id || account.platform || 'unknown')
  const name = account.display_name || account.username || '未命名账号'
  return `${platform}-${name}`
}
```

### 3. 应用到组件

在下拉框选项中使用格式化函数：

```vue
<el-option
  v-for="account in ourAccounts"
  :key="account.id"
  :label="formatAccountLabel(account)"
  :value="account.id"
/>
```

## 改进优势

### 1. 视觉清晰度
- **更紧凑**: 去掉空格，减少视觉干扰
- **更统一**: 使用连字符分隔，格式一致
- **更易读**: 平台名称标准化，识别度高

### 2. 用户体验
- **快速识别**: 平台名称在前，便于快速定位
- **减少歧义**: 标准化的平台名称，避免混淆
- **美观整洁**: 格式统一，界面更美观

### 3. 国际化友好
- **中英文混合**: 支持中文平台名称（微博、小红书等）
- **扩展性强**: 易于添加新平台的显示名称
- **本地化**: 符合中文用户的阅读习惯

## 显示效果示例

### 真实数据示例
```
YouTube-科技频道
TikTok-生活达人
Instagram-美食博主
微博-新闻资讯
小红书-时尚穿搭
抖音-搞笑视频
快手-农村生活
B站-学习UP主
```

### 边界情况处理
```
未知平台-测试账号        # 未知平台ID
YouTube-未命名账号       # 缺少显示名称
unknown-未命名账号       # 完全缺失信息
```

## 代码变更

### 修改文件列表

1. **frontend/src/views/social/BenchmarkAccounts.vue**
   - 添加平台名称映射函数
   - 添加账号标签格式化函数
   - 更新下拉框显示格式

2. **frontend/src/views/doc/components/CreateBenchmarkDialog.vue**
   - 添加相同的格式化函数
   - 更新下拉框显示格式

### 函数复用

两个组件都使用相同的格式化逻辑，确保显示一致性：

```javascript
// 平台名称映射
const getPlatformDisplayName = (platformId: string) => { ... }

// 账号标签格式化  
const formatAccountLabel = (account: any) => { ... }
```

## 扩展性设计

### 1. 新平台支持

添加新平台只需在映射表中增加条目：

```javascript
const platformMap = {
  // 现有平台...
  threads: 'Threads',        // Meta的新平台
  mastodon: 'Mastodon',      // 去中心化社交
  linkedin: 'LinkedIn'       // 职业社交
}
```

### 2. 自定义格式

可以根据需要调整显示格式：

```javascript
// 选项1: 带图标
return `${getIcon(platform)} ${platform}-${name}`

// 选项2: 带状态
return `${platform}-${name} (${status})`

// 选项3: 简化版
return `${platform}/${name}`
```

### 3. 多语言支持

可以根据语言环境显示不同的平台名称：

```javascript
const getPlatformDisplayName = (platformId: string, locale: string) => {
  const platformMaps = {
    'zh-CN': { youtube: 'YouTube', weibo: '微博' },
    'en-US': { youtube: 'YouTube', weibo: 'Weibo' }
  }
  return platformMaps[locale]?.[platformId] || platformId
}
```

## 测试验证

### 1. 显示测试

验证不同平台账号的显示效果：
- [x] YouTube账号显示正确
- [x] 中文平台名称显示正确
- [x] 缺失信息的容错处理
- [x] 格式统一性

### 2. 功能测试

确认格式化不影响功能：
- [x] 账号选择功能正常
- [x] 对标账号创建正常
- [x] 数据传递正确
- [x] ID映射正确

### 3. 边界测试

测试各种边界情况：
- [x] 空账号列表
- [x] 缺失字段处理
- [x] 特殊字符处理
- [x] 长名称处理

## 用户反馈

### 预期改进效果

1. **视觉体验**: 界面更整洁美观
2. **操作效率**: 快速识别和选择账号
3. **认知负担**: 减少理解成本
4. **错误率**: 降低选择错误的概率

### 后续优化方向

1. **图标支持**: 为每个平台添加图标
2. **颜色区分**: 不同平台使用不同颜色
3. **分组显示**: 按平台分组显示账号
4. **搜索优化**: 支持平台名称搜索

## 总结

通过优化账号显示格式，我们实现了：

1. **格式统一**: 所有账号使用"平台-名称"格式
2. **平台标准化**: 使用标准的平台显示名称
3. **代码复用**: 统一的格式化函数
4. **扩展性**: 易于添加新平台支持
5. **用户体验**: 更清晰的视觉效果

这个改进提升了对标账号管理功能的易用性和美观度，为用户提供了更好的操作体验。

---

**注意**: 这个改进保持了功能的完整性，只是优化了显示效果，不影响数据处理和业务逻辑。
