"""
Consul服务发现模块
"""

import logging
import random
from typing import Dict, List, Optional, Tuple
import consul

logger = logging.getLogger(__name__)

class ConsulDiscovery:
    """Consul服务发现类"""

    def __init__(self, consul_host: str, consul_port: int):
        """初始化Consul服务发现

        Args:
            consul_host: Consul主机地址
            consul_port: Consul端口
        """
        self.consul_client = consul.Consul(host=consul_host, port=consul_port)
        logger.info(f"Consul服务发现初始化，连接到: {consul_host}:{consul_port}")

    def get_service(self, service_name: str) -> Optional[Tuple[str, int]]:
        """获取服务实例

        Args:
            service_name: 服务名称

        Returns:
            服务地址和端口的元组，如果没有找到则返回None
        """
        try:
            # 从Consul获取服务列表
            _, services = self.consul_client.catalog.service(service_name)

            if not services:
                logger.warning(f"未找到服务: {service_name}")
                return None

            # 随机选择一个服务实例
            service = random.choice(services)
            address = service["ServiceAddress"] or service["Address"]
            port = service["ServicePort"]

            logger.info(f"已发现服务: {service_name} at {address}:{port}")
            return address, port

        except Exception as e:
            logger.error(f"获取服务实例异常: {str(e)}", exc_info=True)
            return None

    def get_all_services(self, service_name: str) -> Dict[str, Dict]:
        """获取所有服务实例

        Args:
            service_name: 服务名称

        Returns:
            服务ID到服务信息的映射字典
        """
        try:
            # 从Consul获取服务列表
            _, services = self.consul_client.catalog.service(service_name)

            if not services:
                logger.warning(f"未找到服务: {service_name}")
                return {}

            # 构建服务映射
            result = {}
            for service in services:
                service_id = service["ServiceID"]
                address = service["ServiceAddress"] or service["Address"]
                port = service["ServicePort"]

                # 获取服务标签
                tags = service.get("ServiceTags", [])

                # 尝试从标签中获取服务名称
                name = service_name
                for tag in tags:
                    if tag.startswith("name="):
                        name = tag.split("=")[1]
                        break

                result[service_id] = {
                    "id": service_id,
                    "name": name,
                    "host": address,
                    "port": port,
                    "tags": tags
                }

            logger.info(f"已发现 {len(result)} 个 {service_name} 服务实例")
            return result

        except Exception as e:
            logger.error(f"获取所有服务实例异常: {str(e)}", exc_info=True)
            return {}