"""
Core服务配置
"""

import os
import logging
import yaml
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class CoreSettings:
    """Core服务配置类"""

    def __init__(self, config_path: Optional[str] = None):
        """初始化配置

        Args:
            config_path: 配置文件路径，默认为None使用默认配置
        """
        # 默认配置
        self.core_id = os.environ.get("CORE_ID", "core-default")
        self.core_name = os.environ.get("CORE_NAME", f"Core-{self.core_id}")  # 添加服务显示名称
        self.service_name = os.environ.get("SERVICE_NAME", "thunderhub-core")  # 添加服务名称
        self.grpc_port = int(os.environ.get("GRPC_PORT", "50051"))
        self.rest_port = int(os.environ.get("REST_PORT", "8080"))
        self.consul_host = os.environ.get("CONSUL_HOST", "localhost")
        self.consul_port = int(os.environ.get("CONSUL_PORT", "8500"))
        self.ldconsole_path = os.environ.get("LDCONSOLE_PATH", "C:\\LDPlayer\\LDPlayer9\\ldconsole.exe")
        self.max_devices = int(os.environ.get("MAX_DEVICES", "10"))
        self.redis_url = os.environ.get("REDIS_URL", "redis://localhost:6379/0")
        self.log_level = os.environ.get("LOG_LEVEL", "INFO")
        self.device_sync_interval = int(os.environ.get("DEVICE_SYNC_INTERVAL", "3600"))
        self.backend_url = os.environ.get("BACKEND_URL", "http://localhost:8000")

        # 如果提供了配置文件，从文件加载配置
        if config_path and os.path.exists(config_path):
            self._load_from_file(config_path)
            logger.info(f"从配置文件 {config_path} 加载配置")
        else:
            logger.warning(f"配置文件不存在或未提供: {config_path}")

        logger.info(f"Core服务配置加载完成，ID: {self.core_id}, gRPC端口: {self.grpc_port}")

    def _load_from_file(self, config_path: str) -> None:
        """从文件加载配置

        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 更新配置
            if config:
                # 核心配置
                if 'core' in config:
                    core_config = config['core']
                    self.core_id = core_config.get("id", self.core_id)
                    self.core_name = core_config.get("name", self.core_name)  # 读取服务显示名称
                    self.service_name = core_config.get("service_name", self.service_name)  # 读取服务名称
                    self.grpc_port = int(core_config.get("grpc_port", self.grpc_port))
                    self.rest_port = int(core_config.get("rest_port", self.rest_port))

                # 设备配置
                if 'devices' in config:
                    devices_config = config['devices']
                    self.ldconsole_path = devices_config.get("ldconsole_path", self.ldconsole_path)
                    self.max_devices = int(devices_config.get("max_devices", self.max_devices))

                    # 设备同步配置
                    if 'sync' in devices_config:
                        sync_config = devices_config['sync']
                        self.device_sync_interval = int(sync_config.get("interval", self.device_sync_interval))

                # 服务配置
                if 'services' in config:
                    services_config = config['services']

                    # Consul配置
                    if 'consul' in services_config:
                        consul_config = services_config['consul']
                        self.consul_host = consul_config.get("host", self.consul_host)
                        self.consul_port = int(consul_config.get("port", self.consul_port))

                    # Redis配置
                    if 'redis' in services_config:
                        redis_config = services_config['redis']
                        self.redis_url = redis_config.get("url", self.redis_url)

                    # 后端服务配置
                    if 'backend' in services_config:
                        backend_config = services_config['backend']
                        self.backend_url = backend_config.get("url", self.backend_url)

                # 日志配置
                if 'logging' in config:
                    logging_config = config['logging']
                    self.log_level = logging_config.get("level", self.log_level)

            logger.info(f"从文件{config_path}加载配置成功")

        except Exception as e:
            logger.error(f"从文件{config_path}加载配置失败: {str(e)}", exc_info=True)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "core_id": self.core_id,
            "core_name": self.core_name,
            "service_name": self.service_name,
            "grpc_port": self.grpc_port,
            "rest_port": self.rest_port,
            "ldconsole_path": self.ldconsole_path,
            "max_devices": self.max_devices,
            "consul_host": self.consul_host,
            "consul_port": self.consul_port,
            "redis_url": self.redis_url,
            "log_level": self.log_level,
            "device_sync_interval": self.device_sync_interval,
            "backend_url": self.backend_url
        }

