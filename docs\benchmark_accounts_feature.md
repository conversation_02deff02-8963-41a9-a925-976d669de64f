# 对标账号管理功能

## 功能概述

对标账号管理功能是文件管理中心的核心功能之一，用于管理我们现有账号对应的竞品账号信息。通过对标账号管理，可以：

- 跟踪竞品账号的表现数据
- 分析不同类型的内容策略
- 监控行业趋势和热点
- 为内容创作提供参考

## 功能特性

### 🎯 核心功能

1. **对标账号管理**
   - 添加、编辑、删除对标账号
   - 支持多平台账号管理
   - 灵活的分类和标签系统
   - 优先级设置和状态管理

2. **数据跟踪**
   - 粉丝数、关注数、发布数
   - 平均观看数、点赞数
   - 互动率、增长率统计
   - 最后发布时间记录

3. **智能分析**
   - 按平台、类型、状态统计
   - 表现最佳账号排行
   - 数据趋势分析
   - 自动数据获取（开发中）

### 📊 对标类型

- **原创 (Original)**: 完全原创内容的账号
- **二创 (Recreate)**: 基于原内容进行创作的账号  
- **搬运 (Repost)**: 直接转载内容的账号

### 🌐 支持平台

- YouTube
- TikTok  
- Instagram
- 微博
- 小红书
- 抖音
- 快手
- B站

## 数据模型

### 对标账号数据结构

```javascript
{
  "_id": ObjectId,                    // 对标账号ID
  "our_account_id": String,           // 我们的账号ID
  "platform": String,                // 平台名称
  "account_name": String,             // 对标账号名称
  "account_url": String,              // 对标账号链接
  "benchmark_type": String,           // 对标类型: original/recreate/repost
  "description": String,              // 账号描述
  "avatar_url": String,               // 头像URL
  "account_data": {                   // 账号数据
    "followers": Number,              // 粉丝数
    "following": Number,              // 关注数
    "posts_count": Number,            // 发布数
    "avg_views": Number,              // 平均观看数
    "avg_likes": Number,              // 平均点赞数
    "engagement_rate": Number,        // 互动率
    "last_post_date": Date,           // 最后发布时间
    "growth_rate": Number             // 增长率
  },
  "tags": [String],                   // 标签
  "priority": Number,                 // 优先级 1-5
  "status": String,                   // 状态: active/inactive/monitoring
  "notes": String,                    // 备注
  "created_at": Date,                 // 创建时间
  "updated_at": Date,                 // 更新时间
  "created_by": String                // 创建用户
}
```

## API接口

### 对标账号管理

#### 获取对标账号列表
```
GET /api/v1/benchmark/accounts
参数:
- page: 页码
- limit: 每页数量
- our_account_id: 我们的账号ID过滤
- platform: 平台过滤
- benchmark_type: 对标类型过滤
- status: 状态过滤
- search: 搜索关键词
```

#### 创建对标账号
```
POST /api/v1/benchmark/accounts
请求体:
{
  "our_account_id": "账号ID",
  "platform": "平台名称",
  "account_name": "账号名称",
  "account_url": "账号链接",
  "benchmark_type": "对标类型",
  "description": "账号描述",
  "tags": ["标签1", "标签2"],
  "priority": 3
}
```

#### 更新对标账号
```
PUT /api/v1/benchmark/accounts/{account_id}
请求体: 部分更新字段
```

#### 删除对标账号
```
DELETE /api/v1/benchmark/accounts/{account_id}
```

#### 获取统计信息
```
GET /api/v1/benchmark/stats
参数:
- our_account_id: 可选，按账号过滤
```

#### 更新账号数据
```
POST /api/v1/benchmark/accounts/{account_id}/update-data
请求体:
{
  "followers": 粉丝数,
  "following": 关注数,
  "posts_count": 发布数,
  "avg_views": 平均观看数,
  "avg_likes": 平均点赞数,
  "engagement_rate": 互动率,
  "growth_rate": 增长率,
  "last_post_date": "最后发布时间"
}
```

## 前端组件

### 主要组件结构

```
BenchmarkAccountManager.vue (主管理页面)
├── CreateBenchmarkDialog.vue (创建对话框)
├── EditBenchmarkDialog.vue (编辑对话框)
├── UpdateDataDialog.vue (更新数据对话框)
└── AccountDetailDialog.vue (详情对话框)
```

### 功能特性

1. **智能表单**
   - URL自动识别平台
   - 常用标签快速选择
   - 表单验证和提示

2. **数据展示**
   - 卡片式统计展示
   - 表格列表管理
   - 实时数据更新

3. **交互体验**
   - 拖拽优先级调整
   - 批量操作支持
   - 搜索和筛选

## 使用指南

### 1. 添加对标账号

1. 在文件管理中心点击"对标账号"按钮
2. 点击"添加对标账号"
3. 选择我们的账号
4. 输入对标账号信息
5. 设置对标类型和优先级
6. 保存创建

### 2. 管理对标账号

1. 在对标账号列表中查看所有账号
2. 使用筛选器按平台、类型筛选
3. 点击"编辑"修改账号信息
4. 点击"更新数据"刷新账号数据
5. 点击"详情"查看完整信息

### 3. 数据分析

1. 查看统计卡片了解总体情况
2. 分析不同类型账号的表现
3. 关注高优先级账号的动态
4. 定期更新账号数据

## 开发计划

### 已完成功能 ✅

- [x] 基础CRUD操作
- [x] 多平台支持
- [x] 数据统计展示
- [x] 前端管理界面
- [x] 搜索和筛选
- [x] 优先级管理

### 开发中功能 🚧

- [ ] 自动数据获取
- [ ] 数据趋势图表
- [ ] 内容分析功能
- [ ] 定时监控任务

### 计划功能 📋

- [ ] AI内容推荐
- [ ] 竞品分析报告
- [ ] 数据导出功能
- [ ] 移动端适配

## 技术实现

### 后端技术栈

- **FastAPI**: Web框架
- **MongoDB**: 数据存储
- **Pydantic**: 数据验证
- **Python**: 开发语言

### 前端技术栈

- **Vue 3**: 前端框架
- **Element Plus**: UI组件库
- **TypeScript**: 类型安全
- **Axios**: HTTP客户端

### 数据库设计

- **benchmark_accounts**: 对标账号主表
- **benchmark_account_groups**: 账号分组表（预留）
- **account_data_history**: 数据历史记录（预留）

## 注意事项

1. **数据隐私**: 确保对标账号数据的合规使用
2. **更新频率**: 建议定期更新账号数据以保持准确性
3. **平台限制**: 遵守各平台的API使用规范
4. **存储优化**: 大量数据时考虑分页和缓存策略

## 故障排除

### 常见问题

1. **账号URL无法识别**
   - 检查URL格式是否正确
   - 确认平台是否支持
   - 手动选择平台类型

2. **数据更新失败**
   - 检查网络连接
   - 验证账号权限
   - 查看错误日志

3. **页面加载缓慢**
   - 减少每页显示数量
   - 使用筛选器缩小范围
   - 检查数据库性能

---

**注意**: 这是对标账号管理功能的完整实现，包含了后端API、前端界面和数据管理的所有核心功能。
