# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from app.proto import device_pb2 as device__pb2


class DeviceServiceStub(object):
    """设备服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetDeviceList = channel.unary_unary(
                '/device.DeviceService/GetDeviceList',
                request_serializer=device__pb2.DeviceListRequest.SerializeToString,
                response_deserializer=device__pb2.DeviceListResponse.FromString,
                )
        self.StartDevice = channel.unary_unary(
                '/device.DeviceService/StartDevice',
                request_serializer=device__pb2.DeviceRequest.SerializeToString,
                response_deserializer=device__pb2.DeviceResponse.FromString,
                )
        self.StopDevice = channel.unary_unary(
                '/device.DeviceService/StopDevice',
                request_serializer=device__pb2.DeviceRequest.SerializeToString,
                response_deserializer=device__pb2.DeviceResponse.FromString,
                )
        self.RestartDevice = channel.unary_unary(
                '/device.DeviceService/RestartDevice',
                request_serializer=device__pb2.DeviceRequest.SerializeToString,
                response_deserializer=device__pb2.DeviceResponse.FromString,
                )
        self.GetDeviceInfo = channel.unary_unary(
                '/device.DeviceService/GetDeviceInfo',
                request_serializer=device__pb2.DeviceRequest.SerializeToString,
                response_deserializer=device__pb2.DeviceInfoResponse.FromString,
                )
        self.CreateDevice = channel.unary_unary(
                '/device.DeviceService/CreateDevice',
                request_serializer=device__pb2.CreateDeviceRequest.SerializeToString,
                response_deserializer=device__pb2.CreateDeviceResponse.FromString,
                )
        self.RemoveDevice = channel.unary_unary(
                '/device.DeviceService/RemoveDevice',
                request_serializer=device__pb2.DeviceRequest.SerializeToString,
                response_deserializer=device__pb2.DeviceResponse.FromString,
                )
        self.ExecuteCommand = channel.unary_unary(
                '/device.DeviceService/ExecuteCommand',
                request_serializer=device__pb2.CommandRequest.SerializeToString,
                response_deserializer=device__pb2.CommandResponse.FromString,
                )


class DeviceServiceServicer(object):
    """设备服务
    """

    def GetDeviceList(self, request, context):
        """获取设备列表
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartDevice(self, request, context):
        """启动设备
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopDevice(self, request, context):
        """停止设备
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RestartDevice(self, request, context):
        """重启设备
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeviceInfo(self, request, context):
        """获取设备信息
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDevice(self, request, context):
        """创建新设备
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RemoveDevice(self, request, context):
        """删除设备
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ExecuteCommand(self, request, context):
        """执行设备命令
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DeviceServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetDeviceList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeviceList,
                    request_deserializer=device__pb2.DeviceListRequest.FromString,
                    response_serializer=device__pb2.DeviceListResponse.SerializeToString,
            ),
            'StartDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.StartDevice,
                    request_deserializer=device__pb2.DeviceRequest.FromString,
                    response_serializer=device__pb2.DeviceResponse.SerializeToString,
            ),
            'StopDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.StopDevice,
                    request_deserializer=device__pb2.DeviceRequest.FromString,
                    response_serializer=device__pb2.DeviceResponse.SerializeToString,
            ),
            'RestartDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.RestartDevice,
                    request_deserializer=device__pb2.DeviceRequest.FromString,
                    response_serializer=device__pb2.DeviceResponse.SerializeToString,
            ),
            'GetDeviceInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeviceInfo,
                    request_deserializer=device__pb2.DeviceRequest.FromString,
                    response_serializer=device__pb2.DeviceInfoResponse.SerializeToString,
            ),
            'CreateDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDevice,
                    request_deserializer=device__pb2.CreateDeviceRequest.FromString,
                    response_serializer=device__pb2.CreateDeviceResponse.SerializeToString,
            ),
            'RemoveDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.RemoveDevice,
                    request_deserializer=device__pb2.DeviceRequest.FromString,
                    response_serializer=device__pb2.DeviceResponse.SerializeToString,
            ),
            'ExecuteCommand': grpc.unary_unary_rpc_method_handler(
                    servicer.ExecuteCommand,
                    request_deserializer=device__pb2.CommandRequest.FromString,
                    response_serializer=device__pb2.CommandResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'device.DeviceService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DeviceService(object):
    """设备服务
    """

    @staticmethod
    def GetDeviceList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/device.DeviceService/GetDeviceList',
            device__pb2.DeviceListRequest.SerializeToString,
            device__pb2.DeviceListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/device.DeviceService/StartDevice',
            device__pb2.DeviceRequest.SerializeToString,
            device__pb2.DeviceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StopDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/device.DeviceService/StopDevice',
            device__pb2.DeviceRequest.SerializeToString,
            device__pb2.DeviceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RestartDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/device.DeviceService/RestartDevice',
            device__pb2.DeviceRequest.SerializeToString,
            device__pb2.DeviceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDeviceInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/device.DeviceService/GetDeviceInfo',
            device__pb2.DeviceRequest.SerializeToString,
            device__pb2.DeviceInfoResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CreateDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/device.DeviceService/CreateDevice',
            device__pb2.CreateDeviceRequest.SerializeToString,
            device__pb2.CreateDeviceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RemoveDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/device.DeviceService/RemoveDevice',
            device__pb2.DeviceRequest.SerializeToString,
            device__pb2.DeviceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ExecuteCommand(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/device.DeviceService/ExecuteCommand',
            device__pb2.CommandRequest.SerializeToString,
            device__pb2.CommandResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
