development:
  allow_origins:
    - http://localhost:5173
    - http://127.0.0.1:5173
    - http://*************:5173
    - http://***************
    - ws://localhost:5173
    - ws://127.0.0.1:5173
    - ws://*************:5173
    - ws://***************
    - http://*************:8000
    - http://*************
    - https://*************
    - ws://*************
    - wss://*************

  allow_credentials: true
  allow_methods: ["*"]
  allow_headers: ["*"]
  expose_headers: ["*"]
  allow_origin_regex: "https?://(localhost|127\\.0\\.0\\.1|192\\.168\\.123\\.2|192\\.168\\.123\\.137)(:\\d+)?|ws(s)?://(localhost|127\\.0\\.0\\.1|192\\.168\\.123\\.2|192\\.168\\.123\\.137)(:\\d+)?"

production:
  allow_origins:
    - https://your-production-domain.com
    - wss://your-production-domain.com
  allow_credentials: true
  allow_methods: ["GET", "POST", "OPTIONS"]
  allow_headers: ["Authorization", "Content-Type"]
  expose_headers: []
  allow_origin_regex: null