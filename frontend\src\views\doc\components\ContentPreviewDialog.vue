<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`🎬 内容预览 - ${content?.title || '无标题'}`"
    width="800px"
    @close="handleClose"
  >
    <div class="content-preview" v-if="content">
      <!-- 内容信息 -->
      <div class="content-header">
        <div class="content-cover-section">
          <img 
            :src="content.cover_url" 
            :alt="content.title"
            class="preview-cover"
            @error="handleImageError"
          />
          <div class="content-type-badge">
            {{ getContentTypeText(content.content_type) }}
          </div>
        </div>
        
        <div class="content-info">
          <h3 class="content-title">{{ content.title || '无标题' }}</h3>
          
          <div class="content-meta">
            <div class="meta-row">
              <span class="meta-label">内容ID:</span>
              <span class="meta-value">{{ content.content_id }}</span>
            </div>
            <div class="meta-row">
              <span class="meta-label">来源平台:</span>
              <el-tag :type="getPlatformColor(content.platform)" size="small">
                {{ getPlatformText(content.platform) }}
              </el-tag>
            </div>
            <div class="meta-row">
              <span class="meta-label">来源账号:</span>
              <span class="meta-value">{{ content.account_name }}</span>
            </div>
            <div class="meta-row">
              <span class="meta-label">发布时间:</span>
              <span class="meta-value">{{ content.year }}年{{ content.month }}月</span>
            </div>
            <div class="meta-row">
              <span class="meta-label">采集时间:</span>
              <span class="meta-value">{{ formatDateTime(content.created_at) }}</span>
            </div>
          </div>

          <div class="content-stats">
            <div class="stat-item" v-if="content.like_count">
              <span class="stat-icon">👍</span>
              <span class="stat-text">{{ formatNumber(content.like_count) }} 点赞</span>
            </div>
            <div class="stat-item" v-if="content.view_count">
              <span class="stat-icon">👁️</span>
              <span class="stat-text">{{ formatNumber(content.view_count) }} 播放</span>
            </div>
            <div class="stat-item" v-if="content.file_size">
              <span class="stat-icon">💾</span>
              <span class="stat-text">{{ formatFileSize(content.file_size) }}</span>
            </div>
            <div class="stat-item" v-if="content.duration">
              <span class="stat-icon">⏱️</span>
              <span class="stat-text">{{ formatDuration(content.duration) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 链接信息 -->
      <div class="links-section">
        <h4>🔗 链接信息</h4>
        <div class="link-item">
          <span class="link-label">原始链接:</span>
          <div class="link-value">
            <el-input 
              :value="content.content_url" 
              readonly 
              size="small"
            >
              <template #append>
                <el-button @click="copyLink(content.content_url)" size="small">
                  复制
                </el-button>
              </template>
            </el-input>
          </div>
        </div>
        
        <div class="link-item" v-if="content.real_content_url">
          <span class="link-label">下载链接:</span>
          <div class="link-value">
            <el-input 
              :value="content.real_content_url" 
              readonly 
              size="small"
            >
              <template #append>
                <el-button @click="copyLink(content.real_content_url)" size="small">
                  复制
                </el-button>
              </template>
            </el-input>
          </div>
        </div>
        
        <div class="link-item" v-if="!content.real_content_url">
          <el-alert
            title="暂无下载链接"
            description="该内容还没有获取到真实的下载地址，请重新采集或检查内容状态"
            type="warning"
            :closable="false"
          />
        </div>
      </div>

      <!-- 下载状态 -->
      <div class="status-section">
        <h4>📊 下载状态</h4>
        <div class="status-info">
          <div class="status-item">
            <span class="status-label">当前状态:</span>
            <el-tag :type="getDownloadStatusColor(content.download_status)">
              {{ getDownloadStatusText(content.download_status) }}
            </el-tag>
          </div>
          
          <div class="status-item" v-if="content.download_path">
            <span class="status-label">下载路径:</span>
            <span class="status-value">{{ content.download_path }}</span>
          </div>
          
          <div class="status-item" v-if="content.updated_at">
            <span class="status-label">更新时间:</span>
            <span class="status-value">{{ formatDateTime(content.updated_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <el-button 
          type="primary" 
          @click="openOriginalLink"
          v-if="content.content_url"
        >
          <el-icon><Link /></el-icon>
          打开原始页面
        </el-button>
        
        <el-button 
          type="success" 
          @click="downloadContent"
          :disabled="!content.real_content_url || content.download_status === 'downloading'"
        >
          <el-icon><Download /></el-icon>
          立即下载
        </el-button>
        
        <el-button 
          type="warning" 
          @click="refreshContent"
        >
          <el-icon><Refresh /></el-icon>
          刷新信息
        </el-button>
        
        <el-button 
          type="info" 
          @click="editContent"
        >
          <el-icon><Edit /></el-icon>
          编辑信息
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Link, Download, Refresh, Edit } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  content: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const dialogVisible = ref(false)

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 工具方法
const formatDateTime = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDuration = (seconds: number) => {
  if (!seconds) return '未知'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const getContentTypeText = (type: string) => {
  const typeMap = {
    'video': '视频',
    'image': '图片',
    'audio': '音频',
    'text': '文本'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getPlatformText = (platform: string) => {
  const platformMap = {
    'douyin': '抖音',
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'instagram': 'Instagram'
  }
  return platformMap[platform as keyof typeof platformMap] || platform
}

const getPlatformColor = (platform: string) => {
  const colorMap = {
    'douyin': 'danger',
    'youtube': 'warning',
    'tiktok': 'info',
    'instagram': 'success'
  }
  return colorMap[platform as keyof typeof colorMap] || 'info'
}

const getDownloadStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'info',
    'downloading': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const getDownloadStatusText = (status: string) => {
  const textMap = {
    'pending': '待下载',
    'downloading': '下载中',
    'completed': '已完成',
    'failed': '失败'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMDAgNjBMMTIwIDgwSDgwTDEwMCA2MFoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'
}

// 事件处理
const copyLink = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const openOriginalLink = () => {
  if (props.content?.content_url) {
    window.open(props.content.content_url, '_blank')
  }
}

const downloadContent = () => {
  // TODO: 实现下载功能
  ElMessage.success('开始下载内容')
}

const refreshContent = () => {
  // TODO: 实现刷新功能
  ElMessage.info('刷新内容信息')
}

const editContent = () => {
  // TODO: 实现编辑功能
  ElMessage.info('编辑内容信息')
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.content-preview {
  max-height: 70vh;
  overflow-y: auto;
}

.content-header {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
}

.content-cover-section {
  position: relative;
  flex-shrink: 0;
}

.preview-cover {
  width: 200px;
  height: 120px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid #e4e7ed;
}

.content-type-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.content-info {
  flex: 1;
  min-width: 0;
}

.content-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.content-meta {
  margin-bottom: 16px;
}

.meta-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.meta-label {
  width: 80px;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
}

.meta-value {
  color: #303133;
  font-size: 14px;
}

.content-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.stat-icon {
  font-size: 16px;
}

.links-section,
.status-section {
  margin-bottom: 24px;
}

.links-section h4,
.status-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.link-item {
  margin-bottom: 12px;
}

.link-label {
  display: block;
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.link-value {
  width: 100%;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-label {
  width: 80px;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
}

.status-value {
  color: #303133;
  font-size: 14px;
}

.actions-section {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer {
  text-align: right;
}
</style>
