<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <!-- 基本信息 -->
      <h3 class="form-section-title">基本信息</h3>
      
      <el-form-item label="平台" prop="platform_id">
        <el-select v-model="form.platform_id" placeholder="选择平台" style="width: 100%">
          <el-option
            v-for="platform in platforms"
            :key="platform.id"
            :label="platform.name"
            :value="platform.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" placeholder="请输入用户名" />
      </el-form-item>
      
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          placeholder="请输入密码"
          show-password
        />
      </el-form-item>
      
      <el-form-item label="显示名称" prop="display_name">
        <el-input v-model="form.display_name" placeholder="请输入显示名称" />
      </el-form-item>
      
      <el-form-item label="恢复邮箱" prop="recovery_email">
        <el-input v-model="form.recovery_email" placeholder="请输入恢复邮箱" />
      </el-form-item>
      
      <el-form-item label="恢复码" prop="recovery_code">
        <el-input v-model="form.recovery_code" placeholder="请输入恢复码" />
      </el-form-item>
      
      <!-- 高级设置 -->
      <h3 class="form-section-title">高级设置</h3>
      
      <el-form-item label="Core服务" prop="core_service_id">
        <el-select v-model="form.core_service_id" placeholder="选择Core服务" style="width: 100%">
          <el-option
            v-for="service in coreServices"
            :key="service.id"
            :label="service.name"
            :value="service.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="form.status" placeholder="选择状态" style="width: 100%">
          <el-option label="活跃" value="active" />
          <el-option label="非活跃" value="inactive" />
          <el-option label="已暂停" value="suspended" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入标签"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </el-form-item>

      <el-form-item label="更新日期" prop="last_updated_date">
        <el-date-picker
          v-model="form.last_updated_date"
          type="date"
          placeholder="选择账号更新日期"
          style="width: 100%"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :clearable="true"
        />
        <div class="form-item-tip">
          设置账号的更新日期，系统将在接近更新日期前3天开始提醒
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { SocialAccount, SocialPlatform } from '@/types/social'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '添加账号'
  },
  formData: {
    type: Object as () => Partial<SocialAccount>,
    default: () => ({})
  },
  platforms: {
    type: Array as () => SocialPlatform[],
    default: () => []
  },
  coreServices: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'submit'])

// 对话框可见性
const dialogVisible = ref(props.visible)

// 监听visible属性变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})

// 监听dialogVisible变化，同步更新父组件的visible属性
watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 表单引用
const formRef = ref<FormInstance>()

// 提交状态
const submitting = ref(false)

// 表单数据
const form = reactive<Partial<SocialAccount>>({
  platform_id: '',
  username: '',
  password: '',
  display_name: '',
  recovery_email: '',
  recovery_code: '',
  core_service_id: '',
  status: 'active',
  tags: [],
  description: '',
  last_updated_date: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  platform_id: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  core_service_id: [
    { required: true, message: '请选择Core服务', trigger: 'change' }
  ]
})

// 监听formData变化，更新表单数据
watch(() => props.formData, (val) => {
  Object.keys(val).forEach(key => {
    form[key as keyof SocialAccount] = val[key as keyof SocialAccount]
  })
}, { immediate: true, deep: true })

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 提交表单数据
    emit('submit', { ...form })
    
    // 重置表单
    formRef.value.resetFields()
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section-title {
  margin-top: 20px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #409eff;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}
</style>
