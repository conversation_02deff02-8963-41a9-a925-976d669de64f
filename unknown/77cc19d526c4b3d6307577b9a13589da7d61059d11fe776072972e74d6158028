/**
 * 账号过期状态计算工具
 */

// 过期状态枚举
export enum ExpiryStatus {
  NORMAL = 'normal',        // 正常状态
  WARNING_3 = 'warning_3',  // 3天内过期警告
  WARNING_2 = 'warning_2',  // 2天内过期警告  
  WARNING_1 = 'warning_1',  // 1天内过期警告
  EXPIRED = 'expired'       // 已过期
}

// 过期状态信息
export interface ExpiryInfo {
  status: ExpiryStatus
  daysRemaining: number
  message: string
  color: string
  bgColor: string
}

/**
 * 计算账号过期状态
 * @param lastUpdatedDate 最后更新日期字符串
 * @returns 过期状态信息
 */
export function calculateExpiryStatus(lastUpdatedDate?: string): ExpiryInfo {
  // 如果没有设置更新日期，返回正常状态
  if (!lastUpdatedDate) {
    return {
      status: ExpiryStatus.NORMAL,
      daysRemaining: 0,
      message: '未设置更新日期',
      color: '#909399',
      bgColor: '#f4f4f5'
    }
  }

  const now = new Date()
  const updateDate = new Date(lastUpdatedDate)
  
  // 计算距离更新日期的天数（负数表示已过期）
  const timeDiff = updateDate.getTime() - now.getTime()
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24))

  // 根据剩余天数返回不同的状态
  if (daysDiff < 0) {
    // 已过期
    return {
      status: ExpiryStatus.EXPIRED,
      daysRemaining: daysDiff,
      message: `已过期 ${Math.abs(daysDiff)} 天`,
      color: '#ffffff',
      bgColor: '#f56c6c'
    }
  } else if (daysDiff === 0) {
    // 今天过期
    return {
      status: ExpiryStatus.WARNING_1,
      daysRemaining: daysDiff,
      message: '今天过期',
      color: '#ffffff',
      bgColor: '#e6a23c'
    }
  } else if (daysDiff === 1) {
    // 1天后过期
    return {
      status: ExpiryStatus.WARNING_1,
      daysRemaining: daysDiff,
      message: '1天后过期',
      color: '#ffffff',
      bgColor: '#e6a23c'
    }
  } else if (daysDiff === 2) {
    // 2天后过期
    return {
      status: ExpiryStatus.WARNING_2,
      daysRemaining: daysDiff,
      message: '2天后过期',
      color: '#ffffff',
      bgColor: '#f78989'
    }
  } else if (daysDiff === 3) {
    // 3天后过期
    return {
      status: ExpiryStatus.WARNING_3,
      daysRemaining: daysDiff,
      message: '3天后过期',
      color: '#606266',
      bgColor: '#fdf6ec'
    }
  } else {
    // 正常状态（超过3天）
    return {
      status: ExpiryStatus.NORMAL,
      daysRemaining: daysDiff,
      message: `${daysDiff}天后过期`,
      color: '#67c23a',
      bgColor: '#f0f9ff'
    }
  }
}

/**
 * 格式化日期显示
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString?: string): string {
  if (!dateString) return '-'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

/**
 * 获取过期状态的CSS类名
 * @param status 过期状态
 * @returns CSS类名
 */
export function getExpiryStatusClass(status: ExpiryStatus): string {
  switch (status) {
    case ExpiryStatus.EXPIRED:
      return 'expiry-expired'
    case ExpiryStatus.WARNING_1:
      return 'expiry-warning-1'
    case ExpiryStatus.WARNING_2:
      return 'expiry-warning-2'
    case ExpiryStatus.WARNING_3:
      return 'expiry-warning-3'
    default:
      return 'expiry-normal'
  }
}
