# IP管理模块关联设备功能

## 功能概述

为IP管理模块开发了关联设备功能，允许用户将代理IP与活跃状态的设备进行关联。该功能确保只有处于活跃状态（运行中、启动中）的设备才能被选择进行关联。

## 功能特性

### 1. 活跃账号设备过滤
- 只显示关联了状态为 `active`（活跃）的社交媒体账号的设备
- 通过查询 `social_accounts` 表找到活跃账号
- 通过查询 `device_account_mappings` 表找到关联了这些活跃账号的设备
- 确保只有有效的账号设备组合才能进行代理IP关联

### 2. 设备关联管理
- 支持一个代理IP关联多个设备
- 支持批量选择设备进行关联
- 显示设备详细信息（名称、ID、状态、类型、Core服务）
- 支持取消设备关联

### 3. 用户界面优化
- 直观的设备选择对话框
- 设备状态标签显示
- 关联设备数量统计
- 操作结果反馈

## 技术实现

### 后端API

#### 1. 获取活跃设备列表
```
GET /api/v1/proxy/devices/active
```
返回所有处于活跃状态的设备列表。

#### 2. 关联设备和代理IP
```
POST /api/v1/proxy/{proxy_id}/associate/{device_id}
```
将指定设备与代理IP进行关联。

#### 3. 取消设备关联
```
DELETE /api/v1/proxy/{proxy_id}/disassociate/{device_id}
```
取消指定设备与代理IP的关联。

### 数据模型

#### 活跃设备信息
```typescript
interface ActiveDevice {
  id: string          // 设备ID
  name: string        // 设备名称
  status: string      // 设备状态（running/starting）
  type: string        // 设备类型
  core_id: string     // Core服务ID
  updated_at: string  // 更新时间
}
```

#### 关联设备信息
```typescript
interface AssociatedDevice {
  device_id: string      // 设备ID
  mapping_id: string     // 关联映射ID
  device_name: string    // 设备名称
  device_status: string  // 设备状态
  device_type: string    // 设备类型
}
```

### 前端组件

#### 1. 设备关联对话框
- 位置：`frontend/src/views/device/ProxyManagement.vue`
- 功能：显示当前代理IP的关联设备列表，支持添加和移除关联

#### 2. 设备选择对话框
- 功能：显示活跃设备列表，支持多选进行批量关联
- 特性：实时状态显示、设备信息展示

## 使用说明

### 1. 查看设备关联
1. 在代理IP管理页面，点击任意代理IP行的"关联设备"按钮
2. 在弹出的对话框中查看当前关联的设备列表
3. 可以看到设备名称、ID、状态、类型等信息

### 2. 添加设备关联
1. 在设备关联对话框中，点击"添加设备关联"按钮
2. 在设备选择对话框中，查看所有活跃状态的设备
3. 选择要关联的设备（支持多选）
4. 点击"关联选中设备"按钮完成关联

### 3. 移除设备关联
1. 在设备关联对话框的设备列表中，点击对应设备的"移除"按钮
2. 确认操作后，设备关联将被取消

## 状态说明

### 设备状态
- **运行中** (`running`): 设备正常运行，可以进行关联
- **启动中** (`starting`): 设备正在启动，可以进行关联
- **已停止** (`stopped`): 设备已停止，不显示在关联列表中
- **错误** (`error`): 设备出现错误，不显示在关联列表中
- **未知** (`unknown`): 设备状态未知，不显示在关联列表中

### 关联状态
- **活跃** (`active`): 关联关系有效
- **非活跃** (`inactive`): 关联关系已取消

## 测试

### 运行测试脚本
```bash
python test_proxy_device_association.py
```

测试脚本将验证以下功能：
1. 获取活跃设备列表
2. 创建代理IP和设备关联
3. 验证关联结果
4. 取消设备关联
5. 清理测试数据

### 手动测试
1. 启动所有服务：`start_services.bat`
2. 访问前端页面：`http://localhost:3000`
3. 进入代理IP管理页面
4. 测试设备关联功能

## 注意事项

1. **设备状态实时性**：设备状态信息来自数据库，确保Core服务正常同步设备状态
2. **权限控制**：所有API都需要用户认证
3. **错误处理**：前端会显示操作结果，后端会记录错误日志
4. **数据一致性**：关联关系存储在独立的映射表中，支持历史记录

## 状态说明

### 账号状态
- **活跃** (`active`): 账号状态正常，关联的设备可以进行代理IP关联
- **非活跃** (`inactive`): 账号状态非活跃，关联的设备不显示在关联列表中
- **已暂停** (`suspended`): 账号已暂停，关联的设备不显示在关联列表中

### 设备筛选逻辑
1. 查询 `social_accounts` 表，找到所有 `status = 'active'` 的账号
2. 查询 `device_account_mappings` 表，找到关联了这些活跃账号的设备映射
3. 查询 `devices` 表，获取这些设备的详细信息
4. 只有关联了活跃账号的设备才会显示在代理IP关联列表中

### 关联状态
- **活跃** (`active`): 关联关系有效
- **非活跃** (`inactive`): 关联关系已取消

## 未来改进

1. 支持设备分组关联
2. 添加关联历史记录查看
3. 支持关联规则配置
4. 添加设备状态变更通知
5. 支持批量取消关联
