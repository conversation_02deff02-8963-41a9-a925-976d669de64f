<template>
  <div class="task-result">
    <div class="result-header">
      <h1>📊 任务执行结果</h1>
      <p class="header-description">查看任务的详细执行结果和相关信息</p>
    </div>

    <div class="result-content" v-loading="loading">
      <el-card v-if="taskData" class="task-info-card">
        <template #header>
          <div class="card-header">
            <span>📝 任务信息</span>
            <el-tag :type="getStatusType(taskData.status)" size="large">
              {{ getStatusText(taskData.status) }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ taskData.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(taskData.status)">
              {{ getStatusText(taskData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="平台">{{ taskData.platform_name || taskData.platform_id }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ taskData.account_name || taskData.account_id }}</el-descriptions-item>
          <el-descriptions-item label="内容路径" :span="2">{{ taskData.content_path }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(taskData.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatTime(taskData.start_time) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ formatTime(taskData.end_time) }}</el-descriptions-item>
          <el-descriptions-item label="执行耗时">{{ calculateDuration(taskData.start_time, taskData.end_time) }}</el-descriptions-item>
          <el-descriptions-item label="进度" :span="2">
            <el-progress 
              :percentage="taskData.progress || 0" 
              :status="getProgressStatus(taskData.status)"
              :stroke-width="8"
            />
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card class="logs-card" v-if="taskData">
        <template #header>
          <div class="card-header">
            <span>📋 执行日志</span>
            <el-button type="primary" size="small" @click="refreshLogs" :loading="logsLoading">
              刷新日志
            </el-button>
          </div>
        </template>

        <div class="logs-content">
          <el-timeline v-if="logs.length > 0">
            <el-timeline-item
              v-for="(log, index) in logs"
              :key="index"
              :timestamp="formatTime(log.timestamp)"
              :type="getLogType(log.level)"
              size="small"
            >
              <div class="log-message">
                <span class="log-level" :class="`log-level-${log.level}`">
                  [{{ log.level.toUpperCase() }}]
                </span>
                {{ log.message }}
              </div>
            </el-timeline-item>
          </el-timeline>
          <el-empty v-else description="暂无执行日志" />
        </div>
      </el-card>

      <el-card class="actions-card" v-if="taskData">
        <template #header>
          <span>🔧 操作</span>
        </template>

        <div class="action-buttons">
          <el-button type="primary" @click="goBack">
            返回
          </el-button>
          <el-button type="info" @click="downloadLogs" v-if="logs.length > 0">
            下载日志
          </el-button>
          <el-button type="success" @click="showUploadStatusDialog" v-if="isYouTubeTask">
            查看上传状态
          </el-button>
        </div>
      </el-card>

      <el-empty v-if="!taskData && !loading" description="未找到任务信息" />
    </div>

    <!-- 上传状态弹窗 -->
    <el-dialog
      v-model="uploadStatusDialogVisible"
      title="📊 上传状态"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="upload-status-content" v-loading="uploadStatusLoading">
        <!-- 上传进度 -->
        <el-card class="progress-card">
          <template #header>
            <span>📈 上传进度</span>
          </template>

          <div class="upload-progress">
            <el-progress
              :percentage="uploadProgress"
              :status="getUploadProgressStatus()"
              :stroke-width="12"
              text-inside
            />
            <div class="progress-info">
              <span class="progress-text">{{ getUploadProgressText() }}</span>
              <span class="progress-time">{{ formatTime(uploadStatusData.updated_at) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 上传详情 -->
        <el-card class="details-card">
          <template #header>
            <span>📝 上传详情</span>
          </template>

          <el-descriptions :column="2" border v-if="uploadStatusData">
            <el-descriptions-item label="视频标题">{{ uploadStatusData.title || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="上传状态">
              <el-tag :type="getUploadStatusType()">{{ getUploadStatusText() }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ formatFileSize(uploadStatusData.file_size) }}</el-descriptions-item>
            <el-descriptions-item label="视频时长">{{ formatDuration(uploadStatusData.duration) }}</el-descriptions-item>
            <el-descriptions-item label="开始时间">{{ formatTime(uploadStatusData.start_time) }}</el-descriptions-item>
            <el-descriptions-item label="预计完成">{{ formatTime(uploadStatusData.estimated_end_time) }}</el-descriptions-item>
            <el-descriptions-item label="YouTube链接" :span="2" v-if="uploadStatusData.youtube_url">
              <el-link :href="uploadStatusData.youtube_url" target="_blank" type="primary">
                {{ uploadStatusData.youtube_url }}
              </el-link>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 实时日志 -->
        <el-card class="logs-card">
          <template #header>
            <div class="card-header">
              <span>📋 实时日志</span>
              <el-button type="primary" size="small" @click="refreshUploadLogs" :loading="uploadLogsLoading">
                刷新
              </el-button>
            </div>
          </template>

          <div class="upload-logs">
            <el-timeline v-if="uploadLogs.length > 0">
              <el-timeline-item
                v-for="(log, index) in uploadLogs"
                :key="index"
                :timestamp="formatTime(log.timestamp)"
                :type="getLogType(log.level)"
                size="small"
              >
                <div class="log-message">
                  <span class="log-level" :class="`log-level-${log.level}`">
                    [{{ log.level.toUpperCase() }}]
                  </span>
                  {{ log.message }}
                </div>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无上传日志" />
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadStatusDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="refreshUploadStatus" :loading="uploadStatusLoading">
            刷新状态
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getTaskDetail, getTaskLogs } from '@/api/task'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const logsLoading = ref(false)
const taskData = ref(null)
const logs = ref([])

// 计算属性
const isYouTubeTask = computed(() => {
  return taskData.value && (
    taskData.value.platform_name === '油管' || 
    taskData.value.platform_id === 'youtube' || 
    taskData.value.platform_id === '681efeeecd836bd64b9c2a1e'
  )
})

// 初始化
onMounted(() => {
  const taskId = route.query.taskId as string
  if (taskId) {
    fetchTaskData(taskId)
  } else {
    ElMessage.error('缺少任务ID参数')
  }
})

// 获取任务数据
const fetchTaskData = async (taskId: string) => {
  try {
    loading.value = true
    console.log('获取任务数据:', taskId)

    // 调用真实的API获取任务详情
    const response = await getTaskDetail(taskId)
    console.log('任务详情响应:', response)

    if (response && response.data && response.data.task) {
      taskData.value = response.data.task
      console.log('任务数据:', taskData.value)
    } else {
      console.warn('API响应数据格式异常:', response)
      ElMessage.warning('未找到任务信息')
      return
    }

    // 获取日志
    await fetchLogs(taskId)

  } catch (error) {
    console.error('获取任务数据失败:', error)
    ElMessage.error('获取任务数据失败')
  } finally {
    loading.value = false
  }
}

// 获取任务日志
const fetchLogs = async (taskId: string) => {
  try {
    logsLoading.value = true
    console.log('获取任务日志:', taskId)

    // 调用真实的API获取任务日志
    const response = await getTaskLogs(taskId)
    console.log('任务日志响应:', response)

    if (response && response.data && response.data.logs) {
      logs.value = response.data.logs
      console.log('任务日志数据:', logs.value)
    } else {
      console.warn('日志API响应数据格式异常:', response)
      logs.value = []
    }

  } catch (error) {
    console.error('获取任务日志失败:', error)
    ElMessage.warning('获取任务日志失败')
    logs.value = []
  } finally {
    logsLoading.value = false
  }
}

// 刷新日志
const refreshLogs = () => {
  if (taskData.value) {
    fetchLogs(taskData.value.id)
  }
}

// 返回
const goBack = () => {
  router.back()
}

// 下载日志
const downloadLogs = () => {
  const logText = logs.value.map(log => 
    `[${formatTime(log.timestamp)}] [${log.level.toUpperCase()}] ${log.message}`
  ).join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `task_${taskData.value.id}_logs.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// 查看上传状态
const viewUploadStatus = () => {
  router.push('/social/upload-status')
}

// 工具函数
const getStatusType = (status) => {
  const types = {
    'pending': 'info',
    'running': 'primary',
    'paused': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'canceled': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '等待中',
    'running': '运行中',
    'paused': '已暂停',
    'completed': '已完成',
    'failed': '失败',
    'canceled': '已取消'
  }
  return texts[status] || status
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return ''
}

const getLogType = (level) => {
  const types = {
    'success': 'success',
    'warning': 'warning',
    'error': 'danger',
    'info': 'primary'
  }
  return types[level] || 'primary'
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}

const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return '-'
  
  const start = new Date(startTime)
  const end = new Date(endTime)
  const duration = end.getTime() - start.getTime()
  
  const minutes = Math.floor(duration / 60000)
  const seconds = Math.floor((duration % 60000) / 1000)
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  } else {
    return `${seconds}秒`
  }
}
</script>

<style scoped>
.task-result {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.result-header {
  margin-bottom: 20px;
}

.result-header h1 {
  margin: 0 0 8px 0;
  color: #409EFF;
  font-size: 1.8rem;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #409EFF;
}

.logs-content {
  max-height: 400px;
  overflow-y: auto;
}

.log-message {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-level {
  font-weight: bold;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 60px;
  text-align: center;
}

.log-level-info {
  background-color: #e1f5fe;
  color: #0277bd;
}

.log-level-success {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.log-level-warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.log-level-error {
  background-color: #ffebee;
  color: #c62828;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
</style>
