# 任务执行器架构设计

**版本**: v1.0.0  
**最后更新**: 2025/05/16  

## 1. 概述

任务执行器是ThunderHub Core服务的核心组件，负责执行各类任务，包括YouTube视频上传等。本文档详细描述任务执行器的架构设计、实现方式和与其他组件的交互。

## 2. 架构图

```mermaid
graph TD
    A[后端: FastAPI] -->|gRPC/REST| B[Core服务: 任务执行器]
    B -->|任务执行| C[设备控制器]
    C -->|设备操作| D[雷电模拟器]
    D -->|应用控制| E[YouTube/社交应用]
    B -->|状态发布| F[Redis]
    F -->|状态同步| A
    A -->|WebSocket| G[前端: Vue3]
    B -->|文件操作| H[文件系统]
    B -->|任务日志| I[内存/Redis]
```

## 3. 核心组件

### 3.1 任务执行器 (TaskExecutor)

任务执行器是Core服务中负责任务调度和执行的核心组件，主要功能包括：

- 任务创建与管理
- 任务状态跟踪
- 任务执行流程控制
- 任务状态发布到Redis
- 错误处理与重试机制

### 3.2 设备控制器 (DeviceController)

设备控制器负责与雷电模拟器交互，控制设备行为：

- 设备启动/停止
- 应用安装/启动
- 文件传输
- 设备状态监控

### 3.3 应用自动化 (AppiumDriver)

基于Appium的应用自动化组件，负责控制应用UI操作：

- 元素定位与交互
- 界面导航
- 表单填写
- 状态检测

## 4. 任务执行流程

### 4.1 通用任务执行流程

1. **任务创建**：后端通过gRPC/REST API创建任务
2. **任务初始化**：任务执行器初始化任务状态和日志
3. **设备准备**：检查并准备设备（启动模拟器等）
4. **任务执行**：执行具体任务逻辑（如YouTube上传）
5. **状态更新**：实时更新任务状态并发布到Redis
6. **任务完成**：更新最终状态，清理资源

### 4.2 YouTube上传任务流程

1. **准备阶段**：
   - 验证视频文件
   - 获取账号信息
   - 准备上传参数

2. **执行阶段**：
   - 启动YouTube应用
   - 导航到上传界面
   - 选择视频文件
   - 填写视频信息
   - 执行上传操作

3. **监控阶段**：
   - 监控上传进度
   - 检测上传状态
   - 处理可能的错误

4. **完成阶段**：
   - 验证上传结果
   - 更新任务状态
   - 记录上传日志

## 5. 数据结构

### 5.1 任务数据结构

```python
{
    "task_id": str,                # 任务唯一ID
    "task_type": str,              # 任务类型（如"youtube_upload"）
    "status": str,                 # 任务状态（pending/running/completed/failed/canceled）
    "progress": int,               # 进度（0-100）
    "device_id": str,              # 设备ID
    "platform_id": str,            # 平台ID
    "account_id": str,             # 账号ID
    "content_path": str,           # 内容路径
    "start_time": str,             # 开始时间（ISO格式）
    "estimated_end_time": str,     # 预计结束时间
    "parameters": dict,            # 任务参数
    "result": dict,                # 任务结果
    "error": str                   # 错误信息
}
```

### 5.2 任务日志结构

```python
{
    "message": str,                # 日志消息
    "level": str,                  # 日志级别（info/warning/error/success）
    "timestamp": str               # 时间戳（ISO格式）
}
```

## 6. 接口设计

### 6.1 任务执行器接口

```python
class TaskExecutor:
    async def initialize() -> bool
    async def create_task(task_data: Dict) -> bool
    async def start_task(task_id: str) -> bool
    async def pause_task(task_id: str) -> bool
    async def cancel_task(task_id: str) -> bool
    async def get_task_status(task_id: str) -> Dict
    async def get_task_logs(task_id: str) -> List[Dict]
```

### 6.2 YouTube上传器接口

```python
class YouTubeUploader:
    async def connect() -> bool
    async def disconnect() -> None
    async def launch_youtube() -> bool
    async def navigate_to_upload() -> bool
    async def select_video(video_path: str) -> bool
    async def fill_video_info(title: str, description: str, privacy: str) -> bool
    async def upload_video(max_wait_time: int) -> bool
```

## 7. 错误处理

### 7.1 错误类型

- **设备错误**：设备无法启动、连接失败
- **应用错误**：应用启动失败、界面元素未找到
- **上传错误**：文件不存在、上传失败、网络错误
- **系统错误**：资源不足、权限问题

### 7.2 错误处理策略

- **重试机制**：关键操作失败后自动重试（最多3次）
- **降级策略**：核心功能失败时采用备选方案
- **错误日志**：详细记录错误信息和上下文
- **状态恢复**：系统重启后恢复任务状态

## 8. 与其他组件交互

### 8.1 与Redis交互

- 发布任务状态到Redis通道（task:{task_id}:status）
- 保存最新状态到Redis键（task:{task_id}:latest）
- 设置状态过期时间（24小时）

### 8.2 与设备管理器交互

- 获取设备控制器
- 控制设备启动/停止
- 监控设备状态

### 8.3 与文件系统交互

- 读取视频文件
- 验证文件有效性
- 传输文件到设备

## 9. 实现注意事项

### 9.1 性能优化

- 使用异步IO（asyncio）提高并发性能
- 批量处理减少操作次数
- 缓存常用数据减少重复计算

### 9.2 可靠性保障

- 完善的错误处理和重试机制
- 状态持久化确保系统重启后恢复
- 详细的日志记录便于问题排查

### 9.3 扩展性设计

- 插件化架构支持新任务类型
- 标准化接口便于集成新功能
- 配置驱动减少硬编码
