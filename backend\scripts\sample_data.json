{"platforms": [{"id": "youtube", "name": "YouTube", "icon": "youtube-icon.png", "website": "https://www.youtube.com", "status": "active", "features": ["video", "live", "comment"], "app_info": {"package_name": "com.google.android.youtube", "main_activity": "com.google.android.youtube.HomeActivity", "version": "18.20.38", "min_android_version": "8.0"}, "automation": {"type": "appium", "selectors": {"login_button": "//android.widget.Button[@resource-id='com.google.android.youtube:id/sign_in_button']", "post_button": "//android.widget.ImageView[@content-desc='创建']", "comment_button": "//android.widget.ImageView[@content-desc='评论']", "like_button": "//android.widget.ImageView[@content-desc='顶']", "share_button": "//android.widget.ImageView[@content-desc='分享']"}, "actions": {"login": [{"action": "click", "selector": "//android.widget.Button[@resource-id='com.google.android.youtube:id/sign_in_button']", "value": ""}, {"action": "input", "selector": "//android.widget.EditText[@resource-id='identifierId']", "value": "{username}"}], "post": [{"action": "click", "selector": "//android.widget.ImageView[@content-desc='创建']", "value": ""}, {"action": "click", "selector": "//android.widget.TextView[@text='上传视频']", "value": ""}]}, "adb_commands": {"start_app": "adb shell am start -n com.google.android.youtube/com.google.android.youtube.HomeActivity", "stop_app": "adb shell am force-stop com.google.android.youtube", "clear_data": "adb shell pm clear com.google.android.youtube"}}, "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}, {"id": "tiktok", "name": "TikTok", "icon": "tiktok-icon.png", "website": "https://www.tiktok.com", "status": "active", "features": ["video", "live", "comment"], "app_info": {"package_name": "com.zhiliaoapp.musically", "main_activity": "com.ss.android.ugc.aweme.main.MainActivity", "version": "26.9.3", "min_android_version": "5.0"}, "automation": {"type": "appium", "selectors": {"login_button": "//android.widget.TextView[@text='登录']", "post_button": "//android.widget.ImageView[@content-desc='发布']", "comment_button": "//android.widget.ImageView[@content-desc='评论']", "like_button": "//android.widget.ImageView[@content-desc='喜欢']", "share_button": "//android.widget.ImageView[@content-desc='分享']"}, "actions": {"login": [{"action": "click", "selector": "//android.widget.TextView[@text='登录']", "value": ""}, {"action": "click", "selector": "//android.widget.TextView[@text='使用手机号或邮箱登录']", "value": ""}], "post": [{"action": "click", "selector": "//android.widget.ImageView[@content-desc='发布']", "value": ""}, {"action": "click", "selector": "//android.widget.TextView[@text='上传']", "value": ""}]}, "adb_commands": {"start_app": "adb shell am start -n com.zhiliaoapp.musically/com.ss.android.ugc.aweme.main.MainActivity", "stop_app": "adb shell am force-stop com.zhiliaoapp.musically", "clear_data": "adb shell pm clear com.zhiliaoapp.musically"}}, "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}, {"id": "do<PERSON><PERSON>", "name": "抖音", "icon": "douyin-icon.png", "website": "https://www.douyin.com", "status": "active", "features": ["video", "live", "comment"], "app_info": {"package_name": "com.ss.android.ugc.aweme", "main_activity": "com.ss.android.ugc.aweme.main.MainActivity", "version": "23.3.0", "min_android_version": "5.0"}, "automation": {"type": "appium", "selectors": {"login_button": "//android.widget.TextView[@text='登录']", "post_button": "//android.widget.ImageView[@content-desc='发布']", "comment_button": "//android.widget.ImageView[@content-desc='评论']", "like_button": "//android.widget.ImageView[@content-desc='喜欢']", "share_button": "//android.widget.ImageView[@content-desc='分享']"}, "actions": {"login": [{"action": "click", "selector": "//android.widget.TextView[@text='登录']", "value": ""}, {"action": "click", "selector": "//android.widget.TextView[@text='密码登录']", "value": ""}], "post": [{"action": "click", "selector": "//android.widget.ImageView[@content-desc='发布']", "value": ""}, {"action": "click", "selector": "//android.widget.TextView[@text='上传']", "value": ""}]}, "adb_commands": {"start_app": "adb shell am start -n com.ss.android.ugc.aweme/com.ss.android.ugc.aweme.main.MainActivity", "stop_app": "adb shell am force-stop com.ss.android.ugc.aweme", "clear_data": "adb shell pm clear com.ss.android.ugc.aweme"}}, "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00"}], "accounts": [{"id": "acc_youtube_001", "username": "thunderhub_official", "display_name": "雷电中心官方", "platform_id": "youtube", "app_id": "youtube", "platform_name": "YouTube", "device_id": "ldplayer_1", "core_service_id": "default", "status": "active", "avatar": "https://example.com/avatars/thunderhub.jpg", "description": "雷电中心官方账号，分享最新资讯和教程", "followers": 10000, "following": 500, "posts_count": 120, "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00", "auth_data": {"token": "encrypted_token_data", "cookies": "encrypted_cookies_data", "expires_at": "2023-09-15T00:00:00"}, "settings": {"auto_reply": true, "notification": true, "privacy_level": "public"}, "tags": ["官方", "教程", "资讯"]}, {"id": "acc_tiktok_001", "username": "thunderhub_tiktok", "display_name": "雷电中心TikTok", "platform_id": "tiktok", "app_id": "tiktok", "platform_name": "TikTok", "device_id": "ldplayer_2", "core_service_id": "default", "status": "active", "avatar": "https://example.com/avatars/thunderhub_tiktok.jpg", "description": "雷电中心TikTok官方账号", "followers": 5000, "following": 200, "posts_count": 50, "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00", "auth_data": {"token": "encrypted_token_data", "cookies": "encrypted_cookies_data", "expires_at": "2023-09-15T00:00:00"}, "settings": {"auto_reply": false, "notification": true, "privacy_level": "public"}, "tags": ["官方", "短视频"]}, {"id": "acc_douyin_001", "username": "thunderhub_douyin", "display_name": "雷电中心抖音", "platform_id": "do<PERSON><PERSON>", "app_id": "do<PERSON><PERSON>", "platform_name": "抖音", "device_id": "ldplayer_3", "core_service_id": "default", "status": "active", "avatar": "https://example.com/avatars/thunderhub_douyin.jpg", "description": "雷电中心抖音官方账号", "followers": 8000, "following": 300, "posts_count": 80, "created_at": "2023-08-15T00:00:00", "updated_at": "2023-08-15T00:00:00", "auth_data": {"token": "encrypted_token_data", "cookies": "encrypted_cookies_data", "expires_at": "2023-09-15T00:00:00"}, "settings": {"auto_reply": true, "notification": true, "privacy_level": "public"}, "tags": ["官方", "短视频", "直播"]}], "export_time": "2023-08-15T00:00:00"}