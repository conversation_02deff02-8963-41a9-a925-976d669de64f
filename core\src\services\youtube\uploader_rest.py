"""
YouTube上传器剩余部分
"""

import os
import logging
import asyncio
import time
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class YouTubeUploaderRest:
    """YouTube上传器剩余方法"""
    
    async def fill_video_info(self, title: str, description: str, privacy: str = 'public') -> bool:
        """填写视频信息
        
        Args:
            title: 视频标题
            description: 视频描述
            privacy: 隐私设置 (public/private/unlisted)
            
        Returns:
            bool: 是否成功填写
        """
        try:
            # 填写标题
            title_field = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((AppiumBy.ID, 'com.google.android.youtube:id/title_edit'))
            )
            title_field.clear()
            title_field.send_keys(title)
            logger.info(f"已填写标题: {title}")
            
            # 填写描述
            description_field = self.driver.find_element(AppiumBy.ID, 'com.google.android.youtube:id/description_edit')
            description_field.clear()
            description_field.send_keys(description)
            logger.info(f"已填写描述")
            
            # 设置隐私选项
            privacy_button = self.driver.find_element(AppiumBy.ID, 'com.google.android.youtube:id/privacy_spinner')
            privacy_button.click()
            
            # 等待隐私选项菜单出现
            await asyncio.sleep(2)
            
            # 选择隐私选项
            privacy_option = None
            if privacy == 'public':
                privacy_text = '公开'
            elif privacy == 'private':
                privacy_text = '私人'
            elif privacy == 'unlisted':
                privacy_text = '未列出'
            else:
                privacy_text = '公开'
                
            privacy_option = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((AppiumBy.XPATH, f"//android.widget.TextView[@text='{privacy_text}']"))
            )
            privacy_option.click()
            logger.info(f"已设置隐私选项: {privacy_text}")
            
            # 点击完成按钮
            done_button = self.driver.find_element(AppiumBy.ID, 'com.google.android.youtube:id/done_button')
            done_button.click()
            logger.info("已点击完成按钮")
            
            return True
            
        except Exception as e:
            logger.error(f"填写视频信息异常: {str(e)}", exc_info=True)
            return False
            
    async def upload_video(self, max_wait_time: int = 600) -> bool:
        """上传视频
        
        Args:
            max_wait_time: 最长等待时间（秒）
            
        Returns:
            bool: 是否成功上传
        """
        try:
            # 点击上传按钮
            upload_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((AppiumBy.ID, 'com.google.android.youtube:id/upload_button'))
            )
            upload_button.click()
            logger.info("已点击上传按钮")
            
            # 等待上传开始
            await asyncio.sleep(5)
            
            # 监控上传进度
            start_time = time.time()
            upload_complete = False
            
            while not upload_complete and time.time() - start_time < max_wait_time:
                try:
                    # 检查是否有上传完成的提示
                    success_message = self.driver.find_element(AppiumBy.ID, 'com.google.android.youtube:id/upload_success_message')
                    if success_message.is_displayed():
                        logger.info("视频上传成功")
                        upload_complete = True
                        break
                except:
                    # 未找到成功消息，继续等待
                    pass
                    
                try:
                    # 检查是否有错误提示
                    error_message = self.driver.find_element(AppiumBy.ID, 'com.google.android.youtube:id/upload_error_message')
                    if error_message.is_displayed():
                        error_text = error_message.text
                        logger.error(f"上传失败: {error_text}")
                        return False
                except:
                    # 未找到错误消息，继续等待
                    pass
                    
                # 检查上传进度
                try:
                    progress_bar = self.driver.find_element(AppiumBy.ID, 'com.google.android.youtube:id/upload_progress')
                    progress_value = progress_bar.get_attribute('progress')
                    logger.info(f"上传进度: {progress_value}%")
                except:
                    # 未找到进度条，可能界面已经变化
                    pass
                    
                # 等待一段时间再检查
                await asyncio.sleep(10)
                
            if upload_complete:
                return True
            else:
                logger.error(f"上传超时，已等待{max_wait_time}秒")
                return False
                
        except Exception as e:
            logger.error(f"上传视频异常: {str(e)}", exc_info=True)
            return False
