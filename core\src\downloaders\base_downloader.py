"""
基础下载器类
参考YouTube上传器的BaseUploader设计
"""

import os
import logging
import asyncio
import json
import hashlib
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

logger = logging.getLogger(__name__)


class BaseDownloader(ABC):
    """基础下载器抽象类"""

    def __init__(self, platform: str, download_path: str):
        """初始化基础下载器
        
        Args:
            platform: 平台名称 (douyin, youtube, etc.)
            download_path: 下载路径
        """
        self.platform = platform
        self.download_path = download_path
        self.status = "idle"
        self.progress = 0
        self.downloaded_count = 0
        self.total_count = 0
        self.error_message = None
        self.logs = []
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None
        self.log_callback: Optional[Callable] = None
        
        # 确保下载目录存在
        os.makedirs(download_path, exist_ok=True)
        
        logger.info(f"初始化{platform}下载器，下载路径: {download_path}")

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback

    def set_log_callback(self, callback: Callable):
        """设置日志回调函数"""
        self.log_callback = callback

    def update_progress(self, progress: int, message: str = ""):
        """更新进度"""
        self.progress = progress
        if self.progress_callback:
            self.progress_callback(progress, message)
        self.add_log(f"进度: {progress}% - {message}")

    def set_status(self, status: str, message: str = ""):
        """设置状态"""
        self.status = status
        if self.status_callback:
            self.status_callback(status, message)
        self.add_log(f"状态: {status} - {message}")

    def add_log(self, message: str):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs.append(log_entry)
        if self.log_callback:
            self.log_callback(log_entry)
        logger.info(log_entry)

    @abstractmethod
    async def download_account_content(
        self, 
        account_url: str, 
        account_name: str,
        download_config: Dict[str, Any]
    ) -> bool:
        """下载账号内容
        
        Args:
            account_url: 账号URL
            account_name: 账号名称
            download_config: 下载配置
            
        Returns:
            bool: 是否下载成功
        """
        pass

    @abstractmethod
    async def get_account_info(self, account_url: str) -> Dict[str, Any]:
        """获取账号信息
        
        Args:
            account_url: 账号URL
            
        Returns:
            Dict: 账号信息
        """
        pass

    @abstractmethod
    async def get_video_list(
        self, 
        account_url: str, 
        max_count: int = 50,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """获取视频列表
        
        Args:
            account_url: 账号URL
            max_count: 最大数量
            filters: 过滤条件
            
        Returns:
            List: 视频列表
        """
        pass

    @abstractmethod
    async def download_video(
        self, 
        video_info: Dict[str, Any], 
        save_path: str
    ) -> bool:
        """下载单个视频
        
        Args:
            video_info: 视频信息
            save_path: 保存路径
            
        Returns:
            bool: 是否下载成功
        """
        pass

    def generate_filename(self, video_info: Dict[str, Any], naming_rule: str = "timestamp") -> str:
        """生成文件名
        
        Args:
            video_info: 视频信息
            naming_rule: 命名规则
            
        Returns:
            str: 文件名
        """
        if naming_rule == "timestamp":
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"{timestamp}_{video_info.get('id', 'unknown')}.mp4"
        elif naming_rule == "title":
            title = video_info.get('title', 'untitled')
            # 清理文件名中的非法字符
            title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            return f"{title}_{video_info.get('id', 'unknown')}.mp4"
        elif naming_rule == "id":
            return f"{video_info.get('id', 'unknown')}.mp4"
        else:
            return f"{video_info.get('id', 'unknown')}.mp4"

    def save_metadata(self, video_info: Dict[str, Any], save_path: str):
        """保存视频元数据
        
        Args:
            video_info: 视频信息
            save_path: 保存路径
        """
        try:
            metadata_path = save_path.replace('.mp4', '.json')
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(video_info, f, ensure_ascii=False, indent=2)
            self.add_log(f"元数据已保存: {metadata_path}")
        except Exception as e:
            self.add_log(f"保存元数据失败: {str(e)}")

    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件哈希值
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {str(e)}")
            return ""

    def is_file_exists(self, file_path: str) -> bool:
        """检查文件是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否存在
        """
        return os.path.exists(file_path) and os.path.getsize(file_path) > 0

    async def cleanup(self):
        """清理资源"""
        self.add_log("清理下载器资源")
        # 子类可以重写此方法进行特定的清理操作

    def get_download_summary(self) -> Dict[str, Any]:
        """获取下载摘要
        
        Returns:
            Dict: 下载摘要
        """
        return {
            "platform": self.platform,
            "download_path": self.download_path,
            "status": self.status,
            "progress": self.progress,
            "downloaded_count": self.downloaded_count,
            "total_count": self.total_count,
            "error_message": self.error_message,
            "logs_count": len(self.logs)
        }
