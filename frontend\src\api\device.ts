import { deviceSocketManager } from '../socket.io'

export interface Device {
  id: string
  name: string
  status: string
  cpu: string
  memory: string
  network: string
}

export interface BatchOperationResult {
  success: boolean
  message: string
  deviceId: string
}

export const getDevices = async (): Promise<{data: Device[]}> => {
  return deviceSocketManager.withSocket(socket => {
    return new Promise(resolve => {
      socket.emit('get_devices', (devices: Device[]) => {
        resolve({ data: devices })
      })
    })
  })
}

export const startDevice = async (id: string): Promise<void> => {
  return deviceSocketManager.withSocket(socket => {
    return new Promise(resolve => {
      socket.emit('start_device', id, () => {
        resolve()
      })
    })
  })
}

export const stopDevice = async (id: string): Promise<void> => {
  return deviceSocketManager.withSocket(socket => {
    return new Promise(resolve => {
      socket.emit('stop_device', id, () => {
        resolve()
      })
    })
  })
}

export const batchStartDevices = async (ids: string[]): Promise<BatchOperationResult[]> => {
  return deviceSocketManager.withSocket(socket => {
    return new Promise(resolve => {
      socket.emit('batch_start_devices', ids, (results: BatchOperationResult[]) => {
        resolve(results)
      })
    })
  })
}

export const batchStopDevices = async (ids: string[]): Promise<BatchOperationResult[]> => {
  return deviceSocketManager.withSocket(socket => {
    return new Promise(resolve => {
      socket.emit('batch_stop_devices', ids, (results: BatchOperationResult[]) => {
        resolve(results)
      })
    })
  })
}

export const batchInstallApps = async (ids: string[], appPath: string): Promise<BatchOperationResult[]> => {
  return deviceSocketManager.withSocket(socket => {
    return new Promise(resolve => {
      socket.emit('batch_install_apps', { ids, appPath }, (results: BatchOperationResult[]) => {
        resolve(results)
      })
    })
  })
}

export const batchRunScripts = async (ids: string[], script: string): Promise<BatchOperationResult[]> => {
  return deviceSocketManager.withSocket(socket => {
    return new Promise(resolve => {
      socket.emit('batch_run_scripts', { ids, script }, (results: BatchOperationResult[]) => {
        resolve(results)
      })
    })
  })
}