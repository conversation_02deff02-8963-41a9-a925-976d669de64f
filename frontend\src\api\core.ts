import request from '@/utils/request'

// 获取Core服务列表
export const getCoreServices = () => {
  return request({
    url: '/api/v1/cores',
    method: 'get'
  })
}

// 获取特定Core服务的详细信息
export const getCoreServiceDetails = (id: string) => {
  return request({
    url: `/api/v1/cores/${id}`,
    method: 'get'
  })
}

// 获取Core服务状态
export const getCoreServiceStatus = (id: string) => {
  return request({
    url: `/api/v1/cores/${id}/status`,
    method: 'get'
  })
}

// 启动Core服务
export const startCoreService = (id: string) => {
  return request({
    url: `/api/v1/cores/${id}/start`,
    method: 'post'
  })
}

// 停止Core服务
export const stopCoreService = (id: string) => {
  return request({
    url: `/api/v1/cores/${id}/stop`,
    method: 'post'
  })
}

// 重启Core服务
export const restartCoreService = (id: string) => {
  return request({
    url: `/api/v1/cores/${id}/restart`,
    method: 'post'
  })
}
