<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入账号"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="import-dialog-content">
      <!-- 导入说明 -->
      <div class="import-instructions">
        <h3>导入说明</h3>
        <div class="format-selector">
          <el-radio-group v-model="importFormat">
            <el-radio label="text">文本格式</el-radio>
            <el-radio label="csv">CSV格式</el-radio>
          </el-radio-group>
          <el-button v-if="importFormat === 'csv'" type="primary" link @click="downloadTemplate">
            下载CSV模板
          </el-button>
        </div>

        <div v-if="importFormat === 'text'">
          <p>支持从文本文件导入账号，格式如下：</p>
          <ul>
            <li>每行一个账号，格式为：<code>平台代码：用户名----密码----恢复邮箱----恢复码----备注</code></li>
            <li>支持的平台代码：GG (YouTube)、FB (Facebook) 等</li>
            <li>账号前的行将被视为显示名称</li>
          </ul>
          <p>示例：</p>
          <pre>
A-HK-0-1-00
GG：<EMAIL>----Longer889614$----<EMAIL>----Kkoazf465k----mn
FB：<EMAIL>----tirtho----AKAGWLFEE5PBULZF5KWNZ746G5OF4QVI----61560202463493</pre>
        </div>

        <div v-else>
          <p>支持从CSV文件导入账号，CSV文件应包含以下列：</p>
          <ul>
            <li><code>username</code>: 用户名（必填）</li>
            <li><code>platform_id</code>: 平台ID（必填，必须是系统中已存在的平台ID）</li>
            <li><code>password</code>: 密码</li>
            <li><code>display_name</code>: 显示名称</li>
            <li><code>recovery_email</code>: 恢复邮箱</li>
            <li><code>recovery_code</code>: 恢复码</li>
            <li><code>description</code>: 描述/备注</li>
            <li><code>status</code>: 状态（默认为active）</li>
            <li><code>tags</code>: 标签（可选，多个标签用逗号分隔）</li>
          </ul>
          <p>您可以先导出账号，然后修改CSV文件后再导入。Core服务ID将使用下方选择的默认值。</p>
          <p class="warning-text">注意：platform_id必须是系统中已存在的平台ID，如果平台不存在，请先添加平台。</p>
        </div>
      </div>

      <!-- 导入选项 - 仅在文本格式时显示 -->
      <div v-if="importFormat === 'text'" class="import-options">
        <h3>导入选项</h3>
        <el-form :model="importOptions" label-width="120px">
          <el-form-item label="Core服务">
            <el-select v-model="importOptions.coreServiceId" placeholder="选择Core服务">
              <el-option
                v-for="service in coreServices"
                :key="service.id"
                :label="service.name"
                :value="service.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="平台映射">
            <div v-for="(platformValue, code) in importOptions.platformMapping" :key="code" class="platform-mapping-item">
              <span class="platform-code">{{ code }}</span>
              <el-select v-model="importOptions.platformMapping[code]" placeholder="选择平台">
                <el-option
                  v-for="platform in platforms"
                  :key="platform.id"
                  :label="platform.name"
                  :value="platform.id"
                />
              </el-select>
              <el-button type="danger" circle @click="removePlatformMapping(String(code))">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <div class="add-mapping">
              <el-input v-model="newMapping.code" placeholder="平台代码" style="width: 100px" />
              <el-select v-model="newMapping.platformId" placeholder="选择平台">
                <el-option
                  v-for="platform in platforms"
                  :key="platform.id"
                  :label="platform.name"
                  :value="platform.id"
                />
              </el-select>
              <el-button type="primary" circle @click="addPlatformMapping">
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="冲突处理">
            <el-radio-group v-model="importOptions.conflictStrategy">
              <el-radio label="update">更新现有账号</el-radio>
              <el-radio label="skip">跳过已存在账号</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- CSV导入时的简化选项 -->
      <div v-else class="import-options">
        <h3>导入选项</h3>
        <el-form label-width="120px">
          <el-form-item label="Core服务">
            <el-select v-model="csvCoreServiceId" placeholder="选择默认Core服务">
              <el-option
                v-for="service in coreServices"
                :key="service.id"
                :label="service.name"
                :value="service.id"
              />
            </el-select>
            <div class="option-hint">
              仅当CSV中未指定Core服务ID时使用
            </div>
          </el-form-item>

          <el-form-item label="冲突处理">
            <el-radio-group v-model="importOptions.conflictStrategy">
              <el-radio label="update">更新现有账号</el-radio>
              <el-radio label="skip">跳过已存在账号</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 文本输入区域 -->
      <div class="import-text-area">
        <h3>账号数据</h3>
        <el-input
          v-model="importText"
          type="textarea"
          :rows="10"
          :placeholder="importFormat === 'csv' ? '请粘贴CSV数据或拖拽CSV文件到此处' : '请粘贴账号数据或拖拽文件到此处'"
          @drop.prevent="handleFileDrop"
          @dragover.prevent
        />
        <div class="file-upload">
          <el-upload
            ref="uploadRef"
            action=""
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            :accept="importFormat === 'csv' ? '.csv' : '.txt'"
          >
            <el-button type="primary">选择文件</el-button>
          </el-upload>
          <span class="file-hint">
            {{ importFormat === 'csv' ? '支持.csv格式文件' : '支持.txt格式文件' }}
          </span>
        </div>
      </div>

      <!-- 预览区域 -->
      <div v-if="previewData.length > 0" class="import-preview">
        <h3>导入预览 ({{ previewData.length }} 个账号)</h3>
        <el-table :data="previewData.slice(0, 5)" style="width: 100%" border>
          <el-table-column prop="platform_id" label="平台" width="100" />
          <el-table-column prop="username" label="用户名" min-width="150" />
          <el-table-column prop="display_name" label="显示名称" min-width="120" />
          <el-table-column prop="description" label="备注" min-width="120" />
        </el-table>
        <div v-if="previewData.length > 5" class="more-preview">
          还有 {{ previewData.length - 5 }} 个账号未显示...
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePreview" :disabled="!importText">
          预览
        </el-button>
        <el-button
          type="success"
          @click="handleImport"
          :disabled="!importText || previewData.length === 0"
          :loading="importing"
        >
          导入
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Plus } from '@element-plus/icons-vue'
import { importAccounts } from '@/api/social'
import type { SocialPlatform } from '@/types/social'

// 定义Core服务类型
interface CoreService {
  id: string;
  name: string;
  [key: string]: any;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  platforms: {
    type: Array as () => SocialPlatform[],
    default: () => []
  },
  coreServices: {
    type: Array as () => CoreService[],
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'import'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 导入格式
const importFormat = ref<'text' | 'csv'>('text')

// 导入文本
const importText = ref('')
const previewData = ref<any[]>([])
const importing = ref(false)

// CSV导入的默认Core服务ID
const csvCoreServiceId = ref('')

// 定义平台映射类型
interface PlatformMapping {
  [key: string]: string;
}

// 导入选项
const importOptions = ref({
  coreServiceId: '',
  platformMapping: {
    'GG': 'youtube',
    'FB': 'facebook'
  } as PlatformMapping,
  conflictStrategy: 'update'
})

// 新增平台映射
const newMapping = ref({
  code: '',
  platformId: ''
})

// 初始化Core服务ID
watch(() => props.coreServices, (services) => {
  if (services.length > 0) {
    // 设置文本导入的Core服务ID
    if (!importOptions.value.coreServiceId) {
      importOptions.value.coreServiceId = services[0].id
    }

    // 设置CSV导入的Core服务ID
    if (!csvCoreServiceId.value) {
      csvCoreServiceId.value = services[0].id
    }
  }
}, { immediate: true })

// 添加平台映射
const addPlatformMapping = () => {
  if (!newMapping.value.code || !newMapping.value.platformId) {
    ElMessage.warning('请输入平台代码和选择平台')
    return
  }

  importOptions.value.platformMapping[newMapping.value.code] = newMapping.value.platformId
  newMapping.value.code = ''
  newMapping.value.platformId = ''
}

// 移除平台映射
const removePlatformMapping = (code: string) => {
  delete importOptions.value.platformMapping[code]
}

// 处理文件拖放
const handleFileDrop = (event: DragEvent) => {
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    readFile(files[0])
  }
}

// 处理文件选择
const handleFileChange = (file: any) => {
  readFile(file.raw)
}

// 读取文件内容
const readFile = (file: File) => {
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    importText.value = e.target?.result as string || ''
  }

  // 使用UTF-8编码读取文件
  // 如果是CSV文件，尝试检测BOM标记
  if (file.name.toLowerCase().endsWith('.csv')) {
    // 先读取文件的前几个字节来检测BOM
    const bomReader = new FileReader()
    bomReader.onload = (e) => {
      const buffer = e.target?.result as ArrayBuffer
      const dataView = new DataView(buffer)

      // 检测UTF-8 BOM (EF BB BF)
      if (buffer.byteLength >= 3 &&
          dataView.getUint8(0) === 0xEF &&
          dataView.getUint8(1) === 0xBB &&
          dataView.getUint8(2) === 0xBF) {
        console.log('检测到UTF-8 BOM标记')
        // 如果有BOM，使用UTF-8读取
        reader.readAsText(file, 'UTF-8')
      }
      // 检测UTF-16LE BOM (FF FE)
      else if (buffer.byteLength >= 2 &&
               dataView.getUint8(0) === 0xFF &&
               dataView.getUint8(1) === 0xFE) {
        console.log('检测到UTF-16LE BOM标记')
        reader.readAsText(file, 'UTF-16LE')
      }
      // 检测UTF-16BE BOM (FE FF)
      else if (buffer.byteLength >= 2 &&
               dataView.getUint8(0) === 0xFE &&
               dataView.getUint8(1) === 0xFF) {
        console.log('检测到UTF-16BE BOM标记')
        reader.readAsText(file, 'UTF-16BE')
      }
      // 没有BOM，尝试使用GBK编码（中文Windows系统常用）
      else {
        console.log('未检测到BOM标记，尝试使用GBK编码')
        try {
          // 直接尝试使用GBK编码
          reader.readAsText(file, 'gbk')
          console.log('使用GBK编码读取文件')
        } catch (e) {
          // 如果GBK失败，回退到UTF-8
          console.error('GBK编码读取失败:', e)
          reader.readAsText(file, 'UTF-8')
          console.log('回退到UTF-8编码读取文件')
        }
      }
    }
    bomReader.readAsArrayBuffer(file.slice(0, 4))
  } else {
    // 非CSV文件使用UTF-8
    reader.readAsText(file, 'UTF-8')
  }
}

// 下载CSV模板
const downloadTemplate = () => {
  // CSV模板头部 - 移除core_service_id和avatar字段
  const headers = [
    'username', 'password', 'display_name', 'platform_id',
    'status', 'recovery_email', 'recovery_code',
    'description', 'tags'
  ]

  // 创建CSV内容
  let csvContent = headers.join(',') + '\n'

  // 添加示例数据 - 使用实际平台ID
  // 获取第一个可用的平台ID作为示例
  let examplePlatformId = ''
  if (props.platforms && props.platforms.length > 0) {
    examplePlatformId = props.platforms[0].id
  } else {
    examplePlatformId = 'youtube' // 默认值
  }

  csvContent += `example_user,password123,示例账号,${examplePlatformId},active,<EMAIL>,ABCDEF123456,这是一个示例账号,\n`

  // 添加UTF-8 BOM标记，确保Excel等应用能正确识别中文
  const BOM = new Uint8Array([0xEF, 0xBB, 0xBF])
  const csvContentWithBOM = new Blob([BOM, csvContent], { type: 'text/csv;charset=utf-8;' })

  // 创建URL
  const url = URL.createObjectURL(csvContentWithBOM)

  // 创建下载链接
  const link = document.createElement('a')
  link.href = url
  link.setAttribute('download', 'accounts_template.csv')
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // 释放URL对象
  setTimeout(() => URL.revokeObjectURL(url), 100)
}

// 解析CSV内容
const parseCSV = (text: string) => {
  // 简单的CSV解析器
  const lines = text.split('\n')
  if (lines.length < 2) return [] // 至少需要标题行和一行数据

  // 获取标题行
  const headers = lines[0].split(',').map(h => h.trim())

  const accounts = []

  // 从第二行开始解析数据
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    // 分割CSV行
    const values = line.split(',')

    // 创建账号对象
    const account: Record<string, any> = {}

    // 填充账号数据
    for (let j = 0; j < headers.length; j++) {
      if (j < values.length && values[j].trim()) {
        account[headers[j]] = values[j].trim()
      }
    }

    // 确保必要字段存在
    if (account.username && account.platform_id) {
      // 设置Core服务ID
      if (!account.core_service_id) {
        account.core_service_id = csvCoreServiceId.value
      }

      // 设置状态
      if (!account.status) {
        account.status = 'active'
      }

      accounts.push(account)
    }
  }

  return accounts
}

// 解析文本内容
const parseImportText = () => {
  if (!importText.value) return []

  // 根据导入格式选择不同的解析方法
  if (importFormat.value === 'csv') {
    return parseCSV(importText.value)
  }

  // 文本格式解析
  const lines = importText.value.split('\n')
  const accounts = []
  let currentDisplayName = ''

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    // 检查是否是账号标识行
    const isPlatformLine = Object.keys(importOptions.value.platformMapping).some(
      code => line.includes(code + '：') || line.includes(code + ':')
    )

    if (!isPlatformLine) {
      // 可能是显示名称行
      currentDisplayName = line
      continue
    }

    // 解析平台和账号信息
    let platformCode = ''
    for (const code of Object.keys(importOptions.value.platformMapping)) {
      if (line.includes(code + '：') || line.includes(code + ':')) {
        platformCode = code
        break
      }
    }

    if (!platformCode) continue

    const platformId = importOptions.value.platformMapping[platformCode]

    // 分割账号信息
    let accountPart = ''
    if (line.includes(platformCode + '：')) {
      accountPart = line.split(platformCode + '：')[1]
    } else {
      accountPart = line.split(platformCode + ':')[1]
    }

    // 处理不同的分隔符
    let parts = []
    if (accountPart.includes('----')) {
      parts = accountPart.split('----')
    } else if (accountPart.includes('|')) {
      parts = accountPart.split('|')
    } else {
      parts = [accountPart]
    }

    const username = parts[0].trim()

    // 定义完整的账号类型
    interface SocialAccount {
      username: string;
      platform_id: string;
      core_service_id: string;
      display_name: string;
      status: string;
      password?: string;
      recovery_email?: string;
      recovery_code?: string;
      description?: string;
      tags?: string[];
    }

    const account: SocialAccount = {
      username,
      platform_id: platformId,
      core_service_id: importOptions.value.coreServiceId,
      display_name: currentDisplayName,
      status: 'active'
    }

    // 添加密码
    if (parts.length > 1) {
      account.password = parts[1].trim()
    }

    // 添加恢复邮箱
    if (parts.length > 2) {
      account.recovery_email = parts[2].trim()
    }

    // 添加恢复码
    if (parts.length > 3) {
      account.recovery_code = parts[3].trim()
    }

    // 添加备注/描述
    if (parts.length > 4) {
      account.description = parts[4].trim()
    }

    accounts.push(account)
  }

  return accounts
}

// 预览导入数据
const handlePreview = () => {
  previewData.value = parseImportText()
  if (previewData.value.length === 0) {
    ElMessage.warning('未找到有效的账号数据')
  } else {
    ElMessage.success(`找到 ${previewData.value.length} 个账号`)
  }
}

// 执行导入
const handleImport = async () => {
  if (previewData.value.length === 0) {
    handlePreview()
    if (previewData.value.length === 0) return
  }

  importing.value = true
  try {
    const response = await importAccounts(
      importText.value,
      importOptions.value.platformMapping,
      importOptions.value.coreServiceId,
      importFormat.value
    )

    // 处理Axios响应对象
    const result = response.data || response

    ElMessage.success(`成功导入 ${result.imported_count} 个账号`)

    if (result.errors && result.errors.length > 0) {
      console.error('导入错误:', result.errors)
      ElMessage.warning(`导入过程中有 ${result.errors.length} 个错误，请查看控制台`)
    }

    emit('import', result)
    dialogVisible.value = false
  } catch (error) {
    console.error('导入账号失败:', error)
    ElMessage.error('导入账号失败')
  } finally {
    importing.value = false
  }
}
</script>

<style scoped>
.import-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.import-instructions {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
}

.import-instructions pre {
  background-color: #eee;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.format-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.platform-mapping-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.platform-code {
  width: 50px;
  font-weight: bold;
}

.add-mapping {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.file-upload {
  margin-top: 10px;
}

.more-preview {
  margin-top: 10px;
  color: #666;
  font-style: italic;
}

.option-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.warning-text {
  color: #E6A23C;
  font-weight: bold;
  margin-top: 10px;
  padding: 8px;
  background-color: #FDF6EC;
  border-radius: 4px;
  border-left: 3px solid #E6A23C;
}
</style>
