<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频时长解析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-item {
            display: flex;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            align-items: center;
        }
        .test-input {
            flex: 1;
            margin-right: 15px;
            font-family: monospace;
        }
        .test-output {
            flex: 1;
            margin-right: 15px;
        }
        .test-category {
            flex: 1;
            font-weight: 600;
        }
        .video-very-short { background-color: #f0f9ff; }
        .video-short { background-color: #fdf6ec; }
        .video-medium { background-color: #ecf5ff; }
        .video-long { background-color: #fef0f0; }
        .video-very-long { background-color: #fef0f0; border-left: 4px solid #f56c6c; }
        
        .duration-text-very-short { color: #67c23a; }
        .duration-text-short { color: #e6a23c; }
        .duration-text-medium { color: #409eff; }
        .duration-text-long { color: #f78989; }
        .duration-text-very-long { color: #f56c6c; }
        
        button {
            padding: 8px 16px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #337ecc;
        }
        .custom-input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>视频时长解析测试</h1>
        
        <div class="test-section">
            <h3>问题分析</h3>
            <p>从您提供的信息看，视频文件显示：</p>
            <ul>
                <li>媒体信息中显示：⏱️ 1:28</li>
                <li>视频时长列显示：未知时长</li>
            </ul>
            <p>这说明时长数据存在，但我们的解析函数没有正确获取到。可能的原因：</p>
            <ul>
                <li>时长数据存储在不同的字段中</li>
                <li>时长格式是字符串而不是数字</li>
                <li>数据结构与预期不符</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>时间字符串解析测试</h3>
            <div style="display: flex; font-weight: bold; padding: 10px; background: #f8f9fa;">
                <div style="flex: 1;">输入格式</div>
                <div style="flex: 1;">解析结果（秒）</div>
                <div style="flex: 1;">分类结果</div>
            </div>
            <div id="parseTests"></div>
        </div>
        
        <div class="test-section">
            <h3>自定义测试</h3>
            <input type="text" id="customInput" class="custom-input" placeholder="输入时间格式，如 1:28" />
            <button onclick="testCustomInput()">测试解析</button>
            <div id="customResult"></div>
        </div>
        
        <div class="test-section">
            <h3>文件对象结构测试</h3>
            <p>模拟不同的文件对象结构：</p>
            <div id="fileStructureTests"></div>
        </div>
    </div>

    <script>
        // 时间字符串解析函数
        function parseTimeStringToSeconds(timeString) {
            if (!timeString || typeof timeString !== 'string') return 0;
            
            const parts = timeString.trim().split(':').map(part => parseInt(part, 10));
            
            if (parts.length === 2) {
                const [minutes, seconds] = parts;
                return (minutes || 0) * 60 + (seconds || 0);
            } else if (parts.length === 3) {
                const [hours, minutes, seconds] = parts;
                return (hours || 0) * 3600 + (minutes || 0) * 60 + (seconds || 0);
            }
            
            return 0;
        }

        // 视频时长分类
        function calculateVideoDurationCategory(duration) {
            if (!duration || duration <= 0) return { category: 'unknown', message: '未知时长', class: '', textClass: '' };
            if (duration < 35) return { category: 'very_short', message: '极短视频', class: 'video-very-short', textClass: 'duration-text-very-short' };
            if (duration <= 60) return { category: 'short', message: '短视频', class: 'video-short', textClass: 'duration-text-short' };
            if (duration <= 180) return { category: 'medium', message: '中等视频', class: 'video-medium', textClass: 'duration-text-medium' };
            if (duration <= 480) return { category: 'long', message: '长视频', class: 'video-long', textClass: 'duration-text-long' };
            return { category: 'very_long', message: '超长视频', class: 'video-very-long', textClass: 'duration-text-very-long' };
        }

        // 获取视频时长（模拟改进后的函数）
        function getVideoDurationFromFile(file) {
            if (!file) return 0;
            
            let duration = 0;
            
            // 检查各种可能的时长字段
            if (file.media_info?.duration) {
                duration = file.media_info.duration;
            } else if (file.duration) {
                duration = file.duration;
            } else if (file.file_info?.duration) {
                duration = file.file_info.duration;
            } else if (file.metadata?.duration) {
                duration = file.metadata.duration;
            } else if (file.video_info?.duration) {
                duration = file.video_info.duration;
            }
            
            // 如果时长是字符串格式，转换为秒数
            if (typeof duration === 'string') {
                duration = parseTimeStringToSeconds(duration);
            }
            
            return duration || 0;
        }

        // 运行解析测试
        function runParseTests() {
            const testCases = [
                '1:28',
                '0:30',
                '2:15',
                '0:01:28',
                '1:30:45',
                '10:00',
                '0:05',
                '15:30',
                '',
                null,
                undefined,
                '1:28:30'
            ];
            
            const testsDiv = document.getElementById('parseTests');
            let html = '';
            
            testCases.forEach(testCase => {
                const seconds = parseTimeStringToSeconds(testCase);
                const category = calculateVideoDurationCategory(seconds);
                
                html += `
                    <div class="test-item ${category.class}">
                        <div class="test-input">${testCase || 'null/undefined'}</div>
                        <div class="test-output">${seconds} 秒</div>
                        <div class="test-category ${category.textClass}">${category.message}</div>
                    </div>
                `;
            });
            
            testsDiv.innerHTML = html;
        }

        // 测试自定义输入
        function testCustomInput() {
            const input = document.getElementById('customInput').value;
            const resultDiv = document.getElementById('customResult');
            
            const seconds = parseTimeStringToSeconds(input);
            const category = calculateVideoDurationCategory(seconds);
            
            resultDiv.innerHTML = `
                <div class="test-item ${category.class}" style="margin-top: 10px;">
                    <div class="test-input">${input}</div>
                    <div class="test-output">${seconds} 秒</div>
                    <div class="test-category ${category.textClass}">${category.message}</div>
                </div>
            `;
        }

        // 测试文件对象结构
        function testFileStructures() {
            const testFiles = [
                {
                    name: '测试1 - media_info.duration字符串',
                    file: { name: 'test1.mp4', media_info: { duration: '1:28' } }
                },
                {
                    name: '测试2 - media_info.duration数字',
                    file: { name: 'test2.mp4', media_info: { duration: 88 } }
                },
                {
                    name: '测试3 - duration字符串',
                    file: { name: 'test3.mp4', duration: '2:15' }
                },
                {
                    name: '测试4 - file_info.duration',
                    file: { name: 'test4.mp4', file_info: { duration: '0:45' } }
                },
                {
                    name: '测试5 - 无时长信息',
                    file: { name: 'test5.mp4' }
                }
            ];
            
            const testsDiv = document.getElementById('fileStructureTests');
            let html = '';
            
            testFiles.forEach(test => {
                const duration = getVideoDurationFromFile(test.file);
                const category = calculateVideoDurationCategory(duration);
                
                html += `
                    <div class="test-item ${category.class}">
                        <div style="flex: 2;">
                            <strong>${test.name}</strong><br>
                            <small>结构: ${JSON.stringify(test.file)}</small>
                        </div>
                        <div class="test-output">${duration} 秒</div>
                        <div class="test-category ${category.textClass}">${category.message}</div>
                    </div>
                `;
            });
            
            testsDiv.innerHTML = html;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            runParseTests();
            testFileStructures();
        });
    </script>
</body>
</html>
