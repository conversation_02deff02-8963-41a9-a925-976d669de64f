<template>
  <el-dialog
    v-model="dialogVisible"
    title="对标账号详情"
    width="800px"
    @close="handleClose"
  >
    <div v-if="benchmarkAccount" class="benchmark-detail">
      <!-- 基本信息 -->
      <el-card class="info-card" style="margin-bottom: 20px;">
        <template #header>
          <span>📋 基本信息</span>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="账号名称">
            {{ benchmarkAccount.account_name }}
          </el-descriptions-item>
          <el-descriptions-item label="平台">
            <el-tag type="primary">{{ getPlatformText(benchmarkAccount.platform) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="对标类型">
            <el-tag :type="getBenchmarkTypeColor(benchmarkAccount.benchmark_type)">
              {{ getBenchmarkTypeText(benchmarkAccount.benchmark_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <span class="priority-stars">
              {{ '★'.repeat(benchmarkAccount.priority) }}{{ '☆'.repeat(5 - benchmarkAccount.priority) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(benchmarkAccount.status)">
              {{ getStatusText(benchmarkAccount.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="账号链接">
            <el-link :href="benchmarkAccount.account_url" target="_blank" type="primary">
              {{ benchmarkAccount.account_url }}
            </el-link>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 账号数据 -->
      <el-card class="info-card" style="margin-bottom: 20px;">
        <template #header>
          <span>📊 账号数据</span>
        </template>
        
        <div v-if="hasAccountData" class="account-data">
          <el-row :gutter="20">
            <el-col :span="8" v-if="benchmarkAccount.account_data?.followers">
              <div class="data-item">
                <div class="data-icon">👥</div>
                <div class="data-content">
                  <div class="data-value">{{ formatNumber(benchmarkAccount.account_data.followers) }}</div>
                  <div class="data-label">粉丝数</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8" v-if="benchmarkAccount.account_data?.posts_count">
              <div class="data-item">
                <div class="data-icon">📝</div>
                <div class="data-content">
                  <div class="data-value">{{ formatNumber(benchmarkAccount.account_data.posts_count) }}</div>
                  <div class="data-label">内容数</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8" v-if="benchmarkAccount.account_data?.engagement_rate">
              <div class="data-item">
                <div class="data-icon">📈</div>
                <div class="data-content">
                  <div class="data-value">{{ (benchmarkAccount.account_data.engagement_rate * 100).toFixed(1) }}%</div>
                  <div class="data-label">互动率</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div v-else class="no-account-data">
          <el-empty description="暂无账号数据" />
        </div>
      </el-card>

      <!-- 下载信息 -->
      <el-card class="info-card">
        <template #header>
          <span>📥 下载信息</span>
        </template>
        
        <div class="download-info">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="预计下载路径">
              <div class="download-path">
                <div class="path-preview">{{ getDownloadPath() }}</div>
                <div class="path-explanation">
                  <small>基础路径/我们的平台/我们的账号/对标账号名称/发布月份/</small>
                </div>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="最后下载时间">
              <span v-if="benchmarkAccount.last_download_time">
                {{ formatTime(benchmarkAccount.last_download_time) }}
              </span>
              <span v-else class="no-data">从未下载</span>
            </el-descriptions-item>
            <el-descriptions-item label="下载状态">
              <el-tag :type="getDownloadStatusColor(benchmarkAccount.download_status)">
                {{ getDownloadStatusText(benchmarkAccount.download_status) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="downloadContent">
          立即下载
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
  benchmarkAccount: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const dialogVisible = ref(false)

// 计算属性
const hasAccountData = computed(() => {
  const data = props.benchmarkAccount?.account_data
  return data && (
    data.followers || 
    data.posts_count || 
    data.engagement_rate
  )
})

// 监听器
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 工具方法
const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getPlatformText = (platform: string) => {
  const textMap = {
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'douyin': '抖音',
    'instagram': 'Instagram',
    'facebook': 'Facebook',
    'twitter': 'Twitter'
  }
  return textMap[platform as keyof typeof textMap] || platform
}

const getStatusColor = (status: string) => {
  const colorMap = {
    'active': 'success',
    'monitoring': 'primary',
    'inactive': 'warning'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    'active': '活跃',
    'monitoring': '监控中',
    'inactive': '暂停'
  }
  return textMap[status as keyof typeof textMap] || status
}

const getDownloadStatusColor = (status: string) => {
  const colorMap = {
    'completed': 'success',
    'downloading': 'primary',
    'failed': 'danger',
    'pending': 'warning',
    'never': 'info'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const getDownloadStatusText = (status: string) => {
  const textMap = {
    'completed': '已完成',
    'downloading': '下载中',
    'failed': '失败',
    'pending': '等待中',
    'never': '从未下载'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleString('zh-CN')
}

const getDownloadPath = () => {
  if (!props.benchmarkAccount) return ''
  
  const basePath = 'H:\\PublishSystem\\'
  const currentMonth = new Date().toISOString().slice(0, 7)
  
  // 这里需要从父组件传入我们的账号信息，或者从对标账号中获取关联信息
  return `${basePath}平台名称\\我们的账号\\${props.benchmarkAccount.account_name}\\${currentMonth}\\`
}

// 业务方法
const downloadContent = () => {
  if (!props.benchmarkAccount) return
  
  ElMessage.info(`开始下载 ${props.benchmarkAccount.account_name} 的内容...`)
  // TODO: 实现下载功能
  handleClose()
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.benchmark-detail {
  max-height: 600px;
  overflow-y: auto;
}

.info-card {
  border: 1px solid #e4e7ed;
}

.priority-stars {
  color: #f39c12;
  font-size: 14px;
}

.account-data {
  padding: 10px 0;
}

.data-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.data-icon {
  font-size: 24px;
  margin-right: 12px;
}

.data-content {
  flex: 1;
}

.data-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.data-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.no-account-data {
  padding: 20px 0;
  text-align: center;
}

.download-info {
  padding: 10px 0;
}

.download-path {
  font-size: 12px;
}

.path-preview {
  color: #303133;
  margin-bottom: 4px;
  word-break: break-all;
  font-family: monospace;
}

.path-explanation {
  color: #909399;
}

.no-data {
  color: #c0c4cc;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
