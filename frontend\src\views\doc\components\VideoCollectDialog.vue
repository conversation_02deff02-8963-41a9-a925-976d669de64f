<template>
  <el-dialog
    v-model="dialogVisible"
    title="📋 创建采集任务"
    width="700px"
    @close="handleClose"
  >
    <div class="collect-config">
      <!-- 账号信息 -->
      <div class="account-info-section">
        <h4>📋 对标账号信息</h4>
        <div class="account-card">
          <div class="account-header">
            <div class="account-avatar">
              {{ account?.account_name?.charAt(0)?.toUpperCase() || 'A' }}
            </div>
            <div class="account-details">
              <div class="account-name">{{ account?.account_name }}</div>
              <div class="account-meta">
                <el-tag size="small" type="info">{{ account?.platform }}</el-tag>
                <el-tag size="small" :type="getBenchmarkTypeColor(account?.benchmark_type)">
                  {{ getBenchmarkTypeText(account?.benchmark_type) }}
                </el-tag>
              </div>
              <div class="account-url">
                <a :href="account?.account_url" target="_blank" class="url-link">
                  {{ account?.account_url }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务配置表单 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="margin-top: 20px"
      >
        <el-form-item label="任务名称" prop="task_name">
          <el-input
            v-model="form.task_name"
            placeholder="请输入任务名称"
            maxlength="50"
            show-word-limit
          />
          <div class="form-tip">
            为这个采集任务起一个便于识别的名称
          </div>
        </el-form-item>

        <el-form-item label="采集内容" prop="content_types">
          <el-checkbox-group v-model="form.content_types">
            <el-checkbox value="video">
              <div class="content-option">
                <span class="content-icon">🎬</span>
                <span class="content-text">视频内容</span>
              </div>
            </el-checkbox>
            <el-checkbox value="image">
              <div class="content-option">
                <span class="content-icon">🖼️</span>
                <span class="content-text">图片内容</span>
              </div>
            </el-checkbox>
            <el-checkbox value="text">
              <div class="content-option">
                <span class="content-icon">📝</span>
                <span class="content-text">文本内容</span>
              </div>
            </el-checkbox>
          </el-checkbox-group>
          <div class="form-tip">
            选择要采集的内容类型，可多选
          </div>
        </el-form-item>

        <el-form-item label="采集深度" prop="collect_depth">
          <el-radio-group v-model="form.collect_depth">
            <el-radio value="basic">
              <div class="depth-option">
                <div class="depth-title">基础信息</div>
                <div class="depth-desc">标题、链接、封面、基础数据</div>
              </div>
            </el-radio>
            <el-radio value="detailed">
              <div class="depth-option">
                <div class="depth-title">详细信息</div>
                <div class="depth-desc">包含基础信息 + 真实下载地址</div>
              </div>
            </el-radio>
            <el-radio value="complete">
              <div class="depth-option">
                <div class="depth-title">完整信息</div>
                <div class="depth-desc">包含详细信息 + 评论、相关推荐等</div>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="采集数量" prop="max_items">
          <el-input-number
            v-model="form.max_items"
            :min="10"
            :max="1000"
            :step="10"
            style="width: 150px"
          />
          <span style="margin-left: 8px; color: #909399;">个内容</span>
          <div class="form-tip">
            建议首次采集不超过200个内容，避免触发平台限制
          </div>
        </el-form-item>

        <el-form-item label="执行方式" prop="execution_mode">
          <el-radio-group v-model="form.execution_mode">
            <el-radio value="immediate">立即执行</el-radio>
            <el-radio value="scheduled">定时执行</el-radio>
          </el-radio-group>
          <div class="form-tip">
            选择任务的执行方式
          </div>
        </el-form-item>

        <el-form-item
          label="执行时间"
          v-if="form.execution_mode === 'scheduled'"
        >
          <el-date-picker
            v-model="form.scheduled_time"
            type="datetime"
            placeholder="选择执行时间"
            style="width: 100%"
            :disabled-date="disabledDate"
          />
          <div class="form-tip">
            任务将在指定时间自动执行
          </div>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-radio-group v-model="form.time_range_type">
            <el-radio value="all">全部时间</el-radio>
            <el-radio value="recent">最近内容</el-radio>
            <el-radio value="custom">自定义范围</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="最近天数"
          v-if="form.time_range_type === 'recent'"
        >
          <el-select v-model="form.recent_days" style="width: 200px">
            <el-option label="最近7天" :value="7" />
            <el-option label="最近30天" :value="30" />
            <el-option label="最近90天" :value="90" />
            <el-option label="最近180天" :value="180" />
          </el-select>
        </el-form-item>

        <el-form-item
          label="时间范围"
          v-if="form.time_range_type === 'custom'"
        >
          <el-date-picker
            v-model="form.custom_date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="任务优先级">
          <el-radio-group v-model="form.priority">
            <el-radio value="low">低优先级</el-radio>
            <el-radio value="normal">普通优先级</el-radio>
            <el-radio value="high">高优先级</el-radio>
          </el-radio-group>
          <div class="form-tip">
            高优先级任务会优先执行
          </div>
        </el-form-item>

        <el-form-item label="失败处理">
          <el-checkbox v-model="form.auto_retry">自动重试</el-checkbox>
          <el-checkbox v-model="form.skip_errors">跳过错误继续</el-checkbox>
          <el-checkbox v-model="form.notify_completion">完成时通知</el-checkbox>
        </el-form-item>

        <el-form-item label="高级选项">
          <el-collapse>
            <el-collapse-item title="更多配置" name="advanced">
              <el-form-item label="并发数量">
                <el-input-number
                  v-model="form.concurrent_limit"
                  :min="1"
                  :max="10"
                  style="width: 150px"
                />
                <span style="margin-left: 8px; color: #909399;">个并发</span>
              </el-form-item>

              <el-form-item label="请求间隔">
                <el-input-number
                  v-model="form.request_delay"
                  :min="1"
                  :max="10"
                  style="width: 150px"
                />
                <span style="margin-left: 8px; color: #909399;">秒</span>
              </el-form-item>

              <el-form-item label="其他选项">
                <el-checkbox v-model="form.include_metadata">包含元数据</el-checkbox>
                <el-checkbox v-model="form.save_raw_data">保存原始数据</el-checkbox>
                <el-checkbox v-model="form.enable_proxy">使用代理</el-checkbox>
              </el-form-item>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
      </el-form>

      <!-- 任务预览 -->
      <div class="task-preview">
        <h4>📋 任务预览</h4>
        <div class="preview-content">
          <div class="preview-item">
            <span class="preview-label">任务名称:</span>
            <span class="preview-value">{{ form.task_name || '未设置' }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">目标账号:</span>
            <span class="preview-value">{{ account?.account_name }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">采集内容:</span>
            <span class="preview-value">{{ getContentTypesText() }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">采集数量:</span>
            <span class="preview-value">{{ form.max_items }} 个</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">执行方式:</span>
            <span class="preview-value">{{ form.execution_mode === 'immediate' ? '立即执行' : '定时执行' }}</span>
          </div>
          <div class="preview-item" v-if="form.execution_mode === 'scheduled' && form.scheduled_time">
            <span class="preview-label">执行时间:</span>
            <span class="preview-value">{{ formatDateTime(form.scheduled_time) }}</span>
          </div>
        </div>

        <el-alert
          :title="getEstimateText()"
          type="info"
          :closable="false"
          show-icon
          style="margin-top: 12px"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ form.execution_mode === 'immediate' ? '立即创建任务' : '创建定时任务' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { startVideoCollectTask, getAccountMonths } from '@/api/video-collect'

interface Props {
  modelValue: boolean
  account: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  task_name: '',
  content_types: ['video'] as string[],
  collect_depth: 'detailed',
  max_items: 100,
  execution_mode: 'immediate',
  scheduled_time: null as Date | null,
  time_range_type: 'recent',
  recent_days: 30,
  custom_date_range: null as [Date, Date] | null,
  priority: 'normal',
  auto_retry: true,
  skip_errors: true,
  notify_completion: true,
  concurrent_limit: 3,
  request_delay: 2,
  include_metadata: true,
  save_raw_data: false,
  enable_proxy: false
})

// 表单验证规则
const rules: FormRules = {
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '任务名称长度应在2-50字符之间', trigger: 'blur' }
  ],
  content_types: [
    { required: true, type: 'array', min: 1, message: '请至少选择一种内容类型', trigger: 'change' }
  ],
  collect_depth: [
    { required: true, message: '请选择采集深度', trigger: 'change' }
  ],
  max_items: [
    { required: true, message: '请设置采集数量', trigger: 'blur' },
    { type: 'number', min: 10, max: 1000, message: '采集数量应在10-1000之间', trigger: 'blur' }
  ],
  execution_mode: [
    { required: true, message: '请选择执行方式', trigger: 'change' }
  ]
}

// 计算属性
const defaultTaskName = computed(() => {
  if (!props.account) return ''
  const timestamp = new Date().toLocaleDateString('zh-CN').replace(/\//g, '')
  return `${props.account.account_name}_采集任务_${timestamp}`
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  } else if (props.account && !form.task_name) {
    // 自动生成任务名称
    form.task_name = defaultTaskName.value
  }
})

// 方法
const resetForm = () => {
  Object.assign(form, {
    task_name: '',
    content_types: ['video'],
    collect_depth: 'detailed',
    max_items: 100,
    execution_mode: 'immediate',
    scheduled_time: null,
    time_range_type: 'recent',
    recent_days: 30,
    custom_date_range: null,
    priority: 'normal',
    auto_retry: true,
    skip_errors: true,
    notify_completion: true,
    concurrent_limit: 3,
    request_delay: 2,
    include_metadata: true,
    save_raw_data: false,
    enable_proxy: false
  })
}

const disabledDate = (time: Date) => {
  // 禁用过去的日期
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getContentTypesText = () => {
  const typeMap = {
    'video': '视频',
    'image': '图片',
    'text': '文本'
  }
  return form.content_types.map(type => typeMap[type as keyof typeof typeMap]).join('、') || '未选择'
}

const formatDateTime = (date: Date | null) => {
  if (!date) return ''
  return date.toLocaleString('zh-CN')
}

const getEstimateText = () => {
  const depth = form.collect_depth
  const count = form.max_items
  const types = form.content_types.length

  let timeEstimate = ''
  let baseTime = 0

  // 根据采集深度估算时间
  if (depth === 'basic') {
    baseTime = count * 0.5 // 每个内容0.5分钟
  } else if (depth === 'detailed') {
    baseTime = count * 1 // 每个内容1分钟
  } else {
    baseTime = count * 2 // 每个内容2分钟
  }

  // 根据内容类型调整时间
  baseTime = baseTime * types * 0.8

  const minutes = Math.ceil(baseTime)
  if (minutes >= 60) {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    timeEstimate = `约需 ${hours} 小时 ${remainingMinutes} 分钟`
  } else {
    timeEstimate = `约需 ${minutes} 分钟`
  }

  return `预计采集 ${count} 个内容，${timeEstimate}完成`
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 构建采集任务数据
    const collectTask = {
      task_name: form.task_name,
      account_id: props.account._id,
      account_name: props.account.account_name,
      account_url: props.account.account_url,
      platform: props.account.platform,
      task_config: {
        content_types: form.content_types,
        collect_depth: form.collect_depth,
        max_items: form.max_items,
        execution_mode: form.execution_mode,
        scheduled_time: form.scheduled_time?.toISOString(),
        time_range: {
          type: form.time_range_type,
          recent_days: form.recent_days,
          custom_range: form.custom_date_range ? {
            start: form.custom_date_range[0].toISOString(),
            end: form.custom_date_range[1].toISOString()
          } : null
        },
        priority: form.priority,
        options: {
          auto_retry: form.auto_retry,
          skip_errors: form.skip_errors,
          notify_completion: form.notify_completion,
          concurrent_limit: form.concurrent_limit,
          request_delay: form.request_delay,
          include_metadata: form.include_metadata,
          save_raw_data: form.save_raw_data,
          enable_proxy: form.enable_proxy
        }
      }
    }

    console.log('提交采集任务:', collectTask)

    // 调用实际的API
    const response = await startVideoCollectTask(collectTask)

    // 处理API响应
    const data = response.data || response
    if (data.success) {
      const taskType = form.execution_mode === 'immediate' ? '采集任务已创建并启动' : '定时采集任务已创建'
      ElMessage.success(taskType + '！')
      emit('success')
      dialogVisible.value = false
    } else {
      throw new Error(data.error || '创建采集任务失败')
    }

  } catch (error: any) {
    console.error('启动采集任务失败:', error)
    ElMessage.error(error.message || '启动采集任务失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.collect-config {
  max-height: 70vh;
  overflow-y: auto;
}

.account-info-section {
  margin-bottom: 20px;
}

.account-info-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.account-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #409eff;
}

.account-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.account-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  flex-shrink: 0;
}

.account-details {
  flex: 1;
  min-width: 0;
}

.account-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.account-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.account-url {
  font-size: 12px;
}

.url-link {
  color: #409eff;
  text-decoration: none;
  word-break: break-all;
}

.url-link:hover {
  text-decoration: underline;
}

.content-option {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.content-icon {
  font-size: 16px;
}

.content-text {
  font-size: 14px;
}

.depth-option {
  margin-left: 8px;
}

.depth-title {
  font-weight: 600;
  color: #303133;
}

.depth-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.task-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #409eff;
  margin-top: 20px;
}

.task-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  align-items: center;
}

.preview-label {
  width: 80px;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
}

.preview-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.estimate-info {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-radio) {
  display: block;
  margin-bottom: 12px;
  margin-right: 0;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
}
</style>
