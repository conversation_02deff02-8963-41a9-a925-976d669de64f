import request from '../../utils/request'

export interface DeviceMapping {
  id: string
  account_id: string
  platform_id: string
  app_id: string
  created_at?: string
  updated_at?: string
  settings?: {
    auto_login?: boolean
    keep_alive?: boolean
    [key: string]: any
  }
}

export interface Device {
  id: string
  name: string
  status: 'running'|'stopped'|'error'|'starting'|'stopping'|'unknown'
  config?: Record<string, any>
  updated_at: string
  core_id?: string  // Core服务ID
  mappings?: DeviceMapping[]  // 设备关联信息
}

export const getDevices = async (params?: {
  include_config?: boolean
  include_mappings?: boolean
  core_id?: string
}): Promise<Device[]> => {
  const res = await request.get<Device[]>('/api/devices', {
    params: {
      include_config: true,
      ...params
    }
  })
  return res.data
}

export const startDevice = async (id: string): Promise<void> => {
  await request.post(`/api/devices/${id}/start`)
}

export const stopDevice = async (id: string): Promise<void> => {
  await request.post(`/api/devices/${id}/stop`)
}

export const batchOperation = async (
  operation: 'start'|'stop',
  ids: string[]
): Promise<void> => {
  await request.post('/api/devices/batch', {
    operation,
    device_ids: ids
  })
}

export const getCoreServices = async (): Promise<{id: string, name: string}[]> => {
  const res = await request.get<{id: string, name: string}[]>('/api/v1/cores')
  return res.data
}