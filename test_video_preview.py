#!/usr/bin/env python3
"""
测试视频预览功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加core目录到Python路径
core_dir = Path(__file__).parent / "core"
sys.path.insert(0, str(core_dir))

from core.src.services.video_preview_service import VideoPreviewService
from core.src.services.file_server import start_file_server, stop_file_server

async def test_video_preview():
    """测试视频预览功能"""
    
    # 启动文件服务器
    print("启动文件服务器...")
    try:
        file_server = await start_file_server(host="localhost", port=8001)
        print("文件服务器启动成功: http://localhost:8001")
    except Exception as e:
        print(f"启动文件服务器失败: {e}")
        return
    
    # 创建视频预览服务
    video_service = VideoPreviewService()
    
    # 测试视频文件路径（请根据实际情况修改）
    test_video_path = r"H:\PublishSystem\test_video.mp4"
    
    # 如果测试视频不存在，创建一个示例路径
    if not os.path.exists(test_video_path):
        print(f"测试视频文件不存在: {test_video_path}")
        print("请将一个MP4视频文件放在上述路径，或修改test_video_path变量")
        await stop_file_server()
        return
    
    try:
        print(f"测试视频文件: {test_video_path}")
        
        # 测试获取视频信息
        print("\n1. 测试获取视频信息...")
        video_info = await video_service.get_video_info(test_video_path)
        print(f"视频时长: {video_info.get('duration', 0)} 秒")
        print(f"视频分辨率: {video_info['video']['width']}x{video_info['video']['height']}")
        
        # 测试生成缩略图
        print("\n2. 测试生成缩略图...")
        thumbnail_result = await video_service.generate_thumbnail(
            video_path=test_video_path,
            max_width=320,
            max_height=180,
            quality=85
        )
        
        if thumbnail_result['success']:
            print(f"缩略图生成成功:")
            print(f"  路径: {thumbnail_result['thumbnail_path']}")
            print(f"  HTTP URL: {thumbnail_result['thumbnail_url']}")
            print(f"  尺寸: {thumbnail_result['actual_width']}x{thumbnail_result['actual_height']}")
            print(f"  文件大小: {thumbnail_result['thumbnail_size']} 字节")
            print(f"  生成时间: {thumbnail_result['generation_time_ms']} 毫秒")
            
            # 测试HTTP访问
            print(f"\n3. 测试HTTP访问...")
            print(f"请在浏览器中访问: {thumbnail_result['thumbnail_url']}")
            
        else:
            print(f"缩略图生成失败: {thumbnail_result.get('error', '未知错误')}")
        
        # 测试生成预览片段
        print("\n4. 测试生成预览片段...")
        preview_result = await video_service.generate_preview_clip(
            video_path=test_video_path,
            start_time=0,
            duration=10,
            output_quality='medium'
        )
        
        if preview_result['success']:
            print(f"预览片段生成成功:")
            print(f"  路径: {preview_result['preview_clip_path']}")
            print(f"  文件大小: {preview_result['preview_clip_size']} 字节")
        else:
            print(f"预览片段生成失败: {preview_result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 停止文件服务器
        print("\n停止文件服务器...")
        await stop_file_server()
        print("测试完成")

if __name__ == "__main__":
    asyncio.run(test_video_preview())
