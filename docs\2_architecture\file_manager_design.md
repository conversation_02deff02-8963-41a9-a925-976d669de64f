# 文件管理模块设计文档

**版本**: v1.0.0  
**最后更新**: 2025/01/15  

## 1. 模块概述

文件管理模块是ThunderHub系统中用于管理和下载竞品内容的核心模块，提供竞品内容的收集、存储、分析和管理功能。

### 1.1 主要功能

- **竞品内容下载**: 支持从YouTube、TikTok、Instagram等平台下载竞品内容
- **内容分类管理**: 提供灵活的分类体系，支持多级分类
- **元数据提取**: 自动提取视频、图片等内容的元数据信息
- **去重检测**: 基于文件哈希值的智能去重机制
- **批量操作**: 支持批量下载、分类、标签管理
- **搜索过滤**: 提供强大的搜索和过滤功能
- **内容分析**: 集成情感分析、关键词提取等AI功能

### 1.2 技术架构

```mermaid
graph TD
    A[前端文件管理界面] --> B[Backend API]
    B --> C[文件管理服务]
    B --> D[下载任务服务]
    B --> E[内容分析服务]
    
    C --> F[MongoDB - 内容数据]
    D --> G[Redis - 任务队列]
    E --> H[AI分析引擎]
    
    I[下载器] --> J[本地存储]
    I --> K[元数据提取器]
    K --> F
```

## 2. 数据库设计

### 2.1 核心集合

#### competitor_content (竞品内容)
- 存储下载的竞品内容信息
- 包含文件信息、元数据、分析结果
- 支持多种内容类型：视频、图片、音频、文本

#### content_categories (内容分类)
- 层级分类体系
- 支持自定义分类和标签
- 颜色和图标标识

#### download_tasks (下载任务)
- 下载任务管理
- 支持单个、批量、频道、播放列表下载
- 实时进度跟踪

### 2.2 索引设计

```javascript
// 竞品内容索引
db.competitor_content.createIndex({ "platform": 1, "content_type": 1 })
db.competitor_content.createIndex({ "metadata.tags": 1 })
db.competitor_content.createIndex({ "file_info.hash": 1 }, { unique: true })
db.competitor_content.createIndex({ "download_info.download_date": -1 })
db.competitor_content.createIndex({ "metadata.publish_date": -1 })

// 分类索引
db.content_categories.createIndex({ "parent_id": 1, "sort_order": 1 })
db.content_categories.createIndex({ "name": 1 }, { unique: true })

// 下载任务索引
db.download_tasks.createIndex({ "status": 1, "created_at": -1 })
db.download_tasks.createIndex({ "created_by": 1, "created_at": -1 })
```

## 3. API设计

### 3.1 内容管理API

```http
# 获取内容列表
GET /api/v1/content/list
Query: page, limit, platform, category, tags, search

# 获取内容详情
GET /api/v1/content/{content_id}

# 更新内容信息
PUT /api/v1/content/{content_id}

# 删除内容
DELETE /api/v1/content/{content_id}

# 批量操作
POST /api/v1/content/batch
Body: { action: "delete|categorize|tag", content_ids: [], data: {} }
```

### 3.2 下载管理API

```http
# 创建下载任务
POST /api/v1/download/tasks
Body: { urls: [], config: {}, filters: {} }

# 获取下载任务列表
GET /api/v1/download/tasks

# 获取任务详情
GET /api/v1/download/tasks/{task_id}

# 取消任务
POST /api/v1/download/tasks/{task_id}/cancel

# 重试失败项
POST /api/v1/download/tasks/{task_id}/retry
```

### 3.3 分类管理API

```http
# 获取分类树
GET /api/v1/categories/tree

# 创建分类
POST /api/v1/categories

# 更新分类
PUT /api/v1/categories/{category_id}

# 删除分类
DELETE /api/v1/categories/{category_id}
```

## 4. 前端组件设计

### 4.1 主要组件

- **ContentManager.vue**: 主文件管理界面
- **ContentList.vue**: 内容列表组件
- **ContentDetail.vue**: 内容详情组件
- **DownloadTaskManager.vue**: 下载任务管理
- **CategoryManager.vue**: 分类管理
- **ContentSearch.vue**: 搜索过滤组件
- **BatchOperations.vue**: 批量操作组件

### 4.2 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ 文件管理中心                                                  │
├─────────────┬───────────────────────────────────────────────┤
│ 分类树      │ 内容列表                                        │
│ - 全部内容  │ ┌─────────────────────────────────────────────┐ │
│ - 视频      │ │ 搜索框 | 过滤器 | 批量操作                   │ │
│ - 图片      │ ├─────────────────────────────────────────────┤ │
│ - 音频      │ │ 内容卡片网格/列表视图                       │ │
│ - 自定义分类│ │ [缩略图] [标题] [平台] [时间] [操作]         │ │
│             │ └─────────────────────────────────────────────┘ │
├─────────────┼───────────────────────────────────────────────┤
│ 下载任务    │ 内容详情/预览                                   │
│ - 进行中    │ ┌─────────────────────────────────────────────┐ │
│ - 已完成    │ │ 内容预览 | 元数据 | 分析结果                │ │
│ - 失败      │ └─────────────────────────────────────────────┘ │
└─────────────┴───────────────────────────────────────────────┘
```

## 5. 实施计划

### 第一阶段：基础架构 (1-2周)
1. 数据库集合设计和创建
2. 基础API接口开发
3. 前端基础组件开发

### 第二阶段：核心功能 (2-3周)
1. 内容管理功能
2. 分类管理功能
3. 基础搜索过滤

### 第三阶段：下载功能 (2-3周)
1. 下载器集成
2. 任务管理系统
3. 进度跟踪

### 第四阶段：高级功能 (1-2周)
1. 批量操作
2. 内容分析
3. 性能优化

## 6. 技术要点

### 6.1 文件存储策略
- 本地存储：`/data/competitor_content/{platform}/{category}/{date}/`
- 文件命名：`{hash}_{original_name}.{ext}`
- 缩略图生成：自动生成视频和图片缩略图

### 6.2 下载器集成
- 支持yt-dlp、gallery-dl等下载工具
- 异步任务队列处理
- 失败重试机制

### 6.3 性能优化
- 分页加载
- 虚拟滚动
- 图片懒加载
- 缓存策略
