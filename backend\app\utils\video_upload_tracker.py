"""
视频上传跟踪工具
用于在上传过程中更新视频去重记录
"""

import logging
import os
from typing import Optional
from app.core.services.video_deduplication import VideoDeduplicationService
from app.core.database import get_mongo_db

logger = logging.getLogger(__name__)

class VideoUploadTracker:
    """视频上传跟踪器"""
    
    def __init__(self):
        self.db = get_mongo_db()
        self.dedup_service = VideoDeduplicationService(self.db)
    
    def update_upload_success(self, video_path: str, task_id: str, youtube_video_id: str) -> bool:
        """更新上传成功状态
        
        Args:
            video_path: 视频文件路径
            task_id: 任务ID
            youtube_video_id: YouTube视频ID
            
        Returns:
            bool: 是否更新成功
        """
        try:
            success = self.dedup_service.update_upload_status(
                video_path, task_id, "success", youtube_video_id=youtube_video_id
            )
            
            if success:
                logger.info(f"更新上传成功状态: {task_id} -> {youtube_video_id}")
            else:
                logger.warning(f"更新上传成功状态失败: {task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新上传成功状态异常: {str(e)}")
            return False
    
    def update_upload_failure(self, video_path: str, task_id: str, error_message: str) -> bool:
        """更新上传失败状态
        
        Args:
            video_path: 视频文件路径
            task_id: 任务ID
            error_message: 错误信息
            
        Returns:
            bool: 是否更新成功
        """
        try:
            success = self.dedup_service.update_upload_status(
                video_path, task_id, "failed", error_message=error_message
            )
            
            if success:
                logger.info(f"更新上传失败状态: {task_id} -> {error_message}")
            else:
                logger.warning(f"更新上传失败状态失败: {task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新上传失败状态异常: {str(e)}")
            return False
    
    def update_upload_progress(self, video_path: str, task_id: str, status: str) -> bool:
        """更新上传进度状态
        
        Args:
            video_path: 视频文件路径
            task_id: 任务ID
            status: 状态（processing, uploading等）
            
        Returns:
            bool: 是否更新成功
        """
        try:
            success = self.dedup_service.update_upload_status(
                video_path, task_id, status
            )
            
            if success:
                logger.info(f"更新上传进度状态: {task_id} -> {status}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新上传进度状态异常: {str(e)}")
            return False
    
    def check_if_duplicate(self, video_path: str, platform_id: str, account_id: str) -> tuple:
        """检查视频是否重复
        
        Args:
            video_path: 视频文件路径
            platform_id: 平台ID
            account_id: 账号ID
            
        Returns:
            tuple: (是否重复, 重复记录)
        """
        try:
            return self.dedup_service.is_duplicate_for_account(
                video_path, platform_id, account_id
            )
        except Exception as e:
            logger.error(f"检查视频重复异常: {str(e)}")
            return False, None
    
    def get_upload_history(self, video_path: str) -> list:
        """获取视频上传历史
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            list: 上传历史记录
        """
        try:
            return self.dedup_service.get_upload_history(video_path)
        except Exception as e:
            logger.error(f"获取上传历史异常: {str(e)}")
            return []

# 全局实例
upload_tracker = VideoUploadTracker()

def get_upload_tracker() -> VideoUploadTracker:
    """获取上传跟踪器实例"""
    return upload_tracker
