<template>
  <div class="benchmark-download">
    <!-- 页面头部 -->
    <div class="management-header">
      <h1>📥 对标账号下载</h1>
      <p class="header-description">以我们的账号为主角，管理和下载对标账号内容到对应的账号目录</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-icon">👤</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total_our_accounts }}</div>
                <div class="stat-label">我们的账号</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card benchmark">
            <div class="stat-content">
              <div class="stat-icon">🎯</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total_benchmark_accounts }}</div>
                <div class="stat-label">对标账号</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card downloading">
            <div class="stat-content">
              <div class="stat-icon">⬇️</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.downloading_tasks }}</div>
                <div class="stat-label">下载中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card completed">
            <div class="stat-content">
              <div class="stat-icon">✅</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.completed_today }}</div>
                <div class="stat-label">今日完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选说明 -->
    <div class="filter-help" style="margin-bottom: 16px;">
      <el-alert
        title="筛选说明：这里筛选的是我们的账号（有对标账号的），不是对标账号本身。例如：我们的YouTube账号 → 对标抖音账号"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="showCreateDownloadDialog = true">
          <el-icon><Download /></el-icon>
          创建下载任务
        </el-button>
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      <div class="toolbar-right">
        <!-- Core服务选择 -->
        <el-select
          v-model="selectedCoreService"
          placeholder="选择Core服务"
          style="width: 180px"
          @change="loadOurAccounts"
        >
          <el-option label="所有Core服务" value="" />
          <el-option
            v-for="service in coreServices"
            :key="service.id"
            :value="service.id"
          >
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>{{ service.name }}</span>
              <el-tag
                :type="getCoreServiceStatusColor(service.status || 'unknown')"
                size="small"
              >
                {{ getCoreServiceStatusText(service.status || 'unknown') }}
              </el-tag>
            </div>
          </el-option>
        </el-select>

        <el-select v-model="filterPlatform" placeholder="我们的平台筛选" style="width: 140px; margin-left: 10px" @change="loadOurAccounts">
          <el-option label="全部我们的平台" value="" />
          <el-option label="YouTube" value="youtube" />
          <el-option label="TikTok" value="tiktok" />
          <el-option label="Instagram" value="instagram" />
          <el-option label="抖音" value="douyin" />
        </el-select>

        <el-select v-model="filterAccountStatus" placeholder="账号状态" style="width: 120px; margin-left: 10px" @change="loadOurAccounts">
          <el-option label="全部" value="" />
          <el-option label="活跃" value="active" />
          <el-option label="暂停" value="inactive" />
          <el-option label="已停用" value="suspended" />
        </el-select>

        <el-input
          v-model="searchQuery"
          placeholder="搜索我们的账号..."
          style="width: 200px; margin-left: 10px"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 我们的账号列表 -->
    <div class="content-list">
      <el-table
        :data="ourAccounts"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        row-key="id"
        :expand-row-keys="expandedRows"
        @expand-change="handleExpandChange"
      >
        <template #empty>
          <div style="padding: 40px; text-align: center;">
            <el-icon size="48" style="color: #c0c4cc; margin-bottom: 16px;">
              <User />
            </el-icon>
            <p style="color: #909399; margin: 0;">暂无已关联对标账号的账号</p>
            <p style="color: #c0c4cc; font-size: 12px; margin: 8px 0 0 0;">
              请先在对标账号管理中为我们的账号添加对标账号
            </p>
          </div>
        </template>

        <el-table-column type="expand" width="55">
          <template #default="{ row: ourAccount }">
            <div class="benchmark-accounts-section">
              <div class="section-header">
                <h4>🎯 {{ ourAccount.display_name || ourAccount.username }} 的对标账号</h4>
                <div class="section-actions">
                  <el-button
                    type="success"
                    size="small"
                    @click="downloadAllBenchmarks(ourAccount)"
                    :disabled="!ourAccount.benchmark_count || ourAccount.benchmark_count === 0"
                  >
                    批量下载
                  </el-button>
                </div>
              </div>

              <div v-if="ourAccount.benchmark_accounts && ourAccount.benchmark_accounts.length > 0" class="benchmark-list">
                <el-table
                  :data="ourAccount.benchmark_accounts"
                  size="small"
                  style="width: 100%"
                >
                  <el-table-column type="selection" width="40" />

                  <el-table-column label="对标账号" width="250">
                    <template #default="{ row: benchmark }">
                      <div class="benchmark-info">
                        <div class="benchmark-name">{{ benchmark.account_name }}</div>
                        <div class="benchmark-meta">
                          <el-tag size="small" :type="getBenchmarkTypeColor(benchmark.benchmark_type)">
                            {{ getBenchmarkTypeText(benchmark.benchmark_type) }}
                          </el-tag>
                          <span class="priority-stars" style="margin-left: 8px;">
                            {{ '★'.repeat(benchmark.priority) }}{{ '☆'.repeat(5 - benchmark.priority) }}
                          </span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="账号数据" width="200">
                    <template #default="{ row: benchmark }">
                      <div class="account-stats">
                        <div v-if="benchmark.account_data?.followers" class="stat-item">
                          <span class="stat-label">粉丝:</span>
                          <span class="stat-value">{{ formatNumber(benchmark.account_data.followers) }}</span>
                        </div>
                        <div v-if="benchmark.account_data?.posts_count" class="stat-item">
                          <span class="stat-label">内容:</span>
                          <span class="stat-value">{{ formatNumber(benchmark.account_data.posts_count) }}</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="采集内容" width="180">
                    <template #default="{ row: benchmark }">
                      <div class="content-status">
                        <div v-if="benchmark.content_stats" class="content-info">
                          <div class="content-summary">
                            <span class="content-count">📚 {{ benchmark.content_stats.total || 0 }} 个内容</span>
                            <span class="download-ready">📥 {{ benchmark.content_stats.downloadable || 0 }} 可下载</span>
                          </div>
                          <div class="content-actions">
                            <el-button
                              size="small"
                              type="primary"
                              link
                              @click="viewAccountContent(benchmark)"
                              v-if="benchmark.content_stats.total > 0"
                            >
                              查看内容
                            </el-button>
                            <el-button
                              size="small"
                              type="success"
                              link
                              @click="createDownloadTask(ourAccount, benchmark)"
                              v-if="benchmark.content_stats.downloadable > 0"
                            >
                              创建下载任务
                            </el-button>
                          </div>
                        </div>
                        <div v-else class="no-content">
                          <el-tag size="small" type="info">无内容</el-tag>
                          <div style="margin-top: 4px;">
                            <el-button
                              size="small"
                              type="primary"
                              link
                              @click="goToCollectPage(benchmark)"
                            >
                              去采集
                            </el-button>
                          </div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="下载路径" min-width="300">
                    <template #default="{ row: benchmark }">
                      <div class="download-path">
                        <div class="path-preview">{{ getBenchmarkDownloadPath(ourAccount, benchmark) }}</div>
                        <div class="path-explanation">
                          <small>{{ ourAccount.platform_name }}/{{ ourAccount.display_name || ourAccount.username }}/{{ benchmark.account_name }}/{{ getCurrentMonth() }}/</small>
                        </div>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="150">
                    <template #default="{ row: benchmark }">
                      <div class="action-buttons">
                        <el-button
                          type="primary"
                          size="small"
                          @click="downloadBenchmarkAccount(ourAccount, benchmark)"
                        >
                          下载
                        </el-button>
                        <el-button
                          size="small"
                          @click="viewBenchmarkDetail(benchmark)"
                        >
                          详情
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div v-else class="no-benchmark-accounts">
                <el-empty description="暂无对标账号" :image-size="60">
                  <div class="empty-tip">
                    <p>请前往对标账号管理页面添加对标账号</p>
                  </div>
                </el-empty>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="我们的账号" width="300" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="our-account-info">
              <div class="account-header">
                <span class="account-name">{{ row.display_name || row.username }}</span>
                <el-tag
                  :type="getAccountStatusColor(row.status)"
                  size="small"
                  style="margin-left: 8px;"
                >
                  {{ getAccountStatusText(row.status) }}
                </el-tag>
              </div>
              <div class="account-meta">
                <el-tag size="small" type="info">{{ row.platform_name }}</el-tag>
                <el-tag size="small" type="success" style="margin-left: 4px;">{{ row.core_service_name }}</el-tag>
              </div>
              <div class="account-stats" v-if="row.followers || row.following">
                <span v-if="row.followers" class="stat-item">粉丝: {{ formatNumber(row.followers) }}</span>
                <span v-if="row.following" class="stat-item" style="margin-left: 8px;">关注: {{ formatNumber(row.following) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="设备信息" width="200">
          <template #default="{ row }">
            <div class="device-info">
              <div v-if="row.device_name" class="device-linked">
                <div class="device-name">📱 {{ row.device_name }}</div>
                <div class="device-status">
                  <el-tag size="small" :type="getDeviceStatusColor(row.device_status || 'unknown')">
                    {{ getDeviceStatusText(row.device_status || 'unknown') }}
                  </el-tag>
                </div>
              </div>
              <div v-else class="device-unlinked">
                <el-tag size="small" type="warning">未关联设备</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="对标账号数量" width="150">
          <template #default="{ row }">
            <div class="benchmark-count">
              <el-tag size="large" :type="getBenchmarkCountColor(row.benchmark_count)">
                {{ row.benchmark_count || 0 }} 个对标账号
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="下载路径" width="300" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="download-path">
              <div class="path-preview">{{ getAccountDownloadPath(row) }}</div>
              <div class="path-explanation">
                <small>基础路径/{{ row.platform_name || getPlatformName(row.platform_id) }}/{{ row.display_name || row.username }}/</small>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="downloadAllBenchmarks(row)"
                :disabled="!row.benchmark_count || row.benchmark_count === 0"
              >
                下载全部
              </el-button>
              <el-button
                size="small"
                @click="manageAccount(row)"
              >
                管理
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="totalCount"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="loadOurAccounts"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 创建下载任务对话框 -->
    <CreateDownloadDialog
      v-model="showCreateDownloadDialog"
      :core-services="coreServices"
      :our-accounts="ourAccounts"
      @created="handleDownloadTaskCreated"
    />

    <!-- 其他对话框组件 - 暂时注释掉以避免加载问题 -->
    <!--
    <AccountManagementDialog
      v-model="showManagementDialog"
      :account="selectedAccount"
      @updated="handleAccountUpdated"
    />

    <BenchmarkDetailDialog
      v-model="showBenchmarkDetailDialog"
      :benchmark-account="selectedBenchmarkAccount"
    />

    <AddBenchmarkDialog
      v-model="showAddBenchmarkDialog"
      :our-account="selectedAccount"
      @added="handleBenchmarkAdded"
    />
    -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import {
  Download,
  Refresh,
  Search,
  User
} from '@element-plus/icons-vue'
import { getCoreServices } from '@/api/core'
import { getAccounts } from '@/api/social'
import {
  getBenchmarkAccounts,
  createBenchmarkDownloadTask,
  createBatchBenchmarkDownloadTasks
} from '@/api/content'
import CreateDownloadDialog from './components/CreateDownloadDialog.vue'
// 暂时注释掉其他组件导入以避免加载问题
// import AccountManagementDialog from './components/AccountManagementDialog.vue'
// import BenchmarkDetailDialog from './components/BenchmarkDetailDialog.vue'
// import AddBenchmarkDialog from './components/AddBenchmarkDialog.vue'

// 定义数据类型
interface OurAccount {
  id: string
  username: string
  display_name?: string
  platform_id: string
  platform_name?: string
  core_service_id: string
  core_service_name?: string
  status: string
  followers?: number
  following?: number
  posts_count?: number
  device_id?: string
  device_name?: string
  device_status?: string
  benchmark_count?: number
  benchmark_accounts?: BenchmarkAccount[]
}

interface BenchmarkAccount {
  _id: string
  account_name: string
  platform: string
  benchmark_type: string
  priority: number
  status: string
  account_url: string
  our_account_id: string
  description?: string
  tags?: string[]
  last_download_time?: string
  download_status?: string
  account_data?: {
    followers?: number
    posts_count?: number
    engagement_rate?: number
  }
}

interface CoreService {
  id: string
  name: string
  status: string
  file_root_path?: string
}

// 响应式数据
const loading = ref(false)
const ourAccounts = ref<OurAccount[]>([])
const coreServices = ref<CoreService[]>([])
const selectedCoreService = ref('')
const selectedAccount = ref<OurAccount | null>(null)
const selectedBenchmarkAccount = ref<BenchmarkAccount | null>(null)

const stats = ref({
  total_our_accounts: 0,
  total_benchmark_accounts: 0,
  downloading_tasks: 0,
  completed_today: 0
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

// 过滤和搜索
const searchQuery = ref('')
const filterPlatform = ref('')
const filterAccountStatus = ref('')
const selectedItems = ref<OurAccount[]>([])

// 表格展开状态
const expandedRows = ref<string[]>([])

// 对话框状态
const showCreateDownloadDialog = ref(false)
// 其他对话框暂时注释掉
// const showManagementDialog = ref(false)
// const showBenchmarkDetailDialog = ref(false)
// const showAddBenchmarkDialog = ref(false)

// 工具方法
const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getAccountStatusText = (status: string) => {
  const textMap = {
    'active': '活跃',
    'inactive': '暂停',
    'suspended': '已停用'
  }
  return textMap[status as keyof typeof textMap] || status
}

const getAccountStatusColor = (status: string) => {
  const colorMap = {
    'active': 'success',
    'inactive': 'warning',
    'suspended': 'danger'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const getDeviceStatusText = (status: string) => {
  const textMap = {
    'running': '运行中',
    'stopped': '已停止',
    'error': '错误',
    'starting': '启动中',
    'stopping': '停止中',
    'unknown': '未知'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

const getDeviceStatusColor = (status: string) => {
  const colorMap = {
    'running': 'success',
    'stopped': 'info',
    'error': 'danger',
    'starting': 'warning',
    'stopping': 'warning',
    'unknown': ''
  }
  return colorMap[status as keyof typeof colorMap] || ''
}

const getBenchmarkCountColor = (count: number | undefined) => {
  if (!count || count === 0) return 'info'
  if (count <= 3) return 'warning'
  return 'success'
}

const getCoreServiceStatusColor = (status: string) => {
  const colorMap = {
    'running': 'success',
    'active': 'success',
    'stopped': 'danger',
    'error': 'danger',
    'starting': 'warning',
    'unknown': ''
  }
  return colorMap[status as keyof typeof colorMap] || ''
}

const getCoreServiceStatusText = (status: string) => {
  const textMap = {
    'running': '运行中',
    'active': '活跃',
    'stopped': '已停止',
    'error': '错误',
    'starting': '启动中',
    'unknown': '可用'
  }
  return textMap[status as keyof typeof textMap] || '可用'
}

// 移除未使用的状态相关方法

const formatNumber = (num: number | undefined | null) => {
  if (!num || typeof num !== 'number') return '0'
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getCurrentMonth = () => {
  return new Date().toISOString().slice(0, 7)
}

const getAccountDownloadPath = (account: OurAccount) => {
  if (!account) return ''
  const basePath = 'H:\\PublishSystem\\'
  const platformName = account.platform_name || getPlatformName(account.platform_id)
  const accountName = account.display_name || account.username || 'Unknown'
  return `${basePath}${platformName}\\${accountName}\\`
}

const getBenchmarkDownloadPath = (ourAccount: OurAccount, benchmarkAccount: BenchmarkAccount) => {
  if (!ourAccount || !benchmarkAccount) return ''
  const basePath = getAccountDownloadPath(ourAccount)
  const currentMonth = getCurrentMonth()
  return `${basePath}${benchmarkAccount.account_name}\\${currentMonth}\\`
}





// 业务方法
const loadCoreServices = async () => {
  try {
    const res = await getCoreServices()
    const coreServicesData = res.data || res

    const formattedCoreServices = Array.isArray(coreServicesData) ? coreServicesData.map(service => {
      if (!service.id && service._id) {
        return { ...service, id: service._id }
      }
      return service
    }) : []

    coreServices.value = formattedCoreServices
  } catch (error) {
    console.error('获取Core服务列表失败:', error)
    ElMessage.error('获取Core服务列表失败')
    coreServices.value = []
  }
}

const loadOurAccounts = async () => {
  loading.value = true
  try {
    console.log('🚀 开始加载我们的账号和对标账号...')

    // 先测试：直接查询所有对标账号，看看有没有数据
    console.log('📋 先查询所有对标账号...')
    const allBenchmarkResponse = await getBenchmarkAccounts({
      page: 1,
      limit: 100
    })
    console.log('📋 所有对标账号响应:', allBenchmarkResponse)

    if (allBenchmarkResponse && allBenchmarkResponse.items) {
      console.log(`📋 系统中共有 ${allBenchmarkResponse.items.length} 个对标账号`)
      allBenchmarkResponse.items.forEach((benchmark, index) => {
        console.log(`📋 对标账号 ${index + 1}: ${benchmark.account_name} (关联账号ID: ${benchmark.our_account_id})`)
      })
    }

    // 第一步：获取我们的账号列表（暂时不应用筛选，先看所有账号）
    const params: any = {
      limit: 1000 // 获取所有账号
    }

    // 暂时注释掉筛选条件，先获取所有账号看ID格式
    // if (selectedCoreService.value) {
    //   params.core_service_id = selectedCoreService.value
    // }

    // if (filterPlatform.value) {
    //   params.platform_id = filterPlatform.value
    // }

    // if (filterAccountStatus.value) {
    //   params.status = filterAccountStatus.value
    // }

    console.log('👤 调用getAccounts API，参数:', params)
    const accountsResponse = await getAccounts(params)
    console.log('👤 getAccounts API响应:', accountsResponse)
    console.log('👤 响应数据类型:', typeof accountsResponse.data)
    console.log('👤 响应数据内容:', accountsResponse.data)

    // 处理账号响应 - 详细检查数据结构
    let allAccounts: any[] = []

    if (accountsResponse && accountsResponse.data) {
      console.log('👤 检查 accountsResponse.data 的结构...')
      const data = accountsResponse.data as any

      if (Array.isArray(data)) {
        allAccounts = data
        console.log('👤 data 是数组，直接使用')
      } else if (data.data && Array.isArray(data.data)) {
        allAccounts = data.data
        console.log('👤 data.data 是数组，使用 data.data')
      } else if (data.items && Array.isArray(data.items)) {
        allAccounts = data.items
        console.log('👤 data.items 是数组，使用 data.items')
      } else {
        console.warn('⚠️ 无法识别的账号数据结构:', data)
        // 尝试查找所有可能的数组字段
        Object.keys(data).forEach(key => {
          const value = data[key]
          if (Array.isArray(value)) {
            console.log(`👤 发现数组字段 ${key}:`, value)
            if (allAccounts.length === 0) {
              allAccounts = value
              console.log(`👤 使用字段 ${key} 作为账号数组`)
            }
          }
        })
      }
    } else if (Array.isArray(accountsResponse)) {
      allAccounts = accountsResponse
      console.log('👤 响应本身是数组，直接使用')
    } else {
      console.warn('⚠️ 意外的账号API响应格式:', accountsResponse)
      allAccounts = []
    }

    console.log(`👤 获取到 ${allAccounts.length} 个我们的账号:`)
    allAccounts.forEach((account, index) => {
      console.log(`👤 账号 ${index + 1}: ${account.username || account.display_name} (ID: ${account.id})`)
      console.log(`   - 平台ID: ${account.platform_id}`)
      console.log(`   - 设备信息: device_id=${account.device_id}, device_name=${account.device_name}, device_status=${account.device_status}`)
    })

    // 检查ID匹配情况 - 处理时间戳问题
    const benchmarkAccountId = 'youtube_LindaBailey65341071@gmail.com_1747125779'
    console.log(`🔍 查找对标账号关联的我们的账号ID: ${benchmarkAccountId}`)

    // 直接匹配
    let matchingAccount = allAccounts.find(account => account.id === benchmarkAccountId)

    if (!matchingAccount) {
      // 如果直接匹配失败，尝试去掉时间戳匹配
      console.log('🔍 直接匹配失败，尝试去掉时间戳匹配...')

      // 提取基础ID（去掉最后的时间戳）
      const baseId = benchmarkAccountId.replace(/_\d+$/, '') // 去掉最后的 _数字
      console.log(`🔍 基础ID: ${baseId}`)

      matchingAccount = allAccounts.find(account => {
        // 检查账号ID是否以基础ID开头
        const accountBaseId = account.id.replace(/_\d+$/, '')
        return accountBaseId === baseId || account.id.startsWith(baseId)
      })

      if (matchingAccount) {
        console.log(`✅ 通过基础ID匹配找到账号:`, matchingAccount)
      }
    }

    if (matchingAccount) {
      console.log(`✅ 找到匹配的我们的账号:`, matchingAccount)
      console.log(`✅ 这是一个 ${matchingAccount.platform_name || 'Unknown'} 账号，有抖音对标账号`)
    } else {
      console.log(`❌ 未找到ID为 ${benchmarkAccountId} 的我们的账号`)
      console.log('🔍 所有我们的账号ID列表:')
      allAccounts.forEach(account => {
        console.log(`  - ${account.id} (${account.platform_name || 'Unknown'} - ${account.username || account.display_name})`)
      })

      // 尝试模糊匹配
      console.log('🔍 尝试模糊匹配...')
      const possibleMatches = allAccounts.filter(account => {
        return account.id.includes('LindaBailey') ||
               account.username?.includes('LindaBailey') ||
               account.id.includes('youtube')
      })

      if (possibleMatches.length > 0) {
        console.log('🔍 可能匹配的账号:')
        possibleMatches.forEach(account => {
          console.log(`  - ${account.id} (${account.username || account.display_name})`)
        })
      }
    }

    if (allAccounts.length === 0) {
      ourAccounts.value = []
      totalCount.value = 0
      stats.value.total_our_accounts = 0
      stats.value.total_benchmark_accounts = 0
      ElMessage.info('暂无符合条件的账号')
      return
    }

    // 第二步：为每个账号查询对标账号，只保留有对标账号的账号
    const accountsWithBenchmarks: any[] = []

    console.log(`开始为 ${allAccounts.length} 个账号查询对标账号...`)

    // 使用反向查找：从对标账号找到我们的账号
    console.log('🔄 使用反向查找方式：从对标账号数据中提取我们的账号...')

    // 获取所有对标账号
    const allBenchmarkAccounts = allBenchmarkResponse?.items || []

    // 按our_account_id分组，但要处理时间戳问题
    const accountBenchmarkMap = new Map<string, any[]>()

    allBenchmarkAccounts.forEach(benchmark => {
      const ourAccountId = benchmark.our_account_id
      console.log(`🔍 处理对标账号 ${benchmark.account_name}，关联账号ID: ${ourAccountId}`)

      // 尝试找到匹配的我们的账号
      let matchingAccount = allAccounts.find(account => account.id === ourAccountId)

      if (!matchingAccount) {
        // 如果直接匹配失败，尝试去掉时间戳匹配
        const baseId = ourAccountId.replace(/_\d+$/, '')
        matchingAccount = allAccounts.find(account => {
          const accountBaseId = account.id.replace(/_\d+$/, '')
          return accountBaseId === baseId || account.id.startsWith(baseId)
        })

        if (matchingAccount) {
          console.log(`✅ 通过基础ID匹配找到账号: ${matchingAccount.id}`)
        }
      }

      if (matchingAccount) {
        const accountId = matchingAccount.id
        if (!accountBenchmarkMap.has(accountId)) {
          accountBenchmarkMap.set(accountId, [])
        }
        accountBenchmarkMap.get(accountId)!.push(benchmark)
        console.log(`✅ 对标账号 ${benchmark.account_name} 关联到我们的账号 ${accountId}`)
      } else {
        console.warn(`❌ 对标账号 ${benchmark.account_name} 无法找到对应的我们的账号 (ID: ${ourAccountId})`)
      }
    })

    console.log(`🎯 找到 ${accountBenchmarkMap.size} 个有对标账号的我们的账号`)

    // 构建最终结果
    accountBenchmarkMap.forEach((benchmarks, accountId) => {
      const account = allAccounts.find(acc => acc.id === accountId)
      if (account) {
        console.log(`✅ 添加账号 ${account.id} (${account.username || account.display_name})，有 ${benchmarks.length} 个对标账号`)
        accountsWithBenchmarks.push({
          ...account,
          benchmark_count: benchmarks.length,
          benchmark_accounts: benchmarks,
          platform_name: getPlatformName(account.platform_id),
          core_service_name: getCoreServiceName(account.core_service_id)
        })
      }
    })

    console.log('有对标账号的账号列表:', accountsWithBenchmarks)

    // 分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    const paginatedAccounts = accountsWithBenchmarks.slice(startIndex, endIndex)

    ourAccounts.value = paginatedAccounts
    totalCount.value = accountsWithBenchmarks.length

    // 更新统计数据
    stats.value.total_our_accounts = accountsWithBenchmarks.length
    stats.value.total_benchmark_accounts = accountsWithBenchmarks.reduce(
      (sum, account) => sum + (account.benchmark_count || 0), 0
    )

    console.log('最终处理的账号数据:', paginatedAccounts)
    console.log('统计数据:', stats.value)

    if (accountsWithBenchmarks.length === 0) {
      ElMessage.info('暂无关联对标账号的账号')
    }

  } catch (error: any) {
    console.error('加载账号列表失败:', error)

    // 显示更详细的错误信息
    let errorMessage = '加载账号列表失败'
    if (error.response && error.response.data) {
      if (error.response.data.detail) {
        errorMessage += `: ${error.response.data.detail}`
      } else if (error.response.data.message) {
        errorMessage += `: ${error.response.data.message}`
      }
    } else if (error.message) {
      errorMessage += `: ${error.message}`
    }

    ElMessage.error(errorMessage)
    ourAccounts.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

const getPlatformName = (platformId: string) => {
  // 首先尝试简单的平台名称映射
  const platformMap = {
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'douyin': '抖音',
    'instagram': 'Instagram'
  }

  // 如果是简单的平台名称，直接返回
  if (platformMap[platformId as keyof typeof platformMap]) {
    return platformMap[platformId as keyof typeof platformMap]
  }

  // 如果是ObjectId格式，尝试从已知的平台ID映射
  const platformIdMap = {
    '681efeeecd836bd64b9c2a1e': 'YouTube',
    '681efeeecd836bd64b9c2a20': 'TikTok',
    '681efeeecd836bd64b9c2a22': '抖音',
    '681efeeecd836bd64b9c2a24': 'Instagram'
  }

  return platformIdMap[platformId as keyof typeof platformIdMap] || platformId
}

const getCoreServiceName = (coreServiceId: string) => {
  const service = coreServices.value.find(s => s.id === coreServiceId)
  return service ? service.name : coreServiceId
}

const refreshData = async () => {
  await Promise.all([loadCoreServices(), loadOurAccounts()])
  ElMessage.success('数据刷新成功')
}

// 表格展开相关方法
const handleExpandChange = (row: OurAccount, expandedRowsData: OurAccount[]) => {
  if (expandedRowsData.includes(row)) {
    // 展开时加载对标账号详细信息
    if (!expandedRows.value.includes(row.id)) {
      expandedRows.value.push(row.id)
    }
  } else {
    // 收起时移除
    const index = expandedRows.value.indexOf(row.id)
    if (index > -1) {
      expandedRows.value.splice(index, 1)
    }
  }
}

// 账号管理相关方法
const manageAccount = (account: OurAccount) => {
  selectedAccount.value = account
  // showManagementDialog.value = true
  ElMessage.info('账号管理功能开发中...')
}

// 移除添加对标账号功能，使用专门的维护界面

const downloadAllBenchmarks = async (account: OurAccount) => {
  if (!account.benchmark_count || account.benchmark_count === 0) {
    ElMessage.warning('该账号没有对标账号')
    return
  }

  if (!account.benchmark_accounts || account.benchmark_accounts.length === 0) {
    ElMessage.warning('对标账号数据不完整')
    return
  }

  try {
    // 显示批量下载确认对话框
    await ElMessageBox.confirm(
      `确定要批量下载 "${account.display_name || account.username}" 的所有 ${account.benchmark_count} 个对标账号的视频内容吗？\n\n这将创建 ${account.benchmark_count} 个下载任务，可能需要较长时间。`,
      '确认批量下载',
      {
        confirmButtonText: '开始批量下载',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'batch-download-confirm-dialog'
      }
    )

    let loadingInstance = ElLoading.service({
      lock: true,
      text: `正在为 ${account.display_name || account.username} 创建批量下载任务...`,
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 创建批量下载任务
    const batchDownloadTasks = account.benchmark_accounts.map(benchmark => ({
      our_account_id: account.id,
      our_account_name: account.display_name || account.username,
      benchmark_account_id: benchmark._id,
      benchmark_account_name: benchmark.account_name,
      benchmark_account_url: benchmark.account_url,
      platform: benchmark.platform,
      download_path: getBenchmarkDownloadPath(account, benchmark),
      download_config: {
        max_videos: 50,
        video_quality: 'high' as const,
        include_metadata: true,
        skip_existing: true
      }
    }))

    console.log('创建批量下载任务:', batchDownloadTasks)

    // 调用实际的批量下载API
    try {
      const response = await createBatchBenchmarkDownloadTasks(batchDownloadTasks)
      console.log('批量下载任务创建成功:', response)
    } catch (apiError) {
      console.error('批量下载API调用失败，使用模拟模式:', apiError)
      // 如果API调用失败，使用模拟延迟
      await new Promise(resolve => setTimeout(resolve, 3000))
    }

    if (loadingInstance) {
      loadingInstance.close()
    }

    ElMessage.success(`批量下载任务已创建！${account.benchmark_count} 个对标账号的视频将在后台下载`)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建批量下载任务失败:', error)
      ElMessage.error('创建批量下载任务失败，请重试')
    }
  }
}

const downloadBenchmarkAccount = async (ourAccount: OurAccount | null, benchmarkAccount: BenchmarkAccount) => {
  if (!benchmarkAccount) {
    ElMessage.warning('对标账号信息不完整')
    return
  }

  if (!ourAccount) {
    ElMessage.warning('我们的账号信息不完整')
    return
  }

  const downloadPath = getBenchmarkDownloadPath(ourAccount, benchmarkAccount)
  let loadingInstance: any = null

  try {
    // 显示下载确认对话框
    await ElMessageBox.confirm(
      `确定要下载对标账号 "${benchmarkAccount.account_name}" 的视频内容吗？\n\n下载路径：${downloadPath}\n\n这可能需要一些时间，请耐心等待。`,
      '确认下载',
      {
        confirmButtonText: '开始下载',
        cancelButtonText: '取消',
        type: 'info',
        customClass: 'download-confirm-dialog'
      }
    )

    // 创建下载任务
    const downloadTask = {
      our_account_id: ourAccount.id,
      our_account_name: ourAccount.display_name || ourAccount.username,
      benchmark_account_id: benchmarkAccount._id,
      benchmark_account_name: benchmarkAccount.account_name,
      benchmark_account_url: benchmarkAccount.account_url,
      platform: benchmarkAccount.platform,
      download_path: downloadPath,
      download_config: {
        max_videos: 50, // 默认下载最新50个视频
        video_quality: 'high' as const, // 高质量
        include_metadata: true, // 包含元数据
        skip_existing: true // 跳过已存在的文件
      }
    }

    console.log('创建下载任务:', downloadTask)
    console.log('对标账号信息:', {
      _id: benchmarkAccount._id,
      account_name: benchmarkAccount.account_name,
      platform: benchmarkAccount.platform
    })

    // 调用下载API
    loadingInstance = ElLoading.service({
      lock: true,
      text: `正在为 ${benchmarkAccount.account_name} 创建下载任务...`,
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 调用实际的下载API
    try {
      const response = await createBenchmarkDownloadTask(downloadTask)
      console.log('下载任务创建成功:', response)

      if (loadingInstance) {
        loadingInstance.close()
      }
      ElMessage.success(`下载任务已创建！${benchmarkAccount.account_name} 的视频将在后台下载到 ${downloadPath}`)

      // 刷新数据以显示新任务
      await refreshData()
      return

    } catch (apiError: any) {
      console.error('API调用失败:', apiError)
      console.error('错误详情:', apiError.response?.data)

      if (loadingInstance) {
        loadingInstance.close()
      }

      // 显示具体的错误信息
      const errorMessage = apiError.response?.data?.detail || apiError.message || '创建下载任务失败'
      ElMessage.error(`API调用失败: ${errorMessage}`)
      return
    }

  } catch (error) {
    if (loadingInstance) {
      loadingInstance.close()
    }
    if (error !== 'cancel') {
      console.error('创建下载任务失败:', error)
      ElMessage.error('创建下载任务失败，请重试')
    }
  }
}

const viewBenchmarkDetail = (benchmarkAccount: BenchmarkAccount) => {
  selectedBenchmarkAccount.value = benchmarkAccount
  // showBenchmarkDetailDialog.value = true
  ElMessage.info(`查看 ${benchmarkAccount.account_name} 详情功能开发中...`)
}

const handleSearch = () => {
  setTimeout(() => {
    currentPage.value = 1
    loadOurAccounts()
  }, 500)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadOurAccounts()
}

const handleSelectionChange = (selection: OurAccount[]) => {
  selectedItems.value = selection
}

const handleDownloadTaskCreated = () => {
  ElMessage.success('下载任务创建成功')
  loadOurAccounts()
}

// 📚 内容下载相关方法
const viewAccountContent = (benchmarkAccount: BenchmarkAccount) => {
  // 跳转到内容采集页面查看该账号的内容
  ElMessage.info(`跳转到内容采集页面查看 ${benchmarkAccount.account_name} 的内容`)
  // TODO: 可以跳转到内容采集页面并自动筛选该账号
}

const createDownloadTask = async (ourAccount: OurAccount, benchmarkAccount: BenchmarkAccount) => {
  try {
    await ElMessageBox.confirm(
      `确定要为 ${benchmarkAccount.account_name} 创建下载任务吗？`,
      '确认创建下载任务',
      {
        confirmButtonText: '创建任务',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    // 创建内容下载任务
    const downloadTask = {
      our_account_id: ourAccount.id,
      our_account_name: ourAccount.display_name || ourAccount.username,
      benchmark_account_id: benchmarkAccount._id,
      benchmark_account_name: benchmarkAccount.account_name,
      download_type: 'collected_content', // 标识这是下载已采集的内容
      download_path: getBenchmarkDownloadPath(ourAccount, benchmarkAccount),
      download_config: {
        source: 'database', // 从数据库获取内容列表
        max_downloads: 50,
        content_quality: 'high',
        skip_existing: true,
        content_types: ['video', 'image'] // 支持多种内容类型
      }
    }

    console.log('创建内容下载任务:', downloadTask)

    // TODO: 调用内容下载API
    // await createContentDownloadTask(downloadTask)

    ElMessage.success(`${benchmarkAccount.account_name} 的内容下载任务已创建！`)
    await refreshData()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建内容下载任务失败:', error)
      ElMessage.error('创建内容下载任务失败')
    }
  }
}

const goToCollectPage = (benchmarkAccount: BenchmarkAccount) => {
  // 跳转到对标账号管理页面进行采集
  ElMessage.info(`请前往对标账号管理页面为 ${benchmarkAccount.account_name} 进行视频采集`)
  // TODO: 可以直接跳转到对标账号管理页面并自动打开采集对话框
}

// 移除未使用的处理方法
// const handleAccountUpdated = () => {
//   ElMessage.success('账号信息更新成功')
//   loadOurAccounts()
// }

// const handleBenchmarkAdded = () => {
//   ElMessage.success('对标账号添加成功')
//   loadOurAccounts()
// }

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.benchmark-download {
  padding: 20px;
}

.management-header {
  margin-bottom: 24px;
}

.management-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.header-description {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.content-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.account-info {
  padding: 8px 0;
}

.account-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.account-name {
  font-weight: 600;
  color: #303133;
}

.account-meta {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.priority-stars {
  color: #f39c12;
  font-size: 12px;
}

.account-url {
  font-size: 12px;
}

.our-account-info {
  padding: 4px 0;
}

.linked-account .account-display {
  margin-bottom: 4px;
}

.linked-account .platform-name {
  font-weight: 600;
  color: #409eff;
  margin-right: 4px;
}

.linked-account .account-name {
  color: #303133;
}

.account-stats {
  font-size: 12px;
}

.stat-item {
  margin-bottom: 2px;
}

.stat-label {
  color: #909399;
  margin-right: 4px;
}

.stat-value {
  color: #303133;
  font-weight: 500;
}

.download-path {
  font-size: 12px;
}

.path-preview {
  color: #303133;
  margin-bottom: 2px;
  word-break: break-all;
}

.path-explanation {
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 内容状态样式 */
.content-status {
  font-size: 12px;
}

.content-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-summary {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.content-count,
.download-ready {
  color: #666;
  font-size: 11px;
}

.content-actions {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: 4px;
}

.no-content {
  text-align: center;
  padding: 4px 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
