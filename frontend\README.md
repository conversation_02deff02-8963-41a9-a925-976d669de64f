# Frontend模块开发指南

## 当前目录结构

```目录结构
frontend/
├── Dockerfile            # Docker构建文件
├── Dockerfile.builder    # 构建阶段Dockerfile
├── Dockerfile.dependencies # 依赖Dockerfile
├── README.md            # 项目说明
├── index.html           # 主HTML文件
├── nginx.conf           # Nginx配置
├── package.json         # 项目配置
├── package-lock.json    # 依赖锁定文件
├── tsconfig.json        # TypeScript配置
├── tsconfig.node.json   # Node端TS配置
├── vite.config.ts       # Vite TS配置
├── src/                 # 源代码
│   ├── App.vue          # 根组件
│   ├── main.ts          # 应用入口
│   ├── socket.io.ts     # Socket.IO客户端
│   ├── api/             # API接口
│   │   ├── auth.ts      # 认证API
│   │   ├── device.ts    # 设备API
│   │   └── social.ts    # 社交平台API
│   ├── components/      # 公共组件
│   │   └── Layout.vue   # 布局组件
│   ├── router/          # 路由配置
│   │   └── index.ts     # 路由定义
│   ├── stores/          # Pinia状态管理
│   │   └── auth.ts      # 认证状态
│   ├── types/           # 类型定义
│   │   └── social.ts    # 社交平台类型
│   ├── utils/           # 工具函数
│   │   └── request.ts   # 请求封装
│   └── views/           # 页面组件
│       ├── device/      # 设备相关页面
│       ├── doc/         # 文档页面
│       ├── report/      # 报表页面
│       ├── social/      # 社交平台页面
│       └── task/        # 任务页面
└── __tests__/           # 测试文件
```

## 管理指南

### 依赖管理

Frontend模块使用npm进行依赖管理。

#### 安装依赖

```bash
cd frontend && 
npm install
```

#### 更新依赖

```bash
npm update
```

### 运行Frontend模块

```bash
npm run dev  # 开发模式
npm run build  # 生产构建
npm run preview  # 预览生产构建
```

### 开发流程

1. 创建新功能分支
2. 修改代码
3. 运行测试
4. 提交更改
5. 创建Pull Request