"""
gRPC客户端管理模块
"""

import logging
from typing import Optional
from app.core.client import FileServiceClient

logger = logging.getLogger(__name__)

# 全局客户端实例
_file_client: Optional[FileServiceClient] = None

def get_core_client() -> Optional[FileServiceClient]:
    """获取Core服务的文件客户端
    
    Returns:
        FileServiceClient实例，如果连接失败则返回None
    """
    global _file_client
    
    try:
        if _file_client is None:
            # 创建新的客户端实例
            # 这里使用默认的localhost:50051，实际部署时可能需要从配置文件读取
            _file_client = FileServiceClient(host='localhost', port=50051)
            logger.info("已创建Core服务文件客户端")
        
        return _file_client
    
    except Exception as e:
        logger.error(f"获取Core服务客户端失败: {str(e)}")
        return None

def close_core_client():
    """关闭Core服务客户端连接"""
    global _file_client
    
    if _file_client:
        try:
            # 注意：这里是同步关闭，如果需要异步关闭需要在调用处使用await
            # await _file_client.close()
            _file_client = None
            logger.info("已关闭Core服务客户端连接")
        except Exception as e:
            logger.error(f"关闭Core服务客户端失败: {str(e)}")
