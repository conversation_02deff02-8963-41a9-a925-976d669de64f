from abc import ABC, abstractmethod
from typing import Protocol, runtime_checkable, List, Dict, Optional
import logging
import time
from datetime import datetime
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

class PlatformError(Exception):
    """平台操作异常基类"""
    def __init__(self, platform: str, message: str):
        self.platform = platform
        self.message = message
        super().__init__(f"[{platform}] {message}")

@runtime_checkable
class SocialPlatform(Protocol):
    """社交平台统一接口协议"""
    
    @property
    @abstractmethod
    def platform_name(self) -> str:
        """平台名称"""
        pass
    
    @abstractmethod
    def authenticate(self, credentials: dict) -> bool:
        """平台认证"""
        pass
    
    @abstractmethod 
    def post_content(self, content: dict) -> str:
        """发布内容"""
        pass
    
    @abstractmethod
    def get_analytics(self, start_date: str, end_date: str) -> dict:
        """获取分析数据"""
        pass
    
    @abstractmethod
    def get_account_info(self) -> dict:
        """获取账号信息"""
        pass
    
    @abstractmethod
    def interact(self, target_id: str, action: str) -> bool:
        """互动操作"""
        pass

class WeChatAdapter:
    """微信平台适配器实现"""
    
    @property
    def platform_name(self) -> str:
        return "wechat"
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def authenticate(self, credentials: dict) -> bool:
        """微信认证实现"""
        logger.info(f"Authenticating WeChat account: {credentials.get('username')}")
        # 实际调用微信API进行认证
        return True
    
    def post_content(self, content: dict) -> str:
        """发布内容到微信"""
        logger.info(f"Posting to WeChat: {content.get('text')}")
        # 调用微信API发布内容
        return f"wechat_{int(time.time())}"
    
    def get_analytics(self, start_date: str, end_date: str) -> dict:
        """获取微信数据分析"""
        return {
            "platform": "wechat",
            "period": f"{start_date} to {end_date}",
            "metrics": {
                "views": 1000,
                "likes": 200,
                "shares": 50
            }
        }

class WeiboAdapter:
    """微博平台适配器实现"""
    
    @property
    def platform_name(self) -> str:
        return "weibo"
    
    def authenticate(self, credentials: dict) -> bool:
        """微博认证实现"""
        logger.info(f"Authenticating Weibo account: {credentials.get('username')}")
        return True
    
    def post_content(self, content: dict) -> str:
        """发布内容到微博"""
        logger.info(f"Posting to Weibo: {content.get('text')}")
        return f"weibo_{int(time.time())}"

class DouyinAdapter:
    """抖音平台适配器实现"""
    
    @property
    def platform_name(self) -> str:
        return "douyin"
    
    def authenticate(self, credentials: dict) -> bool:
        """抖音认证实现"""
        logger.info(f"Authenticating Douyin account: {credentials.get('username')}")
        return True
    
    def post_content(self, content: dict) -> str:
        """发布内容到抖音"""
        logger.info(f"Posting to Douyin: {content.get('text')}")
        return f"douyin_{int(time.time())}"

class YouTubeAdapter:
    """YouTube平台适配器"""
    
    @property
    def platform_name(self) -> str:
        return "youtube"
    
    def authenticate(self, credentials: dict) -> bool:
        """YouTube认证实现"""
        logger.info(f"Authenticating YouTube account: {credentials.get('email')}")
        return True
    
    def post_content(self, content: dict) -> str:
        """发布视频到YouTube"""
        logger.info(f"Uploading to YouTube: {content.get('title')}")
        return f"yt_{int(time.time())}"
    
    def get_analytics(self, start_date: str, end_date: str) -> dict:
        """获取YouTube分析数据"""
        return {
            "platform": "youtube",
            "metrics": {
                "views": 5000,
                "likes": 1000,
                "comments": 200
            }
        }

class TwitterAdapter:
    """Twitter平台适配器"""
    
    @property
    def platform_name(self) -> str:
        return "twitter"
    
    def authenticate(self, credentials: dict) -> bool:
        """Twitter认证实现"""
        logger.info(f"Authenticating Twitter account: @{credentials.get('username')}")
        return True
    
    def post_content(self, content: dict) -> str:
        """发布推文"""
        logger.info(f"Tweeting: {content.get('text')}")
        return f"tw_{int(time.time())}"

class InstagramAdapter:
    """Instagram平台适配器"""
    
    @property
    def platform_name(self) -> str:
        return "instagram"
    
    def authenticate(self, credentials: dict) -> bool:
        """Instagram认证实现"""
        logger.info(f"Authenticating Instagram account: {credentials.get('username')}")
        return True
    
    def post_content(self, content: dict) -> str:
        """发布Instagram内容"""
        logger.info(f"Posting to Instagram: {content.get('caption')}")
        return f"ig_{int(time.time())}"

def get_adapter(platform: str) -> SocialPlatform:
    """平台适配器工厂"""
    adapters = {
        # 国内平台
        "wechat": WeChatAdapter,
        "weibo": WeiboAdapter,
        "douyin": DouyinAdapter,
        # 海外平台
        "youtube": YouTubeAdapter,
        "twitter": TwitterAdapter,
        "instagram": InstagramAdapter
    }
    if platform.lower() not in adapters:
        raise PlatformError(platform, "Unsupported platform")
    return adapters[platform.lower()]()