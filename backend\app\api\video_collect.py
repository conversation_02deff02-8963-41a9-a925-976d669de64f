"""
视频采集相关API
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
import logging
import uuid
import time
from datetime import datetime

from app.core.schemas.social_repository import SocialDatabaseService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/video-collect", tags=["video-collect"])


def get_social_service(request: Request):
    """获取社媒管理服务实例"""
    db = request.app.state.mongo_db
    service = SocialDatabaseService(db)
    return service


# 请求模型
class VideoCollectTaskRequest(BaseModel):
    """视频采集任务请求"""
    task_name: str = Field(..., description="任务名称")
    account_id: str = Field(..., description="账号ID")
    account_name: str = Field(..., description="账号名称")
    account_url: str = Field(..., description="账号URL")
    platform: str = Field(..., description="平台")
    task_config: Dict[str, Any] = Field(..., description="任务配置")


class TaskStatusUpdate(BaseModel):
    """任务状态更新"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="状态")
    progress: Optional[int] = Field(None, description="进度百分比")
    message: Optional[str] = Field(None, description="状态消息")


class VideoInfo(BaseModel):
    """视频信息"""
    video_id: str
    title: str
    video_url: str
    real_video_url: Optional[str] = None
    cover_url: str
    like_count: str
    view_count: Optional[str] = None
    year: str
    month: str
    platform: str
    account_id: str
    account_name: str
    download_status: str = "pending"
    download_path: Optional[str] = None
    file_size: Optional[int] = None
    duration: Optional[int] = None


async def call_core_service_collect(task_id: str, task_request: VideoCollectTaskRequest) -> bool:
    """调用Core服务执行采集任务（使用gRPC）"""
    try:
        from app.core.client import CoreClient

        logger.info(f"通过gRPC调用Core服务执行采集任务: {task_id}")

        # 创建Core客户端
        core_client = CoreClient()

        # 构建任务数据
        task_data = {
            "task_id": task_id,
            "task_type": "collect",  # 采集任务类型
            "platform_id": task_request.platform,
            "account_id": task_request.account_id,
            # 🔧 重要：采集任务使用Playwright，不需要模拟器设备
            "content_path": "",  # 采集任务不需要内容路径
            "params": {
                "account_url": task_request.account_url,
                "account_name": task_request.account_name,
                "platform": task_request.platform,
                "task_name": task_request.task_name
            }
        }

        # 添加任务配置到params
        if task_request.task_config:
            for key, value in task_request.task_config.items():
                task_data["params"][f"config_{key}"] = str(value)

        logger.info(f"传递给Core的采集任务数据: {task_data}")

        # 创建任务
        create_result = await core_client.create_task(task_id, task_data)
        if not create_result.get("success"):
            logger.error(f"Core服务创建采集任务失败: {create_result.get('error')}")
            return False

        logger.info(f"Core服务采集任务创建成功: {task_id}")

        # 启动任务
        start_result = await core_client.start_task(task_id)
        if not start_result.get("success"):
            logger.error(f"Core服务启动采集任务失败: {start_result.get('error')}")
            return False

        logger.info(f"Core服务采集任务启动成功: {task_id}")
        return True

    except Exception as e:
        logger.error(f"调用Core服务执行采集任务异常: {str(e)}")
        return False





# API接口
@router.post("/start")
async def start_collect_task(
    task_request: VideoCollectTaskRequest,
    background_tasks: BackgroundTasks,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """启动采集任务"""
    try:
        logger.info(f"收到采集任务请求: {task_request.task_name}")

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 构建任务数据
        task_data = {
            "task_id": task_id,
            "task_name": task_request.task_name,
            "task_type": "collect",
            "account_id": task_request.account_id,
            "account_name": task_request.account_name,
            "account_url": task_request.account_url,
            "platform": task_request.platform,
            "task_config": task_request.task_config,
            "status": "pending",
            "progress": 0,
            "created_at": time.time(),
            "updated_at": time.time()
        }

        # 保存任务到数据库
        saved_task_id = await db_service.create_task(task_data)

        # 如果是立即执行，添加到后台任务
        if task_request.task_config.get("execution_mode") == "immediate":
            background_tasks.add_task(execute_collect_task, task_id, task_request, db_service)
            logger.info(f"采集任务 {task_id} 已添加到后台执行队列")
        else:
            logger.info(f"定时采集任务 {task_id} 已创建，等待执行时间")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "采集任务创建成功"
        }
        
    except Exception as e:
        logger.error(f"创建采集任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建采集任务失败: {str(e)}")


@router.get("/status/{task_id}")
async def get_task_status(task_id: str, db_service: SocialDatabaseService = Depends(get_social_service)):
    """获取任务状态"""
    try:
        task = db_service.get_task_by_id(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {
            "success": True,
            "task": {
                "task_id": task.get("task_id", task_id),
                "status": task.get("status", "unknown"),
                "progress": task.get("progress", 0),
                "message": task.get("message", ""),
                "created_at": task.get("created_at"),
                "updated_at": task.get("updated_at")
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.post("/cancel/{task_id}")
async def cancel_task(task_id: str, db_service: SocialDatabaseService = Depends(get_social_service)):
    """取消任务"""
    try:
        # 先尝试通知Core服务取消任务
        core_cancel_success = False
        try:
            from app.core.service_discovery import get_core_client
            core_client = get_core_client()

            logger.info(f"通知Core服务取消采集任务: {task_id}")
            cancel_result = await core_client.cancel_task(task_id)
            core_cancel_success = cancel_result.get("success", False)

            if core_cancel_success:
                logger.info(f"Core服务采集任务 {task_id} 取消成功")
            else:
                logger.warning(f"Core服务采集任务 {task_id} 取消失败: {cancel_result.get('error', '未知错误')}")
        except Exception as e:
            logger.error(f"通知Core服务取消采集任务失败: {str(e)}")

        # 更新任务状态为已取消
        update_data = {
            "status": "cancelled",
            "updated_at": time.time(),
            "message": "任务已被用户取消",
            "core_canceled": core_cancel_success  # 记录Core服务是否成功取消
        }

        success = db_service.update_task_status(task_id, update_data)

        if not success:
            raise HTTPException(status_code=404, detail="任务不存在")

        message = "任务已取消"
        if not core_cancel_success:
            message += "（数据库状态已更新，但Core服务可能仍在运行）"

        return {
            "success": True,
            "message": message
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/statistics")
async def get_statistics(account_id: Optional[str] = None, db_service: SocialDatabaseService = Depends(get_social_service)):
    """获取统计信息"""
    try:
        # TODO: 实现真实的统计逻辑
        # 这里先返回模拟数据
        stats = {
            "total": 0,
            "with_real_url": 0,
            "downloaded": 0,
            "failed": 0,
            "total_size": 0,
            "last_collect_time": None,
            "status": "none"
        }
        
        return {
            "success": True,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/videos/{account_id}")
async def get_videos_by_account(
    account_id: str,
    page: int = 1,
    limit: int = 20,
    search: Optional[str] = None,
    month: Optional[str] = None,
    status: Optional[str] = None,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """获取指定账号的视频列表"""
    try:
        # TODO: 实现真实的视频查询逻辑
        # 这里先返回空列表
        videos = []
        total = 0
        
        return {
            "success": True,
            "videos": videos,
            "total": total,
            "page": page,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"获取视频列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取视频列表失败: {str(e)}")


@router.put("/{task_id}/status")
async def update_task_status(
    task_id: str,
    request: TaskStatusUpdate,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """更新任务状态（供Core服务调用）"""
    try:
        
        update_data = {
            "status": request.status,
            "updated_at": time.time()
        }
        
        if request.progress is not None:
            update_data["progress"] = request.progress
            
        if request.message:
            update_data["message"] = request.message
        
        success = db_service.update_task_status(task_id, update_data)
        
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {
            "success": True,
            "message": "任务状态更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新任务状态失败: {str(e)}")


# 后台任务执行函数
async def execute_collect_task(task_id: str, task_request: VideoCollectTaskRequest, db_service: SocialDatabaseService):
    """执行采集任务"""
    try:
        logger.info(f"开始执行采集任务: {task_id}")

        # 更新任务状态为执行中
        db_service.update_task_status(task_id, {
            "status": "running",
            "progress": 0,
            "updated_at": time.time(),
            "message": "任务开始执行"
        })
        
        # 调用Core服务执行实际的采集任务
        success = await call_core_service_collect(task_id, task_request)

        if not success:
            raise Exception("Core服务执行采集任务失败")
        
        # 更新任务状态为完成
        db_service.update_task_status(task_id, {
            "status": "completed",
            "progress": 100,
            "updated_at": time.time(),
            "message": "任务执行完成"
        })
        
        logger.info(f"采集任务 {task_id} 执行完成")
        
    except Exception as e:
        logger.error(f"执行采集任务失败: {str(e)}")
        
        # 更新任务状态为失败
        try:
            db_service.update_task_status(task_id, {
                "status": "failed",
                "updated_at": time.time(),
                "message": f"任务执行失败: {str(e)}"
            })
        except Exception as update_error:
            logger.error(f"更新任务失败状态时出错: {str(update_error)}")
