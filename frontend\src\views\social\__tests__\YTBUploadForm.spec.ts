import { mount } from '@vue/test-utils'
import YTBUploadForm from '../YTBUploadForm.vue'
import { describe, expect, it, vi } from 'vitest'
import { nextTick } from 'vue'

describe('YTBUploadForm', () => {
  it('渲染表单组件', () => {
    const wrapper = mount(YTBUploadForm)
    expect(wrapper.find('.ytb-upload-form').exists()).toBe(true)
  })

  it('验证文件夹选择功能', async () => {
    const wrapper = mount(YTBUploadForm)
    
    // 模拟文件夹选择
    window.showDirectoryPicker = vi.fn().mockResolvedValue({
      name: 'test-folder'
    })
    
    await wrapper.find('.el-icon-folder-opened').trigger('click')
    await nextTick()
    
    expect(wrapper.vm.form.folderPath).toBe('test-folder')
  })

  it('显示验证错误信息', async () => {
    const wrapper = mount(YTBUploadForm, {
      data() {
        return {
          error: '无效的文件夹'
        }
      }
    })
    
    expect(wrapper.find('.el-alert').text()).toContain('无效的文件夹')
  })

  it('显示加载状态', async () => {
    const wrapper = mount(YTBUploadForm, {
      data() {
        return {
          loading: true,
          progress: 50
        }
      }
    })
    
    expect(wrapper.find('.el-progress').exists()).toBe(true)
    expect(wrapper.find('.el-progress').attributes('percentage')).toBe('50')
  })
})