<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`🎬 ${account?.account_name} 的采集视频`"
    width="1200px"
    @close="handleClose"
  >
    <div class="video-list-container">
      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ videoStats.total }}</div>
              <div class="stat-label">总视频数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ videoStats.with_real_url }}</div>
              <div class="stat-label">有下载地址</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ videoStats.downloaded }}</div>
              <div class="stat-label">已下载</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ formatFileSize(videoStats.total_size) }}</div>
              <div class="stat-label">总大小</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 筛选工具栏 -->
      <div class="filter-toolbar">
        <div class="filter-left">
          <el-input
            v-model="searchQuery"
            placeholder="搜索视频标题..."
            style="width: 250px"
            @input="handleSearch"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <el-select
            v-model="filterMonth"
            placeholder="筛选月份"
            style="width: 120px; margin-left: 10px"
            @change="loadVideos"
            clearable
          >
            <el-option
              v-for="month in availableMonths"
              :key="month"
              :label="month"
              :value="month"
            />
          </el-select>

          <el-select
            v-model="filterStatus"
            placeholder="下载状态"
            style="width: 120px; margin-left: 10px"
            @change="loadVideos"
            clearable
          >
            <el-option label="待下载" value="pending" />
            <el-option label="下载中" value="downloading" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </div>

        <div class="filter-right">
          <el-button @click="loadVideos" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button type="primary" @click="batchDownload" :disabled="selectedVideos.length === 0">
            <el-icon><Download /></el-icon>
            批量下载 ({{ selectedVideos.length }})
          </el-button>
        </div>
      </div>

      <!-- 视频列表 -->
      <div class="video-list">
        <el-table
          :data="videos"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          style="width: 100%"
          height="400"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column label="视频信息" min-width="300">
            <template #default="{ row }">
              <div class="video-info">
                <div class="video-header">
                  <img 
                    :src="row.cover_url" 
                    :alt="row.title"
                    class="video-cover"
                    @error="handleImageError"
                  />
                  <div class="video-details">
                    <div class="video-title">{{ row.title || '无标题' }}</div>
                    <div class="video-meta">
                      <span class="video-id">ID: {{ row.video_id }}</span>
                      <span class="video-date">{{ formatDate(row.created_at) }}</span>
                    </div>
                    <div class="video-stats">
                      <span v-if="row.like_count" class="stat-item">
                        👍 {{ formatNumber(row.like_count) }}
                      </span>
                      <span v-if="row.view_count" class="stat-item">
                        👁️ {{ formatNumber(row.view_count) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="发布时间" width="120">
            <template #default="{ row }">
              <div class="publish-time">
                <div v-if="row.year && row.month">{{ row.year }}年{{ row.month }}月</div>
                <div v-else class="unknown-time">未知</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="下载状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getDownloadStatusColor(row.download_status)">
                {{ getDownloadStatusText(row.download_status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="文件大小" width="100">
            <template #default="{ row }">
              <span v-if="row.file_size">{{ formatFileSize(row.file_size) }}</span>
              <span v-else class="unknown-size">未知</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button 
                  size="small" 
                  type="primary" 
                  link
                  @click="viewVideo(row)"
                >
                  预览
                </el-button>
                <el-button 
                  size="small" 
                  type="success" 
                  link
                  @click="downloadVideo(row)"
                  :disabled="!row.real_video_url || row.download_status === 'downloading'"
                >
                  下载
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  link
                  @click="deleteVideo(row)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          :page-sizes="[20, 50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="loadVideos"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="goToDownloadPage">
          前往下载管理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  getVideosByAccount,
  getVideoStatistics,
  downloadSingleVideo,
  batchDownloadVideos,
  deleteVideo as deleteVideoAPI
} from '@/api/video-collect'

interface Props {
  modelValue: boolean
  account: any
}

const props = defineProps<Props>()
const router = useRouter()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const videos = ref<any[]>([])
const selectedVideos = ref<any[]>([])

// 统计数据
const videoStats = reactive({
  total: 0,
  with_real_url: 0,
  downloaded: 0,
  total_size: 0
})

// 筛选和搜索
const searchQuery = ref('')
const filterMonth = ref('')
const filterStatus = ref('')
const availableMonths = ref<string[]>([])

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val && props.account) {
    loadVideos()
    loadStats()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const loadVideos = async () => {
  if (!props.account) return
  
  loading.value = true
  try {
    // 调用实际的API获取视频列表
    const response = await getVideosByAccount(props.account._id, {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      month: filterMonth.value,
      status: filterStatus.value
    })

    // 处理API响应
    const data = response.data || response
    if (data.success) {
      videos.value = data.videos || []
      totalCount.value = data.total || 0

      // 提取可用月份
      const months = [...new Set(videos.value.map(v => `${v.year}年${v.month}月`))]
      availableMonths.value = months.sort()
    } else {
      throw new Error(data.error || '获取视频列表失败')
    }

  } catch (error) {
    console.error('加载视频列表失败:', error)
    ElMessage.error('加载视频列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  if (!props.account) return

  try {
    // 调用实际的API获取统计数据
    const response = await getVideoStatistics(props.account._id)

    // 处理API响应
    const data = response.data || response
    if (data.success) {
      Object.assign(videoStats, data.statistics)
    } else {
      throw new Error(data.error || '获取统计数据失败')
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

const handleSearch = () => {
  setTimeout(() => {
    currentPage.value = 1
    loadVideos()
  }, 500)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadVideos()
}

const handleSelectionChange = (selection: any[]) => {
  selectedVideos.value = selection
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYwIiBoZWlnaHQ9IjkwIiB2aWV3Qm94PSIwIDAgMTYwIDkwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTYwIiBoZWlnaHQ9IjkwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik04MCA0NUw5MCA1NUg3MEw4MCA0NVoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'
}

const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getDownloadStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'info',
    'downloading': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const getDownloadStatusText = (status: string) => {
  const textMap = {
    'pending': '待下载',
    'downloading': '下载中',
    'completed': '已完成',
    'failed': '失败'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

const viewVideo = (video: any) => {
  if (video.video_url) {
    window.open(video.video_url, '_blank')
  } else {
    ElMessage.warning('视频链接不可用')
  }
}

const downloadVideo = async (video: any) => {
  if (!video.real_video_url) {
    ElMessage.warning('该视频没有可用的下载地址')
    return
  }

  try {
    // TODO: 调用下载API
    ElMessage.success(`开始下载视频: ${video.title}`)
    // 更新状态
    video.download_status = 'downloading'
  } catch (error) {
    ElMessage.error('启动下载失败')
  }
}

const deleteVideo = async (video: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除视频 "${video.title}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // TODO: 调用删除API
    ElMessage.success('视频删除成功')
    loadVideos()
  } catch (error) {
    // 用户取消删除
  }
}

const batchDownload = async () => {
  if (selectedVideos.value.length === 0) {
    ElMessage.warning('请先选择要下载的视频')
    return
  }

  const validVideos = selectedVideos.value.filter(v => v.real_video_url)
  if (validVideos.length === 0) {
    ElMessage.warning('选中的视频都没有可用的下载地址')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要批量下载 ${validVideos.length} 个视频吗？`,
      '确认批量下载',
      {
        confirmButtonText: '开始下载',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    // TODO: 调用批量下载API
    ElMessage.success(`已启动 ${validVideos.length} 个视频的下载任务`)
    loadVideos()
  } catch (error) {
    // 用户取消
  }
}

const goToDownloadPage = () => {
  router.push({ name: 'BenchmarkDownload' })
  handleClose()
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.video-list-container {
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.stats-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.filter-left {
  display: flex;
  align-items: center;
}

.filter-right {
  display: flex;
  gap: 8px;
}

.video-list {
  flex: 1;
  overflow: hidden;
}

.video-info {
  padding: 8px 0;
}

.video-header {
  display: flex;
  gap: 12px;
}

.video-cover {
  width: 80px;
  height: 45px;
  border-radius: 4px;
  object-fit: cover;
  flex-shrink: 0;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
}

.video-meta {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.video-id {
  margin-right: 12px;
}

.video-stats {
  font-size: 12px;
  color: #666;
}

.stat-item {
  margin-right: 12px;
}

.publish-time {
  text-align: center;
  font-size: 12px;
}

.unknown-time,
.unknown-size {
  color: #c0c4cc;
  font-style: italic;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.pagination-container {
  margin-top: 16px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}
</style>











































































































































































































