#!/usr/bin/env python3
"""
初始化音乐库数据脚本
"""
import sys
import os
import asyncio
import requests
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 音乐库测试数据
MUSIC_DATA = [
    {
        "music_id": "KSA012316484",
        "title": "Last Time",
        "tags": ["平静", "梦幻"],
        "duration": "2:30",
        "category": "背景音乐",
        "platform": "youtube"
    },
    {
        "music_id": "BGM001",
        "title": "Morning Light",
        "tags": ["清新", "早晨", "阳光"],
        "duration": "3:15",
        "category": "背景音乐",
        "platform": "youtube"
    },
    {
        "music_id": "BGM002",
        "title": "Peaceful Moments",
        "tags": ["平静", "放松", "冥想"],
        "duration": "4:20",
        "category": "放松音乐",
        "platform": "youtube"
    },
    {
        "music_id": "BGM003",
        "title": "Urban Vibes",
        "tags": ["现代", "城市", "节奏"],
        "duration": "2:45",
        "category": "流行音乐",
        "platform": "youtube"
    },
    {
        "music_id": "BGM004",
        "title": "Nature Sounds",
        "tags": ["自然", "鸟鸣", "森林"],
        "duration": "5:00",
        "category": "自然音效",
        "platform": "youtube"
    },
    {
        "music_id": "BGM005",
        "title": "Energetic Beat",
        "tags": ["活力", "运动", "激励"],
        "duration": "3:30",
        "category": "运动音乐",
        "platform": "youtube"
    },
    {
        "music_id": "BGM006",
        "title": "Romantic Piano",
        "tags": ["浪漫", "钢琴", "温柔"],
        "duration": "4:10",
        "category": "钢琴音乐",
        "platform": "youtube"
    },
    {
        "music_id": "BGM007",
        "title": "Electronic Dreams",
        "tags": ["电子", "科技", "未来"],
        "duration": "3:45",
        "category": "电子音乐",
        "platform": "youtube"
    },
    {
        "music_id": "BGM008",
        "title": "Acoustic Guitar",
        "tags": ["吉他", "民谣", "温暖"],
        "duration": "3:20",
        "category": "民谣音乐",
        "platform": "youtube"
    },
    {
        "music_id": "BGM009",
        "title": "Jazz Cafe",
        "tags": ["爵士", "咖啡", "慵懒"],
        "duration": "4:30",
        "category": "爵士音乐",
        "platform": "youtube"
    }
]

def add_music_via_api(base_url="http://localhost:8000"):
    """通过API添加音乐数据"""
    print("🎵 开始初始化音乐库数据...")
    
    success_count = 0
    error_count = 0
    
    for music in MUSIC_DATA:
        try:
            response = requests.post(
                f"{base_url}/api/music/",
                json=music,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    print(f"✅ 成功添加: {music['title']} ({music['music_id']})")
                    success_count += 1
                else:
                    print(f"❌ 添加失败: {music['title']} - {result.get('message', 'Unknown error')}")
                    error_count += 1
            else:
                print(f"❌ HTTP错误: {music['title']} - {response.status_code}")
                error_count += 1
                
        except Exception as e:
            print(f"❌ 异常: {music['title']} - {str(e)}")
            error_count += 1
    
    print(f"\n📊 初始化完成:")
    print(f"   ✅ 成功: {success_count} 首")
    print(f"   ❌ 失败: {error_count} 首")
    print(f"   📝 总计: {len(MUSIC_DATA)} 首")

def add_music_via_mongodb():
    """直接通过MongoDB添加数据"""
    try:
        from pymongo import MongoClient
        from app.config.database import DatabaseConfig
        
        print("🎵 通过MongoDB直接添加音乐数据...")
        
        # 连接数据库
        db_config = DatabaseConfig()
        client = MongoClient(db_config.mongodb_url)
        db = client[db_config.mongodb_name]
        collection = db.music_library
        
        # 添加时间戳
        for music in MUSIC_DATA:
            music["created_at"] = datetime.utcnow()
            music["updated_at"] = datetime.utcnow()
            music["is_active"] = True
            
            # 生成搜索关键词
            keywords = [music["title"], music["music_id"]] + music["tags"]
            music["search_keywords"] = " ".join(keywords)
        
        # 批量插入
        result = collection.insert_many(MUSIC_DATA)
        
        print(f"✅ 成功添加 {len(result.inserted_ids)} 首音乐到数据库")
        
        # 显示统计信息
        total_count = collection.count_documents({"platform": "youtube"})
        categories = collection.distinct("category", {"platform": "youtube"})
        
        print(f"📊 数据库统计:")
        print(f"   🎵 总音乐数: {total_count}")
        print(f"   📂 分类数: {len(categories)}")
        print(f"   📂 分类列表: {', '.join(categories)}")
        
        client.close()
        
    except ImportError:
        print("❌ 无法导入MongoDB相关模块，请确保已安装pymongo")
    except Exception as e:
        print(f"❌ MongoDB操作失败: {str(e)}")

def main():
    """主函数"""
    print("🎵 音乐库数据初始化工具")
    print("=" * 50)
    
    method = input("请选择初始化方式:\n1. 通过API (推荐)\n2. 直接MongoDB\n请输入选择 (1/2): ").strip()
    
    if method == "1":
        base_url = input("请输入API地址 (默认: http://localhost:8000): ").strip()
        if not base_url:
            base_url = "http://localhost:8000"
        add_music_via_api(base_url)
    elif method == "2":
        add_music_via_mongodb()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
