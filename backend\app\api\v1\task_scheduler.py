"""
任务调度管理API
提供长期任务调度的管理接口
"""

import logging
import time
import datetime
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request
from pydantic import BaseModel, Field

from app.core.client import CoreClient
from app.core.schemas.social_repository import SocialDatabaseService

logger = logging.getLogger(__name__)

router = APIRouter()

def get_database_service(request: Request):
    """获取数据库服务实例"""
    db = request.app.state.mongo_db
    service = SocialDatabaseService(db)
    return service

class ScheduleConfig(BaseModel):
    """调度配置"""
    cron_expression: Optional[str] = Field(None, description="Cron表达式")
    interval_seconds: Optional[int] = Field(None, description="间隔秒数")

class TriggerConfig(BaseModel):
    """触发器配置"""
    watch_path: Optional[str] = Field(None, description="监控路径")
    account_id: Optional[str] = Field(None, description="账号ID")
    platform_id: Optional[str] = Field(None, description="平台ID")
    check_interval: Optional[int] = Field(3600, description="检查间隔（秒）")
    min_interval: Optional[int] = Field(300, description="最小触发间隔（秒）")

class TaskTemplate(BaseModel):
    """任务模板"""
    task_type: str = Field(..., description="任务类型")
    platform_id: Optional[str] = Field(None, description="平台ID")
    account_id: Optional[str] = Field(None, description="账号ID")
    device_id: Optional[str] = Field(None, description="设备ID")
    content_path: Optional[str] = Field(None, description="内容路径")
    metadata: Optional[Dict[str, Any]] = Field(None, description="任务元数据")
    params: Optional[Dict[str, Any]] = Field(None, description="任务参数")

class CreateScheduledTaskRequest(BaseModel):
    """创建调度任务请求"""
    name: str = Field(..., description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    task_type: str = Field(..., description="任务类型：scheduled/triggered")
    trigger_type: str = Field(..., description="触发器类型：cron/interval/file_watch/account_update")
    schedule_config: Optional[ScheduleConfig] = Field(None, description="调度配置")
    trigger_config: Optional[TriggerConfig] = Field(None, description="触发器配置")
    task_template: TaskTemplate = Field(..., description="任务模板")
    enabled: bool = Field(True, description="是否启用")

class UpdateScheduledTaskRequest(BaseModel):
    """更新调度任务请求"""
    name: Optional[str] = Field(None, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    schedule_config: Optional[ScheduleConfig] = Field(None, description="调度配置")
    trigger_config: Optional[TriggerConfig] = Field(None, description="触发器配置")
    task_template: Optional[TaskTemplate] = Field(None, description="任务模板")
    enabled: Optional[bool] = Field(None, description="是否启用")

@router.post("/create")
async def create_scheduled_task(
    request: CreateScheduledTaskRequest,
    db_service: SocialDatabaseService = Depends(get_database_service)
):
    """创建调度任务"""
    try:
        logger.info(f"创建调度任务: {request.name}")
        
        # 构建任务配置
        task_config = {
            "name": request.name,
            "description": request.description,
            "task_type": request.task_type,
            "trigger_type": request.trigger_type,
            "status": "active" if request.enabled else "disabled",
            "created_at": datetime.datetime.now().isoformat(),
            "schedule_config": request.schedule_config.model_dump() if request.schedule_config else {},
            "trigger_config": request.trigger_config.model_dump() if request.trigger_config else {},
            "task_template": request.task_template.model_dump(),
            "execution_count": 0,
            "last_execution": None,
            "next_execution": None
        }
        
        # 调用Core服务创建调度任务 - 自动从 Consul 发现服务
        from app.core.service_discovery import get_core_client
        core_client = get_core_client()
        task_id = await core_client.create_scheduled_task(task_config)
        
        # 保存到数据库
        scheduled_task_data = {
            "task_id": task_id,
            "name": request.name,
            "description": request.description,
            "task_type": request.task_type,
            "trigger_type": request.trigger_type,
            "status": "active" if request.enabled else "disabled",
            "schedule_config": task_config["schedule_config"],
            "trigger_config": task_config["trigger_config"],
            "task_template": task_config["task_template"],
            "execution_count": 0,
            "created_at": datetime.datetime.now(),
            "updated_at": datetime.datetime.now()
        }
        
        # 保存到scheduled_tasks集合
        result = await db_service.db.scheduled_tasks.insert_one(scheduled_task_data)
        
        logger.info(f"调度任务创建成功: {task_id}")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "调度任务创建成功"
        }
        
    except Exception as e:
        logger.error(f"创建调度任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建调度任务失败: {str(e)}")

@router.get("/list")
async def get_scheduled_tasks(
    db_service: SocialDatabaseService = Depends(get_database_service)
):
    """获取调度任务列表"""
    try:
        logger.info("获取调度任务列表")
        
        # 从数据库获取调度任务
        cursor = db_service.db.scheduled_tasks.find({}).sort("created_at", -1)
        tasks = await cursor.to_list(length=None)
        
        # 转换ObjectId为字符串
        for task in tasks:
            task["_id"] = str(task["_id"])
            if "created_at" in task and hasattr(task["created_at"], "isoformat"):
                task["created_at"] = task["created_at"].isoformat()
            if "updated_at" in task and hasattr(task["updated_at"], "isoformat"):
                task["updated_at"] = task["updated_at"].isoformat()
        
        # 同时从Core服务获取最新状态
        try:
            core_client = CoreClient()
            core_tasks = await core_client.get_scheduled_tasks()
            
            # 合并Core服务的状态信息
            core_tasks_dict = {task["task_id"]: task for task in core_tasks}
            
            for task in tasks:
                task_id = task.get("task_id")
                if task_id in core_tasks_dict:
                    core_task = core_tasks_dict[task_id]
                    task["execution_count"] = core_task.get("execution_count", 0)
                    task["last_execution"] = core_task.get("last_execution")
                    task["next_execution"] = core_task.get("next_execution")
                    task["status"] = core_task.get("status", task.get("status"))
        except Exception as core_error:
            logger.warning(f"获取Core服务调度任务状态失败: {str(core_error)}")
        
        return {
            "success": True,
            "tasks": tasks,
            "total": len(tasks)
        }
        
    except Exception as e:
        logger.error(f"获取调度任务列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取调度任务列表失败: {str(e)}")

@router.get("/{task_id}")
async def get_scheduled_task(
    task_id: str,
    db_service: SocialDatabaseService = Depends(get_database_service)
):
    """获取单个调度任务详情"""
    try:
        logger.info(f"获取调度任务详情: {task_id}")
        
        # 从数据库获取任务
        task = db_service.db.scheduled_tasks.find_one({"task_id": task_id})
        if not task:
            raise HTTPException(status_code=404, detail="调度任务不存在")
        
        # 转换ObjectId为字符串
        task["_id"] = str(task["_id"])
        if "created_at" in task and hasattr(task["created_at"], "isoformat"):
            task["created_at"] = task["created_at"].isoformat()
        if "updated_at" in task and hasattr(task["updated_at"], "isoformat"):
            task["updated_at"] = task["updated_at"].isoformat()
        
        # 从Core服务获取最新状态
        try:
            core_client = CoreClient()
            core_tasks = await core_client.get_scheduled_tasks()
            
            for core_task in core_tasks:
                if core_task["task_id"] == task_id:
                    task["execution_count"] = core_task.get("execution_count", 0)
                    task["last_execution"] = core_task.get("last_execution")
                    task["next_execution"] = core_task.get("next_execution")
                    task["status"] = core_task.get("status", task.get("status"))
                    break
        except Exception as core_error:
            logger.warning(f"获取Core服务调度任务状态失败: {str(core_error)}")
        
        return {
            "success": True,
            "task": task
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取调度任务详情失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取调度任务详情失败: {str(e)}")

@router.put("/{task_id}")
async def update_scheduled_task(
    task_id: str,
    request: UpdateScheduledTaskRequest,
    db_service: SocialDatabaseService = Depends(get_database_service)
):
    """更新调度任务"""
    try:
        logger.info(f"更新调度任务: {task_id}")
        
        # 检查任务是否存在
        existing_task = db_service.db.scheduled_tasks.find_one({"task_id": task_id})
        if not existing_task:
            raise HTTPException(status_code=404, detail="调度任务不存在")
        
        # 构建更新数据
        update_data = {"updated_at": datetime.datetime.now()}
        
        if request.name is not None:
            update_data["name"] = request.name
        if request.description is not None:
            update_data["description"] = request.description
        if request.schedule_config is not None:
            update_data["schedule_config"] = request.schedule_config.model_dump()
        if request.trigger_config is not None:
            update_data["trigger_config"] = request.trigger_config.model_dump()
        if request.task_template is not None:
            update_data["task_template"] = request.task_template.model_dump()
        if request.enabled is not None:
            update_data["status"] = "active" if request.enabled else "disabled"
        
        # 更新数据库
        db_service.db.scheduled_tasks.update_one(
            {"task_id": task_id},
            {"$set": update_data}
        )
        
        # TODO: 通知Core服务更新调度任务配置
        
        logger.info(f"调度任务更新成功: {task_id}")
        
        return {
            "success": True,
            "message": "调度任务更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新调度任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新调度任务失败: {str(e)}")

@router.post("/{task_id}/pause")
async def pause_scheduled_task(
    task_id: str,
    db_service: SocialDatabaseService = Depends(get_database_service)
):
    """暂停调度任务"""
    try:
        logger.info(f"暂停调度任务: {task_id}")
        
        # 调用Core服务暂停任务
        core_client = CoreClient()
        success = await core_client.pause_scheduled_task(task_id)
        
        if success:
            # 更新数据库状态
            db_service.db.scheduled_tasks.update_one(
                {"task_id": task_id},
                {"$set": {"status": "paused", "updated_at": datetime.datetime.now()}}
            )
            
            return {
                "success": True,
                "message": "调度任务已暂停"
            }
        else:
            raise HTTPException(status_code=500, detail="暂停调度任务失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"暂停调度任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"暂停调度任务失败: {str(e)}")

@router.post("/{task_id}/resume")
async def resume_scheduled_task(
    task_id: str,
    db_service: SocialDatabaseService = Depends(get_database_service)
):
    """恢复调度任务"""
    try:
        logger.info(f"恢复调度任务: {task_id}")
        
        # 调用Core服务恢复任务
        core_client = CoreClient()
        success = await core_client.resume_scheduled_task(task_id)
        
        if success:
            # 更新数据库状态
            db_service.db.scheduled_tasks.update_one(
                {"task_id": task_id},
                {"$set": {"status": "active", "updated_at": datetime.datetime.now()}}
            )
            
            return {
                "success": True,
                "message": "调度任务已恢复"
            }
        else:
            raise HTTPException(status_code=500, detail="恢复调度任务失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"恢复调度任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"恢复调度任务失败: {str(e)}")

@router.delete("/{task_id}")
async def delete_scheduled_task(
    task_id: str,
    db_service: SocialDatabaseService = Depends(get_database_service)
):
    """删除调度任务"""
    try:
        logger.info(f"删除调度任务: {task_id}")
        
        # 调用Core服务删除任务
        core_client = CoreClient()
        success = await core_client.delete_scheduled_task(task_id)
        
        if success:
            # 从数据库删除
            db_service.db.scheduled_tasks.delete_one({"task_id": task_id})
            
            return {
                "success": True,
                "message": "调度任务已删除"
            }
        else:
            raise HTTPException(status_code=500, detail="删除调度任务失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除调度任务失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除调度任务失败: {str(e)}")
