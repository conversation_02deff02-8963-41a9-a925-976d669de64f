import asyncio
import sys
import os
sys.path.append('src')

from src.services.video_preview_service import VideoPreviewService

async def test_thumbnail():
    print("开始测试缩略图生成...")
    
    # 测试视频文件路径 - 使用一个存在的视频文件
    video_path = r"H:\PublishSystem\youtube\C-HK-2-2-28-铁柱夫妻\凶凶小嫂咂\2025-06"

    # 查找第一个视频文件
    import glob
    video_files = glob.glob(os.path.join(video_path, "*.mp4"))
    if not video_files:
        print("没有找到视频文件")
        return

    video_path = video_files[0]
    
    print(f"视频文件: {video_path}")
    print(f"文件是否存在: {os.path.exists(video_path)}")
    
    if not os.path.exists(video_path):
        print("视频文件不存在，测试结束")
        return
    
    try:
        service = VideoPreviewService()
        print("VideoPreviewService 初始化成功")
        
        # 测试不同的最大尺寸设置
        test_cases = [
            {"max_width": 120, "max_height": 68, "desc": "小尺寸"},
            {"max_width": 320, "max_height": 180, "desc": "中等尺寸"},
            {"max_width": 640, "max_height": 360, "desc": "大尺寸"},
        ]

        for i, test_case in enumerate(test_cases):
            print(f"\n--- 测试 {i+1}: {test_case['desc']} ---")
            result = await service.generate_thumbnail(
                video_path,
                max_width=test_case["max_width"],
                max_height=test_case["max_height"],
                quality=75
            )
        
            print("缩略图生成结果:")
            for key, value in result.items():
                print(f"  {key}: {value}")

            # 检查缩略图文件是否存在
            if result.get('thumbnail_path'):
                thumbnail_exists = os.path.exists(result['thumbnail_path'])
                print(f"缩略图文件是否存在: {thumbnail_exists}")
                if thumbnail_exists:
                    file_size = os.path.getsize(result['thumbnail_path'])
                    print(f"缩略图文件大小: {file_size} 字节")

                    # 使用PIL获取实际生成的缩略图尺寸
                    try:
                        from PIL import Image
                        with Image.open(result['thumbnail_path']) as img:
                            actual_width, actual_height = img.size
                            print(f"实际缩略图尺寸: {actual_width}x{actual_height}")
                    except ImportError:
                        print("PIL未安装，无法获取实际尺寸")
                    except Exception as e:
                        print(f"获取实际尺寸失败: {str(e)}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_thumbnail())
