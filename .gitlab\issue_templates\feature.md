---
description: Propose a new feature or enhancement
---

# Feature Request

## Summary

[Briefly describe the proposed feature or enhancement]

## Problem/Need

[Explain the problem this feature solves or the need it addresses]

## Proposed Solution

[Describe how the feature should work, including any specific requirements]

## Use Case

[Provide examples of how users will benefit from this feature]

- [Use case 1]
- [Use case 2]

## Alternatives Considered

[Optional: Describe any alternative solutions or workarounds]

## Priority

- [ ] High
- [ ] Medium
- [ ] Low

## Acceptance Criteria

[Define what "done" looks like for this feature]

- [ ] [Criterion 1]
- [ ] [Criterion 2]

## TaskList

[Split the issue into smaller tasks in TDD mode]

## Attachments

[Screenshots, mockups, or links to designs]

## Assignee

[@username or leave blank for product manager review]
