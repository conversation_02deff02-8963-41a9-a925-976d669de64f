<template>
  <el-card>
    <template #header>
      <div class="card-header">
        <span>平台表现</span>
      </div>
    </template>
    <el-table :data="platformPerformance" style="width: 100%">
      <el-table-column prop="platform" label="平台" />
      <el-table-column prop="success" label="成功数" />
      <el-table-column prop="failure" label="失败数" />
      <el-table-column prop="rate" label="成功率" />
    </el-table>
  </el-card>
</template>

<script lang="ts" setup>
const platformPerformance = [
  { platform: '微信', success: 12, failure: 1, rate: '92.3%' },
  { platform: '微博', success: 8, failure: 2, rate: '80%' },
  { platform: '抖音', success: 15, failure: 0, rate: '100%' }
]
</script>

<style scoped>
.card-header {
  font-weight: bold;
}
</style>