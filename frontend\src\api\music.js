import request from '@/utils/request'

/**
 * 音乐库API
 */
export const musicApi = {
  /**
   * 获取音乐库列表
   * @param {Object} params - 查询参数
   * @param {string} params.platform - 平台名称
   * @param {string} params.category - 音乐分类
   * @param {string} params.tags - 标签（逗号分隔）
   * @param {string} params.search - 搜索关键词
   * @param {number} params.skip - 跳过数量
   * @param {number} params.limit - 返回数量
   */
  getMusicLibrary(params = {}) {
    return request({
      url: '/api/music/',
      method: 'get',
      params
    })
  },

  /**
   * 创建音乐项目
   * @param {Object} musicData - 音乐数据
   * @param {string} musicData.music_id - 音乐ID
   * @param {string} musicData.title - 音乐标题
   * @param {Array} musicData.tags - 标签数组
   * @param {string} musicData.duration - 时长
   * @param {string} musicData.category - 分类
   * @param {string} musicData.platform - 平台
   */
  createMusic(musicData) {
    return request({
      url: '/api/music/',
      method: 'post',
      data: musicData
    })
  },

  /**
   * 获取音乐分类列表
   * @param {string} platform - 平台名称
   */
  getCategories(platform = 'youtube') {
    return request({
      url: '/api/music/categories',
      method: 'get',
      params: { platform }
    })
  },

  /**
   * 获取音乐标签列表
   * @param {string} platform - 平台名称
   */
  getTags(platform = 'youtube') {
    return request({
      url: '/api/music/tags',
      method: 'get',
      params: { platform }
    })
  }
}

export default musicApi
