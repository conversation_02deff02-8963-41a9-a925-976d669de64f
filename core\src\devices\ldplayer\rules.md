# LDPlayer 控制器实现规范

## 1. 基本要求

所有雷电模拟器控制器的实现必须遵循以下规范：

## 2. 操作函数实现

1. 所有雷电操作函数应通过 `_execute_ldconsole_command` 方法实现，例如：

```python
async def sort_windows(self):
    '''其它逻辑代码'''
    await self._execute_ldconsole_command("sortWnd")
    '''其它逻辑代码'''
```

2. 对于需要处理输出的命令，使用 `execute_command` 方法：

```python 
async def get_running_list(self):
    result = await self.execute_command("runninglist", capture_output=True)
    # 处理输出...
```

## 3. 方法编写要求

每个方法必须包含：

- 必要的日志记录
- try-except 错误处理  
- 明确的返回值
- 完整的docstring文档

## 4. 示例代码

### 简单命令示例

```python
async def quit(self):
    """退出当前模拟器实例"""
    try:
        logger.info(f"正在退出模拟器 {self.device_id}")
        success = await self._execute_ldconsole_command(f"quit --index {self.device_id}")
        logger.info(f"退出结果: {success}")
        return success
    except Exception as e:
        logger.error(f"退出失败: {str(e)}")
        return False
```

### 复杂操作示例

```python
async def sort_windows(self):
    """自动排列模拟器窗口"""
    try:
        logger.info(f"开始排列窗口 {self.device_id}")
        if not await self._check_ldconsole_path():
            return False
            
        result = await self._execute_ldconsole_command("sortWnd")
        logger.info(f"窗口排列结果: {result}")
        return result
    except Exception as e:
        logger.error(f"窗口排列失败: {str(e)}")
        return False
```

## 5. 注意事项

1. 所有命令执行必须处理异常
2. 重要操作需要记录详细日志
3. 保持方法单一职责原则
4. 复杂的业务逻辑应拆分为多个私有方法