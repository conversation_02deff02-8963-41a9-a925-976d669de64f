import request from '../utils/request'

export interface ProxyIP {
  id: string
  region: string
  ip_address: string
  port: number
  proxy_type: string
  username?: string
  password?: string
  v2ray_config?: string
  socks_config?: string
  expire_date?: string
  payment_card?: string
  provider?: string
  status: string
  notes?: string
  created_at: string
  updated_at: string
  associated_devices: Array<{
    device_id: string
    mapping_id: string
    device_name: string
    device_status: string
    device_type: string
  }>
}

export interface ProxyIPCreateRequest {
  region: string
  ip_address: string
  port: number
  proxy_type: string
  username?: string
  password?: string
  v2ray_config?: string
  socks_config?: string
  expire_date?: string
  payment_card?: string
  provider?: string
  notes?: string
}

export interface ProxyIPUpdateRequest {
  region?: string
  ip_address?: string
  port?: number
  proxy_type?: string
  username?: string
  password?: string
  v2ray_config?: string
  socks_config?: string
  expire_date?: string
  payment_card?: string
  provider?: string
  status?: string
  notes?: string
}

export interface ProxyIPListResponse {
  total: number
  items: ProxyIP[]
  regions: string[]
}

export interface ProxyStats {
  total: number
  active: number
  expired: number
  regions: Record<string, number>
}

// 获取代理IP列表
export const getProxyList = async (params?: {
  region?: string
  status?: string
}): Promise<ProxyIPListResponse> => {
  const res = await request.get<ProxyIPListResponse>('/api/v1/proxy/list', {
    params
  })
  return res.data
}

// 根据ID获取代理IP详情
export const getProxyById = async (proxyId: string): Promise<ProxyIP> => {
  const res = await request.get<ProxyIP>(`/api/v1/proxy/${proxyId}`)
  return res.data
}

// 创建代理IP
export const createProxy = async (data: ProxyIPCreateRequest): Promise<{
  success: boolean
  proxy_id: string
  message: string
}> => {
  const res = await request.post('/api/v1/proxy/create', data)
  return res.data
}

// 更新代理IP
export const updateProxy = async (proxyId: string, data: ProxyIPUpdateRequest): Promise<{
  success: boolean
  message: string
}> => {
  const res = await request.put(`/api/v1/proxy/${proxyId}`, data)
  return res.data
}

// 删除代理IP
export const deleteProxy = async (proxyId: string): Promise<{
  success: boolean
  message: string
}> => {
  const res = await request.delete(`/api/v1/proxy/${proxyId}`)
  return res.data
}

// 关联设备和代理IP
export const associateDeviceProxy = async (proxyId: string, deviceId: string): Promise<{
  success: boolean
  mapping_id: string
  message: string
}> => {
  const res = await request.post(`/api/v1/proxy/${proxyId}/associate/${deviceId}`)
  return res.data
}

// 取消设备和代理IP关联
export const disassociateDeviceProxy = async (proxyId: string, deviceId: string): Promise<{
  success: boolean
  message: string
}> => {
  const res = await request.delete(`/api/v1/proxy/${proxyId}/disassociate/${deviceId}`)
  return res.data
}

// 获取所有地区列表
export const getRegions = async (): Promise<string[]> => {
  const res = await request.get<string[]>('/api/v1/proxy/regions/list')
  return res.data
}

// 获取代理IP统计信息
export const getProxyStats = async (): Promise<ProxyStats> => {
  const res = await request.get<ProxyStats>('/api/v1/proxy/stats/summary')
  return res.data
}

// 批量导入代理IP
export const batchImportProxies = async (proxies: ProxyIPCreateRequest[]): Promise<{
  success: boolean
  message: string
  success_count: number
  failed_count: number
  errors: string[]
}> => {
  const res = await request.post('/api/v1/proxy/batch/import', proxies)
  return res.data
}

// 获取即将到期的代理IP列表
export const getExpiringProxies = async (days: number = 30): Promise<ProxyIPListResponse> => {
  const res = await request.get<ProxyIPListResponse>('/api/v1/proxy/expiring/list', {
    params: { days }
  })
  return res.data
}

// 获取活跃设备列表（用于关联选择）
export interface ActiveDevice {
  id: string
  name: string
  status: string
  type: string
  core_id: string
  updated_at: string
}

export const getActiveDevices = async (): Promise<ActiveDevice[]> => {
  const res = await request.get<ActiveDevice[]>('/api/v1/proxy/devices/active')
  return res.data
}
