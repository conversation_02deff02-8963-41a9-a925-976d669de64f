"""
下载任务执行器
参考YouTube任务执行器设计
"""

import os
import logging
import asyncio
import datetime
from typing import Dict, List, Any, Optional

from .downloader_factory import DownloaderFactory

logger = logging.getLogger(__name__)


class DownloadTaskExecutor:
    """下载任务执行器类"""

    def __init__(self, main_service):
        """初始化下载任务执行器
        
        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        self.active_downloaders = {}  # task_id -> downloader
        
    async def execute_download_task(
        self, 
        task_id: str, 
        task: Dict[str, Any], 
        add_task_log, 
        publish_task_status
    ) -> bool:
        """执行下载任务
        
        Args:
            task_id: 任务ID
            task: 任务数据
            add_task_log: 添加任务日志的函数
            publish_task_status: 发布任务状态的函数
            
        Returns:
            bool: 是否执行成功
        """
        try:
            logger.info(f"开始执行下载任务: {task_id}")
            add_task_log(task_id, "info", "开始执行下载任务")

            # 🔧 详细日志：显示接收到的所有任务数据
            logger.info(f"[{task_id}] 接收到的完整任务数据: {task}")

            # 🔧 重要修复：从params中读取下载任务参数
            params = task.get('params', {})

            # 优先从params中读取，如果没有则从task中读取（兼容性）
            platform = (params.get('platform') or task.get('platform', '')).lower()
            account_url = params.get('benchmark_account_url') or task.get('benchmark_account_url', '')
            account_name = params.get('benchmark_account_name') or task.get('benchmark_account_name', '')
            download_path = params.get('download_path') or task.get('download_path', '')

            # 处理download_config（可能是JSON字符串）
            download_config = task.get('download_config', {})
            if 'download_config' in params:
                try:
                    import json
                    download_config = json.loads(params['download_config'])
                except Exception as e:
                    logger.warning(f"解析download_config失败: {e}")
                    download_config = {}

            # 🔧 详细日志：显示解析后的参数
            logger.info(f"[{task_id}] 解析参数 - platform: '{platform}' (类型: {type(platform)})")
            logger.info(f"[{task_id}] 解析参数 - account_url: '{account_url}' (类型: {type(account_url)})")
            logger.info(f"[{task_id}] 解析参数 - account_name: '{account_name}' (类型: {type(account_name)})")
            logger.info(f"[{task_id}] 解析参数 - download_path: '{download_path}' (类型: {type(download_path)})")
            logger.info(f"[{task_id}] 解析参数 - download_config: {download_config}")

            # 验证必要参数
            missing_params = []
            if not platform: missing_params.append("platform")
            if not account_url: missing_params.append("benchmark_account_url")
            if not account_name: missing_params.append("benchmark_account_name")
            if not download_path: missing_params.append("download_path")

            if missing_params:
                error_msg = f"任务参数不完整，缺少: {', '.join(missing_params)}"
                logger.error(f"[{task_id}] {error_msg}")
                add_task_log(task_id, "error", error_msg)
                publish_task_status(task_id, "failed", error_msg)
                return False
            
            # 检查平台是否支持
            if not DownloaderFactory.is_platform_supported(platform):
                error_msg = f"不支持的平台: {platform}"
                add_task_log(task_id, "error", error_msg)
                publish_task_status(task_id, "failed", error_msg)
                return False
            
            # 🔧 强制启用调试模式（有头模式）来调试页面加载问题
            debug_mode = True  # 临时强制启用调试模式
            logger.info(f"[{task_id}] 强制启用调试模式（有头模式）: {debug_mode}")

            # 创建下载器
            downloader = DownloaderFactory.create_downloader(
                platform,
                download_path,
                debug_mode=debug_mode,
                task_id=task_id
            )
            if not downloader:
                error_msg = f"创建{platform}下载器失败"
                add_task_log(task_id, "error", error_msg)
                publish_task_status(task_id, "failed", error_msg)
                return False
            
            # 保存下载器引用
            self.active_downloaders[task_id] = downloader
            
            # 设置回调函数
            downloader.set_progress_callback(
                lambda progress, message: self._on_progress_update(
                    task_id, progress, message, publish_task_status
                )
            )
            downloader.set_status_callback(
                lambda status, message: self._on_status_update(
                    task_id, status, message, add_task_log, publish_task_status
                )
            )
            downloader.set_log_callback(
                lambda log_message: add_task_log(task_id, "info", log_message)
            )
            
            # 更新任务状态
            publish_task_status(task_id, "downloading", "正在下载内容")
            
            # 执行下载
            success = await downloader.download_account_content(
                account_url, account_name, download_config
            )
            
            if success:
                # 生成下载摘要
                summary = downloader.get_download_summary()
                add_task_log(task_id, "info", f"下载完成: {summary}")
                
                # 更新任务状态
                publish_task_status(
                    task_id, 
                    "completed", 
                    f"下载完成，成功下载 {summary['downloaded_count']} 个文件"
                )
                
                logger.info(f"下载任务完成: {task_id}")
                return True
            else:
                error_msg = downloader.error_message or "下载失败"
                add_task_log(task_id, "error", error_msg)
                publish_task_status(task_id, "failed", error_msg)
                logger.error(f"下载任务失败: {task_id} - {error_msg}")
                return False
                
        except Exception as e:
            error_msg = f"执行下载任务异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            add_task_log(task_id, "error", error_msg)
            publish_task_status(task_id, "failed", error_msg)
            return False
        finally:
            # 清理资源
            await self._cleanup_downloader(task_id)
    
    async def execute_batch_download_task(
        self, 
        batch_id: str, 
        tasks: List[Dict[str, Any]], 
        add_task_log, 
        publish_task_status
    ) -> bool:
        """执行批量下载任务
        
        Args:
            batch_id: 批量任务ID
            tasks: 任务列表
            add_task_log: 添加任务日志的函数
            publish_task_status: 发布任务状态的函数
            
        Returns:
            bool: 是否执行成功
        """
        try:
            logger.info(f"开始执行批量下载任务: {batch_id}, 任务数量: {len(tasks)}")
            add_task_log(batch_id, "info", f"开始执行批量下载任务，共 {len(tasks)} 个任务")
            
            publish_task_status(batch_id, "downloading", "正在执行批量下载")
            
            completed_count = 0
            failed_count = 0
            
            # 并发执行下载任务（限制并发数）
            semaphore = asyncio.Semaphore(3)  # 最多同时执行3个下载任务
            
            async def execute_single_task(task):
                async with semaphore:
                    task_id = task.get('task_id')
                    success = await self.execute_download_task(
                        task_id, task, add_task_log, publish_task_status
                    )
                    return task_id, success
            
            # 创建任务协程
            task_coroutines = [execute_single_task(task) for task in tasks]
            
            # 执行所有任务
            results = await asyncio.gather(*task_coroutines, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"批量任务执行异常: {str(result)}")
                else:
                    task_id, success = result
                    if success:
                        completed_count += 1
                    else:
                        failed_count += 1
            
            # 更新批量任务状态
            if failed_count == 0:
                status = "completed"
                message = f"批量下载完成，成功 {completed_count} 个任务"
            elif completed_count == 0:
                status = "failed"
                message = f"批量下载失败，失败 {failed_count} 个任务"
            else:
                status = "completed_with_errors"
                message = f"批量下载完成，成功 {completed_count} 个，失败 {failed_count} 个任务"
            
            add_task_log(batch_id, "info", message)
            publish_task_status(batch_id, status, message)
            
            logger.info(f"批量下载任务完成: {batch_id} - {message}")
            return failed_count == 0
            
        except Exception as e:
            error_msg = f"执行批量下载任务异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            add_task_log(batch_id, "error", error_msg)
            publish_task_status(batch_id, "failed", error_msg)
            return False
    
    async def cancel_download_task(self, task_id: str) -> bool:
        """取消下载任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否取消成功
        """
        try:
            logger.info(f"取消下载任务: {task_id}")
            
            # 获取下载器
            downloader = self.active_downloaders.get(task_id)
            if downloader:
                # 设置状态为取消
                downloader.set_status("cancelled", "任务已取消")
                
                # 清理资源
                await self._cleanup_downloader(task_id)
                
                logger.info(f"下载任务已取消: {task_id}")
                return True
            else:
                logger.warning(f"未找到活跃的下载任务: {task_id}")
                return False
                
        except Exception as e:
            logger.error(f"取消下载任务失败: {task_id} - {str(e)}")
            return False
    
    def _on_progress_update(
        self, 
        task_id: str, 
        progress: int, 
        message: str, 
        publish_task_status
    ):
        """进度更新回调"""
        try:
            # 发布进度更新
            publish_task_status(
                task_id, 
                "downloading", 
                message, 
                {"progress": progress}
            )
        except Exception as e:
            logger.error(f"发布进度更新失败: {str(e)}")
    
    def _on_status_update(
        self, 
        task_id: str, 
        status: str, 
        message: str, 
        add_task_log, 
        publish_task_status
    ):
        """状态更新回调"""
        try:
            add_task_log(task_id, "info", f"状态更新: {status} - {message}")
            
            # 某些状态需要发布到外部
            if status in ["completed", "failed", "cancelled"]:
                publish_task_status(task_id, status, message)
        except Exception as e:
            logger.error(f"发布状态更新失败: {str(e)}")
    
    async def _cleanup_downloader(self, task_id: str):
        """清理下载器资源"""
        try:
            downloader = self.active_downloaders.get(task_id)
            if downloader:
                await downloader.cleanup()
                del self.active_downloaders[task_id]
                logger.info(f"下载器资源已清理: {task_id}")
        except Exception as e:
            logger.error(f"清理下载器资源失败: {task_id} - {str(e)}")
    
    def get_active_tasks(self) -> List[str]:
        """获取活跃的任务列表
        
        Returns:
            List[str]: 活跃的任务ID列表
        """
        return list(self.active_downloaders.keys())
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态信息，如果任务不存在则返回None
        """
        downloader = self.active_downloaders.get(task_id)
        if downloader:
            return downloader.get_download_summary()
        return None
