"""
三拼视频服务
将竖版视频（9:16）拼合成横版视频（16:9）
"""

import os
import asyncio
import subprocess
import logging
from typing import List, Dict, Any, Tuple

logger = logging.getLogger(__name__)

class TripleVideoService:
    """三拼视频服务类"""

    def __init__(self):
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']

    def get_video_aspect_ratio(self, video_path: str) -> Tuple[int, int]:
        """获取视频的宽高比"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams',
                video_path
            ]
            logger.debug(f"执行ffprobe命令获取分辨率")

            # 使用utf-8编码处理，避免Windows编码问题
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=15,
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )

            if result.returncode == 0 and result.stdout:
                import json
                try:
                    data = json.loads(result.stdout)
                    logger.debug(f"ffprobe输出解析成功")

                    for stream in data.get('streams', []):
                        if stream.get('codec_type') == 'video':
                            width = stream.get('width', 0)
                            height = stream.get('height', 0)
                            logger.debug(f"找到视频流: {width}x{height}")
                            if width > 0 and height > 0:
                                return (width, height)

                    logger.warning(f"未找到有效的视频流")
                except json.JSONDecodeError as e:
                    logger.error(f"解析ffprobe输出失败: {str(e)}")
                    logger.debug(f"ffprobe原始输出长度: {len(result.stdout) if result.stdout else 0}")
            else:
                logger.error(f"ffprobe执行失败，返回码: {result.returncode}")
                if result.stderr:
                    logger.error(f"ffprobe错误输出: {result.stderr}")
                if not result.stdout:
                    logger.error(f"ffprobe输出为空")

            return (0, 0)
        except subprocess.TimeoutExpired:
            logger.error(f"ffprobe执行超时")
            return (0, 0)
        except Exception as e:
            logger.error(f"获取视频分辨率异常: {str(e)}", exc_info=True)
            return (0, 0)

    def is_vertical_video(self, video_path: str) -> bool:
        """判断是否为竖版视频（9:16或类似比例）"""
        width, height = self.get_video_aspect_ratio(video_path)
        if width > 0 and height > 0:
            aspect_ratio = width / height
            # 竖版视频的宽高比通常小于1（如9:16 = 0.5625）
            return aspect_ratio < 0.8  # 允许一些误差
        return False

    def get_video_duration(self, video_path: str) -> float:
        """获取视频时长"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format',
                video_path
            ]
            logger.debug(f"获取视频时长")

            # 使用utf-8编码处理，避免Windows编码问题
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=15,
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )

            if result.returncode == 0 and result.stdout:
                import json
                try:
                    data = json.loads(result.stdout)
                    duration = float(data.get('format', {}).get('duration', 0))
                    logger.debug(f"视频时长: {duration}秒")
                    return duration
                except (json.JSONDecodeError, ValueError) as e:
                    logger.error(f"解析视频时长失败: {str(e)}")
                    logger.debug(f"ffprobe输出长度: {len(result.stdout) if result.stdout else 0}")
            else:
                logger.error(f"ffprobe获取时长失败，返回码: {result.returncode}")
                if result.stderr:
                    logger.error(f"错误输出: {result.stderr}")
                if not result.stdout:
                    logger.error(f"ffprobe输出为空")

            return 0.0
        except subprocess.TimeoutExpired:
            logger.error(f"获取视频时长超时")
            return 0.0
        except Exception as e:
            logger.error(f"获取视频时长异常: {str(e)}", exc_info=True)
            return 0.0

    def scan_vertical_videos(self, folder_path: str) -> List[Dict[str, Any]]:
        """扫描文件夹中的竖版视频文件"""
        video_files = []

        try:
            if not os.path.exists(folder_path):
                logger.error(f"文件夹不存在: {folder_path}")
                return video_files

            logger.info(f"开始扫描文件夹: {folder_path}")
            all_files = os.listdir(folder_path)
            logger.info(f"文件夹中共有 {len(all_files)} 个文件/文件夹")

            for filename in all_files:
                file_path = os.path.join(folder_path, filename)
                logger.debug(f"检查文件: {filename}")

                if not os.path.isfile(file_path):
                    logger.debug(f"跳过非文件: {filename}")
                    continue

                # 检查文件扩展名
                file_ext = os.path.splitext(filename)[1][1:].lower()
                logger.debug(f"文件扩展名: {file_ext}")

                if file_ext not in self.supported_video_extensions:
                    logger.debug(f"跳过非视频文件: {filename} (扩展名: {file_ext})")
                    continue

                logger.info(f"发现视频文件: {filename}")

                # 获取视频分辨率
                width, height = self.get_video_aspect_ratio(file_path)
                logger.info(f"视频分辨率: {filename} -> {width}x{height}")

                # 获取视频时长
                duration = self.get_video_duration(file_path)
                logger.info(f"视频时长: {filename} -> {duration:.1f}s")

                # 简化判断逻辑：对于包含日文字符的文件名，直接当作竖版视频处理
                is_vertical = True
                aspect_ratio = 0.0

                if width > 0 and height > 0:
                    aspect_ratio = width / height
                    logger.info(f"宽高比: {aspect_ratio:.3f}")
                    # 竖版视频：宽高比小于1.2（包含9:16=0.5625, 3:4=0.75等）
                    is_vertical = aspect_ratio < 1.2

                    if not is_vertical:
                        logger.info(f"跳过横版视频: {filename} (宽高比: {aspect_ratio:.3f})")
                        continue
                else:
                    # 如果无法获取分辨率，当作竖版视频处理（特别是对于包含特殊字符的文件名）
                    logger.warning(f"无法获取视频分辨率，当作竖版视频处理: {filename}")
                    width, height = 720, 1280  # 设置默认竖版分辨率
                    aspect_ratio = 0.5625  # 9:16

                if duration <= 0:
                    logger.warning(f"无法获取视频时长，使用默认时长: {filename}")
                    duration = 10.0  # 设置默认时长

                video_info = {
                    'name': filename,
                    'path': file_path,
                    'duration': duration,
                    'size': os.path.getsize(file_path),
                    'width': width,
                    'height': height,
                    'aspect_ratio': width / height if height > 0 else 0
                }
                video_files.append(video_info)
                logger.info(f"✅ 添加竖版视频: {filename} ({duration:.1f}s, {width}x{height})")

        except Exception as e:
            logger.error(f"扫描竖版视频文件失败: {str(e)}", exc_info=True)

        # 按文件名排序
        video_files.sort(key=lambda x: x['name'])
        logger.info(f"🎬 共找到 {len(video_files)} 个竖版视频文件")

        # 打印详细信息
        for i, video in enumerate(video_files):
            logger.info(f"  {i+1}. {video['name']} - {video['width']}x{video['height']} - {video['duration']:.1f}s")

        return video_files

    async def create_triple_video(self, folder_path: str, output_path: str, 
                                video_duration: int, output_quality: str) -> Dict[str, Any]:
        """创建三拼视频
        
        Args:
            folder_path: 文件夹路径
            output_path: 输出文件路径
            video_duration: 每个视频片段播放时长（秒）
            output_quality: 输出质量
        
        Returns:
            Dict: 包含success, error, processed_videos等信息
        """
        try:
            logger.info(f"开始创建三拼视频: {folder_path} -> {output_path}")

            # 1. 扫描竖版视频文件
            video_files = self.scan_vertical_videos(folder_path)
            if len(video_files) < 3:
                error_msg = f"至少需要3个竖版视频才能创建三拼视频，当前只有{len(video_files)}个"
                logger.error(error_msg)
                return {"success": False, "error": error_msg, "processed_videos": 0}

            logger.info(f"开始创建三拼视频，包含 {len(video_files)} 个竖版视频")

            # 2. 计算总的视频组数（每3个视频为一组）
            total_groups = (len(video_files) + 2) // 3  # 向上取整
            total_duration = total_groups * video_duration

            logger.info(f"将创建 {total_groups} 个三拼组合，总时长约 {total_duration} 秒")

            # 3. 构建简化的ffmpeg命令
            success = await self._create_simple_triple_video(
                video_files, output_path, video_duration, output_quality
            )

            if success:
                logger.info(f"三拼视频创建成功: {output_path}")
                return {
                    "success": True, 
                    "error": "", 
                    "processed_videos": len(video_files),
                    "output_file": output_path
                }
            else:
                return {
                    "success": False, 
                    "error": "ffmpeg处理失败", 
                    "processed_videos": 0
                }

        except Exception as e:
            error_msg = f"创建三拼视频异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg, "processed_videos": 0}

    async def _create_simple_triple_video(self, video_files: List[Dict[str, Any]],
                                        output_path: str, video_duration: int,
                                        output_quality: str) -> bool:
        """创建正确的三拼视频：左→中→右依次播放，其他区域显示对应视频的第一帧"""
        try:
            if len(video_files) < 1:
                logger.error("至少需要1个视频文件")
                return False

            logger.info(f"开始创建三拼视频，使用视频实际时长")
            logger.info(f"视频文件数量: {len(video_files)}")

            # 使用正确的逻辑：分别处理每个视频，然后连接
            temp_segments = []

            try:
                for i, video in enumerate(video_files):
                    position = i % 3  # 0=左, 1=中, 2=右
                    position_name = ['左', '中', '右'][position]

                    # 获取视频实际时长
                    duration = video.get('duration', 0)
                    if duration <= 0:
                        duration = self.get_video_duration(video['path'])

                    # 确定其他两个位置显示的视频
                    left_video, center_video, right_video = self._get_triple_videos(video_files, i)

                    logger.info(f"处理视频 {i+1}: {video['name']} - {position_name}边播放 - 时长: {duration:.1f}秒")
                    logger.info(f"  左边: {left_video['display']} | 中间: {center_video['display']} | 右边: {right_video['display']}")

                    # 创建单个视频的三拼段
                    segment_path = output_path.replace('.mp4', f'_segment_{i}.mp4')
                    temp_segments.append(segment_path)

                    success = await self._create_triple_segment(
                        left_video, center_video, right_video,
                        segment_path, position, duration, output_quality
                    )

                    if not success:
                        logger.error(f"创建视频段 {i} 失败")
                        return False

                # 连接所有段
                success = await self._concat_video_segments(temp_segments, output_path)
                if not success:
                    logger.error("连接视频段失败")
                    return False

                logger.info(f"三拼视频创建成功: {output_path}")
                return True

            finally:
                # 清理临时文件
                for segment_path in temp_segments:
                    try:
                        if os.path.exists(segment_path):
                            os.remove(segment_path)
                    except:
                        pass

        except Exception as e:
            logger.error(f"创建三拼视频异常: {str(e)}", exc_info=True)
            return False

    def _get_triple_videos(self, video_files: List[Dict[str, Any]], current_index: int) -> tuple:
        """获取三拼视频的左中右视频配置"""
        total_videos = len(video_files)
        position = current_index % 3  # 0=左, 1=中, 2=右

        # 初始化三个位置
        left_video = {"path": None, "type": "static", "display": "黑色"}
        center_video = {"path": None, "type": "static", "display": "黑色"}
        right_video = {"path": None, "type": "static", "display": "黑色"}

        if position == 0:  # 当前视频在左边播放
            left_video = {"path": video_files[current_index]['path'], "type": "playing",
                         "display": f"视频{current_index+1}(播放)"}

            # 中间显示下一个视频的第一帧
            if current_index + 1 < total_videos:
                center_video = {"path": video_files[current_index + 1]['path'], "type": "first_frame",
                               "display": f"视频{current_index+2}(第一帧)"}
            elif current_index >= 2:  # 没有下一个视频，用前面第2个视频的最后一帧
                center_video = {"path": video_files[current_index - 2]['path'], "type": "last_frame",
                               "display": f"视频{current_index-1}(最后一帧)"}
            elif current_index >= 1:  # 只有前1个视频可用
                center_video = {"path": video_files[current_index - 1]['path'], "type": "last_frame",
                               "display": f"视频{current_index}(最后一帧)"}

            # 右边显示下下个视频的第一帧
            if current_index + 2 < total_videos:
                right_video = {"path": video_files[current_index + 2]['path'], "type": "first_frame",
                              "display": f"视频{current_index+3}(第一帧)"}
            elif current_index >= 1:  # 没有足够视频，用前面第1个视频的最后一帧
                right_video = {"path": video_files[current_index - 1]['path'], "type": "last_frame",
                              "display": f"视频{current_index}(最后一帧)"}

        elif position == 1:  # 当前视频在中间播放
            center_video = {"path": video_files[current_index]['path'], "type": "playing",
                           "display": f"视频{current_index+1}(播放)"}

            # 左边显示下一个视频的第一帧
            if current_index + 1 < total_videos:
                left_video = {"path": video_files[current_index + 1]['path'], "type": "first_frame",
                             "display": f"视频{current_index+2}(第一帧)"}
            elif current_index >= 2:  # 没有下一个视频，用前面第2个视频的最后一帧
                left_video = {"path": video_files[current_index - 2]['path'], "type": "last_frame",
                             "display": f"视频{current_index-1}(最后一帧)"}
            elif current_index >= 1:  # 只有前1个视频可用
                left_video = {"path": video_files[current_index - 1]['path'], "type": "last_frame",
                             "display": f"视频{current_index}(最后一帧)"}

            # 右边显示下下个视频的第一帧
            if current_index + 2 < total_videos:
                right_video = {"path": video_files[current_index + 2]['path'], "type": "first_frame",
                              "display": f"视频{current_index+3}(第一帧)"}
            elif current_index + 1 < total_videos:  # 没有第7个视频，用第6个视频的第一帧
                right_video = {"path": video_files[current_index + 1]['path'], "type": "first_frame",
                              "display": f"视频{current_index+2}(第一帧)"}
            elif current_index >= 1:  # 都没有，用前面视频的最后一帧
                right_video = {"path": video_files[current_index - 1]['path'], "type": "last_frame",
                              "display": f"视频{current_index}(最后一帧)"}

        else:  # position == 2, 当前视频在右边播放
            right_video = {"path": video_files[current_index]['path'], "type": "playing",
                          "display": f"视频{current_index+1}(播放)"}

            # 左边显示下一个视频的第一帧，如果没有则用前面第2个视频的最后一帧
            if current_index + 1 < total_videos:
                left_video = {"path": video_files[current_index + 1]['path'], "type": "first_frame",
                             "display": f"视频{current_index+2}(第一帧)"}
            elif current_index >= 2:  # 没有下一个视频，用前面第2个视频的最后一帧
                left_video = {"path": video_files[current_index - 2]['path'], "type": "last_frame",
                             "display": f"视频{current_index-1}(最后一帧)"}
            elif current_index >= 1:  # 只有前1个视频可用
                left_video = {"path": video_files[current_index - 1]['path'], "type": "last_frame",
                             "display": f"视频{current_index}(最后一帧)"}

            # 中间显示下下个视频的第一帧，如果没有则用前面第1个视频的最后一帧
            if current_index + 2 < total_videos:
                center_video = {"path": video_files[current_index + 2]['path'], "type": "first_frame",
                               "display": f"视频{current_index+3}(第一帧)"}
            elif current_index >= 1:  # 没有足够视频，用前面第1个视频的最后一帧
                center_video = {"path": video_files[current_index - 1]['path'], "type": "last_frame",
                               "display": f"视频{current_index}(最后一帧)"}

        return left_video, center_video, right_video

    async def _create_triple_segment(self, left_video: dict, center_video: dict, right_video: dict,
                                   output_path: str, playing_position: int, duration: float,
                                   output_quality: str) -> bool:
        """创建三拼视频段，包含左中右三个区域，其他区域显示静态帧"""
        try:
            playing_video = [left_video, center_video, right_video][playing_position]

            if not playing_video['path']:
                logger.error("播放视频路径为空")
                return False

            logger.debug(f"创建三拼视频段，播放位置: {playing_position}")
            logger.debug(f"播放视频: {playing_video['path']}")

            # 收集所有需要的输入文件
            inputs = []
            input_map = {}

            # 添加播放视频
            inputs.append(playing_video['path'])
            input_map['playing'] = 0

            # 添加其他区域的静态帧视频
            for pos, video in enumerate([left_video, center_video, right_video]):
                if pos != playing_position and video['path'] and video['type'] in ['first_frame', 'last_frame']:
                    if video['path'] not in inputs:
                        inputs.append(video['path'])
                        input_map[f'static_{pos}'] = len(inputs) - 1
                    else:
                        # 如果路径已存在，复用已有的输入
                        input_map[f'static_{pos}'] = inputs.index(video['path'])

            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y']

            # 添加所有输入文件
            for input_path in inputs:
                cmd.extend(['-i', input_path])

            # 构建滤镜
            filter_parts = []

            # 创建黑色背景
            filter_parts.append(f'color=black:size=1920x1138:duration={duration}[bg]')

            # 处理播放视频
            filter_parts.append(
                f'[{input_map["playing"]}:v]scale=640:1138:force_original_aspect_ratio=decrease,'
                f'pad=640:1138:(ow-iw)/2:(oh-ih)/2[playing_scaled]'
            )

            # 处理静态帧
            for pos, video in enumerate([left_video, center_video, right_video]):
                if pos != playing_position and video['path'] and video['type'] in ['first_frame', 'last_frame']:
                    input_idx = input_map.get(f'static_{pos}')
                    if input_idx is not None:
                        if video['type'] == 'first_frame':
                            # 提取第一帧并循环
                            filter_parts.append(
                                f'[{input_idx}:v]scale=640:1138:force_original_aspect_ratio=decrease,'
                                f'pad=640:1138:(ow-iw)/2:(oh-ih)/2,trim=end_frame=1,'
                                f'loop=loop=-1:size=1:start=0,trim=duration={duration}[static_{pos}]'
                            )
                        else:  # last_frame
                            # 提取最后一帧并循环
                            filter_parts.append(
                                f'[{input_idx}:v]scale=640:1138:force_original_aspect_ratio=decrease,'
                                f'pad=640:1138:(ow-iw)/2:(oh-ih)/2,reverse,trim=end_frame=1,'
                                f'loop=loop=-1:size=1:start=0,trim=duration={duration}[static_{pos}]'
                            )

            # 叠加所有区域到背景上
            overlay_chain = '[bg]'

            for pos in range(3):  # 左中右三个位置
                x_pos = pos * 640

                if pos == playing_position:
                    # 播放位置
                    filter_parts.append(f'{overlay_chain}[playing_scaled]overlay={x_pos}:0[overlay_{pos}]')
                    overlay_chain = f'[overlay_{pos}]'
                else:
                    # 静态帧位置
                    video = [left_video, center_video, right_video][pos]
                    if video['path'] and video['type'] in ['first_frame', 'last_frame']:
                        filter_parts.append(f'{overlay_chain}[static_{pos}]overlay={x_pos}:0[overlay_{pos}]')
                        overlay_chain = f'[overlay_{pos}]'

            # 添加滤镜到命令
            filter_complex = ';'.join(filter_parts)
            cmd.extend(['-filter_complex', filter_complex])

            # 映射输出
            cmd.extend(['-map', overlay_chain])
            cmd.extend(['-map', f'{input_map["playing"]}:a'])

            # 设置编码参数
            cmd.extend(['-t', str(duration)])
            cmd.extend(['-c:v', 'libx264', '-preset', 'fast'])
            cmd.extend(['-crf', '23' if output_quality == 'medium' else ('18' if output_quality == 'high' else '28')])
            cmd.extend(['-c:a', 'aac', '-b:a', '128k'])
            cmd.append(output_path)

            logger.debug(f"ffmpeg命令包含 {len(inputs)} 个输入文件")
            logger.debug(f"滤镜: {filter_complex[:100]}...")

            # 执行ffmpeg命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.debug(f"三拼视频段创建成功")
                return True
            else:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "未知错误"
                logger.error(f"三拼视频段创建失败: {error_msg}")
                logger.error(f"完整ffmpeg命令: {' '.join(cmd)}")
                return False

        except Exception as e:
            logger.error(f"创建三拼视频段异常: {str(e)}", exc_info=True)
            return False

    async def _concat_video_segments(self, segment_paths: List[str], output_path: str) -> bool:
        """连接视频段"""
        try:
            logger.info(f"连接 {len(segment_paths)} 个视频段")

            # 创建文件列表
            list_file = output_path.replace('.mp4', '_list.txt')

            with open(list_file, 'w', encoding='utf-8') as f:
                for segment_path in segment_paths:
                    f.write(f"file '{segment_path}'\n")

            # 连接命令
            cmd = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', list_file,
                '-c', 'copy',
                output_path
            ]

            logger.debug(f"连接命令: {' '.join(cmd)}")

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            # 清理列表文件
            try:
                os.remove(list_file)
            except:
                pass

            if process.returncode == 0:
                logger.info("视频段连接成功")
                return True
            else:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "未知错误"
                logger.error(f"视频段连接失败: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"连接视频段异常: {str(e)}", exc_info=True)
            return False


