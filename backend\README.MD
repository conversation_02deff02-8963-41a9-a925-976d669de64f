# Backend模块开发指南

## 当前目录结构

```目录结构
backend/
├── Dockerfile            # Docker构建文件
├── Dockerfile.base       # 基础Docker构建文件
├── Dockerfile.dependencies # 依赖Docker构建文件
├── README.md            # 项目说明
├── requirements.txt     # Python依赖
├── run_server.py        # 运行脚本
├── app/                 # 应用代码
│   ├── main.py          # FastAPI主入口
│   ├── api/             # API路由
│   │   ├── auth.py      # 认证API
│   │   ├── device.py    # 设备API
│   │   └── social.py    # 社交平台API
│   ├── config/          # 配置
│   │   ├── __init__.py
│   │   ├── database.py  # 数据库配置
│   │   └── settings.py  # 应用设置
│   ├── core/            # 核心业务逻辑
│   │   ├── client.py    # 客户端
│   │   ├── security.py  # 安全模块
│   │   ├── schemas/     # 数据模型
│   │   │   ├── device_repository.py
│   │   │   ├── social_repository.py
│   │   │   └── user_repository.py
│   │   └── social/      # 社交平台逻辑
│   │       └── platform_adapter.py
│   └── models/          # 数据库模型
│       └── user.py      # 用户模型
└── scripts/             # 脚本
    ├── generate_social_data.py
    ├── init_devices.py
    ├── init_social_accounts.py
    └── n8n_api_client.py
```

## 管理指南

### 依赖管理

Backend模块使用requirements.txt进行依赖管理。

#### 安装依赖

```bash
cd backend && 
python -m venv venv &&
.\venv\Scripts\activate &&
pip install -r requirements.txt
```

#### 更新依赖

```bash
pip freeze > requirements.txt  或者
pipreqs ./ --force    （推荐）
```

### 运行Backend模块

```bash
python run_server.py
```

### 开发流程

1. 创建新功能分支
2. 修改代码
3. 运行测试
4. 提交更改
5. 创建Pull Request
