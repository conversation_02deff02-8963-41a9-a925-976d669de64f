<template>
  <el-dialog
    v-model="dialogVisible"
    title="✏️ 编辑内容信息"
    width="600px"
    @close="handleClose"
  >
    <div class="content-edit">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="内容标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入内容标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="内容类型" prop="content_type">
          <el-select v-model="form.content_type" style="width: 100%">
            <el-option label="视频" value="video" />
            <el-option label="图片" value="image" />
            <el-option label="音频" value="audio" />
            <el-option label="文本" value="text" />
          </el-select>
        </el-form-item>

        <el-form-item label="发布时间">
          <div style="display: flex; gap: 12px;">
            <el-input-number
              v-model="form.year"
              :min="2020"
              :max="2030"
              placeholder="年份"
              style="width: 100px"
            />
            <span style="line-height: 32px;">年</span>
            <el-input-number
              v-model="form.month"
              :min="1"
              :max="12"
              placeholder="月份"
              style="width: 80px"
            />
            <span style="line-height: 32px;">月</span>
          </div>
        </el-form-item>

        <el-form-item label="互动数据">
          <div style="display: flex; gap: 12px; align-items: center;">
            <div>
              <span style="margin-right: 8px;">点赞:</span>
              <el-input-number
                v-model="form.like_count"
                :min="0"
                style="width: 120px"
              />
            </div>
            <div>
              <span style="margin-right: 8px;">播放:</span>
              <el-input-number
                v-model="form.view_count"
                :min="0"
                style="width: 120px"
              />
            </div>
          </div>
        </el-form-item>

        <el-form-item label="文件信息">
          <div style="display: flex; gap: 12px; align-items: center;">
            <div>
              <span style="margin-right: 8px;">大小(MB):</span>
              <el-input-number
                v-model="fileSizeMB"
                :min="0"
                :precision="2"
                style="width: 120px"
                @change="updateFileSize"
              />
            </div>
            <div>
              <span style="margin-right: 8px;">时长(秒):</span>
              <el-input-number
                v-model="form.duration"
                :min="0"
                style="width: 120px"
              />
            </div>
          </div>
        </el-form-item>

        <el-form-item label="封面链接">
          <el-input
            v-model="form.cover_url"
            placeholder="请输入封面图片链接"
            type="url"
          />
        </el-form-item>

        <el-form-item label="原始链接">
          <el-input
            v-model="form.content_url"
            placeholder="请输入原始内容链接"
            type="url"
          />
        </el-form-item>

        <el-form-item label="下载链接">
          <el-input
            v-model="form.real_content_url"
            placeholder="请输入真实下载链接"
            type="url"
          />
          <div class="form-tip">
            真实下载链接用于实际下载文件，如果为空则无法下载
          </div>
        </el-form-item>

        <el-form-item label="下载状态">
          <el-select v-model="form.download_status" style="width: 200px">
            <el-option label="待下载" value="pending" />
            <el-option label="下载中" value="downloading" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>

        <el-form-item label="下载路径">
          <el-input
            v-model="form.download_path"
            placeholder="文件下载保存路径"
          />
        </el-form-item>

        <el-form-item label="标签">
          <el-input
            v-model="tagsInput"
            placeholder="输入标签，用逗号分隔"
            @blur="updateTags"
          />
          <div class="tags-display" v-if="form.tags && form.tags.length > 0">
            <el-tag
              v-for="tag in form.tags"
              :key="tag"
              closable
              @close="removeTag(tag)"
              style="margin-right: 8px; margin-top: 8px;"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="添加备注信息..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
        >
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
  content: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const tagsInput = ref('')

// 表单数据
const form = reactive({
  title: '',
  content_type: 'video',
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
  like_count: 0,
  view_count: 0,
  file_size: 0,
  duration: 0,
  cover_url: '',
  content_url: '',
  real_content_url: '',
  download_status: 'pending',
  download_path: '',
  tags: [] as string[],
  description: ''
})

// 文件大小（MB）
const fileSizeMB = computed({
  get: () => form.file_size ? (form.file_size / 1024 / 1024) : 0,
  set: (value: number) => {
    form.file_size = value * 1024 * 1024
  }
})

// 表单验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入内容标题', trigger: 'blur' }
  ],
  content_type: [
    { required: true, message: '请选择内容类型', trigger: 'change' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val && props.content) {
    loadContentData()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  }
})

// 方法
const loadContentData = () => {
  if (!props.content) return
  
  Object.assign(form, {
    title: props.content.title || '',
    content_type: props.content.content_type || 'video',
    year: parseInt(props.content.year) || new Date().getFullYear(),
    month: parseInt(props.content.month) || new Date().getMonth() + 1,
    like_count: parseInt(props.content.like_count) || 0,
    view_count: parseInt(props.content.view_count) || 0,
    file_size: props.content.file_size || 0,
    duration: props.content.duration || 0,
    cover_url: props.content.cover_url || '',
    content_url: props.content.content_url || '',
    real_content_url: props.content.real_content_url || '',
    download_status: props.content.download_status || 'pending',
    download_path: props.content.download_path || '',
    tags: props.content.tags || [],
    description: props.content.description || ''
  })
  
  // 更新标签输入框
  tagsInput.value = form.tags.join(', ')
}

const resetForm = () => {
  Object.assign(form, {
    title: '',
    content_type: 'video',
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    like_count: 0,
    view_count: 0,
    file_size: 0,
    duration: 0,
    cover_url: '',
    content_url: '',
    real_content_url: '',
    download_status: 'pending',
    download_path: '',
    tags: [],
    description: ''
  })
  tagsInput.value = ''
}

const updateFileSize = (value: number) => {
  form.file_size = value * 1024 * 1024
}

const updateTags = () => {
  if (tagsInput.value.trim()) {
    form.tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
  } else {
    form.tags = []
  }
}

const removeTag = (tag: string) => {
  const index = form.tags.indexOf(tag)
  if (index > -1) {
    form.tags.splice(index, 1)
    tagsInput.value = form.tags.join(', ')
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 构建更新数据
    const updateData = {
      content_id: props.content.content_id,
      ...form,
      year: form.year.toString(),
      month: form.month.toString().padStart(2, '0'),
      like_count: form.like_count.toString(),
      view_count: form.view_count.toString()
    }

    console.log('提交内容更新:', updateData)

    // TODO: 调用实际的API
    // await updateContentInfo(updateData)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    ElMessage.success('内容信息更新成功！')
    emit('success')
    dialogVisible.value = false

  } catch (error: any) {
    console.error('更新内容信息失败:', error)
    ElMessage.error(error.message || '更新内容信息失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.content-edit {
  max-height: 70vh;
  overflow-y: auto;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.tags-display {
  margin-top: 8px;
}

.dialog-footer {
  text-align: right;
}
</style>
