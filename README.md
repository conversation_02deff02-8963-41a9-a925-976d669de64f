# ThunderHub 自动化测试与管理平台

基于多 Core 架构的 Android 应用自动化测试与管理平台，支持多实例并行执行(MCP)，集成 Appium、LDPlayer 和 Frida 技术栈。

## 功能特性

- 🚀 多实例并行测试与管理能力
- 📱 Appium 驱动封装与服务管理
- 🎮 LDPlayer 模拟器高级管理
- 🔍 Frida 运行时监控与分析
- 📊 数据可视化与报表中心
- 📝 完善的日志与监控系统
- 🧩 模块化设计，易于扩展
- 🔄 多平台社媒账号管理

## 系统架构

- **前端**: Vue3 + Element Plus + Vite
- **后端**: FastAPI + MongoDB + Redis
- **Core**: Python (Windows) + Appium + Frida
- **服务发现**: Consul + Traefik
- **监控**: Prometheus + Grafana

## 1. 环境准备

创建虚拟环境：

```bash
conda create -n thunderhub python=3.9
conda activate thunderhub
```

安装依赖：

```bash
pip install -r requirements.txt
```

### 2. 启动方式

#### 前端+后端+数据库（Docker方式）

使用Docker Compose一键启动所有服务：

```bash
docker-compose up -d
```
#### 单独启动后端服务

```bash
cd backend
# 在backend目录下
.\venv\Scripts\activate
python run_server.py
```

#### 单独启动前端服务

```bash
cd frontend
npm install
npm run dev
```

这将启动以下服务：

- 前端 (Vue3): https://0.0.0.0:5713
- 后端 (FastAPI): http://0.0.0.0:8000
- MongoDB: 27017端口
- Redis: 6379端口
- Consul: 8500端口

#### 启动Core服务（Windows主机）

Core服务需要在Windows主机上运行，与设备或模拟器直接交互：

```powershell
# Windows PowerShell
cd core
.\venv\Scripts\activate
poetry install
poetry run start
```

或使用提供的初始化脚本：

```powershell
# Windows PowerShell
cd core
.\scripts\setup.ps1
```

#### 数据库服务

MongoDB启动（Windows）：

```powershell
# 启动服务
net start MongoDB

# 停止服务
net stop MongoDB
```

MongoDB启动（Docker）：

```bash
docker-compose up mongodb
```

Redis启动：

```bash
docker-compose up redis
```

### 3. 配置模拟器

修改 `config/ldplayer_config.yaml` 中的实例配置

### 4. 运行测试

```bash
# Linux/Mac
./script/run_tests.sh

# Windows
python -m pytest tests/ -v --alluredir=reports
allure serve reports
```

## 项目结构

```
ThunderHub/
├── frontend/                # 前端项目 (Vue3)
├── backend/                 # 后端服务 (FastAPI)
├── core/                    # 核心模块 (Windows)
│   ├── devices/             # 设备控制层
│   │   ├── base.py          # 设备抽象接口
│   │   ├── factory.py       # 设备工厂
│   │   └── ldplayer/        # 雷电模拟器实现
│   ├── services/            # 公共服务
│   │   └── appium/          # Appium服务
│   └── main_service.py      # 主控制服务
├── config/                  # 配置文件
├── docs/                    # 项目文档
└── scripts/                 # 执行脚本
```

## 主要功能模块

- **认证与用户管理**: JWT认证、RBAC权限管理
- **设备管理**: 模拟器生命周期管理、状态监控
- **任务系统**: 测试任务调度、执行与监控
- **社媒应用管理**: 多平台账号管理、内容发布
- **文件管理系统**: 文件浏览、分类与版本管理
- **报表中心**: 测试数据看板、自定义报告生成

## 文档中心

详细文档请参考 `docs/` 目录：

- [项目简介](docs/project_intro.md)
- [系统架构](docs/2_architecture/architecture.md)
- [开发计划](docs/3_development/dev_plan.md)

## 开发指南

请参考代码规范文档：

- [前端代码规范](docs/.codestyle/vue.md)
- [后端代码规范](docs/.codestyle/python.md)
