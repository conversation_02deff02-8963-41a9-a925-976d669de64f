# 视频旋转功能使用说明

## 功能概述

视频旋转功能允许用户对选中的视频文件进行旋转操作，支持向左90度、向右90度和180度旋转。该功能完全在Core服务中执行，使用ffmpeg进行视频处理。

## 功能特点

- ✅ 支持批量操作：可以同时旋转多个视频文件
- ✅ 多种旋转角度：90°（向右）、-90°（向左）、180°
- ✅ 质量控制：支持高、中、低三种输出质量
- ✅ 灵活输出：可选择覆盖原文件或生成新文件
- ✅ 架构合规：所有文件操作都在Core服务中执行
- ✅ 界面简洁：集成到视频处理下拉菜单中，避免按钮过多

## 使用方法

### 1. 选择视频文件
在文件管理界面中，勾选要旋转的视频文件。支持的视频格式包括：
- MP4, AVI, MOV, MKV, WMV, FLV, M4V, WebM, 3GP, TS

### 2. 打开旋转对话框
点击工具栏中的"🎬 视频处理"下拉菜单，选择"🔄 旋转视频"选项。

注意：如果没有选中视频文件，该选项会被禁用。

### 3. 配置旋转参数

#### 旋转角度
- **向右旋转90°（顺时针）**：将竖屏视频转为横屏
- **向左旋转90°（逆时针）**：将横屏视频转为竖屏  
- **旋转180°**：将视频上下颠倒

#### 输出质量
- **高质量**：CRF=18，文件较大，处理时间较长
- **中等质量**：CRF=23，平衡文件大小和质量（推荐）
- **低质量**：CRF=28，文件较小，处理速度较快

#### 输出方式
- **保留原文件，生成新文件**：安全模式，原文件不会被修改
- **覆盖原文件**：直接替换原文件，节省存储空间

#### 文件名后缀（仅在不覆盖模式下）
默认为"_rotated"，可以自定义。新文件名格式：`原文件名 + 后缀 + 扩展名`

### 4. 执行旋转
点击"开始旋转"按钮，系统会：
1. 验证所有参数
2. 调用Core服务进行视频旋转
3. 显示处理进度和结果
4. 自动刷新文件列表

## 技术实现

### 架构设计
```
前端界面 -> Backend API -> Core gRPC服务 -> ffmpeg处理
```

### 核心组件

#### 1. Core服务 (video_rotation_service.py)
- 负责实际的视频旋转处理
- 使用ffmpeg的transpose滤镜
- 支持批量处理和错误处理

#### 2. Backend API (/api/v1/filesystem/rotate-videos)
- 接收前端请求
- 验证参数和文件存在性
- 调用Core服务并返回结果

#### 3. 前端界面 (Manager.vue)
- 集成到视频处理下拉菜单
- 提供直观的参数配置界面
- 显示处理结果和错误信息

### gRPC接口定义
```protobuf
rpc RotateVideos(RotateVideosRequest) returns (RotateVideosResponse) {}

message RotateVideosRequest {
  repeated string video_paths = 1;
  int32 rotation_angle = 2;
  string output_quality = 3;
  bool overwrite_original = 4;
  string output_suffix = 5;
}
```

## 注意事项

### 性能考虑
- 视频旋转需要重新编码，处理时间取决于视频大小和质量设置
- 建议处理大量视频时选择较低的输出质量
- 处理过程中请勿关闭浏览器

### 存储空间
- 不覆盖模式会生成新文件，需要额外存储空间
- 覆盖模式会直接替换原文件，请确保有备份

### 错误处理
- 系统会显示每个文件的处理结果
- 失败的文件会显示具体错误信息
- 部分失败不会影响其他文件的处理

## 故障排除

### 常见问题

1. **"Core服务不可用"**
   - 检查Core服务是否正常运行
   - 确认gRPC连接正常

2. **"文件不存在"**
   - 确认文件路径正确
   - 检查文件是否被其他程序占用

3. **"不支持的旋转角度"**
   - 只支持90、-90、180度旋转
   - 检查前端参数传递是否正确

4. **"ffmpeg执行失败"**
   - 确认Core服务器安装了ffmpeg
   - 检查视频文件是否损坏

### 日志查看
- Core服务日志：`core/logs/`目录
- Backend日志：查看FastAPI日志输出
- 前端错误：浏览器开发者工具控制台

## 更新历史

- **v1.0** (2025-06-26): 初始版本发布
  - 支持基本的视频旋转功能
  - 集成到文件管理界面
  - 完整的错误处理和用户反馈
