# backend/Dockerfile
ARG IMAGE_TAG
FROM 192.168.123.103:5050/docker/ci-images/longer-thunderhub-python-dependencies:$IMAGE_TAG

# 复制应用代码
COPY . .

RUN mkdir -p /srv/logs && chmod 777 /srv/logs

# 暴露端口
EXPOSE 8000

# 设置环境变量
ENV ENV=production \
    DB_MONGODB_URL=mongodb://mongodb:27017 \
    DB_MONGODB_NAME=thunderhub \
    DB_REDIS_URL=redis://redis:6379/1 \
    DB_CONSUL_URL=consul:8500

# 启动命令
CMD ["python", "run_server.py"]
#CMD ["tail", "-f", "/dev/null"]

