"""
设备状态历史服务
用于记录设备状态变更历史
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class DeviceHistoryService:
    """设备状态历史服务"""

    def __init__(self, mongo_db):
        """初始化设备状态历史服务

        Args:
            mongo_db: MongoDB数据库连接
        """
        self.mongo_db = mongo_db

    async def record_status(self, device_id: str, status_data: Dict[str, Any]) -> bool:
        """记录设备状态历史

        Args:
            device_id: 设备ID
            status_data: 状态数据

        Returns:
            是否成功记录
        """
        try:
            # 确保数据库连接有效
            if self.mongo_db is None:
                logger.error("数据库连接无效")
                return False

            # 构建记录数据
            record = {
                "device_id": device_id,
                "timestamp": datetime.now(),
                "status_data": {
                    "status": status_data.get("status", "unknown"),
                    "cpu_usage": status_data.get("cpu_usage", "0%"),
                    "memory_usage": status_data.get("memory_usage", "0%"),
                    "network_status": status_data.get("network_status", "未知")
                }
            }

            # 如果有窗口信息，添加到记录中
            if "window_info" in status_data:
                record["status_data"]["window_info"] = status_data["window_info"]

            # 如果有进程信息，添加到记录中
            if "process_info" in status_data:
                record["status_data"]["process_info"] = status_data["process_info"]

            # 插入记录
            try:
                # 使用同步方式插入记录
                result = self.mongo_db.device_status_history.insert_one(record)

                if result.inserted_id:
                    logger.debug(f"设备{device_id}状态历史记录已保存")
                    return True
                else:
                    logger.warning(f"设备{device_id}状态历史记录保存失败")
                    return False
            except Exception as insert_error:
                logger.error(f"插入设备{device_id}状态历史记录异常: {str(insert_error)}")
                return False

        except Exception as e:
            logger.error(f"记录设备{device_id}状态历史异常: {str(e)}", exc_info=True)
            return False

    async def get_device_history(self, device_id: str, limit: int = 100) -> list:
        """获取设备状态历史

        Args:
            device_id: 设备ID
            limit: 最大记录数

        Returns:
            状态历史记录列表
        """
        try:
            # 确保数据库连接有效
            if self.mongo_db is None:
                logger.error("数据库连接无效")
                return []

            # 查询记录
            try:
                # 使用异步方式查询记录
                cursor = self.mongo_db.device_status_history.find(
                    {"device_id": device_id}
                ).sort("timestamp", -1).limit(limit)

                # 转换为列表
                records = await cursor.to_list(length=limit)
            except Exception as query_error:
                logger.error(f"查询设备{device_id}状态历史记录异常: {str(query_error)}")
                return []

            return records

        except Exception as e:
            logger.error(f"获取设备{device_id}状态历史异常: {str(e)}", exc_info=True)
            return []

    async def clean_old_records(self, days: int = 30) -> int:
        """清理旧记录

        Args:
            days: 保留天数

        Returns:
            清理的记录数
        """
        try:
            # 确保数据库连接有效
            if self.mongo_db is None:
                logger.error("数据库连接无效")
                return 0

            # 计算截止时间
            import datetime as dt
            cutoff_date = datetime.now() - dt.timedelta(days=days)

            # 删除旧记录
            try:
                # 使用同步方式删除记录
                result = self.mongo_db.device_status_history.delete_many(
                    {"timestamp": {"$lt": cutoff_date}}
                )

                deleted_count = result.deleted_count
                logger.info(f"已清理{deleted_count}条设备状态历史记录")
            except Exception as delete_error:
                logger.error(f"删除旧记录异常: {str(delete_error)}")
                return 0

            return deleted_count

        except Exception as e:
            logger.error(f"清理设备状态历史记录异常: {str(e)}", exc_info=True)
            return 0
