#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ThunderHub 数据库初始化脚本
- 初始化MongoDB集合和索引
- 从Core服务获取设备数据
- 提供添加社交媒体平台和账号的功能
- 提供数据导入/导出功能

使用方法:
python init_database.py [--reset] [--import-file FILE] [--export-file FILE]

参数:
  --reset         重置数据库（删除现有集合）
  --import-file   从JSON文件导入数据
  --export-file   将数据导出到JSON文件
"""

import os
import sys
import json
import logging
import argparse
import datetime
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载.env文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载环境变量文件: {env_path}")
else:
    print(f"警告: 环境变量文件不存在: {env_path}")

# 导入项目模块
try:
    from app.config.database import DatabaseConfig
    from app.services.consul_discovery import ConsulDiscovery
except ImportError as e:
    print(f"导入模块失败: {str(e)}")
    print("尝试使用自定义配置...")

    # 定义自定义配置类
    class DatabaseConfig:
        def __init__(self):
            self.mongodb_url = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
            self.mongodb_name = os.getenv("MONGODB_NAME", "thunderhub")
            self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
            self.consul_url = os.getenv("CONSUL_URL", "http://localhost:8500")

            print(f"使用配置: MongoDB={self.mongodb_url}, DB={self.mongodb_name}")
            print(f"使用配置: Redis={self.redis_url}, Consul={self.consul_url}")

    # 简化的ConsulDiscovery类
    class ConsulDiscovery:
        def __init__(self, consul_host, consul_port):
            self.consul_host = consul_host
            self.consul_port = consul_port
            print(f"初始化Consul客户端: {consul_host}:{consul_port}")

        def get_all_services(self, service_name):
            print(f"模拟获取服务: {service_name}")
            return {
                "default": {
                    "id": "default",
                    "name": "默认Core服务",
                    "address": "localhost",
                    "port": 50051,
                    "tags": ["publish_path=H:\\PublishSystem"]
                }
            }

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("init_database")

class DatabaseInitializer:
    """数据库初始化类"""

    def __init__(self):
        """初始化数据库连接"""
        try:
            self.db_config = DatabaseConfig()

            # 连接MongoDB
            try:
                from pymongo import MongoClient
                self.client = MongoClient(self.db_config.mongodb_url, serverSelectionTimeoutMS=5000)
                # 测试连接
                self.client.admin.command('ping')
                self.db = self.client[self.db_config.mongodb_name]
                logger.info(f"MongoDB连接成功: {self.db_config.mongodb_url}/{self.db_config.mongodb_name}")
            except ImportError:
                logger.warning("未安装pymongo模块，使用模拟MongoDB客户端")
                self.client = self._create_mock_mongo_client()
                self.db = self.client[self.db_config.mongodb_name]
            except Exception as e:
                logger.warning(f"MongoDB连接失败: {str(e)}，使用模拟MongoDB客户端")
                self.client = self._create_mock_mongo_client()
                self.db = self.client[self.db_config.mongodb_name]

            # 连接Redis（用于获取设备状态）
            try:
                import redis
                self.redis = redis.Redis.from_url(self.db_config.redis_url, socket_connect_timeout=5)
                # 测试连接
                self.redis.ping()
                logger.info(f"Redis连接成功: {self.db_config.redis_url}")
            except ImportError:
                logger.warning("未安装redis模块，使用模拟Redis客户端")
                self.redis = self._create_mock_redis_client()
            except Exception as e:
                logger.warning(f"Redis连接失败: {str(e)}，使用模拟Redis客户端")
                self.redis = self._create_mock_redis_client()

            # 初始化Consul客户端（用于发现Core服务）
            try:
                consul_url = urlparse(self.db_config.consul_url)
                consul_host = consul_url.hostname or "localhost"
                consul_port = consul_url.port or 8500
                self.consul = ConsulDiscovery(
                    consul_host=consul_host,
                    consul_port=consul_port
                )
                logger.info(f"Consul客户端初始化成功: {consul_host}:{consul_port}")
            except Exception as e:
                logger.warning(f"Consul客户端初始化失败: {str(e)}，使用模拟Consul客户端")
                self.consul = self._create_mock_consul_client()

            logger.info(f"数据库初始化器已创建")
        except Exception as e:
            logger.error(f"初始化数据库连接失败: {str(e)}")
            raise

    def _create_mock_mongo_client(self):
        """创建模拟MongoDB客户端"""
        logger.info("创建模拟MongoDB客户端")

        class MockCollection:
            def __init__(self, name):
                self.name = name
                self.data = []
                logger.info(f"创建模拟集合: {name}")

            def insert_one(self, document):
                document['_id'] = document.get('_id', str(hash(str(document))))
                self.data.append(document)
                logger.info(f"插入文档到{self.name}: {document.get('_id')}")

                class Result:
                    def __init__(self, id):
                        self.inserted_id = id

                return Result(document['_id'])

            def update_one(self, filter, update, upsert=False):
                id_value = filter.get('_id') or filter.get('id')
                found = False

                for i, doc in enumerate(self.data):
                    if (doc.get('_id') == id_value) or (doc.get('id') == id_value):
                        # 更新文档
                        if '$set' in update:
                            for k, v in update['$set'].items():
                                doc[k] = v
                        self.data[i] = doc
                        found = True
                        break

                if not found and upsert:
                    # 如果没找到且upsert=True，则插入
                    new_doc = update.get('$set', {})
                    if '_id' not in new_doc and id_value:
                        new_doc['_id'] = id_value
                    self.insert_one(new_doc)

                class Result:
                    def __init__(self, matched, modified):
                        self.matched_count = matched
                        self.modified_count = modified

                return Result(1 if found else 0, 1 if found else 0)

            def find(self, query=None, **kwargs):
                query = query or {}

                # 简单过滤
                results = []
                for doc in self.data:
                    match = True
                    for k, v in query.items():
                        if k in doc and doc[k] != v:
                            match = False
                            break
                    if match:
                        results.append(doc)

                # 处理skip和limit
                skip = kwargs.get('skip', 0)
                limit = kwargs.get('limit', len(results))
                results = results[skip:skip+limit]

                logger.info(f"查询{self.name}，找到{len(results)}个文档")
                return results

            def find_one(self, query=None):
                results = self.find(query, limit=1)
                return results[0] if results else None

            def create_index(self, keys, **kwargs):
                logger.info(f"在{self.name}上创建索引: {keys}")
                return f"mock_index_{self.name}_{keys}"

            def drop(self):
                logger.info(f"删除集合: {self.name}")
                self.data = []

        class MockDatabase:
            def __init__(self, name):
                self.name = name
                self.collections = {}
                logger.info(f"创建模拟数据库: {name}")

            def __getitem__(self, collection_name):
                if collection_name not in self.collections:
                    self.collections[collection_name] = MockCollection(collection_name)
                return self.collections[collection_name]

            def list_collection_names(self):
                return list(self.collections.keys())

        class MockMongoClient:
            def __init__(self, url):
                self.url = url
                self.databases = {}
                logger.info(f"创建模拟MongoDB客户端: {url}")

            def __getitem__(self, db_name):
                if db_name not in self.databases:
                    self.databases[db_name] = MockDatabase(db_name)
                return self.databases[db_name]

            def close(self):
                logger.info("关闭模拟MongoDB客户端")

        return MockMongoClient(self.db_config.mongodb_url)

    def _create_mock_redis_client(self):
        """创建模拟Redis客户端"""
        logger.info("创建模拟Redis客户端")

        class MockRedis:
            def __init__(self):
                self.data = {}
                self.sets = {}
                logger.info("创建模拟Redis客户端")

            def set(self, key, value, **kwargs):
                self.data[key] = value
                logger.info(f"设置Redis键: {key}")
                return True

            def get(self, key):
                value = self.data.get(key)
                logger.info(f"获取Redis键: {key}, 值: {'有值' if value else '无值'}")
                return value

            def sadd(self, key, *values):
                if key not in self.sets:
                    self.sets[key] = set()
                for value in values:
                    self.sets[key].add(value)
                logger.info(f"向集合{key}添加{len(values)}个值")
                return len(values)

            def smembers(self, key):
                members = self.sets.get(key, set())
                logger.info(f"获取集合{key}的成员，共{len(members)}个")
                return [m.encode('utf-8') if isinstance(m, str) else m for m in members]

            def expire(self, key, seconds):
                logger.info(f"设置键{key}的过期时间: {seconds}秒")
                return True

            def ping(self):
                logger.info("Ping模拟Redis服务器")
                return True

            def pipeline(self):
                return self

            def execute(self):
                logger.info("执行Redis管道命令")
                return []

            def delete(self, key):
                if key in self.data:
                    del self.data[key]
                    logger.info(f"删除Redis键: {key}")
                    return 1
                return 0

        return MockRedis()

    def _create_mock_consul_client(self):
        """创建模拟Consul客户端"""
        logger.info("创建模拟Consul客户端")

        class MockConsulDiscovery:
            def __init__(self):
                logger.info("创建模拟Consul客户端")

            def get_all_services(self, service_name):
                logger.info(f"获取服务: {service_name}")
                return {
                    "default": {
                        "id": "default",
                        "name": "默认Core服务",
                        "address": "localhost",
                        "port": 50051,
                        "tags": ["publish_path=H:\\PublishSystem"]
                    }
                }

        return MockConsulDiscovery()

    def reset_database(self) -> None:
        """重置数据库（删除所有集合）"""
        collections = [
            "devices",
            "device_status_history",
            "social_platforms",
            "platform_apps",  # 新增
            "social_accounts",
            "device_account_mappings"  # 新增
        ]

        for collection in collections:
            try:
                self.db[collection].drop()
                logger.info(f"已删除集合: {collection}")
            except Exception as e:
                logger.error(f"删除集合 {collection} 失败: {str(e)}")

        logger.info("数据库重置完成")

    def create_indexes(self) -> None:
        """创建索引"""
        try:
            # 设备集合索引
            self.db.devices.create_index("name")
            self.db.devices.create_index("type")
            self.db.devices.create_index("status")
            self.db.devices.create_index("core_id")
            self.db.devices.create_index([("type", 1), ("status", 1)])
            self.db.devices.create_index([("core_id", 1), ("status", 1)])
            self.db.devices.create_index([("core_id", 1), ("type", 1)])
            self.db.devices.create_index([("core_id", 1), ("type", 1), ("status", 1)])
            self.db.devices.create_index("updated_at")

            # 设备状态历史索引
            self.db.device_status_history.create_index("device_id")
            self.db.device_status_history.create_index("timestamp")
            self.db.device_status_history.create_index([("device_id", 1), ("timestamp", 1)])

            # 社交平台索引
            self.db.social_platforms.create_index("id", unique=True)
            self.db.social_platforms.create_index("status")
            self.db.social_platforms.create_index("created_at")

            # 平台应用索引（新增）
            self.db.platform_apps.create_index("id", unique=True)
            self.db.platform_apps.create_index("platform_id")
            self.db.platform_apps.create_index("type")
            self.db.platform_apps.create_index("status")
            self.db.platform_apps.create_index([("platform_id", 1), ("type", 1)])

            # 社交账号索引（修改）
            self.db.social_accounts.create_index("id", unique=True)
            self.db.social_accounts.create_index([("username", 1), ("platform_id", 1)], unique=True)
            self.db.social_accounts.create_index("platform_id")
            self.db.social_accounts.create_index("core_service_id")
            self.db.social_accounts.create_index("status")
            self.db.social_accounts.create_index([("platform_id", 1), ("status", 1)])
            self.db.social_accounts.create_index([("core_service_id", 1), ("platform_id", 1)])
            self.db.social_accounts.create_index("created_at")
            self.db.social_accounts.create_index("updated_at")

            # 设备账号映射索引（新增）
            self.db.device_account_mappings.create_index("device_id")
            self.db.device_account_mappings.create_index("account_id")
            self.db.device_account_mappings.create_index("platform_id")
            self.db.device_account_mappings.create_index("app_id")
            self.db.device_account_mappings.create_index("core_service_id")
            self.db.device_account_mappings.create_index("status")
            self.db.device_account_mappings.create_index([("device_id", 1), ("platform_id", 1)], unique=True)
            self.db.device_account_mappings.create_index([("device_id", 1), ("account_id", 1)], unique=True)

            logger.info("所有索引创建完成")
        except Exception as e:
            logger.error(f"创建索引失败: {str(e)}")

    def sync_devices_from_core(self) -> None:
        """从Core服务同步设备数据"""
        try:
            # 获取所有Core服务
            core_services = self.consul.get_all_services("thunderhub-core")
            if not core_services:
                logger.warning("未找到任何Core服务，无法同步设备数据")
                return

            logger.info(f"找到 {len(core_services)} 个Core服务")

            # 遍历每个Core服务
            for core_id, service_info in core_services.items():
                logger.info(f"正在从Core服务 {core_id} 同步设备数据")

                # 从Redis获取该Core服务的设备列表
                device_ids = self.redis.smembers(f"core:{core_id}:devices")
                if not device_ids:
                    logger.warning(f"Core服务 {core_id} 没有设备数据")
                    continue

                device_ids = [did.decode('utf-8') for did in device_ids]
                logger.info(f"Core服务 {core_id} 有 {len(device_ids)} 个设备")

                # 获取每个设备的详细信息
                for device_id in device_ids:
                    device_key = f"device:{core_id}:{device_id}:state"
                    device_data = self.redis.get(device_key)

                    if not device_data:
                        logger.warning(f"设备 {device_id} 没有状态数据")
                        continue

                    try:
                        device_info = json.loads(device_data)

                        # 确保设备数据包含必要字段
                        device_info["core_id"] = core_id
                        device_info["updated_at"] = datetime.datetime.now()

                        # 更新或插入设备数据
                        self.db.devices.update_one(
                            {"_id": device_id},
                            {"$set": device_info},
                            upsert=True
                        )

                        logger.info(f"已同步设备 {device_id} 的数据")
                    except Exception as e:
                        logger.error(f"处理设备 {device_id} 数据失败: {str(e)}")

            logger.info("设备数据同步完成")
        except Exception as e:
            logger.error(f"从Core服务同步设备数据失败: {str(e)}")

    def init_social_platforms(self) -> None:
        """初始化社交媒体平台数据"""
        platforms = [
            {
                "id": "youtube",
                "name": "YouTube",
                "icon": "youtube-icon.png",
                "website": "https://www.youtube.com",
                "status": "active",
                "features": ["video", "live", "comment"],
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "tiktok",
                "name": "TikTok",
                "icon": "tiktok-icon.png",
                "website": "https://www.tiktok.com",
                "status": "active",
                "features": ["video", "live", "comment"],
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "douyin",
                "name": "抖音",
                "icon": "douyin-icon.png",
                "website": "https://www.douyin.com",
                "status": "active",
                "features": ["video", "live", "comment"],
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            }
        ]

        for platform in platforms:
            try:
                # 使用upsert确保不会重复插入
                self.db.social_platforms.update_one(
                    {"id": platform["id"]},
                    {"$set": platform},
                    upsert=True
                )
                logger.info(f"已添加/更新平台: {platform['name']}")
            except Exception as e:
                logger.error(f"添加平台 {platform['name']} 失败: {str(e)}")

        logger.info("社交媒体平台初始化完成")

        # 初始化平台应用
        self.init_platform_apps()

    def init_platform_apps(self) -> None:
        """初始化平台应用数据"""
        apps = [
            {
                "id": "youtube_android",
                "platform_id": "youtube",
                "name": "YouTube Android App",
                "type": "android",
                "status": "active",
                "version": "18.20.38",
                "app_info": {
                    "package_name": "com.google.android.youtube",
                    "main_activity": "com.google.android.youtube.HomeActivity",
                    "min_android_version": "8.0",
                    "download_url": "https://play.google.com/store/apps/details?id=com.google.android.youtube"
                },
                "automation": {
                    "type": "appium",
                    "selectors": {
                        "login_button": "//android.widget.Button[@resource-id='com.google.android.youtube:id/sign_in_button']",
                        "post_button": "//android.widget.ImageView[@content-desc='创建']",
                        "comment_button": "//android.widget.ImageView[@content-desc='评论']",
                        "like_button": "//android.widget.ImageView[@content-desc='顶']",
                        "share_button": "//android.widget.ImageView[@content-desc='分享']"
                    },
                    "actions": {
                        "login": [
                            {
                                "action": "click",
                                "selector": "//android.widget.Button[@resource-id='com.google.android.youtube:id/sign_in_button']",
                                "value": ""
                            },
                            {
                                "action": "input",
                                "selector": "//android.widget.EditText[@resource-id='identifierId']",
                                "value": "{username}"
                            },
                            {
                                "action": "click",
                                "selector": "//android.widget.Button[@text='下一步']",
                                "value": ""
                            },
                            {
                                "action": "input",
                                "selector": "//android.widget.EditText[@resource-id='password']",
                                "value": "{password}"
                            },
                            {
                                "action": "click",
                                "selector": "//android.widget.Button[@text='下一步']",
                                "value": ""
                            }
                        ],
                        "post": [
                            {
                                "action": "click",
                                "selector": "//android.widget.ImageView[@content-desc='创建']",
                                "value": ""
                            },
                            {
                                "action": "click",
                                "selector": "//android.widget.TextView[@text='上传视频']",
                                "value": ""
                            },
                            {
                                "action": "click",
                                "selector": "//android.widget.Button[@text='选择文件']",
                                "value": ""
                            }
                        ]
                    },
                    "commands": {
                        "start_app": "adb shell am start -n com.google.android.youtube/com.google.android.youtube.HomeActivity",
                        "stop_app": "adb shell am force-stop com.google.android.youtube",
                        "clear_data": "adb shell pm clear com.google.android.youtube"
                    }
                },
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "youtube_web",
                "platform_id": "youtube",
                "name": "YouTube Web",
                "type": "web",
                "status": "active",
                "version": "latest",
                "app_info": {
                    "download_url": "https://www.youtube.com"
                },
                "automation": {
                    "type": "selenium",
                    "selectors": {
                        "login_button": "//a[contains(@href, 'signin')]",
                        "post_button": "//a[contains(@href, 'upload')]",
                        "comment_button": "//div[@id='comment-section']",
                        "like_button": "//button[@aria-label='Like']",
                        "share_button": "//button[@aria-label='Share']"
                    }
                },
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "tiktok_android",
                "platform_id": "tiktok",
                "name": "TikTok Android App",
                "type": "android",
                "status": "active",
                "version": "26.9.3",
                "app_info": {
                    "package_name": "com.zhiliaoapp.musically",
                    "main_activity": "com.ss.android.ugc.aweme.main.MainActivity",
                    "min_android_version": "5.0",
                    "download_url": "https://play.google.com/store/apps/details?id=com.zhiliaoapp.musically"
                },
                "automation": {
                    "type": "appium",
                    "selectors": {
                        "login_button": "//android.widget.TextView[@text='登录']",
                        "post_button": "//android.widget.ImageView[@content-desc='发布']",
                        "comment_button": "//android.widget.ImageView[@content-desc='评论']",
                        "like_button": "//android.widget.ImageView[@content-desc='喜欢']",
                        "share_button": "//android.widget.ImageView[@content-desc='分享']"
                    },
                    "commands": {
                        "start_app": "adb shell am start -n com.zhiliaoapp.musically/com.ss.android.ugc.aweme.main.MainActivity",
                        "stop_app": "adb shell am force-stop com.zhiliaoapp.musically",
                        "clear_data": "adb shell pm clear com.zhiliaoapp.musically"
                    }
                },
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "douyin_android",
                "platform_id": "douyin",
                "name": "抖音 Android App",
                "type": "android",
                "status": "active",
                "version": "23.3.0",
                "app_info": {
                    "package_name": "com.ss.android.ugc.aweme",
                    "main_activity": "com.ss.android.ugc.aweme.main.MainActivity",
                    "min_android_version": "5.0",
                    "download_url": "https://www.douyin.com/download"
                },
                "automation": {
                    "type": "appium",
                    "selectors": {
                        "login_button": "//android.widget.TextView[@text='登录']",
                        "post_button": "//android.widget.ImageView[@content-desc='发布']",
                        "comment_button": "//android.widget.ImageView[@content-desc='评论']",
                        "like_button": "//android.widget.ImageView[@content-desc='喜欢']",
                        "share_button": "//android.widget.ImageView[@content-desc='分享']"
                    },
                    "commands": {
                        "start_app": "adb shell am start -n com.ss.android.ugc.aweme/com.ss.android.ugc.aweme.main.MainActivity",
                        "stop_app": "adb shell am force-stop com.ss.android.ugc.aweme",
                        "clear_data": "adb shell pm clear com.ss.android.ugc.aweme"
                    }
                },
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            }
        ]

        for app in apps:
            try:
                # 使用upsert确保不会重复插入
                self.db.platform_apps.update_one(
                    {"id": app["id"]},
                    {"$set": app},
                    upsert=True
                )
                logger.info(f"已添加/更新应用: {app['name']}")
            except Exception as e:
                logger.error(f"添加应用 {app['name']} 失败: {str(e)}")

        logger.info("平台应用初始化完成")

    def add_social_account(self, account_data: Dict[str, Any]) -> str:
        """添加社交媒体账号"""
        try:
            # 确保必填字段存在
            required_fields = ["username", "platform_id"]
            for field in required_fields:
                if field not in account_data:
                    raise ValueError(f"缺少必填字段: {field}")

            # 检查平台是否存在
            platform = self.db.social_platforms.find_one({"id": account_data["platform_id"]})
            if not platform:
                raise ValueError(f"平台不存在: {account_data['platform_id']}")

            # 生成账号ID（如果没有提供）
            if "id" not in account_data:
                account_data["id"] = f"acc_{account_data['platform_id']}_{account_data['username']}"

            # 添加创建和更新时间
            now = datetime.datetime.now()
            account_data["created_at"] = now
            account_data["updated_at"] = now

            # 设置默认状态
            if "status" not in account_data:
                account_data["status"] = "active"

            # 确保平台关联字段正确设置
            account_data["platform_id"] = account_data["platform_id"]  # 主要关联字段

            # 添加平台名称，便于前端显示
            if platform and "name" in platform:
                account_data["platform_name"] = platform["name"]

            # 处理设备关联（如果提供了设备ID）
            if "device_id" in account_data:
                device_id = account_data.pop("device_id")  # 从账号数据中移除设备ID

                # 检查设备是否存在
                device = self.db.devices.find_one({"_id": device_id})
                if not device:
                    raise ValueError(f"设备不存在: {device_id}")

                # 如果设备有core_id，添加到账号数据中
                if device and "core_id" in device:
                    account_data["core_service_id"] = device["core_id"]

                # 如果提供了app_id，使用它；否则尝试找到平台的默认应用
                app_id = account_data.get("app_id")
                if not app_id:
                    # 尝试找到平台的Android应用
                    app = self.db.platform_apps.find_one({
                        "platform_id": account_data["platform_id"],
                        "type": "android"
                    })
                    if app:
                        app_id = app["id"]
                    else:
                        # 如果找不到应用，使用平台ID作为应用ID
                        app_id = account_data["platform_id"]

                # 创建设备账号映射
                self.add_device_account_mapping(
                    device_id=device_id,
                    account_id=account_data["id"],
                    platform_id=account_data["platform_id"],
                    app_id=app_id
                )

            # 插入账号数据
            self.db.social_accounts.update_one(
                {"id": account_data["id"]},
                {"$set": account_data},
                upsert=True
            )

            logger.info(f"已添加/更新账号: {account_data['username']} ({account_data['platform_id']})")
            return account_data["id"]
        except Exception as e:
            logger.error(f"添加账号失败: {str(e)}")
            raise

    def add_device_account_mapping(self, device_id: str, account_id: str, platform_id: str, app_id: str = None) -> str:
        """添加设备账号映射"""
        try:
            # 检查设备是否存在
            device = self.db.devices.find_one({"_id": device_id})
            if not device:
                raise ValueError(f"设备不存在: {device_id}")

            # 检查账号是否存在
            account = self.db.social_accounts.find_one({"id": account_id})
            if not account:
                raise ValueError(f"账号不存在: {account_id}")

            # 检查平台是否存在
            platform = self.db.social_platforms.find_one({"id": platform_id})
            if not platform:
                raise ValueError(f"平台不存在: {platform_id}")

            # 如果没有提供app_id，尝试获取平台的默认应用
            if not app_id:
                app = self.db.platform_apps.find_one({"platform_id": platform_id, "type": "android"})
                if app:
                    app_id = app["id"]
                else:
                    # 如果找不到应用，使用平台ID作为应用ID
                    app_id = platform_id

            # 检查是否已存在该设备和平台的映射
            existing_mapping = self.db.device_account_mappings.find_one({
                "device_id": device_id,
                "platform_id": platform_id
            })

            mapping_id = None

            if existing_mapping:
                # 如果已存在映射，更新为新的账号
                logger.info(f"设备 {device_id} 已存在平台 {platform_id} 的映射，更新为账号 {account_id}")
                mapping_id = existing_mapping["_id"]
                self.db.device_account_mappings.update_one(
                    {"_id": mapping_id},
                    {"$set": {
                        "account_id": account_id,
                        "app_id": app_id,
                        "status": "active",
                        "updated_at": datetime.datetime.now(),
                        "last_used": datetime.datetime.now()
                    }}
                )
            else:
                # 创建新的映射
                mapping_data = {
                    "device_id": device_id,
                    "account_id": account_id,
                    "platform_id": platform_id,
                    "app_id": app_id,
                    "status": "active",
                    "created_at": datetime.datetime.now(),
                    "updated_at": datetime.datetime.now(),
                    "last_used": datetime.datetime.now()
                }

                # 如果设备有core_id，添加到映射数据中
                if device and "core_id" in device:
                    mapping_data["core_service_id"] = device["core_id"]

                result = self.db.device_account_mappings.insert_one(mapping_data)
                mapping_id = result.inserted_id

            logger.info(f"已添加/更新设备账号映射: 设备={device_id}, 账号={account_id}, 平台={platform_id}, 应用={app_id}")
            return str(mapping_id)
        except Exception as e:
            logger.error(f"添加设备账号映射失败: {str(e)}")
            raise

    def import_data(self, file_path: str) -> None:
        """从JSON文件导入数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 导入平台数据
            if "platforms" in data:
                for platform in data["platforms"]:
                    try:
                        self.db.social_platforms.update_one(
                            {"id": platform["id"]},
                            {"$set": platform},
                            upsert=True
                        )
                    except Exception as e:
                        logger.error(f"导入平台 {platform.get('id')} 失败: {str(e)}")

                logger.info(f"已导入 {len(data['platforms'])} 个平台")

            # 导入账号数据
            if "accounts" in data:
                for account in data["accounts"]:
                    try:
                        self.add_social_account(account)
                    except Exception as e:
                        logger.error(f"导入账号 {account.get('username')} 失败: {str(e)}")

                logger.info(f"已导入 {len(data['accounts'])} 个账号")

            logger.info(f"从 {file_path} 导入数据完成")
        except Exception as e:
            logger.error(f"导入数据失败: {str(e)}")

    def export_data(self, file_path: str) -> None:
        """导出数据到JSON文件"""
        try:
            # 获取平台数据
            platforms = list(self.db.social_platforms.find({}, {"_id": 0}))

            # 获取账号数据
            accounts = list(self.db.social_accounts.find({}, {"_id": 0}))

            # 构建导出数据
            export_data = {
                "platforms": platforms,
                "accounts": accounts,
                "export_time": datetime.datetime.now().isoformat()
            }

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)

            logger.info(f"已导出 {len(platforms)} 个平台和 {len(accounts)} 个账号到 {file_path}")
        except Exception as e:
            logger.error(f"导出数据失败: {str(e)}")

    def interactive_add_account(self) -> None:
        """交互式添加账号"""
        try:
            print("\n=== 交互式添加社交媒体账号 ===")

            # 获取可用平台
            platforms = list(self.db.social_platforms.find({"status": "active"}))
            if not platforms:
                print("没有可用的平台，请先添加平台")
                return

            # 显示平台列表
            print("\n可用平台:")
            for i, platform in enumerate(platforms):
                print(f"{i+1}. {platform['name']} ({platform['id']})")

            # 选择平台
            platform_idx = int(input("\n请选择平台 (输入序号): ")) - 1
            if platform_idx < 0 or platform_idx >= len(platforms):
                print("无效的选择")
                return

            selected_platform = platforms[platform_idx]
            print(f"已选择平台: {selected_platform['name']} (ID: {selected_platform['id']})")

            # 输入账号信息
            username = input("\n请输入账号用户名: ")
            password = input("请输入账号密码 (可选): ")
            display_name = input("请输入显示名称 (可选): ")
            description = input("请输入账号描述 (可选): ")

            # 构建账号数据
            account_data = {
                "username": username,
                "platform_id": selected_platform["id"],
                "status": "active"
            }

            if password:
                account_data["password"] = password

            if display_name:
                account_data["display_name"] = display_name

            if description:
                account_data["description"] = description

            # 添加平台名称
            account_data["platform_name"] = selected_platform["name"]

            # 添加账号
            account_id = self.add_social_account(account_data)

            print("\n账号添加成功!")
            print(f"ID: {account_id}")
            print(f"用户名: {username}")
            print(f"平台: {selected_platform['name']} (ID: {selected_platform['id']})")

            # 询问是否要关联设备
            link_device = input("\n是否要关联设备? (y/n): ").lower() == 'y'
            if link_device:
                # 获取可用设备
                devices = list(self.db.devices.find({}))
                if not devices:
                    print("没有可用的设备，请先同步设备数据")
                    return

                # 显示设备列表
                print("\n可用设备:")
                for i, device in enumerate(devices):
                    device_name = device.get('name', 'Unknown')
                    device_id = device.get('_id', 'Unknown')
                    core_id = device.get('core_id', 'Unknown')
                    print(f"{i+1}. {device_name} (ID: {device_id}, Core: {core_id})")

                # 选择设备
                device_idx = int(input("\n请选择设备 (输入序号): ")) - 1
                if device_idx < 0 or device_idx >= len(devices):
                    print("无效的选择")
                    return

                selected_device = devices[device_idx]
                print(f"已选择设备: {selected_device.get('name', 'Unknown')} (ID: {selected_device.get('_id', 'Unknown')}, Core: {selected_device.get('core_id', 'Unknown')})")

                # 获取平台应用
                apps = list(self.db.platform_apps.find({"platform_id": selected_platform["id"], "status": "active"}))

                app_id = None
                if apps:
                    # 显示应用列表
                    print("\n可用应用:")
                    for i, app in enumerate(apps):
                        print(f"{i+1}. {app.get('name', 'Unknown')} (ID: {app.get('id', 'Unknown')}, 类型: {app.get('type', 'Unknown')})")

                    # 选择应用
                    app_idx = int(input("\n请选择应用 (输入序号): ")) - 1
                    if app_idx >= 0 and app_idx < len(apps):
                        selected_app = apps[app_idx]
                        app_id = selected_app["id"]
                        print(f"已选择应用: {selected_app.get('name', 'Unknown')} (ID: {app_id})")

                # 添加设备账号映射
                mapping_id = self.add_device_account_mapping(
                    device_id=selected_device["_id"],
                    account_id=account_id,
                    platform_id=selected_platform["id"],
                    app_id=app_id
                )

                print(f"\n设备账号映射添加成功! ID: {mapping_id}")

            # 查询添加的账号
            added_account = self.db.social_accounts.find_one({"id": account_id})
            if added_account:
                print("\n账号详细信息:")
                for key, value in added_account.items():
                    if key != "_id":  # 跳过MongoDB的_id字段
                        print(f"  {key}: {value}")

                # 如果关联了设备，显示映射信息
                if link_device:
                    mapping = self.db.device_account_mappings.find_one({"account_id": account_id})
                    if mapping:
                        print("\n设备账号映射详细信息:")
                        for key, value in mapping.items():
                            if key != "_id":  # 跳过MongoDB的_id字段
                                print(f"  {key}: {value}")

        except Exception as e:
            print(f"添加账号失败: {str(e)}")

    def run(self, args) -> None:
        """运行初始化流程"""
        # 如果需要重置数据库
        if args.reset:
            self.reset_database()

        # 创建索引
        self.create_indexes()

        # 从Core服务同步设备数据
        self.sync_devices_from_core()

        # 初始化社交媒体平台
        self.init_social_platforms()

        # 如果提供了导入文件
        if args.import_file:
            self.import_data(args.import_file)

        # 如果提供了导出文件
        if args.export_file:
            self.export_data(args.export_file)

        # 如果没有提供导入文件，进入交互模式
        if not args.import_file and not args.export_file:
            while True:
                print("\n=== ThunderHub 数据库初始化工具 ===")
                print("1. 从Core服务同步设备数据")
                print("2. 添加社交媒体账号")
                print("3. 导出数据到文件")
                print("4. 导入数据从文件")
                print("0. 退出")

                choice = input("\n请选择操作: ")

                if choice == "1":
                    self.sync_devices_from_core()
                elif choice == "2":
                    self.interactive_add_account()
                elif choice == "3":
                    file_path = input("请输入导出文件路径: ")
                    self.export_data(file_path)
                elif choice == "4":
                    file_path = input("请输入导入文件路径: ")
                    self.import_data(file_path)
                elif choice == "0":
                    break
                else:
                    print("无效的选择，请重试")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ThunderHub 数据库初始化工具")
    parser.add_argument("--reset", action="store_true", help="重置数据库（删除现有集合）")
    parser.add_argument("--import-file", type=str, help="从JSON文件导入数据")
    parser.add_argument("--export-file", type=str, help="将数据导出到JSON文件")

    args = parser.parse_args()

    initializer = DatabaseInitializer()
    initializer.run(args)


if __name__ == "__main__":
    main()
