# 长期任务调度器使用示例

## 概述

长期任务调度器支持两种类型的任务：
1. **定时任务 (Scheduled Tasks)** - 按照时间计划执行
2. **触发任务 (Triggered Tasks)** - 基于条件触发执行

## 定时任务示例

### 1. 每日定时上传任务

```json
{
  "name": "每日YouTube上传",
  "description": "每天晚上8点自动上传视频到YouTube",
  "task_type": "scheduled",
  "trigger_type": "cron",
  "schedule_config": {
    "cron_expression": "0 0 20 * * *"
  },
  "task_template": {
    "task_type": "youtube_upload",
    "platform_id": "681efeeecd836bd64b9c2a1e",
    "account_id": "your_youtube_account_id",
    "device_id": "device_001",
    "content_path": "/content/daily_videos/"
  },
  "enabled": true
}
```

### 2. 间隔时间采集任务

```json
{
  "name": "定期内容采集",
  "description": "每2小时采集一次对标账号内容",
  "task_type": "scheduled",
  "trigger_type": "interval",
  "schedule_config": {
    "interval_seconds": 7200
  },
  "task_template": {
    "task_type": "collect",
    "platform_id": "681efeeecd836bd64b9c2a22",
    "account_id": "benchmark_account_id",
    "params": {
      "account_url": "https://www.douyin.com/user/benchmark_user",
      "account_name": "对标账号名称"
    }
  },
  "enabled": true
}
```

## 触发任务示例

### 1. 文件监控触发任务

```json
{
  "name": "新视频自动上传",
  "description": "监控文件夹，有新视频时自动上传",
  "task_type": "triggered",
  "trigger_type": "file_watch",
  "trigger_config": {
    "watch_path": "/content/new_videos/",
    "min_interval": 300
  },
  "task_template": {
    "task_type": "youtube_upload",
    "platform_id": "681efeeecd836bd64b9c2a1e",
    "account_id": "your_youtube_account_id",
    "device_id": "device_001"
  },
  "enabled": true
}
```

### 2. 账号更新触发任务

```json
{
  "name": "对标账号更新监控",
  "description": "监控对标账号更新，有新内容时自动下载",
  "task_type": "triggered",
  "trigger_type": "account_update",
  "trigger_config": {
    "account_id": "benchmark_account_id",
    "platform_id": "681efeeecd836bd64b9c2a22",
    "check_interval": 3600
  },
  "task_template": {
    "task_type": "benchmark_download",
    "platform": "douyin",
    "benchmark_account_url": "https://www.douyin.com/user/benchmark_user",
    "download_path": "/downloads/benchmark/"
  },
  "enabled": true
}
```

## Cron表达式说明

Cron表达式格式：`秒 分 时 日 月 周`

常用示例：
- `0 0 12 * * *` - 每天中午12点执行
- `0 30 9 * * 1-5` - 工作日上午9:30执行
- `0 0 0 1 * *` - 每月1号午夜执行
- `0 */15 * * * *` - 每15分钟执行一次
- `0 0 8,20 * * *` - 每天上午8点和晚上8点执行

## API使用示例

### 创建调度任务

```javascript
// 前端调用示例
import { taskSchedulerApi } from '@/api/taskScheduler'

const createScheduledTask = async () => {
  const taskData = {
    name: "每日YouTube上传",
    description: "每天晚上8点自动上传视频",
    task_type: "scheduled",
    trigger_type: "cron",
    schedule_config: {
      cron_expression: "0 0 20 * * *"
    },
    task_template: {
      task_type: "youtube_upload",
      platform_id: "681efeeecd836bd64b9c2a1e",
      account_id: "your_account_id",
      device_id: "device_001",
      content_path: "/content/videos/"
    },
    enabled: true
  }
  
  try {
    const response = await taskSchedulerApi.createTask(taskData)
    console.log('任务创建成功:', response.data.task_id)
  } catch (error) {
    console.error('创建任务失败:', error)
  }
}
```

### 管理调度任务

```javascript
// 获取任务列表
const tasks = await taskSchedulerApi.getTasks()

// 暂停任务
await taskSchedulerApi.pauseTask(taskId)

// 恢复任务
await taskSchedulerApi.resumeTask(taskId)

// 删除任务
await taskSchedulerApi.deleteTask(taskId)
```

## 后端API调用示例

```python
# Python后端调用示例
import requests

# 创建调度任务
task_data = {
    "name": "定时采集任务",
    "task_type": "scheduled",
    "trigger_type": "interval",
    "schedule_config": {
        "interval_seconds": 3600
    },
    "task_template": {
        "task_type": "collect",
        "platform_id": "platform_id",
        "account_id": "account_id"
    },
    "enabled": True
}

response = requests.post(
    "http://localhost:8000/api/v1/task-scheduler/create",
    json=task_data
)

if response.status_code == 200:
    result = response.json()
    print(f"任务创建成功: {result['task_id']}")
```

## 注意事项

1. **任务模板配置**：确保任务模板中的参数正确，包括平台ID、账号ID、设备ID等
2. **Cron表达式**：使用标准的6位Cron表达式（包含秒）
3. **文件监控**：监控路径必须存在且可访问
4. **触发间隔**：设置合理的最小触发间隔，避免频繁执行
5. **资源管理**：长期任务会持续占用系统资源，请合理规划任务数量

## 故障排除

1. **任务不执行**：检查任务状态是否为"active"，Cron表达式是否正确
2. **文件监控失效**：确认监控路径存在且有读取权限
3. **任务执行失败**：查看任务日志，检查设备状态和网络连接
4. **调度器停止**：检查Core服务状态，确认调度器正常运行

## 扩展功能

系统支持自定义触发器，可以根据业务需求扩展：
- 数据库变更触发
- API回调触发
- 外部事件触发
- 复杂条件组合触发
