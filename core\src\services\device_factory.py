from typing import Dict, Type, Any
from src.devices.base import DeviceInterface
import importlib
import logging

class DeviceFactory:
    """设备工厂（支持动态注册）"""
    
    def __init__(self):
        self._registry: Dict[str, Type[DeviceInterface]] = {}
        self.logger = logging.getLogger(__name__)
        
    def register(self, device_type: str, controller_cls: Type[DeviceInterface]):
        """注册设备控制器"""
        required_methods = ['start', 'stop', 'get_status', 'execute_command']
        for method in required_methods:
            if not hasattr(controller_cls, method):
                raise TypeError(f"{controller_cls.__name__} 必须实现{method}方法")
        self._registry[device_type] = controller_cls
        self.logger.info(f"注册设备类型: {device_type} -> {controller_cls.__name__}")
        
    def create(self, config: Dict[str, Any]) -> DeviceInterface:
        """创建设备实例"""
        device_type = config.get('type')
        if not device_type:
            raise ValueError("设备配置缺少type字段")
            
        if controller_cls := self._registry.get(device_type):
            try:
                return controller_cls(config)
            except Exception as e:
                self.logger.error(f"创建设备失败: {str(e)}")
                raise
            
        raise ValueError(f"未知的设备类型: {device_type}")

    def load_from_module(self, module_path: str):
        """从模块动态加载设备控制器"""
        try:
            module = importlib.import_module(module_path)
            if hasattr(module, 'register_devices'):
                module.register_devices(self)
        except ImportError as e:
            self.logger.warning(f"加载设备模块失败: {module_path} - {str(e)}")

# 全局工厂实例
device_factory = DeviceFactory()

# 默认注册雷电模拟器
try:
    from src.devices.ldplayer.controller import LDPlayerController
    device_factory.register('ldplayer', LDPlayerController)
except ImportError:
    logging.getLogger(__name__).warning("无法加载雷电模拟器控制器")
