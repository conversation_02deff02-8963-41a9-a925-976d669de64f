"""
视频采集API
提供抖音视频采集相关的API接口
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime

from ..downloaders.douyin_playwright_downloader import DouyinPlaywrightDownloader
from .backend_client import BackendAPIClient

logger = logging.getLogger(__name__)


class VideoCollectAPI:
    """视频采集API类"""
    
    def __init__(self, config, backend_url: str = "http://localhost:8000"):
        """初始化视频采集API

        Args:
            config: 配置对象
            backend_url: Backend服务地址
        """
        self.config = config
        self.backend_url = backend_url
        self.active_tasks = {}  # task_id -> downloader
        
    async def start_collect_task(
        self,
        task_id: str,
        account_url: str,
        account_name: str,
        account_id: str,
        collect_mode: str = "basic_collect",
        target_months: List[Dict[str, str]] = None,
        download_path: str = None
    ) -> Dict[str, Any]:
        """启动视频采集任务
        
        Args:
            task_id: 任务ID
            account_url: 账号URL
            account_name: 账号名称
            account_id: 账号ID
            collect_mode: 采集模式
            target_months: 目标月份列表
            download_path: 下载路径
            
        Returns:
            Dict: 任务启动结果
        """
        try:
            logger.info(f"启动视频采集任务: {task_id}")
            
            if task_id in self.active_tasks:
                return {
                    'success': False,
                    'error': '任务已在运行中'
                }
            
            # 准备下载路径
            if not download_path:
                download_path = f"H:\\PublishSystem\\{account_name}\\{datetime.now().strftime('%Y-%m')}"
            
            # 创建Backend API客户端
            backend_api = BackendAPIClient(self.backend_url)

            # 创建下载器
            downloader = DouyinPlaywrightDownloader(
                download_path=download_path,
                task_id=task_id,
                debug_mode=True,  # 启用调试模式
                backend_api_client=backend_api
            )
            
            # 保存下载器引用
            self.active_tasks[task_id] = downloader
            
            # 准备采集配置
            collect_config = {
                'mode': collect_mode,
                'get_real_urls': collect_mode in ['full_collect', 'collect_and_download'],
                'download_files': collect_mode == 'collect_and_download',
                'target_months': target_months or [],
                'use_month_filter': bool(target_months),
                'max_videos': 500
            }
            
            # 异步执行采集任务
            asyncio.create_task(
                self._execute_collect_task(
                    task_id, downloader, account_url, account_name, account_id, collect_config
                )
            )
            
            return {
                'success': True,
                'task_id': task_id,
                'message': '采集任务已启动'
            }
            
        except Exception as e:
            error_msg = f"启动采集任务失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    async def _execute_collect_task(
        self,
        task_id: str,
        downloader: DouyinPlaywrightDownloader,
        account_url: str,
        account_name: str,
        account_id: str,
        collect_config: Dict[str, Any]
    ):
        """执行采集任务
        
        Args:
            task_id: 任务ID
            downloader: 下载器实例
            account_url: 账号URL
            account_name: 账号名称
            account_id: 账号ID
            collect_config: 采集配置
        """
        try:
            logger.info(f"开始执行采集任务: {task_id}")
            
            # 执行视频采集
            result = await downloader.collect_videos_to_database(
                account_url, account_name, account_id, collect_config
            )
            
            # 如果需要下载文件
            if collect_config.get('download_files'):
                download_result = await downloader.download_videos_from_database(
                    account_id, collect_config
                )
                result['download_result'] = download_result
            
            logger.info(f"采集任务完成: {task_id}, 结果: {result}")
            
        except Exception as e:
            logger.error(f"执行采集任务失败: {task_id} - {str(e)}")
        finally:
            # 清理资源
            await downloader.cleanup()
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态信息
        """
        try:
            if task_id in self.active_tasks:
                downloader = self.active_tasks[task_id]
                return {
                    'task_id': task_id,
                    'status': 'running',
                    'logs': downloader.logs[-20:] if hasattr(downloader, 'logs') else []
                }
            else:
                return {
                    'task_id': task_id,
                    'status': 'not_found',
                    'message': '任务不存在或已完成'
                }
                
        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return {
                'task_id': task_id,
                'status': 'error',
                'error': str(e)
            }
    
    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 取消结果
        """
        try:
            if task_id in self.active_tasks:
                downloader = self.active_tasks[task_id]
                await downloader.cleanup()
                del self.active_tasks[task_id]
                
                return {
                    'success': True,
                    'message': '任务已取消'
                }
            else:
                return {
                    'success': False,
                    'error': '任务不存在'
                }
                
        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_account_months(self, account_url: str) -> Dict[str, Any]:
        """获取账号可用的发布月份
        
        Args:
            account_url: 账号URL
            
        Returns:
            Dict: 可用月份列表
        """
        try:
            logger.info(f"获取账号可用月份: {account_url}")
            
            # 创建临时下载器
            downloader = DouyinPlaywrightDownloader(
                download_path="/tmp",
                debug_mode=True,
                db_client=self.db_client
            )
            
            try:
                # 获取账号信息
                account_info = await downloader.get_account_info(account_url)
                if not account_info or account_info.get('error'):
                    return {
                        'success': False,
                        'error': '获取账号信息失败'
                    }
                
                # 获取可用月份
                available_months = await downloader.get_available_months()
                
                return {
                    'success': True,
                    'account_info': account_info,
                    'available_months': available_months
                }
                
            finally:
                await downloader.cleanup()
                
        except Exception as e:
            error_msg = f"获取账号月份失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    async def get_videos_statistics(self, account_id: str = None) -> Dict[str, Any]:
        """获取视频统计信息
        
        Args:
            account_id: 账号ID（可选）
            
        Returns:
            Dict: 统计信息
        """
        try:
            stats = await self.video_model.get_download_statistics(account_id)
            return {
                'success': True,
                'statistics': stats
            }
            
        except Exception as e:
            logger.error(f"获取视频统计失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_videos_by_account(self, account_id: str, limit: int = 100) -> Dict[str, Any]:
        """获取指定账号的视频列表
        
        Args:
            account_id: 账号ID
            limit: 限制数量
            
        Returns:
            Dict: 视频列表
        """
        try:
            videos = await self.video_model.get_videos_by_account(account_id, limit)
            return {
                'success': True,
                'videos': videos,
                'count': len(videos)
            }
            
        except Exception as e:
            logger.error(f"获取账号视频列表失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def start_download_from_database(
        self,
        task_id: str,
        account_id: str,
        download_path: str = None,
        max_downloads: int = 50
    ) -> Dict[str, Any]:
        """从数据库启动视频下载任务
        
        Args:
            task_id: 任务ID
            account_id: 账号ID
            download_path: 下载路径
            max_downloads: 最大下载数量
            
        Returns:
            Dict: 任务启动结果
        """
        try:
            logger.info(f"启动数据库视频下载任务: {task_id}")
            
            if task_id in self.active_tasks:
                return {
                    'success': False,
                    'error': '任务已在运行中'
                }
            
            # 准备下载路径
            if not download_path:
                download_path = f"H:\\PublishSystem\\downloads\\{datetime.now().strftime('%Y-%m-%d')}"
            
            # 创建下载器
            downloader = DouyinPlaywrightDownloader(
                download_path=download_path,
                task_id=task_id,
                debug_mode=False,  # 下载时不需要调试模式
                db_client=self.db_client
            )
            
            # 保存下载器引用
            self.active_tasks[task_id] = downloader
            
            # 准备下载配置
            download_config = {
                'max_downloads': max_downloads
            }
            
            # 异步执行下载任务
            asyncio.create_task(
                self._execute_download_task(task_id, downloader, account_id, download_config)
            )
            
            return {
                'success': True,
                'task_id': task_id,
                'message': '下载任务已启动'
            }
            
        except Exception as e:
            error_msg = f"启动下载任务失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    async def _execute_download_task(
        self,
        task_id: str,
        downloader: DouyinPlaywrightDownloader,
        account_id: str,
        download_config: Dict[str, Any]
    ):
        """执行下载任务
        
        Args:
            task_id: 任务ID
            downloader: 下载器实例
            account_id: 账号ID
            download_config: 下载配置
        """
        try:
            logger.info(f"开始执行下载任务: {task_id}")
            
            # 执行视频下载
            result = await downloader.download_videos_from_database(account_id, download_config)
            
            logger.info(f"下载任务完成: {task_id}, 结果: {result}")
            
        except Exception as e:
            logger.error(f"执行下载任务失败: {task_id} - {str(e)}")
        finally:
            # 清理资源
            await downloader.cleanup()
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
