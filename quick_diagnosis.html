<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ThunderHub 视频加载问题快速诊断</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h2 {
            color: #34495e;
            margin-top: 0;
        }
        .status {
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }
        .solution-box {
            background: #e8f5e8;
            border: 1px solid #4CAF50;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .solution-box h3 {
            color: #2e7d32;
            margin-top: 0;
        }
        .code {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ThunderHub 视频加载问题快速诊断</h1>
        
        <div class="test-section">
            <h2>🚀 自动诊断</h2>
            <button onclick="runFullDiagnosis()" id="diagnosisBtn">开始完整诊断</button>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div id="overallStatus"></div>
        </div>

        <div class="test-section">
            <h2>🌐 服务连接状态</h2>
            <button onclick="checkServices()">检查服务状态</button>
            <div id="serviceStatus"></div>
        </div>

        <div class="test-section">
            <h2>🎬 视频API测试</h2>
            <input type="text" id="videoPath" placeholder="输入视频文件路径 (例如: C:\Videos\test.mp4)" 
                   style="width: 70%; padding: 8px; margin-right: 10px;">
            <button onclick="testVideoAPI()">测试视频API</button>
            <div id="videoApiStatus"></div>
        </div>

        <div class="test-section">
            <h2>🔧 常见解决方案</h2>
            <div class="solution-box">
                <h3>1. 启动所有服务</h3>
                <p>确保以下服务正在运行：</p>
                <div class="code">
                    # 启动Backend (端口 8000)
                    cd backend
                    python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
                    
                    # 启动Core服务 (端口 50051 和 8001)
                    cd core
                    python src/run.py
                    
                    # 启动Frontend (端口 3000)
                    cd frontend
                    npm run dev
                </div>
                <p>或者使用提供的启动脚本：</p>
                <div class="code">start_services.bat 或 start_services.ps1</div>
            </div>

            <div class="solution-box">
                <h3>2. 检查防火墙设置</h3>
                <p>确保以下端口未被防火墙阻止：</p>
                <ul>
                    <li>8000 (Backend API)</li>
                    <li>8001 (文件服务器)</li>
                    <li>50051 (Core gRPC)</li>
                    <li>3000 (Frontend)</li>
                </ul>
            </div>

            <div class="solution-box">
                <h3>3. 视频文件路径</h3>
                <p>确保：</p>
                <ul>
                    <li>视频文件确实存在</li>
                    <li>路径中没有特殊字符</li>
                    <li>应用有读取文件的权限</li>
                    <li>支持的格式：MP4, AVI, MOV, MKV, WMV, FLV</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 浏览器开发者工具检查</h2>
            <p>按 F12 打开开发者工具，检查：</p>
            <ol>
                <li><strong>Console标签页</strong>：查看是否有JavaScript错误</li>
                <li><strong>Network标签页</strong>：查看API请求状态
                    <ul>
                        <li>/api/v1/filesystem/video/thumbnail</li>
                        <li>/api/v1/filesystem/video/preview-info</li>
                        <li>http://localhost:8001/files/...</li>
                    </ul>
                </li>
                <li><strong>Application标签页</strong>：清除缓存和Cookie</li>
            </ol>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 3;

        function updateProgress(step) {
            const percentage = (step / totalSteps) * 100;
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        async function runFullDiagnosis() {
            const btn = document.getElementById('diagnosisBtn');
            btn.disabled = true;
            btn.textContent = '诊断中...';
            
            currentStep = 0;
            updateProgress(0);
            
            document.getElementById('overallStatus').innerHTML = '<div class="status info">开始诊断...</div>';
            
            // 步骤1：检查服务
            currentStep++;
            updateProgress(currentStep);
            await checkServices();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 步骤2：测试基本API
            currentStep++;
            updateProgress(currentStep);
            await testBasicAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 步骤3：完成
            currentStep++;
            updateProgress(currentStep);
            
            btn.disabled = false;
            btn.textContent = '重新诊断';
            
            document.getElementById('overallStatus').innerHTML += '<div class="status success">诊断完成！请查看上述结果。</div>';
        }

        async function checkServices() {
            const statusDiv = document.getElementById('serviceStatus');
            statusDiv.innerHTML = '<div class="status info">检查服务状态中...</div>';
            
            const services = [
                { name: 'Backend API', url: 'http://localhost:8000/docs', port: 8000 },
                { name: 'Core文件服务器', url: 'http://localhost:8001/health', port: 8001 }
            ];
            
            let results = '';
            
            for (const service of services) {
                try {
                    const response = await fetch(service.url, { 
                        method: 'GET',
                        mode: 'cors'
                    });
                    
                    if (response.ok) {
                        results += `<div class="status success">✓ ${service.name}: 连接正常 (端口 ${service.port})</div>`;
                    } else {
                        results += `<div class="status error">✗ ${service.name}: 响应异常 ${response.status} (端口 ${service.port})</div>`;
                    }
                } catch (error) {
                    results += `<div class="status error">✗ ${service.name}: 连接失败 (端口 ${service.port}) - ${error.message}</div>`;
                }
            }
            
            statusDiv.innerHTML = results;
        }

        async function testBasicAPI() {
            try {
                const response = await fetch('http://localhost:8000/api/v1/filesystem/folder-list', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        path: 'C:\\',
                        include_md5: false,
                        include_media_info: false
                    })
                });
                
                if (response.ok) {
                    console.log('基本API测试通过');
                } else {
                    console.error('基本API测试失败:', response.status);
                }
            } catch (error) {
                console.error('基本API测试异常:', error);
            }
        }

        async function testVideoAPI() {
            const videoPath = document.getElementById('videoPath').value;
            const statusDiv = document.getElementById('videoApiStatus');
            
            if (!videoPath) {
                statusDiv.innerHTML = '<div class="status warning">请输入视频文件路径</div>';
                return;
            }
            
            statusDiv.innerHTML = '<div class="status info">测试视频API中...</div>';
            
            try {
                // 测试缩略图生成
                const thumbnailResponse = await fetch('http://localhost:8000/api/v1/filesystem/video/thumbnail', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_path: videoPath,
                        max_width: 320,
                        max_height: 180,
                        quality: 85
                    })
                });
                
                if (thumbnailResponse.ok) {
                    const data = await thumbnailResponse.json();
                    if (data.success) {
                        statusDiv.innerHTML = `
                            <div class="status success">✓ 缩略图API测试成功</div>
                            <div class="status info">缩略图URL: ${data.thumbnail_url || 'N/A'}</div>
                        `;
                    } else {
                        statusDiv.innerHTML = `<div class="status error">✗ 缩略图生成失败: ${data.error}</div>`;
                    }
                } else {
                    statusDiv.innerHTML = `<div class="status error">✗ 缩略图API请求失败: ${thumbnailResponse.status}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">✗ 视频API测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkServices();
        };
    </script>
</body>
</html>
