"""
热重载启动脚本
监控代码变化并自动重新加载修改的模块
"""

import os
import sys
import time
import logging
import importlib
import asyncio
import threading
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("hot-reload")

# 需要监控的目录
SRC_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "src")

# 需要排除的目录和文件
EXCLUDE_DIRS = [
    "__pycache__",
    ".git",
    ".idea",
    ".vscode",
    "venv",
]

# 需要监控的文件扩展名
WATCH_EXTENSIONS = [
    ".py",
]

# 主服务实例
main_service = None
main_task = None

class CodeChangeHandler(FileSystemEventHandler):
    """代码变更处理器"""

    def __init__(self):
        self.last_modified = {}
        self.reload_lock = threading.Lock()
        self.reload_timer = None

    def on_modified(self, event):
        if event.is_directory:
            return

        file_path = event.src_path

        # 检查文件扩展名
        if not any(file_path.endswith(ext) for ext in WATCH_EXTENSIONS):
            return

        # 检查是否在排除目录中
        if any(excl_dir in file_path for excl_dir in EXCLUDE_DIRS):
            return

        # 获取文件的修改时间
        modified_time = os.path.getmtime(file_path)

        # 检查是否是重复事件
        if file_path in self.last_modified and self.last_modified[file_path] == modified_time:
            return

        self.last_modified[file_path] = modified_time

        # 取消之前的定时器
        if self.reload_timer:
            self.reload_timer.cancel()

        # 设置新的定时器，延迟0.5秒执行重载
        # 这样可以避免文件保存过程中的多次触发
        self.reload_timer = threading.Timer(0.5, self.reload_code, args=[file_path])
        self.reload_timer.start()

    def reload_code(self, file_path):
        """重新加载修改的模块"""
        with self.reload_lock:
            try:
                # 将文件路径转换为模块路径
                rel_path = os.path.relpath(file_path, os.path.dirname(SRC_DIR))
                module_path = os.path.splitext(rel_path)[0].replace(os.path.sep, ".")

                # 如果是__init__.py文件，获取其所在目录作为模块路径
                if os.path.basename(file_path) == "__init__.py":
                    module_path = os.path.dirname(module_path).replace(os.path.sep, ".")

                logger.info(f"检测到文件变更: {file_path}")
                logger.info(f"尝试重新加载模块: {module_path}")

                # 查找已加载的模块
                modules_to_reload = []
                for name, module in list(sys.modules.items()):
                    # 只重新加载我们的项目模块
                    if name.startswith("src.") and (
                        name == module_path or
                        name.startswith(f"{module_path}.") or
                        module_path.startswith(f"{name}.")
                    ):
                        modules_to_reload.append((name, module))

                # 按照依赖顺序重新加载模块
                modules_to_reload.sort(key=lambda x: len(x[0].split(".")), reverse=True)

                for name, module in modules_to_reload:
                    try:
                        logger.info(f"重新加载模块: {name}")
                        importlib.reload(module)
                    except Exception as e:
                        logger.error(f"重新加载模块 {name} 失败: {str(e)}")

                # 创建一个异步任务来重新启动服务
                # 使用线程安全的方式调度异步任务
                try:
                    # 尝试获取当前线程的事件循环
                    try:
                        loop = asyncio.get_event_loop_policy().get_event_loop()
                    except RuntimeError:
                        # 如果当前线程没有事件循环，创建一个新的
                        logger.info("当前线程没有事件循环，创建一个新的")
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                    if loop.is_running():
                        # 如果循环正在运行，使用run_coroutine_threadsafe
                        asyncio.run_coroutine_threadsafe(restart_service_async(), loop)
                        logger.info("已调度服务重启任务")
                    else:
                        # 如果循环未运行，直接运行协程
                        logger.info("事件循环未运行，直接运行协程")
                        loop.run_until_complete(restart_service_async())
                except Exception as loop_error:
                    logger.error(f"调度重启任务失败: {str(loop_error)}", exc_info=True)
                    # 尝试使用asyncio.run作为备选方案
                    try:
                        logger.info("尝试使用asyncio.run作为备选方案")
                        # 在新线程中运行
                        import threading
                        threading.Thread(target=lambda: asyncio.run(restart_service_async())).start()
                        logger.info("已在新线程中启动重启任务")
                    except Exception as run_error:
                        logger.error(f"备选方案也失败: {str(run_error)}", exc_info=True)

            except Exception as e:
                logger.error(f"重新加载代码失败: {str(e)}", exc_info=True)

async def restart_service_async():
    """异步重新启动服务"""
    global main_service, main_task

    try:
        logger.info("正在异步重新启动服务...")

        # 停止当前服务
        if main_service:
            try:
                logger.info(f"正在停止主服务: {main_service}")
                await main_service.stop()
                logger.info("已停止当前服务")
            except Exception as stop_error:
                logger.error(f"停止服务异常: {str(stop_error)}", exc_info=True)
        else:
            logger.warning("没有找到主服务实例，无法停止")

        # 等待服务完全停止
        await asyncio.sleep(1)

        # 重新导入主模块
        try:
            logger.info("重新导入主模块...")
            import src.main
            importlib.reload(src.main)
            logger.info("主模块已重新导入")
        except Exception as import_error:
            logger.error(f"重新导入主模块异常: {str(import_error)}", exc_info=True)
            raise

        # 取消当前任务
        if main_task and not main_task.done():
            try:
                logger.info(f"取消当前任务: {main_task}")
                main_task.cancel()
                try:
                    await main_task
                except asyncio.CancelledError:
                    logger.info("已取消旧任务")
                except Exception as task_error:
                    logger.error(f"取消任务异常: {str(task_error)}")
            except Exception as cancel_error:
                logger.error(f"取消任务异常: {str(cancel_error)}")

        # 创建新的服务实例和任务
        try:
            # 导入main函数
            from src.main import main

            # 创建新的任务
            logger.info("创建新的服务任务...")
            main_task = asyncio.create_task(main())
            logger.info(f"新任务已创建: {main_task}")

            # 等待服务初始化
            logger.info("等待服务初始化...")
            await asyncio.sleep(2)

            # 获取新的主服务实例
            from src.main import get_main_service
            main_service = get_main_service()

            if main_service:
                logger.info(f"获取到新的主服务实例: {main_service}")
            else:
                logger.warning("未能获取新的主服务实例")

            logger.info("服务已重新启动")
        except Exception as create_error:
            logger.error(f"创建新服务异常: {str(create_error)}", exc_info=True)
            raise

    except Exception as e:
        logger.error(f"重新启动服务失败: {str(e)}", exc_info=True)
        # 尝试恢复
        logger.info("尝试恢复服务...")
        try:
            from src.main import main
            main_task = asyncio.create_task(main())
            logger.info("服务已尝试恢复")
        except Exception as recovery_error:
            logger.error(f"恢复服务失败: {str(recovery_error)}", exc_info=True)

def restart_service():
    """兼容性函数，用于非异步上下文"""
    try:
        # 尝试获取当前线程的事件循环
        try:
            loop = asyncio.get_event_loop_policy().get_event_loop()
        except RuntimeError:
            # 如果当前线程没有事件循环，创建一个新的
            logger.info("当前线程没有事件循环，创建一个新的")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        if loop.is_running():
            # 如果循环正在运行，使用run_coroutine_threadsafe
            logger.info("事件循环正在运行，使用run_coroutine_threadsafe")
            future = asyncio.run_coroutine_threadsafe(restart_service_async(), loop)
            # 等待结果
            try:
                result = future.result(timeout=30)
                logger.info(f"重启服务完成，结果: {result}")
            except asyncio.TimeoutError:
                logger.warning("重启服务超时，但仍在后台运行")
            except Exception as future_error:
                logger.error(f"等待重启服务结果时出错: {str(future_error)}", exc_info=True)
        else:
            # 如果循环未运行，直接运行协程
            logger.info("事件循环未运行，直接运行协程")
            loop.run_until_complete(restart_service_async())
    except Exception as e:
        logger.error(f"重启服务失败: {str(e)}", exc_info=True)
        # 尝试使用asyncio.run作为备选方案
        try:
            logger.info("尝试使用asyncio.run作为备选方案")
            asyncio.run(restart_service_async())
        except Exception as run_error:
            logger.error(f"备选方案也失败: {str(run_error)}", exc_info=True)
            # 最后的备选方案：在新线程中运行
            try:
                logger.info("尝试在新线程中运行")
                import threading
                threading.Thread(target=lambda: asyncio.run(restart_service_async())).start()
                logger.info("已在新线程中启动重启任务")
            except Exception as thread_error:
                logger.error(f"所有重启方法都失败: {str(thread_error)}", exc_info=True)

async def start_service_with_hot_reload():
    """启动带有热重载的服务"""
    global main_service, main_task

    try:
        # 导入主模块
        from src.main import main, get_main_service

        # 启动服务
        logger.info("正在启动服务...")
        main_task = asyncio.create_task(main())

        # 等待服务初始化
        await asyncio.sleep(2)

        # 获取主服务实例
        try:
            main_service = get_main_service()
            if main_service:
                logger.info(f"获取到主服务实例: {main_service}")
            else:
                logger.warning("未能获取主服务实例，热重载可能无法正常工作")
        except Exception as service_error:
            logger.warning(f"获取主服务实例失败: {str(service_error)}")

        # 启动文件监控
        logger.info(f"开始监控目录: {SRC_DIR}")
        event_handler = CodeChangeHandler()
        observer = Observer()
        observer.schedule(event_handler, SRC_DIR, recursive=True)
        observer.start()

        try:
            # 保持主线程运行
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("接收到终止信号，正在停止服务...")
            observer.stop()

        observer.join()

    except Exception as e:
        logger.error(f"启动服务失败: {str(e)}", exc_info=True)

def main():
    """入口函数"""
    try:
        # 运行服务
        asyncio.run(start_service_with_hot_reload())
    except KeyboardInterrupt:
        logger.info("服务已终止")

if __name__ == "__main__":
    main()
