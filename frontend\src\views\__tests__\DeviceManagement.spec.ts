import { mount, flushPromises } from '@vue/test-utils'
import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest'
import { nextTick } from 'vue'
import DeviceManagement from '@/views/DeviceManagement.vue'
import * as echarts from 'echarts'
import axios from 'axios'

// Mock echarts
vi.mock('echarts', () => ({
  init: vi.fn().mockReturnValue({
    setOption: vi.fn(),
    dispose: vi.fn()
  })
}))

vi.mock('axios')

const mockDevices = [
  {
    id: '1',
    name: '模拟器1',
    status: '运行中',
    cpu: '30%',
    memory: '45%',
    network: '正常'
  },
  {
    id: '2',
    name: '模拟器2',
    status: '已停止',
    cpu: '0%',
    memory: '0%',
    network: '断开'
  }
]

describe('DeviceManagement.vue', () => {
  // Mock API模块，添加100ms延时模拟真实请求
  vi.mock('@/api/device', () => ({
    getDevices: vi.fn(() => new Promise(resolve =>
      setTimeout(() => resolve({ data: mockDevices }), 100)
    )),
    startDevice: vi.fn(() => new Promise(resolve =>
      setTimeout(resolve, 100)
    )),
    stopDevice: vi.fn(() => new Promise(resolve =>
      setTimeout(resolve, 100)
    ))
  }))

  const mountComponent = async () => {
    const wrapper = mount(DeviceManagement)
    
    // 等待所有异步操作完成
    await flushPromises()
    await nextTick()
    await nextTick()
    
    return wrapper
  }

  beforeEach(() => {
    vi.resetAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('正确渲染设备列表', async () => {
    const wrapper = await mountComponent()
    
    // 确保数据加载完成
    await flushPromises()
    await nextTick()
    await nextTick()
    
    // 检查组件数据
    expect(wrapper.vm.devices).toEqual(mockDevices)
    
    // 检查表格渲染
    const table = wrapper.findComponent({ name: 'ElTable' })
    expect(table.exists()).toBe(true)
    
    // 检查表格行
    const rows = wrapper.findAll('.el-table__row')
    expect(rows).toHaveLength(mockDevices.length)
    
    // 检查内容
    expect(wrapper.text()).toContain('模拟器1')
    expect(wrapper.text()).toContain('模拟器2')
  })

  it('正确显示设备状态标签', async () => {
    const wrapper = await mountComponent()
    
    // 确保数据加载和DOM更新
    await new Promise(resolve => setTimeout(resolve, 200)) // 增加等待时间
    await flushPromises()
    await nextTick()
    await nextTick()

    // 使用更可靠的表格行查询方式
    const rows = wrapper.findAll('.el-table__row')
    expect(rows).toHaveLength(2)

    // 验证每行的状态标签
    const runningTag = rows[0].find('.el-tag')
    const stoppedTag = rows[1].find('.el-tag')

    expect(runningTag.exists()).toBe(true)
    expect(stoppedTag.exists()).toBe(true)
    expect(runningTag.classes()).toContain('el-tag--success')
    expect(stoppedTag.classes()).toContain('el-tag--danger')
  })

  it('点击刷新按钮调用API', async () => {
    const wrapper = await mountComponent()
    
    // 重置mock调用记录
    const { getDevices } = await import('@/api/device')
    vi.mocked(getDevices).mockClear()
    
    // 找到刷新按钮并点击
    const buttons = wrapper.findAll('.el-button')
    const refreshBtn = buttons.find(btn => btn.text().includes('刷新'))
    expect(refreshBtn).toBeDefined()
    await refreshBtn?.trigger('click')
    
    // 验证API调用
    await nextTick()
    expect(getDevices).toHaveBeenCalledTimes(1)
  })


  it('选择设备后启动/停止功能', async () => {
    const wrapper = await mountComponent()
    
    // 模拟选择第一个设备
    wrapper.vm.selectedDevices = [wrapper.vm.devices[0]]
    await nextTick()
    
    // 获取按钮
    const buttons = wrapper.findAll('.el-button')
    const startBtn = buttons.find(btn => btn.text().includes('启动'))
    const stopBtn = buttons.find(btn => btn.text().includes('停止'))
    
    expect(startBtn).toBeDefined()
    expect(stopBtn).toBeDefined()
    
    // 重置mock调用记录
    const { startDevice, stopDevice } = await import('@/api/device')
    vi.mocked(startDevice).mockClear()
    vi.mocked(stopDevice).mockClear()
    
    // 测试启动功能
    await startBtn?.trigger('click')
    
    // 验证表格加载状态
    expect(wrapper.find('.el-loading-mask').exists()).toBe(true)
    
    // 等待API异步操作
    await new Promise(resolve => setTimeout(resolve, 150))
    await flushPromises()
    await nextTick()
    await nextTick()
    
    // 验证加载状态已结束
    expect(wrapper.find('.el-loading-mask').exists()).toBe(false)
    
    // 先验证API调用
    expect(startDevice).toHaveBeenCalledWith('1')
    // 再验证loading状态
    expect(wrapper.vm.loading).toBe(false)
    
    // 测试停止功能
    await stopBtn?.trigger('click')
    expect(wrapper.vm.loading).toBe(true)
    
    // 等待更长时间确保API调用完成
    await new Promise(resolve => setTimeout(resolve, 100))
    await flushPromises()
    await nextTick()
    await nextTick()
    
    expect(stopDevice).toHaveBeenCalledWith('1')
    expect(wrapper.vm.loading).toBe(false)
  })

  it('渲染状态监控图表', async () => {
    const wrapper = mount(DeviceManagement)
    await nextTick()
    
    expect(wrapper.find('.status-chart').exists()).toBe(true)
    expect(wrapper.find('h3').text()).toBe('设备状态监控')
  })
})