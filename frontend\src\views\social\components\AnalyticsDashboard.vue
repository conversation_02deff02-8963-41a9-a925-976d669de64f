<template>
  <div class="analytics-dashboard">
    <h1 style="color: #4361ee; margin: 10px 0 20px">数据分析</h1>
    <div class="filter-bar">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="fetchAnalytics"
      />
      <el-select
        v-model="selectedAccounts"
        multiple
        placeholder="选择账号"
        style="width: 300px; margin-left: 10px"
        clearable
      >
        <el-option
          v-for="account in accounts"
          :key="account.id"
          :label="account.username"
          :value="account.id"
        />
      </el-select>
      <el-button
        type="primary"
        style="margin-left: 10px"
        @click="fetchAnalytics"
      >
        查询
      </el-button>
    </div>

    <div class="metrics-grid">
      <el-card shadow="hover" class="metric-card">
        <template #header>
          <div class="card-header">
            <span>总互动量</span>
          </div>
        </template>
        <el-statistic :value="totalInteractions" />
        <div class="metric-trend">
          <span :class="interactionTrend >= 0 ? 'up' : 'down'">
            {{ interactionTrend >= 0 ? '↑' : '↓' }} {{ Math.abs(interactionTrend) }}%
          </span>
          较上周
        </div>
      </el-card>

      <el-card shadow="hover" class="metric-card">
        <template #header>
          <div class="card-header">
            <span>新增粉丝</span>
          </div>
        </template>
        <el-statistic :value="newFollowers" />
        <div class="metric-trend">
          <span :class="followerTrend >= 0 ? 'up' : 'down'">
            {{ followerTrend >= 0 ? '↑' : '↓' }} {{ Math.abs(followerTrend) }}%
          </span>
          较上周
        </div>
      </el-card>

      <el-card shadow="hover" class="metric-card">
        <template #header>
          <div class="card-header">
            <span>内容发布</span>
          </div>
        </template>
        <el-statistic :value="postCount" />
        <div class="metric-trend">
          <span :class="postTrend >= 0 ? 'up' : 'down'">
            {{ postTrend >= 0 ? '↑' : '↓' }} {{ Math.abs(postTrend) }}%
          </span>
          较上周
        </div>
      </el-card>

      <el-card shadow="hover" class="metric-card">
        <template #header>
          <div class="card-header">
            <span>平均互动率</span>
          </div>
        </template>
        <el-statistic :value="engagementRate" :precision="2">
          <template #suffix>%</template>
        </el-statistic>
        <div class="metric-trend">
          <span :class="engagementTrend >= 0 ? 'up' : 'down'">
            {{ engagementTrend >= 0 ? '↑' : '↓' }} {{ Math.abs(engagementTrend) }}%
          </span>
          较上周
        </div>
      </el-card>
    </div>

    <div class="chart-container">
      <el-card shadow="hover" class="chart-card">
        <template #header>
          <div class="card-header">
            <span>互动趋势</span>
          </div>
        </template>
        <div class="chart-placeholder">
          <el-icon :size="50"><TrendCharts /></el-icon>
          <p>互动数据趋势图</p>
        </div>
      </el-card>

      <el-card shadow="hover" class="chart-card">
        <template #header>
          <div class="card-header">
            <span>内容类型分布</span>
          </div>
        </template>
        <div class="chart-placeholder">
          <el-icon :size="50"><PieChart /></el-icon>
          <p>内容类型分布图</p>
        </div>
      </el-card>
    </div>

    <div class="table-container">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>内容表现排行</span>
          </div>
        </template>
        <el-table :data="topPosts" style="width: 100%">
          <el-table-column prop="content" label="内容" width="300">
            <template #default="scope">
              <div class="post-content">
                {{ scope.row.content.substring(0, 50) }}{{ scope.row.content.length > 50 ? '...' : '' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="account" label="账号" />
          <el-table-column prop="views" label="浏览量" sortable />
          <el-table-column prop="likes" label="点赞数" sortable />
          <el-table-column prop="comments" label="评论数" sortable />
          <el-table-column prop="shares" label="分享数" sortable />
          <el-table-column prop="engagement" label="互动率" sortable>
            <template #default="scope">
              {{ scope.row.engagement }}%
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { TrendCharts, PieChart } from '@element-plus/icons-vue'
import { getAnalytics, getAccounts } from '@/api/social'
import { ElMessage } from 'element-plus'
import type { SocialAccount } from '@/types/social'

const props = defineProps<{
  appId: string
}>()

const dateRange = ref<[Date, Date]>([
  new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
  new Date()
])
const selectedAccounts = ref<string[]>([])
const accounts = ref<SocialAccount[]>([])
const analyticsData = ref<any[]>([])

// 模拟数据
const totalInteractions = ref(12543)
const newFollowers = ref(324)
const postCount = ref(28)
const engagementRate = ref(3.42)
const interactionTrend = ref(12.5)
const followerTrend = ref(8.2)
const postTrend = ref(-5.3)
const engagementTrend = ref(2.1)

const topPosts = ref([
  {
    content: '这是一条热门内容，获得了大量互动...',
    account: '账号1',
    views: 5423,
    likes: 1245,
    comments: 324,
    shares: 156,
    engagement: 5.2
  },
  {
    content: '产品推广内容，效果不错...',
    account: '账号2',
    views: 4321,
    likes: 987,
    comments: 210,
    shares: 98,
    engagement: 4.5
  }
])

// 获取分析数据
const fetchAnalytics = async () => {
  try {
    const [startDate, endDate] = dateRange.value
    const res = await getAnalytics(
      props.appId,
      selectedAccounts.value.join(','),
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    )
    analyticsData.value = res
    // TODO: 处理分析数据
  } catch (error) {
    ElMessage.error('获取分析数据失败')
    console.error(error)
  }
}

// 获取账号列表
const fetchAccounts = async () => {
  try {
    const res = await getAccounts(props.appId)
    accounts.value = res
  } catch (error) {
    console.error('获取账号列表失败', error)
  }
}

// 监听appId变化
watch(() => props.appId, (newVal) => {
  if (newVal) {
    fetchAnalytics()
    fetchAccounts()
  }
})

onMounted(() => {
  fetchAnalytics()
  fetchAccounts()
})
</script>

<style scoped>
.analytics-dashboard {
  padding: 20px;
}

.filter-bar {
  display: flex;
  margin-bottom: 20px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.metric-card {
  height: 120px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-trend {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
}

.up {
  color: #67c23a;
}

.down {
  color: #f56c6c;
}

.chart-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  height: 300px;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  color: #999;
}

.table-container {
  margin-top: 20px;
}

.post-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>