#!/usr/bin/env python3
"""
测试后端API的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://192.168.123.2:8000"

def test_accounts_api():
    """测试获取账号列表API"""

    # 测试参数
    platform_id = "681efeeecd836bd64b9c2a1e"  # YouTube平台的_id
    core_service_id = "core-123.2"

    # API端点
    url = f"{BASE_URL}/api/v1/social/accounts"

    # 请求参数
    params = {
        "platform_id": platform_id,
        "core_service_id": core_service_id
    }

    print(f"测试API: {url}")
    print(f"参数: {params}")

    # 先获取认证token
    auth_url = f"{BASE_URL}/auth/token"
    auth_data = {
        "username": "test",
        "password": "test123"
    }

    try:
        # 获取token
        auth_response = requests.post(auth_url, data=auth_data, timeout=10)
        if auth_response.status_code == 200:
            token_data = auth_response.json()
            token = token_data.get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
            print(f"获取到认证token: {token[:20]}...")
        else:
            print(f"认证失败: {auth_response.text}")
            return

        # 发送请求
        response = requests.get(url, params=params, headers=headers, timeout=10)

        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

            if 'data' in data and isinstance(data['data'], list):
                print(f"找到 {len(data['data'])} 个账号")
                for i, account in enumerate(data['data'][:3]):  # 只显示前3个
                    print(f"账号 {i+1}: {account.get('username', 'N/A')} - {account.get('display_name', 'N/A')}")
            else:
                print("响应格式不正确或没有账号数据")
        else:
            print(f"请求失败: {response.text}")

    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        print(f"原始响应: {response.text}")

if __name__ == "__main__":
    test_accounts_api()
