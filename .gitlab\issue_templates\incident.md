---
description: Report a system incident or outage
---

# Incident Report

## Summary

[Briefly describe the incident, e.g., service outage, performance degradation]

## Timeline

- **Start Time**: [YYYY-MM-DD HH:MM UTC]
- **Detected Time**: [YYYY-MM-DD HH:MM UTC]
- **Resolved Time**: [YYYY-MM-DD HH:MM UTC, or "Ongoing"]

## Impact

[Describe affected systems, users, or services]

- [Impact 1]
- [Impact 2]

## Root Cause

[Initial analysis of what caused the incident, or "Under investigation"]

## Actions Taken

[List steps taken to mitigate or resolve the issue]

- [Action 1]
- [Action 2]

## Next Steps

[List planned actions, e.g., post-mortem, preventive measures]

- [ ] [Step 1]
- [ ] [Step 2]

## Priority

- [ ] Critical
- [ ] High
- [ ] Medium

## Attachments

- Logs: [Paste or attach]
- Screenshots: [Attach or link]
- Monitoring Data: [Links to dashboards]

## Assignee

[@oncall or leave blank for incident manager]
