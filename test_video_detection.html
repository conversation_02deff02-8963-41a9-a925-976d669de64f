<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频文件检测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-item {
            display: flex;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            align-items: center;
            border: 1px solid #eee;
        }
        .test-input {
            flex: 2;
            margin-right: 15px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-result {
            flex: 1;
            margin-right: 15px;
            text-align: center;
        }
        .test-duration {
            flex: 1;
            text-align: center;
        }
        .pass {
            background-color: #f0f9ff;
            border-color: #67c23a;
        }
        .fail {
            background-color: #fef0f0;
            border-color: #f56c6c;
        }
        .video-very-short { background-color: #f0f9ff; }
        .video-short { background-color: #fdf6ec; }
        .video-medium { background-color: #ecf5ff; }
        .video-long { background-color: #fef0f0; }
        .video-very-long { background-color: #fef0f0; border-left: 4px solid #f56c6c; }
        
        .duration-text-very-short { color: #67c23a; font-weight: 500; }
        .duration-text-short { color: #e6a23c; font-weight: 500; }
        .duration-text-medium { color: #409eff; font-weight: 500; }
        .duration-text-long { color: #f78989; font-weight: 600; }
        .duration-text-very-long { color: #f56c6c; font-weight: 600; }
        .duration-text-unknown { color: #909399; font-weight: 400; }
    </style>
</head>
<body>
    <div class="container">
        <h1>视频文件检测和时长解析测试</h1>
        
        <div class="test-section">
            <h3>问题分析</h3>
            <p>根据您提供的信息：</p>
            <ul>
                <li>文件名：<code>一百公斤黄金换一块废铁？真相震惊所有人！#西游记 #无支祁 #架海紫金梁.mp4</code></li>
                <li>媒体信息显示：<code>⏱️ 1:28</code></li>
                <li>视频时长列显示：<code>未知时长</code></li>
            </ul>
            <p>可能的问题：</p>
            <ul>
                <li>视频文件检测失败（扩展名获取问题）</li>
                <li>时长数据字段位置不正确</li>
                <li>数据类型转换问题</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>视频文件检测测试</h3>
            <div style="display: flex; font-weight: bold; padding: 10px; background: #f8f9fa;">
                <div style="flex: 2;">文件对象</div>
                <div style="flex: 1;">检测结果</div>
                <div style="flex: 1;">时长解析</div>
            </div>
            <div id="videoDetectionTests"></div>
        </div>
        
        <div class="test-section">
            <h3>实际文件对象模拟</h3>
            <p>基于您提供的信息，模拟实际的文件对象结构：</p>
            <div id="realFileTest"></div>
        </div>
    </div>

    <script>
        // 改进的视频文件检测函数
        function isVideoFile(file) {
            if (!file || file.is_directory) return false;
            
            const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v'];
            
            let extension = '';
            
            if (file.extension) {
                extension = file.extension.toLowerCase();
                if (!extension.startsWith('.')) {
                    extension = '.' + extension;
                }
            } else if (file.name) {
                const lastDotIndex = file.name.lastIndexOf('.');
                if (lastDotIndex > 0) {
                    extension = file.name.substring(lastDotIndex).toLowerCase();
                }
            }
            
            return videoExtensions.includes(extension);
        }

        // 时长解析函数
        function parseTimeStringToSeconds(timeString) {
            if (!timeString || typeof timeString !== 'string') return 0;
            
            const parts = timeString.trim().split(':').map(part => parseInt(part, 10));
            
            if (parts.length === 2) {
                const [minutes, seconds] = parts;
                return (minutes || 0) * 60 + (seconds || 0);
            } else if (parts.length === 3) {
                const [hours, minutes, seconds] = parts;
                return (hours || 0) * 3600 + (minutes || 0) * 60 + (seconds || 0);
            }
            
            return 0;
        }

        // 获取视频时长
        function getVideoDurationFromFile(file) {
            if (!file || !isVideoFile(file)) return 0;
            
            let duration = 0;
            
            if (file.media_info?.duration) {
                duration = file.media_info.duration;
            } else if (file.duration) {
                duration = file.duration;
            } else if (file.file_info?.duration) {
                duration = file.file_info.duration;
            } else if (file.metadata?.duration) {
                duration = file.metadata.duration;
            } else if (file.video_info?.duration) {
                duration = file.video_info.duration;
            }
            
            if (typeof duration === 'string') {
                duration = parseTimeStringToSeconds(duration);
            }
            
            return duration || 0;
        }

        // 视频时长分类
        function calculateVideoDurationCategory(duration) {
            if (!duration || duration <= 0) return { category: 'unknown', message: '未知时长', class: '', textClass: 'duration-text-unknown' };
            if (duration < 35) return { category: 'very_short', message: '极短视频', class: 'video-very-short', textClass: 'duration-text-very-short' };
            if (duration <= 60) return { category: 'short', message: '短视频', class: 'video-short', textClass: 'duration-text-short' };
            if (duration <= 180) return { category: 'medium', message: '中等视频', class: 'video-medium', textClass: 'duration-text-medium' };
            if (duration <= 480) return { category: 'long', message: '长视频', class: 'video-long', textClass: 'duration-text-long' };
            return { category: 'very_long', message: '超长视频', class: 'video-very-long', textClass: 'duration-text-very-long' };
        }

        // 运行视频检测测试
        function runVideoDetectionTests() {
            const testFiles = [
                {
                    name: '测试1: 有extension字段',
                    file: { 
                        name: '一百公斤黄金换一块废铁？真相震惊所有人！#西游记 #无支祁 #架海紫金梁.mp4',
                        extension: 'mp4',
                        is_directory: false,
                        media_info: { duration: 88 }
                    }
                },
                {
                    name: '测试2: 有extension字段带点',
                    file: { 
                        name: '一百公斤黄金换一块废铁？真相震惊所有人！#西游记 #无支祁 #架海紫金梁.mp4',
                        extension: '.mp4',
                        is_directory: false,
                        media_info: { duration: 88 }
                    }
                },
                {
                    name: '测试3: 无extension字段，从文件名提取',
                    file: { 
                        name: '一百公斤黄金换一块废铁？真相震惊所有人！#西游记 #无支祁 #架海紫金梁.mp4',
                        is_directory: false,
                        media_info: { duration: 88 }
                    }
                },
                {
                    name: '测试4: 时长为字符串',
                    file: { 
                        name: 'test.mp4',
                        extension: 'mp4',
                        is_directory: false,
                        media_info: { duration: '1:28' }
                    }
                },
                {
                    name: '测试5: 时长在不同字段',
                    file: { 
                        name: 'test.mp4',
                        extension: 'mp4',
                        is_directory: false,
                        duration: 88
                    }
                }
            ];
            
            const testsDiv = document.getElementById('videoDetectionTests');
            let html = '';
            
            testFiles.forEach(test => {
                const isVideo = isVideoFile(test.file);
                const duration = getVideoDurationFromFile(test.file);
                const category = calculateVideoDurationCategory(duration);
                
                const resultClass = isVideo && duration > 0 ? 'pass' : 'fail';
                
                html += `
                    <div class="test-item ${resultClass} ${category.class}">
                        <div class="test-input">
                            <strong>${test.name}</strong><br>
                            <small>${JSON.stringify(test.file, null, 2)}</small>
                        </div>
                        <div class="test-result">
                            视频检测: ${isVideo ? '✅' : '❌'}<br>
                            时长: ${duration}秒
                        </div>
                        <div class="test-duration ${category.textClass}">
                            ${category.message}
                        </div>
                    </div>
                `;
            });
            
            testsDiv.innerHTML = html;
        }

        // 测试实际文件对象
        function testRealFile() {
            // 基于您提供的信息构建的文件对象
            const realFile = {
                name: '一百公斤黄金换一块废铁？真相震惊所有人！#西游记 #无支祁 #架海紫金梁.mp4',
                is_directory: false,
                media_info: {
                    duration: 88  // 1:28 = 88秒
                },
                // 可能的其他字段
                extension: 'mp4',
                size: 229726617, // 约218.89MB
                content_type: 'video'
            };
            
            const testsDiv = document.getElementById('realFileTest');
            
            const isVideo = isVideoFile(realFile);
            const duration = getVideoDurationFromFile(realFile);
            const category = calculateVideoDurationCategory(duration);
            
            const resultClass = isVideo && duration > 0 ? 'pass' : 'fail';
            
            testsDiv.innerHTML = `
                <div class="test-item ${resultClass} ${category.class}">
                    <div class="test-input">
                        <strong>实际文件对象模拟</strong><br>
                        <small>${JSON.stringify(realFile, null, 2)}</small>
                    </div>
                    <div class="test-result">
                        视频检测: ${isVideo ? '✅ 成功' : '❌ 失败'}<br>
                        时长解析: ${duration}秒<br>
                        预期: 88秒 (1:28)
                    </div>
                    <div class="test-duration ${category.textClass}">
                        ${category.message}<br>
                        <small>应该显示: 短视频</small>
                    </div>
                </div>
            `;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            runVideoDetectionTests();
            testRealFile();
        });
    </script>
</body>
</html>
