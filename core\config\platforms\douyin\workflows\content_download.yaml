# 抖音内容下载工作流配置
workflow:
  name: "抖音内容下载"
  description: "完整的抖音账号内容下载流程"
  version: "1.0"
  platform: "douyin"

  # 工作流步骤
  steps:
    - name: "初始化下载器"
      id: "init_downloader"
      description: "初始化抖音下载器和相关配置"
      action: "init"
      required: true
      config:
        timeout: 30
        retry_count: 3

    - name: "解析账号URL"
      id: "parse_account_url"
      description: "解析抖音账号URL，提取用户ID"
      action: "parse_url"
      required: true
      config:
        url_patterns:
          - "https://www.douyin.com/user/{user_id}"
          - "https://v.douyin.com/{short_id}"
        timeout: 10

    - name: "获取账号信息"
      id: "get_account_info"
      description: "获取抖音账号的基本信息"
      action: "fetch_account_info"
      required: true
      config:
        api_endpoint: "https://www.douyin.com/aweme/v1/web/aweme/profile/"
        timeout: 30
        retry_count: 3
        headers:
          User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
          Referer: "https://www.douyin.com/"

    - name: "获取视频列表"
      id: "get_video_list"
      description: "获取账号的视频列表"
      action: "fetch_video_list"
      required: true
      config:
        api_endpoint: "https://www.douyin.com/aweme/v1/web/aweme/post/"
        page_size: 20
        max_pages: 10
        timeout: 30
        retry_count: 3
        rate_limit:
          requests_per_second: 1
          delay_between_requests: 1

    - name: "应用过滤条件"
      id: "apply_filters"
      description: "根据配置的过滤条件筛选视频"
      action: "filter_videos"
      required: false
      config:
        filters:
          min_views: "{min_views}"
          min_likes: "{min_likes}"
          keywords: "{keywords}"
          date_range: "{date_range}"
          content_types: "{content_types}"

    - name: "创建下载目录"
      id: "create_directories"
      description: "创建下载所需的目录结构"
      action: "create_dirs"
      required: true
      config:
        directories:
          - "videos"
          - "metadata"
          - "covers"
        permissions: "755"

    - name: "下载视频文件"
      id: "download_videos"
      description: "批量下载视频文件"
      action: "download_batch"
      required: true
      config:
        concurrent_downloads: 3
        timeout_per_video: 300
        retry_count: 3
        chunk_size: 8192
        verify_download: true
        naming_rule: "{naming_rule}"
        quality: "{video_quality}"

    - name: "下载封面图片"
      id: "download_covers"
      description: "下载视频封面图片"
      action: "download_covers"
      required: false
      config:
        concurrent_downloads: 5
        timeout_per_image: 60
        retry_count: 2
        formats: ["jpg", "png"]

    - name: "保存元数据"
      id: "save_metadata"
      description: "保存视频和账号的元数据信息"
      action: "save_metadata"
      required: true
      config:
        include_account_info: true
        include_video_stats: true
        include_download_info: true
        format: "json"
        encoding: "utf-8"

    - name: "生成下载报告"
      id: "generate_report"
      description: "生成下载任务的详细报告"
      action: "generate_report"
      required: true
      config:
        include_summary: true
        include_logs: true
        include_errors: true
        format: "json"
        max_log_entries: 100

    - name: "清理临时文件"
      id: "cleanup"
      description: "清理下载过程中产生的临时文件"
      action: "cleanup"
      required: false
      config:
        remove_temp_files: true
        remove_cache: false
        keep_logs: true

# 配置参数
config:
  # 网络配置
  network:
    timeout: 30
    retry_count: 3
    rate_limit:
      enabled: true
      requests_per_second: 2
      burst_size: 5
    
  # 下载配置
  download:
    concurrent_limit: 3
    chunk_size: 8192
    verify_integrity: true
    resume_support: true
    
  # 文件配置
  files:
    naming_rules:
      timestamp: "{timestamp}_{video_id}.mp4"
      title: "{title}_{video_id}.mp4"
      id: "{video_id}.mp4"
      custom: "{custom_pattern}.mp4"
    
    quality_options:
      high: "1080p"
      medium: "720p"
      low: "480p"
    
    metadata_format: "json"
    
  # 过滤配置
  filters:
    min_views: 0
    min_likes: 0
    min_duration: 0
    max_duration: 0
    keywords: []
    exclude_keywords: []
    date_range:
      start_date: null
      end_date: null
    
  # 错误处理
  error_handling:
    max_retries: 3
    retry_delay: 5
    continue_on_error: true
    log_errors: true
    
  # 日志配置
  logging:
    level: "INFO"
    max_entries: 1000
    include_timestamps: true
    include_progress: true
