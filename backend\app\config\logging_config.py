"""
Backend日志配置
支持按日期和类型分文件存储
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path


class DateFolderFileHandler(logging.handlers.TimedRotatingFileHandler):
    """按日期文件夹组织的文件处理器"""

    def __init__(self, base_log_dir: str, log_type: str, when='midnight', interval=1, backupCount=30):
        self.base_log_dir = Path(base_log_dir)
        self.log_type = log_type

        # 确保基础日志目录存在
        self.base_log_dir.mkdir(parents=True, exist_ok=True)

        # 生成当前日期的文件夹和文件名
        current_date = datetime.now().strftime('%Y-%m-%d')
        self.date_dir = self.base_log_dir / current_date
        self.date_dir.mkdir(parents=True, exist_ok=True)

        filename = self.date_dir / f"{log_type}.log"

        super().__init__(
            filename=str(filename),
            when=when,
            interval=interval,
            backupCount=backupCount,
            encoding='utf-8'
        )

    def rotation_filename(self, default_name):
        """自定义轮转文件名"""
        # 获取新的日期
        new_date = datetime.now().strftime('%Y-%m-%d')
        new_date_dir = self.base_log_dir / new_date
        new_date_dir.mkdir(parents=True, exist_ok=True)

        return str(new_date_dir / f"{self.log_type}.log")


def setup_backend_logging(log_dir: str = "logs"):
    """
    设置Backend日志配置
    
    Args:
        log_dir: 日志目录路径
    """
    
    # 创建日志目录
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置根日志级别
    root_logger.setLevel(logging.DEBUG)
    
    # 定义日志格式
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 1. 控制台处理器 - 只显示INFO及以上级别
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    root_logger.addHandler(console_handler)
    
    # 2. 全量日志文件 - 记录所有日志
    all_handler = DateFolderFileHandler(log_dir, "all", when='midnight', backupCount=30)
    all_handler.setLevel(logging.DEBUG)
    all_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(all_handler)

    # 3. 错误日志文件 - 只记录ERROR及以上级别
    error_handler = DateFolderFileHandler(log_dir, "error", when='midnight', backupCount=90)
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)

    # 4. API日志文件 - 记录API相关日志
    api_handler = DateFolderFileHandler(log_dir, "api", when='midnight', backupCount=30)
    api_handler.setLevel(logging.INFO)
    api_handler.setFormatter(detailed_formatter)
    
    # 为API相关的logger添加专门的处理器
    api_loggers = [
        'app.api',
        'app.api.auth',
        'app.api.device', 
        'app.api.social',
        'app.api.task',
        'app.api.v1',
        'uvicorn.access'
    ]
    
    for logger_name in api_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(api_handler)
        logger.propagate = True  # 仍然传播到根logger
    
    # 5. 数据库日志文件 - 记录数据库相关日志
    db_handler = DateFolderFileHandler(log_dir, "database", when='midnight', backupCount=30)
    db_handler.setLevel(logging.INFO)
    db_handler.setFormatter(detailed_formatter)

    db_loggers = [
        'app.core.schemas',
        'app.config.database',
        'pymongo',
        'motor'
    ]

    for logger_name in db_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(db_handler)
        logger.propagate = True

    # 6. Core服务日志文件 - 记录与Core服务通信的日志
    core_handler = DateFolderFileHandler(log_dir, "core", when='midnight', backupCount=30)
    core_handler.setLevel(logging.DEBUG)
    core_handler.setFormatter(detailed_formatter)

    core_loggers = [
        'app.core.client',
        'app.services.core_service'
    ]

    for logger_name in core_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(core_handler)
        logger.propagate = True

    # 7. 任务日志文件 - 记录任务相关日志
    task_handler = DateFolderFileHandler(log_dir, "task", when='midnight', backupCount=30)
    task_handler.setLevel(logging.DEBUG)
    task_handler.setFormatter(detailed_formatter)
    
    task_loggers = [
        'app.api.task',
        'app.api.v1.benchmark_download',
        'app.services.redis_sync_service'
    ]
    
    for logger_name in task_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(task_handler)
        logger.propagate = True
    
    # 设置第三方库的日志级别
    logging.getLogger('pymongo').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('grpc').setLevel(logging.WARNING)
    
    # 记录日志配置完成
    logger = logging.getLogger(__name__)
    logger.info(f"Backend日志配置完成，日志目录: {log_path.absolute()}")
    logger.info("日志文件组织结构:")
    logger.info("  logs/")
    logger.info("  ├── YYYY-MM-DD/")
    logger.info("  │   ├── all.log: 全量日志")
    logger.info("  │   ├── error.log: 错误日志")
    logger.info("  │   ├── api.log: API请求日志")
    logger.info("  │   ├── database.log: 数据库操作日志")
    logger.info("  │   ├── core.log: Core服务通信日志")
    logger.info("  │   └── task.log: 任务执行日志")
    logger.info("  └── YYYY-MM-DD/ (其他日期的日志)")


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的logger
    
    Args:
        name: logger名称
        
    Returns:
        logging.Logger: 配置好的logger实例
    """
    return logging.getLogger(name)


# 便捷的logger获取函数
def get_api_logger() -> logging.Logger:
    """获取API日志记录器"""
    return logging.getLogger('app.api')


def get_db_logger() -> logging.Logger:
    """获取数据库日志记录器"""
    return logging.getLogger('app.database')


def get_core_logger() -> logging.Logger:
    """获取Core服务日志记录器"""
    return logging.getLogger('app.core')


def get_task_logger() -> logging.Logger:
    """获取任务日志记录器"""
    return logging.getLogger('app.task')
