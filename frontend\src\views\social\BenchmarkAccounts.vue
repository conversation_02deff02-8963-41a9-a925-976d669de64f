<template>
  <div class="benchmark-accounts">
    <!-- 页面头部 -->
    <div class="management-header">
      <h1>🎯 对标账号管理</h1>
      <p class="header-description">管理和分析竞品账号，跟踪行业趋势，为内容策略提供数据支持</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total_count }}</div>
                <div class="stat-label">总对标账号</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card original">
            <div class="stat-content">
              <div class="stat-icon">🎯</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.by_type?.original || 0 }}</div>
                <div class="stat-label">原创账号</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card recreate">
            <div class="stat-content">
              <div class="stat-icon">🔄</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.by_type?.recreate || 0 }}</div>
                <div class="stat-label">二创账号</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card repost">
            <div class="stat-content">
              <div class="stat-icon">📋</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.by_type?.repost || 0 }}</div>
                <div class="stat-label">搬运账号</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 简化的工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索对标账号..."
          style="width: 250px"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-input
          v-model="ourAccountSearchQuery"
          placeholder="搜索关联账号..."
          style="width: 200px; margin-left: 10px"
          @input="handleOurAccountSearch"
          clearable
        >
          <template #prefix>
            <el-icon><User /></el-icon>
          </template>
        </el-input>

        <el-select
          v-model="filterPlatform"
          placeholder="平台筛选"
          style="width: 120px; margin-left: 10px"
          @change="loadBenchmarkAccounts"
          clearable
        >
          <el-option label="YouTube" value="youtube" />
          <el-option label="TikTok" value="tiktok" />
          <el-option label="Instagram" value="instagram" />
          <el-option label="抖音" value="douyin" />
          <el-option label="微博" value="weibo" />
          <el-option label="小红书" value="xiaohongshu" />
          <el-option label="快手" value="kuaishou" />
          <el-option label="B站" value="bilibili" />
        </el-select>

        <el-select
          v-model="filterType"
          placeholder="对标类型"
          style="width: 120px; margin-left: 10px"
          @change="loadBenchmarkAccounts"
          clearable
        >
          <el-option label="原创" value="original" />
          <el-option label="二创" value="recreate" />
          <el-option label="搬运" value="repost" />
        </el-select>
      </div>

      <div class="toolbar-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加对标账号
        </el-button>

        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 简化的对标账号列表 -->
    <div class="account-list">
      <el-table
        :data="benchmarkAccounts"
        v-loading="loading"
        stripe
        style="width: 100%"
        :empty-text="loading ? '加载中...' : '暂无对标账号'"
      >
        <el-table-column label="对标账号信息" min-width="200">
          <template #default="{ row }">
            <div class="account-card">
              <div class="account-header">
                <div class="account-avatar-placeholder">
                  {{ row.account_name.charAt(0).toUpperCase() }}
                </div>
                <div class="account-details">
                  <div class="account-name">
                    <a :href="row.account_url" target="_blank" class="account-link">
                      {{ row.account_name }}
                    </a>
                  </div>
                  <div class="account-meta">
                    <el-tag size="small" type="info">{{ row.platform }}</el-tag>
                    <el-tag
                      :type="getBenchmarkTypeColor(row.benchmark_type)"
                      size="small"
                      style="margin-left: 4px"
                    >
                      {{ getBenchmarkTypeText(row.benchmark_type) }}
                    </el-tag>
                    <span class="priority-stars">
                      {{ '★'.repeat(row.priority) }}{{ '☆'.repeat(5 - row.priority) }}
                    </span>
                  </div>
                  <div v-if="row.description" class="account-description">
                    {{ row.description }}
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="关联账号" min-width="150">
          <template #default="{ row }">
            <div class="our-account-info">
              <div v-if="row.our_account_info && row.our_account_info.status !== 'not_found'" class="account-item">
                <div class="account-name">
                  {{ formatOurAccountName(row.our_account_info) }}
                </div>
                <el-tag size="small">{{ getPlatformDisplayName(row.our_account_info.platform_id) }}</el-tag>
              </div>
              <div v-else class="no-account">
                <el-tag type="warning" size="small">未关联</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="数据概览" min-width="120">
          <template #default="{ row }">
            <div class="account-data">
              <div v-if="hasAccountData(row)" class="data-summary">
                <div v-if="row.account_data.followers" class="data-item">
                  👥 {{ formatNumber(row.account_data.followers) }}
                </div>
                <div v-if="row.account_data.posts_count" class="data-item">
                  📝 {{ formatNumber(row.account_data.posts_count) }}
                </div>
                <div v-if="row.account_data.engagement_rate" class="data-item">
                  📈 {{ (row.account_data.engagement_rate * 100).toFixed(1) }}%
                </div>
              </div>
              <div v-else class="no-data">
                <el-button size="small" type="primary" link @click="updateData(row)">
                  获取数据
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="采集任务" min-width="150">
          <template #default="{ row }">
            <div class="collect-task-info">
              <div v-if="row.collect_tasks && row.collect_tasks.length > 0" class="task-summary">
                <div class="task-item">
                  📋 {{ row.collect_tasks.length }} 个任务
                </div>
                <div class="task-item">
                  📅 {{ getLastTaskTime(row.collect_tasks) }}
                </div>
                <div class="task-status">
                  <el-tag
                    :type="getTaskStatusColor(getLastTaskStatus(row.collect_tasks))"
                    size="small"
                  >
                    {{ getTaskStatusText(getLastTaskStatus(row.collect_tasks)) }}
                  </el-tag>
                </div>
              </div>
              <div v-else class="no-task">
                <el-button
                  size="small"
                  type="success"
                  @click="createCollectTask(row)"
                  :loading="row.creating_task"
                >
                  创建采集任务
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="视频采集" min-width="150">
          <template #default="{ row }">
            <div class="video-collect-info">
              <div v-if="row.video_stats" class="collect-stats">
                <div class="stat-item">
                  🎬 {{ row.video_stats.total || 0 }} 个视频
                </div>
                <div class="stat-item">
                  ⬇️ {{ row.video_stats.downloaded || 0 }} 已下载
                </div>
                <div class="stat-item">
                  📅 {{ formatDate(row.video_stats.last_collect_time) }}
                </div>
              </div>
              <div v-else class="no-collect">
                <el-tag type="info" size="small">未采集</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="标签" min-width="100">
          <template #default="{ row }">
            <div class="tags">
              <el-tag
                v-for="tag in row.tags.slice(0, 2)"
                :key="tag"
                size="small"
                style="margin-bottom: 2px"
              >
                {{ tag }}
              </el-tag>
              <div v-if="row.tags.length > 2" class="more-tags">
                +{{ row.tags.length - 2 }}个
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-dropdown @command="(command) => handleActionCommand(command, row)">
                <el-button size="small" type="primary">
                  操作 <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="view">
                      <el-icon><View /></el-icon> 查看详情
                    </el-dropdown-item>
                    <el-dropdown-item command="edit">
                      <el-icon><Edit /></el-icon> 编辑账号
                    </el-dropdown-item>
                    <el-dropdown-item command="update">
                      <el-icon><Refresh /></el-icon> 更新数据
                    </el-dropdown-item>
                    <el-dropdown-item divided command="create-task">
                      <el-icon><Plus /></el-icon> 创建采集任务
                    </el-dropdown-item>
                    <el-dropdown-item command="view-tasks" v-if="row.collect_tasks && row.collect_tasks.length > 0">
                      <el-icon><List /></el-icon> 查看任务 ({{ row.collect_tasks.length }})
                    </el-dropdown-item>
                    <el-dropdown-item command="view-content">
                      <el-icon><Collection /></el-icon> 查看采集内容
                    </el-dropdown-item>
                    <el-dropdown-item divided command="delete" style="color: #f56c6c;">
                      <el-icon><Delete /></el-icon> 删除账号
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="totalCount"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="loadBenchmarkAccounts"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 创建对标账号对话框 -->
    <CreateBenchmarkDialog 
      v-model="showCreateDialog"
      :our-accounts="ourAccounts"
      @success="handleCreateSuccess"
    />

    <!-- 编辑对标账号对话框 -->
    <EditBenchmarkDialog 
      v-model="showEditDialog"
      :account="selectedAccount"
      @success="handleEditSuccess"
    />

    <!-- 更新数据对话框 -->
    <UpdateDataDialog 
      v-model="showUpdateDataDialog"
      :account="selectedAccount"
      @success="handleUpdateDataSuccess"
    />

    <!-- 账号详情对话框 -->
    <AccountDetailDialog
      v-model="showDetailDialog"
      :account="selectedAccount"
    />

    <!-- 采集任务配置对话框 -->
    <VideoCollectDialog
      v-model="showVideoCollectDialog"
      :account="selectedAccount"
      @success="handleCollectTaskSuccess"
    />

    <!-- 视频列表对话框 -->
    <VideoListDialog
      v-model="showVideoListDialog"
      :account="selectedAccount"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Plus, Refresh, ArrowDown, View, Edit, Delete,
  List, Collection, User
} from '@element-plus/icons-vue'

// API导入
import {
  getBenchmarkAccounts,
  getBenchmarkStats,
  updateBenchmarkAccount,
  deleteBenchmarkAccount,
  type BenchmarkAccount,
  type BenchmarkAccountStats
} from '@/api/content'

import { getAccounts } from '@/api/social'
import { useAuthStore } from '@/stores/auth'

// 组件导入
import CreateBenchmarkDialog from '../doc/components/CreateBenchmarkDialog.vue'
import EditBenchmarkDialog from '../doc/components/EditBenchmarkDialog.vue'
import UpdateDataDialog from '../doc/components/UpdateDataDialog.vue'
import AccountDetailDialog from '../doc/components/AccountDetailDialog.vue'
import VideoCollectDialog from '../doc/components/VideoCollectDialog.vue'
import VideoListDialog from '../doc/components/VideoListDialog.vue'

// 获取认证状态
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const benchmarkAccounts = ref<BenchmarkAccount[]>([])
const stats = ref<BenchmarkAccountStats>({
  total_count: 0,
  by_platform: {},
  by_type: {},
  by_status: {},
  top_performers: []
})
const ourAccounts = ref<any[]>([])

// 筛选和搜索
const selectedOurAccount = ref('')
const filterPlatform = ref('')
const filterType = ref('')
const searchQuery = ref('')
const ourAccountSearchQuery = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showUpdateDataDialog = ref(false)
const showDetailDialog = ref(false)
const showVideoCollectDialog = ref(false)
const showVideoListDialog = ref(false)
const selectedAccount = ref<BenchmarkAccount | null>(null)

// 方法
const loadOurAccounts = async () => {
  try {
    console.log('开始加载我们的账号列表')
    console.log('当前用户认证状态:', authStore.isAuthenticated)
    console.log('当前用户信息:', authStore.user)

    const response = await getAccounts()
    console.log('获取账号API响应:', response)

    // 处理API响应数据结构
    // request.ts会将响应包装成 {success: true, data: AccountsResponse}
    // AccountsResponse格式: {data: SocialAccount[], total: number, page: number, page_size: number}
    let accountsData = []

    if (response && response.success && response.data && response.data.data) {
      // 正确的数据路径：response.data.data
      accountsData = response.data.data
      console.log('从response.data.data提取账号数据:', accountsData)
    } else if (response && response.data && Array.isArray(response.data)) {
      // 备选路径：response.data直接是数组
      accountsData = response.data
      console.log('从response.data提取账号数据:', accountsData)
    } else {
      console.warn('未知的响应格式:', response)
      accountsData = []
    }

    ourAccounts.value = accountsData
    console.log('最终的账号列表:', ourAccounts.value)

    // 如果仍然没有账号数据，添加测试数据以便查看格式效果
    if (ourAccounts.value.length === 0) {
      console.warn('没有找到任何账号数据，添加测试数据')
      ourAccounts.value = [
        {
          id: 'test_youtube_1',
          username: 'my_youtube_channel',
          display_name: '我的YouTube频道',
          platform_id: 'youtube',
          core_service_id: 'core1',
          status: 'active'
        },
        {
          id: 'test_tiktok_1',
          username: 'my_tiktok_account',
          display_name: '我的TikTok账号',
          platform_id: 'tiktok',
          core_service_id: 'core1',
          status: 'active'
        },
        {
          id: 'test_weibo_1',
          username: 'my_weibo_account',
          display_name: '我的微博账号',
          platform_id: 'weibo',
          core_service_id: 'core1',
          status: 'active'
        }
      ]
      ElMessage.info('已添加测试账号数据，可以查看格式效果')
    } else {
      console.log(`成功加载 ${ourAccounts.value.length} 个账号`)
    }
  } catch (error) {
    console.error('加载账号列表失败:', error)
    console.error('错误详情:', error.response || error)
    ElMessage.error(`加载账号列表失败: ${error.message || error}`)
    ourAccounts.value = []
  }
}

const loadBenchmarkAccounts = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      our_account_id: selectedOurAccount.value || undefined,
      platform: filterPlatform.value || undefined,
      benchmark_type: filterType.value || undefined,
      search: searchQuery.value || undefined,
      our_account_search: ourAccountSearchQuery.value || undefined
    }

    console.log('加载对标账号，参数:', params)
    const response = await getBenchmarkAccounts(params)
    benchmarkAccounts.value = response.items
    totalCount.value = response.total
  } catch (error) {
    console.error('加载对标账号失败:', error)
    ElMessage.error('加载对标账号失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await getBenchmarkStats(selectedOurAccount.value || undefined)
    stats.value = response
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const refreshData = async () => {
  await Promise.all([
    loadBenchmarkAccounts(),
    loadStats()
  ])
  ElMessage.success('数据刷新成功')
}

// 工具方法
const getPlatformDisplayName = (platformId: string) => {
  // 根据数据库中的平台ObjectId映射到显示名称
  const platformIdMap = {
    '681efeeecd836bd64b9c2a1e': 'YouTube', // 油管
    '681efeeecd836bd64b9c2a20': 'TikTok',  // TT
    '681efeeecd836bd64b9c2a22': '抖音',     // 抖音
    '6822ecaa62fd956eb6d2c071': 'Facebook', // 脸书
    '6822ebfc05340d5a3d867138': 'AWS'       // AWS
  }

  // 如果是ObjectId格式，使用映射表
  if (platformIdMap[platformId]) {
    return platformIdMap[platformId]
  }

  // 如果是传统的平台ID格式，使用原来的映射
  const platformMap = {
    youtube: 'YouTube',
    tiktok: 'TikTok',
    instagram: 'Instagram',
    weibo: '微博',
    xiaohongshu: '小红书',
    douyin: '抖音',
    kuaishou: '快手',
    bilibili: 'B站'
  }

  return platformMap[platformId as keyof typeof platformMap] || platformId
}

const formatAccountLabel = (account: any) => {
  // 获取平台名称
  const platform = getPlatformDisplayName(account.platform_id || 'unknown')

  // 获取账号名称，优先使用display_name，然后是username的前缀部分
  let name = account.display_name
  if (!name && account.username) {
    // 如果username是邮箱格式，取@前面的部分
    name = account.username.includes('@') ? account.username.split('@')[0] : account.username
  }
  if (!name) {
    name = '未命名账号'
  }

  const result = `${platform}-${name}`
  console.log('BenchmarkAccounts格式化结果:', {
    platform_id: account.platform_id,
    platform: platform,
    display_name: account.display_name,
    username: account.username,
    final_result: result
  })
  return result
}

const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    active: '活跃',
    inactive: '非活跃',
    monitoring: '监控中'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getStatusColor = (status: string) => {
  const colorMap = {
    active: 'success',
    inactive: 'danger',
    monitoring: 'warning'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleDateString('zh-CN')
}

const hasAccountData = (account: BenchmarkAccount) => {
  const data = account.account_data
  return data.followers || data.posts_count || data.engagement_rate
}

const formatOurAccountName = (accountInfo: any) => {
  if (!accountInfo) return '未知账号'

  // 获取账号名称，优先使用display_name，然后是username的前缀部分
  let name = accountInfo.display_name
  if (!name && accountInfo.username) {
    // 如果username是邮箱格式，取@前面的部分
    name = accountInfo.username.includes('@') ? accountInfo.username.split('@')[0] : accountInfo.username
  }
  if (!name) {
    name = '未命名账号'
  }

  return name
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 事件处理
const handleSearch = () => {
  setTimeout(() => {
    currentPage.value = 1
    loadBenchmarkAccounts()
  }, 500)
}

const handleOurAccountSearch = () => {
  setTimeout(() => {
    currentPage.value = 1
    loadBenchmarkAccounts()
  }, 500)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadBenchmarkAccounts()
}

const updatePriority = async (account: BenchmarkAccount) => {
  try {
    await updateBenchmarkAccount(account._id!, { priority: account.priority })
    ElMessage.success('优先级更新成功')
  } catch (error) {
    console.error('更新优先级失败:', error)
    ElMessage.error('更新优先级失败')
  }
}

const viewAccount = (account: BenchmarkAccount) => {
  selectedAccount.value = account
  showDetailDialog.value = true
}

const editAccount = (account: BenchmarkAccount) => {
  selectedAccount.value = account
  showEditDialog.value = true
}

const updateData = (account: BenchmarkAccount) => {
  selectedAccount.value = account
  showUpdateDataDialog.value = true
}

const deleteAccount = async (account: BenchmarkAccount) => {
  try {
    await deleteBenchmarkAccount(account._id!)
    ElMessage.success('删除成功')
    loadBenchmarkAccounts()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  refreshData()
}

const handleEditSuccess = () => {
  showEditDialog.value = false
  refreshData()
}

const handleUpdateDataSuccess = () => {
  showUpdateDataDialog.value = false
  refreshData()
}

// 📋 采集任务相关方法
const createCollectTask = (account: BenchmarkAccount) => {
  selectedAccount.value = account
  showVideoCollectDialog.value = true
}

const handleActionCommand = (command: string, account: BenchmarkAccount) => {
  selectedAccount.value = account

  switch (command) {
    case 'view':
      viewAccount(account)
      break
    case 'edit':
      editAccount(account)
      break
    case 'update':
      updateData(account)
      break
    case 'create-task':
      createCollectTask(account)
      break
    case 'view-tasks':
      // 查看该账号的采集任务列表
      ElMessage.info(`查看 ${account.account_name} 的采集任务功能开发中...`)
      break
    case 'view-content':
      // 跳转到内容采集页面，并筛选该账号
      ElMessage.info(`跳转到内容采集页面查看 ${account.account_name} 的内容`)
      // TODO: 可以使用路由跳转并传递筛选参数
      break
    case 'delete':
      ElMessageBox.confirm(
        '确定删除这个对标账号吗？删除后将无法恢复！',
        '确认删除',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        deleteAccount(account)
      }).catch(() => {
        // 用户取消删除
      })
      break
  }
}

const handleCollectTaskSuccess = () => {
  ElMessage.success('采集任务已创建')
  loadBenchmarkAccounts() // 刷新列表以显示任务状态
}

// 工具方法
const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 任务状态相关工具方法
const getLastTaskTime = (tasks: any[]) => {
  if (!tasks || tasks.length === 0) return '无任务'
  const lastTask = tasks.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0]
  return formatDate(lastTask.created_at)
}

const getLastTaskStatus = (tasks: any[]) => {
  if (!tasks || tasks.length === 0) return 'none'
  const lastTask = tasks.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0]
  return lastTask.status || 'unknown'
}

const getTaskStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'info',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'cancelled': 'info',
    'none': 'info'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const getTaskStatusText = (status: string) => {
  const textMap = {
    'pending': '待执行',
    'running': '执行中',
    'completed': '已完成',
    'failed': '执行失败',
    'cancelled': '已取消',
    'none': '无任务'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

// 生命周期
onMounted(() => {
  console.log('对标账号页面已挂载，开始加载数据')
  loadOurAccounts()
  refreshData()
})
</script>

<style scoped>
.benchmark-accounts {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.management-header {
  margin-bottom: 20px;
}

.management-header h1 {
  font-size: 24px;
  color: #303133;
  margin: 0 0 8px 0;
}

.header-description {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.stat-card.total .stat-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.total .stat-number,
.stat-card.total .stat-label {
  color: white;
}

.stat-card.original .stat-content {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card.original .stat-number,
.stat-card.original .stat-label {
  color: white;
}

.stat-card.recreate .stat-content {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-card.recreate .stat-number,
.stat-card.recreate .stat-label {
  color: white;
}

.stat-card.repost .stat-content {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.stat-card.repost .stat-number,
.stat-card.repost .stat-label {
  color: white;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 内容列表 */
.account-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.account-info {
  padding: 8px 0;
}

.account-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.account-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
}

.account-avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 13px;
  margin-right: 10px;
  flex-shrink: 0;
}

/* 账号卡片样式 */
.account-card {
  padding: 6px 0;
}

.account-header {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.account-details {
  flex: 1;
  min-width: 0;
}

.account-name {
  margin-bottom: 6px;
}

.account-link {
  font-weight: 500;
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
  line-height: 1.3;
}

.account-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.account-meta {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.priority-stars {
  font-size: 12px;
  color: #faad14;
  margin-left: 4px;
}

.account-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 我们的账号信息样式 */
.our-account-info .account-item {
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.our-account-info .account-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  font-size: 13px;
}

.our-account-info .account-platform {
  display: flex;
  gap: 4px;
  align-items: center;
}

.our-account-info .not-found-account {
  padding: 8px;
  background: #fdf6ec;
  border-radius: 6px;
  border-left: 3px solid #e6a23c;
}

.our-account-info .not-found-account .account-name {
  font-weight: 500;
  color: #e6a23c;
  margin-bottom: 2px;
  font-size: 13px;
}

.our-account-info .not-found-account .account-id {
  font-size: 11px;
  color: #909399;
  margin-bottom: 4px;
  word-break: break-all;
}

.our-account-info .no-account {
  padding: 8px;
  background: #fef0f0;
  border-radius: 6px;
  border-left: 3px solid #f56c6c;
}

.our-account-info .no-account .account-name {
  font-weight: 500;
  color: #f56c6c;
  margin-bottom: 2px;
  font-size: 13px;
}

.our-account-info .no-account .account-id {
  font-size: 11px;
  color: #909399;
  margin-bottom: 4px;
  word-break: break-all;
}

/* 数据概览样式 */
.account-data {
  padding: 4px 0;
}

.data-summary {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.data-item {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.no-data {
  text-align: center;
  padding: 8px 0;
}

/* 标签样式 */
.tags {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.more-tags {
  font-size: 11px;
  color: #999;
}

/* 采集任务信息样式 */
.collect-task-info {
  padding: 4px 0;
}

.task-summary {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.task-item {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-status {
  margin-top: 4px;
}

.no-task {
  text-align: center;
  padding: 8px 0;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-start;
}

/* 表格行高调整 */
:deep(.el-table .el-table__row) {
  height: auto;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

/* 链接按钮样式 */
:deep(.el-button--small.is-link) {
  padding: 2px 4px;
  font-size: 12px;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .benchmark-accounts {
    padding: 10px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }
}
</style>
