<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>递归调用修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #f0f9ff;
            border-color: #67c23a;
        }
        .error {
            background-color: #fef0f0;
            border-color: #f56c6c;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #f0f9ff;
            color: #67c23a;
        }
        .fail {
            background-color: #fef0f0;
            color: #f56c6c;
        }
        button {
            padding: 8px 16px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #337ecc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>递归调用修复测试</h1>
        
        <div class="test-section">
            <h3>问题描述</h3>
            <p>原始代码中 <code>getVideoDurationTextClass</code> 函数调用了自己，导致无限递归：</p>
            <pre><code>const getVideoDurationTextClass = (file: any) => {
  const info = getVideoDurationInfo(file)
  return getVideoDurationTextClass(info.category)  // ❌ 递归调用自己
}</code></pre>
        </div>
        
        <div class="test-section">
            <h3>修复方案</h3>
            <p>重命名导入的工具函数，避免名称冲突：</p>
            <pre><code>// 导入时重命名
import { 
  getVideoDurationTextClass as getVideoDurationTextClassUtil
} from '@/utils/videoDuration'

// 修复后的函数
const getVideoDurationTextClass = (file: any) => {
  const info = getVideoDurationInfo(file)
  return getVideoDurationTextClassUtil(info.category)  // ✅ 调用工具函数
}</code></pre>
        </div>
        
        <div class="test-section">
            <h3>功能测试</h3>
            <button onclick="testVideoDurationFunctions()">测试视频时长分类函数</button>
            <button onclick="testPaginationLogic()">测试分页逻辑</button>
            <button onclick="testFilterLogic()">测试过滤逻辑</button>
            
            <div id="testResults"></div>
        </div>
        
        <div class="test-section">
            <h3>分页问题分析</h3>
            <p>可能的分页问题原因：</p>
            <ul>
                <li>✅ 递归调用导致页面崩溃</li>
                <li>✅ 过滤逻辑没有正确应用到分页</li>
                <li>✅ 总数计算基于错误的数据源</li>
                <li>⚠️ 文件加载逻辑可能有问题</li>
                <li>⚠️ API返回的数据结构可能不正确</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟视频时长分类工具函数
        const VideoDurationCategory = {
            VERY_SHORT: 'very_short',
            SHORT: 'short',
            MEDIUM: 'medium',
            LONG: 'long',
            VERY_LONG: 'very_long'
        };

        function calculateVideoDurationCategory(duration) {
            if (!duration || duration <= 0) return { category: VideoDurationCategory.VERY_SHORT, message: '未知时长' };
            if (duration < 35) return { category: VideoDurationCategory.VERY_SHORT, message: '极短视频' };
            if (duration <= 60) return { category: VideoDurationCategory.SHORT, message: '短视频' };
            if (duration <= 180) return { category: VideoDurationCategory.MEDIUM, message: '中等视频' };
            if (duration <= 480) return { category: VideoDurationCategory.LONG, message: '长视频' };
            return { category: VideoDurationCategory.VERY_LONG, message: '超长视频' };
        }

        function getVideoDurationTextClassUtil(category) {
            switch (category) {
                case VideoDurationCategory.VERY_SHORT: return 'duration-text-very-short';
                case VideoDurationCategory.SHORT: return 'duration-text-short';
                case VideoDurationCategory.MEDIUM: return 'duration-text-medium';
                case VideoDurationCategory.LONG: return 'duration-text-long';
                case VideoDurationCategory.VERY_LONG: return 'duration-text-very-long';
                default: return 'duration-text-unknown';
            }
        }

        // 模拟修复后的函数
        function getVideoDurationInfo(file) {
            const duration = file.duration || 0;
            return calculateVideoDurationCategory(duration);
        }

        function getVideoDurationTextClass(file) {
            const info = getVideoDurationInfo(file);
            return getVideoDurationTextClassUtil(info.category);  // 正确调用工具函数
        }

        // 测试函数
        function testVideoDurationFunctions() {
            const resultsDiv = document.getElementById('testResults');
            let results = '<h4>视频时长分类测试结果：</h4>';
            
            const testFiles = [
                { name: 'short.mp4', duration: 30 },
                { name: 'medium.mp4', duration: 120 },
                { name: 'long.mp4', duration: 300 },
                { name: 'very_long.mp4', duration: 600 }
            ];
            
            let allPassed = true;
            
            testFiles.forEach(file => {
                try {
                    const textClass = getVideoDurationTextClass(file);
                    const info = getVideoDurationInfo(file);
                    
                    results += `<div class="test-result pass">
                        ✅ ${file.name} (${file.duration}s): ${info.message} - ${textClass}
                    </div>`;
                } catch (error) {
                    allPassed = false;
                    results += `<div class="test-result fail">
                        ❌ ${file.name}: 错误 - ${error.message}
                    </div>`;
                }
            });
            
            if (allPassed) {
                results += '<div class="test-result pass"><strong>✅ 所有视频时长分类测试通过！</strong></div>';
            } else {
                results += '<div class="test-result fail"><strong>❌ 部分测试失败</strong></div>';
            }
            
            resultsDiv.innerHTML = results;
        }

        function testPaginationLogic() {
            const resultsDiv = document.getElementById('testResults');
            let results = '<h4>分页逻辑测试结果：</h4>';
            
            // 模拟分页逻辑
            const totalItems = 25;
            const pageSize = 10;
            const currentPage = 2;
            
            const start = (currentPage - 1) * pageSize;
            const end = start + pageSize;
            const totalPages = Math.ceil(totalItems / pageSize);
            
            results += `<div class="test-result pass">
                ✅ 总数: ${totalItems}, 每页: ${pageSize}, 当前页: ${currentPage}
            </div>`;
            results += `<div class="test-result pass">
                ✅ 开始索引: ${start}, 结束索引: ${end}, 总页数: ${totalPages}
            </div>`;
            
            if (start >= 0 && end <= totalItems && totalPages > 0) {
                results += '<div class="test-result pass"><strong>✅ 分页逻辑正确！</strong></div>';
            } else {
                results += '<div class="test-result fail"><strong>❌ 分页逻辑有问题</strong></div>';
            }
            
            resultsDiv.innerHTML = results;
        }

        function testFilterLogic() {
            const resultsDiv = document.getElementById('testResults');
            let results = '<h4>过滤逻辑测试结果：</h4>';
            
            // 模拟文件列表
            const fileList = [
                { name: 'video1.mp4', platform: 'youtube', content_type: 'video' },
                { name: 'video2.mp4', platform: 'tiktok', content_type: 'video' },
                { name: 'image1.jpg', platform: 'youtube', content_type: 'image' },
                { name: 'video3.mp4', platform: 'youtube', content_type: 'video' }
            ];
            
            // 测试平台过滤
            const youtubeFiles = fileList.filter(file => file.platform === 'youtube');
            results += `<div class="test-result pass">
                ✅ YouTube平台过滤: ${youtubeFiles.length} 个文件
            </div>`;
            
            // 测试内容类型过滤
            const videoFiles = fileList.filter(file => file.content_type === 'video');
            results += `<div class="test-result pass">
                ✅ 视频类型过滤: ${videoFiles.length} 个文件
            </div>`;
            
            // 测试搜索过滤
            const searchResults = fileList.filter(file => file.name.toLowerCase().includes('video1'));
            results += `<div class="test-result pass">
                ✅ 搜索过滤 'video1': ${searchResults.length} 个文件
            </div>`;
            
            results += '<div class="test-result pass"><strong>✅ 过滤逻辑正确！</strong></div>';
            
            resultsDiv.innerHTML = results;
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            testVideoDurationFunctions();
        });
    </script>
</body>
</html>
