<template>
  <div class="task-management">
    <div class="management-header">
      <h1>🎮 任务管理</h1>
      <p class="header-description">实时管理正在运行的任务，支持启动、暂停、取消等操作</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="stat-card running">
            <div class="stat-content">
              <div class="stat-icon">🏃</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.running }}</div>
                <div class="stat-label">运行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-icon">⏳</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pending }}</div>
                <div class="stat-label">等待中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card paused">
            <div class="stat-content">
              <div class="stat-icon">⏸️</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.paused }}</div>
                <div class="stat-label">已暂停</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card failed">
            <div class="stat-content">
              <div class="stat-icon">❌</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.failed }}</div>
                <div class="stat-label">失败</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card completed">
            <div class="stat-content">
              <div class="stat-icon">✅</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总任务</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="refreshTasks" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-switch
          v-model="autoRefresh"
          @change="toggleAutoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
          style="margin-left: 10px;"
        />
        <el-button type="success" @click="startAllPendingTasks" :disabled="stats.pending === 0">
          <el-icon><VideoPlay /></el-icon>
          启动所有等待任务
        </el-button>
        <el-button type="warning" @click="pauseAllRunningTasks" :disabled="stats.running === 0">
          <el-icon><VideoPause /></el-icon>
          暂停所有运行任务
        </el-button>
      </div>
      <div class="toolbar-right">
        <!-- WebSocket连接状态指示器 -->
        <el-tag
          :type="socketConnected ? 'success' : 'danger'"
          size="small"
          style="margin-right: 10px;"
        >
          {{ socketConnected ? '🔗 实时连接' : '❌ 连接断开' }}
        </el-tag>

        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" @change="filterTasks">
          <el-option label="全部" value="" />
          <el-option label="运行中" value="running" />
          <el-option label="等待中" value="pending" />
          <el-option label="已暂停" value="paused" />
          <el-option label="失败" value="failed" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="canceled" />
        </el-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索任务..."
          style="width: 200px; margin-left: 10px"
          @input="filterTasks"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <el-table
        ref="tableRef"
        :data="treeData"
        v-loading="loading"
        stripe
        row-key="id"
        :tree-props="{ children: 'children' }"
        :default-expand-all="false"
        :expand-row-keys="expandedRows"
        @row-click="handleRowClick"
        style="width: 100%"
      >
        <el-table-column label="任务信息" width="400" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="task-info">
              <div class="task-id">
                <span class="id-text">{{ row.id }}</span>
                <el-tag
                  :type="getTaskTypeColor(row.task_type)"
                  size="small"
                  style="margin-left: 8px;"
                >
                  {{ getTaskTypeText(row.task_type) }}
                </el-tag>
              </div>
              <div v-if="row.workflow_name" class="workflow-name">
                🔧 {{ row.workflow_name }}
              </div>
              <div v-if="row.task_type === 'main'" class="subtask-info">
                📦 {{ row.completed_subtasks || 0 }}/{{ row.total_subtasks || 0 }} 子任务
              </div>
              <div v-if="row.task_type === 'subtask'" class="video-file">
                📹 {{ getFileName(row.video_file) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态与进度" width="250">
          <template #default="{ row }">
            <div class="status-progress-column">
              <!-- 状态标签 -->
              <div class="status-row">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
                <span class="progress-text">{{ getTaskProgress(row) }}%</span>
              </div>

              <!-- 进度条 -->
              <el-progress
                :percentage="getTaskProgress(row)"
                :status="getProgressStatus(row.status)"
                :stroke-width="4"
                :show-text="false"
                style="margin: 5px 0;"
              />

              <!-- 失败原因（如果失败） -->
              <div v-if="row.status === 'failed'" class="failure-info">
                <el-tooltip :content="getDetailedFailureReason(row)" placement="top">
                  <div class="failure-reason">
                    <el-icon class="failure-icon"><Warning /></el-icon>
                    {{ getFailureReason(row) }}
                  </div>
                </el-tooltip>
              </div>

              <!-- 成功结果（如果成功） -->
              <div v-if="row.status === 'completed'" class="success-info">
                <div class="success-result" @click="viewTaskResult(row)">
                  <el-icon class="success-icon"><SuccessFilled /></el-icon>
                  <span>任务完成，点击查看结果</span>
                </div>
              </div>

              <!-- 当前步骤（如果运行中） -->
              <div v-if="row.status === 'running'" class="current-step">
                <el-icon class="step-icon"><Loading /></el-icon>
                {{ getCurrentStepText(row) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="platform_name" label="平台" width="100" />
        <el-table-column prop="account_name" label="账号" width="120" show-overflow-tooltip />

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-if="row.status === 'pending'"
                type="success"
                size="small"
                @click.stop="startTask(row)"
              >
                启动
              </el-button>
              <el-button
                v-if="row.status === 'running'"
                type="warning"
                size="small"
                @click.stop="pauseTask(row)"
              >
                暂停
              </el-button>
              <el-button
                v-if="row.status === 'paused'"
                type="success"
                size="small"
                @click.stop="resumeTask(row)"
              >
                恢复
              </el-button>
              <el-button
                v-if="['pending', 'running', 'paused'].includes(row.status)"
                type="danger"
                size="small"
                @click.stop="cancelTask(row)"
              >
                取消
              </el-button>
              <el-button
                v-if="['failed', 'canceled'].includes(row.status)"
                type="success"
                size="small"
                @click.stop="restartTask(row)"
              >
                重启
              </el-button>
              <el-button
                v-if="['completed', 'failed', 'canceled'].includes(row.status)"
                type="danger"
                size="small"
                @click.stop="deleteTask(row)"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click.stop="viewTaskDetail(row)"
              >
                详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      title="📋 任务详情" 
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedTask" class="task-detail">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="平台">{{ selectedTask.platform_name }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ selectedTask.account_name }}</el-descriptions-item>
          <el-descriptions-item label="设备">{{ selectedTask.device_id }}</el-descriptions-item>
          <el-descriptions-item label="进度">{{ selectedTask.progress || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="内容路径" :span="2">{{ selectedTask.content_path }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(selectedTask.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatTime(selectedTask.start_time) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间" v-if="selectedTask.end_time">{{ formatTime(selectedTask.end_time) }}</el-descriptions-item>
          <el-descriptions-item label="工作流类型" v-if="selectedTask.workflow_name">{{ selectedTask.workflow_name }}</el-descriptions-item>
        </el-descriptions>

        <!-- 错误信息 -->
        <div v-if="selectedTask.status === 'failed' && hasErrorLogs" class="error-info" style="margin-top: 20px;">
          <h3 style="color: #f56c6c;">❌ 错误信息</h3>
          <el-alert
            type="error"
            :closable="false"
            style="margin-bottom: 10px;"
          >
            <template #title>
              <div class="error-summary">
                <div><strong>任务执行失败</strong></div>
                <div style="margin-top: 5px; font-size: 14px;">
                  {{ getErrorSummary() }}
                </div>
              </div>
            </template>
          </el-alert>
        </div>

        <!-- 实时日志 -->
        <div class="task-logs" style="margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3>📋 实时日志</h3>
            <div class="log-stats">
              <el-tag v-if="getLogStats().error > 0" type="danger" size="small">
                错误: {{ getLogStats().error }}
              </el-tag>
              <el-tag v-if="getLogStats().warning > 0" type="warning" size="small" style="margin-left: 5px;">
                警告: {{ getLogStats().warning }}
              </el-tag>
              <el-tag type="info" size="small" style="margin-left: 5px;">
                总计: {{ taskLogs.length }}
              </el-tag>
            </div>
          </div>
          <div class="logs-container">
            <el-timeline v-if="taskLogs.length > 0">
              <el-timeline-item
                v-for="(log, index) in taskLogs"
                :key="index"
                :timestamp="formatTime(log.timestamp)"
                :type="getLogType(log.level)"
                size="small"
                :class="{ 'error-log-item': log.level === 'error' }"
              >
                <div class="log-content">
                  <span class="log-level" :class="`log-level-${log.level}`">
                    [{{ log.level.toUpperCase() }}]
                  </span>
                  <span class="log-message" :class="{ 'error-message': log.level === 'error' }">
                    {{ log.message }}
                  </span>
                </div>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无日志" />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="refreshTaskDetail">刷新</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务结果弹窗 -->
    <TaskResult
      v-model="resultDialogVisible"
      :task="selectedTask"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, VideoPlay, VideoPause, Search, Warning, SuccessFilled, Loading } from '@element-plus/icons-vue'
import { getRunningTasks, startTask as apiStartTask, pauseTask as apiPauseTask, cancelTask as apiCancelTask, deleteTask as deleteTaskApi } from '@/api/task'
import TaskResult from '@/components/TaskResult.vue'
import { deviceSocketManager } from '@/socket.io'
import type { Socket } from 'socket.io-client'

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const statusFilter = ref('')
const searchKeyword = ref('')
const detailDialogVisible = ref(false)
const resultDialogVisible = ref(false)
const selectedTask = ref(null)
const taskLogs = ref([])
const autoRefresh = ref(true) // 默认开启自动刷新
const expandedRows = ref([]) // 展开的行
const tableRef = ref() // 表格引用

// WebSocket相关
const socket = ref<Socket | null>(null)
const socketConnected = ref(false)

// 统计数据
const stats = computed(() => {
  const running = tasks.value.filter(t => t.status === 'running').length
  const pending = tasks.value.filter(t => t.status === 'pending').length
  const paused = tasks.value.filter(t => t.status === 'paused').length
  const failed = tasks.value.filter(t => t.status === 'failed').length
  const completed = tasks.value.filter(t => t.status === 'completed').length
  const total = tasks.value.length

  return { running, pending, paused, failed, completed, total }
})

// 过滤后的任务列表
const filteredTasks = computed(() => {
  let filtered = tasks.value

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(task =>
      task.id.toLowerCase().includes(keyword) ||
      task.platform_name?.toLowerCase().includes(keyword) ||
      task.account_name?.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 树形数据结构
const treeData = computed(() => {
  // 过滤掉空ID的任务
  const validTasks = filteredTasks.value.filter(task => task.id && task.id.trim() !== '')

  const mainTasks = validTasks.filter(task => task.task_type === 'main')
  const singleTasks = validTasks.filter(task =>
    task.task_type === 'single' ||
    !task.task_type ||
    task.task_type === 'collect' ||  // 🔧 新增：支持采集任务
    task.task_type === 'benchmark_download' ||  // 🔧 新增：支持下载任务
    task.task_type === 'benchmark_download_batch'  // 🔧 新增：支持批量下载任务
  )
  const subtasks = validTasks.filter(task => task.task_type === 'subtask')



  // 构建树形结构
  const treeNodes = []

  // 添加主任务及其子任务
  mainTasks.forEach(mainTask => {
    const children = subtasks
      .filter(subtask => subtask.parent_task_id === mainTask.id)
      .sort((a, b) => (a.subtask_index || 0) - (b.subtask_index || 0))



    const mainTaskNode = {
      ...mainTask,
      // 只有当有子任务时才添加children属性
      ...(children.length > 0 && { children: children })
    }

    treeNodes.push(mainTaskNode)

    // 自动展开有运行中子任务的主任务
    if (children.some(child => child.status === 'running')) {
      if (!expandedRows.value.includes(mainTask.id)) {
        expandedRows.value.push(mainTask.id)
      }
    }
  })

  // 添加单任务（不添加children属性）
  singleTasks.forEach(singleTask => {
    treeNodes.push({
      ...singleTask
      // 单任务不需要children属性
    })
  })

  return treeNodes
})

// 定时器
let refreshTimer = null

// 初始化WebSocket连接
const initWebSocket = async () => {
  try {
    console.log('🔌 初始化任务管理WebSocket连接...')

    // 使用设备WebSocket管理器获取Socket实例
    socket.value = await deviceSocketManager.getSocket()

    console.log('✅ 任务管理WebSocket连接成功')
    socketConnected.value = true

    // 监听连接状态
    socket.value.on('connect', () => {
      console.log('🔗 任务管理WebSocket已连接')
      socketConnected.value = true
    })

    socket.value.on('disconnect', () => {
      console.log('❌ 任务管理WebSocket连接断开')
      socketConnected.value = false
    })

    socket.value.on('reconnect', () => {
      console.log('🔄 任务管理WebSocket重连成功')
      socketConnected.value = true
      // 重连后重新获取任务列表
      fetchTasks()
    })

    // 监听任务状态更新事件
    socket.value.on('task_status_update', (data) => {
      console.log('📡 收到任务状态更新:', data)
      handleTaskStatusUpdate(data)
    })

    // 监听设备状态更新（可能包含任务信息）
    socket.value.on('device_status_update', (data) => {
      console.log('📱 收到设备状态更新:', data)
      // 如果设备状态包含任务信息，触发任务列表刷新
      if (data.tasks || data.task_id) {
        fetchTasks()
      }
    })

  } catch (error) {
    console.error('❌ 初始化任务管理WebSocket失败:', error)
    socketConnected.value = false

    // WebSocket初始化失败时，启用定时刷新作为备选方案
    if (!refreshTimer) {
      refreshTimer = setInterval(fetchTasks, 10000) // 10秒间隔
      console.log('🔄 启用定时刷新作为备选方案')
    }
  }
}

// 关闭WebSocket连接
const closeWebSocket = () => {
  if (socket.value) {
    console.log('🔌 关闭任务管理WebSocket连接')
    socket.value.disconnect()
    socket.value = null
    socketConnected.value = false
  }
}

// 处理任务状态更新
const handleTaskStatusUpdate = (data) => {
  if (!data || !data.task_id) {
    return
  }

  const taskId = data.task_id
  const taskIndex = tasks.value.findIndex(task => task.id === taskId)

  if (taskIndex !== -1) {
    // 更新现有任务
    const updatedTask = {
      ...tasks.value[taskIndex],
      status: data.status || tasks.value[taskIndex].status,
      progress: data.progress !== undefined ? data.progress : tasks.value[taskIndex].progress,
      updated_at: new Date().toISOString()
    }

    // 如果有其他字段需要更新
    if (data.start_time) updatedTask.start_time = data.start_time
    if (data.end_time) updatedTask.end_time = data.end_time
    if (data.completed_subtasks !== undefined) updatedTask.completed_subtasks = data.completed_subtasks

    tasks.value[taskIndex] = updatedTask

    console.log(`✅ 更新任务 ${taskId} 状态: ${data.status} (${data.progress || 0}%)`)

    // 如果当前正在查看这个任务的详情，刷新详情
    if (selectedTask.value && selectedTask.value.id === taskId) {
      selectedTask.value = updatedTask
      // 可选：刷新日志
      // fetchTaskLogs(taskId)
    }
  } else {
    // 新任务，重新获取任务列表
    console.log(`🆕 发现新任务 ${taskId}，刷新任务列表`)
    fetchTasks()
  }
}

// 初始化
onMounted(() => {
  fetchTasks()
  initWebSocket()
  // 默认不启动自动刷新，由用户手动控制
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  closeWebSocket()
})

// 切换自动刷新
const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    // 启动自动刷新
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }
    refreshTimer = setInterval(fetchTasks, 30000) // 30秒间隔
    ElMessage.success('已启用自动刷新（30秒间隔）')
  } else {
    // 停止自动刷新
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    ElMessage.info('已关闭自动刷新')
  }
}

// 获取运行中的任务
const fetchTasks = async () => {
  // 如果正在加载中，跳过这次刷新
  if (loading.value) {
    return
  }

  try {
    loading.value = true
    const response = await getRunningTasks()

    if (response && response.data) {
      tasks.value = response.data.tasks || []
    } else {
      console.warn('API响应格式异常:', response)
      // 不显示错误消息，避免频繁弹窗
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    // 只在手动刷新时显示错误消息
    // ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新任务（手动刷新，显示错误消息）
const refreshTasks = async () => {
  try {
    loading.value = true
    const response = await getRunningTasks()

    if (response && response.data) {
      tasks.value = response.data.tasks || []
      ElMessage.success('刷新成功')
    } else {
      console.warn('API响应格式异常:', response)
      ElMessage.warning('数据格式异常')
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 筛选任务
const filterTasks = () => {
  // 触发计算属性重新计算
}

// 启动任务
const startTask = async (task) => {
  try {
    await apiStartTask(task.id)
    ElMessage.success('任务启动成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('启动任务失败')
  }
}

// 暂停任务
const pauseTask = async (task) => {
  try {
    await apiPauseTask(task.id)
    ElMessage.success('任务暂停成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('暂停任务失败')
  }
}

// 恢复任务
const resumeTask = async (task) => {
  try {
    await apiStartTask(task.id)
    ElMessage.success('任务恢复成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('恢复任务失败')
  }
}

// 重启任务
const restartTask = async (task) => {
  try {
    console.log('🔄 准备重启任务:', task.id)

    const statusText = task.status === 'failed' ? '失败' : '已取消'
    await ElMessageBox.confirm(
      `确定要重启${statusText}的任务 ${task.id} 吗？`,
      '确认重启',
      {
        confirmButtonText: '确定重启',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    console.log('✅ 用户确认重启，开始调用API:', task.id)
    const result = await apiStartTask(task.id)
    console.log('📡 API调用结果:', result)

    ElMessage.success('任务重启成功')
    await fetchTasks()
  } catch (error) {
    console.error('❌ 重启任务失败:', error)
    if (error !== 'cancel') {
      ElMessage.error(`重启任务失败: ${error.message || error}`)
    }
  }
}

// 取消任务
const cancelTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 ${task.id} 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await apiCancelTask(task.id)
    ElMessage.success('任务取消成功')
    await fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

// 删除任务
const deleteTask = async (task) => {
  try {
    const taskTypeText = getTaskTypeText(task.task_type)
    let confirmMessage = `确定要删除${taskTypeText} ${task.id} 吗？`

    // 如果是主任务，提醒会同时删除子任务
    if (task.task_type === 'main') {
      confirmMessage += '\n注意：删除主任务会同时删除所有子任务！'
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    // 调用删除API
    await deleteTaskApi(task.id)
    ElMessage.success('任务删除成功')
    await fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

// 批量操作
const startAllPendingTasks = async () => {
  const pendingTasks = tasks.value.filter(t => t.status === 'pending')
  for (const task of pendingTasks) {
    try {
      await apiStartTask(task.id)
    } catch (error) {
      console.error(`启动任务 ${task.id} 失败:`, error)
    }
  }
  ElMessage.success('批量启动完成')
  await fetchTasks()
}

const pauseAllRunningTasks = async () => {
  const runningTasks = tasks.value.filter(t => t.status === 'running')
  for (const task of runningTasks) {
    try {
      await apiPauseTask(task.id)
    } catch (error) {
      console.error(`暂停任务 ${task.id} 失败:`, error)
    }
  }
  ElMessage.success('批量暂停完成')
  await fetchTasks()
}

// 查看任务详情
const viewTaskDetail = (task) => {
  selectedTask.value = task
  detailDialogVisible.value = true
  fetchTaskLogs(task.id)
}

const handleRowClick = (row) => {
  viewTaskDetail(row)
}

// 获取任务日志
const fetchTaskLogs = async (taskId) => {
  try {
    console.log('获取任务日志:', taskId)

    // 调用真实的API获取任务日志
    const response = await fetch(`/api/tasks/detail/${taskId}/logs?limit=100&offset=0`)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('任务日志响应:', data)

    if (data && data.logs) {
      taskLogs.value = data.logs
      console.log('任务日志数据:', taskLogs.value)
    } else {
      console.warn('日志API响应数据格式异常:', data)
      taskLogs.value = []
    }

  } catch (error) {
    console.error('获取任务日志失败:', error)
    ElMessage.warning('获取任务日志失败')
    taskLogs.value = []
  }
}

const refreshTaskDetail = () => {
  if (selectedTask.value) {
    fetchTaskLogs(selectedTask.value.id)
  }
}

// 检查是否有错误日志
const hasErrorLogs = computed(() => {
  return taskLogs.value.some(log => log.level === 'error')
})

// 获取错误摘要
const getErrorSummary = () => {
  if (!selectedTask.value || selectedTask.value.status !== 'failed') {
    return ''
  }

  // 从日志中提取错误信息
  const errorLogs = taskLogs.value.filter(log => log.level === 'error')
  if (errorLogs.length === 0) {
    return '任务执行过程中发生未知错误'
  }

  // 获取最后一个错误日志作为主要错误
  const lastError = errorLogs[errorLogs.length - 1]
  let errorMsg = lastError.message

  // 如果有多个错误，显示错误数量
  if (errorLogs.length > 1) {
    errorMsg += ` (共${errorLogs.length}个错误)`
  }

  return errorMsg
}

// 获取日志统计
const getLogStats = () => {
  const stats = {
    error: 0,
    warning: 0,
    info: 0,
    success: 0
  }

  taskLogs.value.forEach(log => {
    if (stats.hasOwnProperty(log.level)) {
      stats[log.level]++
    }
  })

  return stats
}

// 获取失败原因（简短版本，用于列表显示）
const getFailureReason = (task) => {
  if (task.status !== 'failed') return ''

  // 优先从任务数据中获取失败原因
  if (task.failure_reason) {
    return task.failure_reason
  }

  // 从最新的错误日志中提取失败原因
  const errorLogs = taskLogs.value.filter(log => log.level === 'error')
  if (errorLogs.length > 0) {
    const lastError = errorLogs[errorLogs.length - 1]
    const message = lastError.message

    // 根据错误消息类型返回简短描述
    if (message.includes('步骤失败')) {
      const stepMatch = message.match(/步骤失败:\s*(.+)/)
      if (stepMatch) {
        return `步骤失败: ${stepMatch[1]}`
      }
    }

    if (message.includes('连接') || message.includes('设备')) {
      return '设备连接失败'
    }

    if (message.includes('元素') || message.includes('定位')) {
      return '界面元素定位失败'
    }

    if (message.includes('超时') || message.includes('timeout')) {
      return '操作超时'
    }

    // 返回错误消息的前30个字符
    return message.length > 30 ? message.substring(0, 30) + '...' : message
  }

  // 根据进度推断失败原因
  const progress = task.progress || 0
  if (progress < 20) {
    return '设备连接失败'
  } else if (progress < 40) {
    return '应用启动失败'
  } else if (progress < 60) {
    return '内容处理失败'
  } else if (progress < 80) {
    return '上传过程失败'
  } else {
    return '任务完成失败'
  }
}

// 获取详细失败原因（用于tooltip显示）
const getDetailedFailureReason = (task) => {
  if (task.status !== 'failed') return ''

  // 优先从任务数据中获取详细失败原因
  if (task.failure_details) {
    return task.failure_details
  }

  // 从错误日志中构建详细信息
  const errorLogs = taskLogs.value.filter(log => log.level === 'error')
  if (errorLogs.length > 0) {
    const errorMessages = errorLogs.map(log => log.message).join('\n')
    return errorMessages
  }

  // 根据进度提供详细建议
  const progress = task.progress || 0
  const platform = task.platform_name || '平台'

  if (progress < 20) {
    return `设备连接失败：无法连接到设备${task.device_id}。建议检查设备是否正常运行，Appium服务是否启动。`
  } else if (progress < 40) {
    return `${platform}应用启动失败：无法启动或应用崩溃。建议检查应用是否已安装，版本是否兼容。`
  } else if (progress < 60) {
    return `内容处理失败：视频文件处理或界面操作失败。建议检查视频文件格式，应用界面是否正常。`
  } else if (progress < 80) {
    return `上传过程失败：网络连接问题或${platform}服务异常。建议检查网络连接，稍后重试。`
  } else {
    return `任务完成失败：最后步骤执行失败。建议查看详细日志了解具体原因。`
  }
}

// 获取当前步骤文本（用于运行中任务）
const getCurrentStepText = (task) => {
  if (task.status !== 'running') return ''

  const progress = task.progress || 0

  if (progress < 10) {
    return '正在连接设备...'
  } else if (progress < 20) {
    return '正在启动应用...'
  } else if (progress < 40) {
    return '正在处理内容...'
  } else if (progress < 60) {
    return '正在填写信息...'
  } else if (progress < 80) {
    return '正在上传内容...'
  } else if (progress < 95) {
    return '正在完成任务...'
  } else {
    return '即将完成...'
  }
}

// 查看任务结果
const viewTaskResult = (task) => {
  if (task.status !== 'completed') return

  selectedTask.value = task
  resultDialogVisible.value = true
}

// 工具函数
const getStatusType = (status) => {
  const types = {
    'pending': 'info',
    'running': 'primary', 
    'paused': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'canceled': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '等待中',
    'running': '运行中',
    'paused': '已暂停', 
    'completed': '已完成',
    'failed': '失败',
    'canceled': '已取消'
  }
  return texts[status] || status
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return ''
}

const getLogType = (level) => {
  const types = {
    'success': 'success',
    'warning': 'warning', 
    'error': 'danger',
    'info': 'primary'
  }
  return types[level] || 'primary'
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}

// 任务类型相关函数
const getTaskTypeText = (taskType) => {
  const texts = {
    'main': '主任务',
    'subtask': '子任务',
    'single': '单任务',
    'collect': '采集任务',  // 🔧 新增：采集任务
    'benchmark_download': '下载任务',  // 🔧 新增：下载任务
    'benchmark_download_batch': '批量下载'  // 🔧 新增：批量下载任务
  }
  return texts[taskType] || '单任务'
}

const getTaskTypeColor = (taskType) => {
  const colors = {
    'main': 'primary',
    'subtask': 'info',
    'single': 'success',
    'collect': 'warning',  // 🔧 新增：采集任务用橙色
    'benchmark_download': 'danger',  // 🔧 新增：下载任务用红色
    'benchmark_download_batch': 'danger'  // 🔧 新增：批量下载任务用红色
  }
  return colors[taskType] || 'success'
}

// 获取任务进度
const getTaskProgress = (task) => {
  if (task.task_type === 'main') {
    // 主任务进度 = 已完成子任务数 / 总子任务数 * 100
    const completed = task.completed_subtasks || 0
    const total = task.total_subtasks || 1
    return Math.round((completed / total) * 100)
  }
  return task.progress || 0
}

// 获取文件名
const getFileName = (filePath) => {
  if (!filePath) return ''
  return filePath.split(/[/\\]/).pop() || filePath
}


</script>

<style scoped>
.task-management {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.management-header {
  margin-bottom: 20px;
}

.management-header h1 {
  margin: 0 0 8px 0;
  color: #409EFF;
  font-size: 1.8rem;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 2rem;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

/* 统计卡片特定样式 */
.stat-card.running {
  border-left: 4px solid #e6a23c;
}

.stat-card.pending {
  border-left: 4px solid #409eff;
}

.stat-card.paused {
  border-left: 4px solid #909399;
}

.stat-card.failed {
  border-left: 4px solid #f56c6c;
}

.stat-card.failed .stat-number {
  color: #f56c6c;
}

.stat-card.completed {
  border-left: 4px solid #67c23a;
}

.stat-card.completed .stat-number {
  color: #67c23a;
}

.stat-card.total {
  border-left: 4px solid #409eff;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.task-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.task-detail {
  max-height: 500px;
  overflow-y: auto;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.log-level {
  font-weight: bold;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 8px;
}

.log-level-info {
  background-color: #e1f5fe;
  color: #0277bd;
}

.log-level-success {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.log-level-warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.log-level-error {
  background-color: #ffebee;
  color: #c62828;
}

/* 任务信息样式 */
.task-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-id {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.id-text {
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.workflow-name {
  font-size: 12px;
  color: #409EFF;
  margin-top: 4px;
  font-weight: 500;
}

.subtask-info {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.video-file {
  font-size: 12px;
  color: #67c23a;
  font-weight: normal;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 树形表格样式优化 */
:deep(.el-table__expand-icon) {
  color: #409EFF;
  font-size: 16px !important;
  width: 20px !important;
  height: 20px !important;
  line-height: 20px !important;
  border-radius: 4px;
  background-color: #f0f9ff;
  border: 1px solid #409EFF;
  transition: all 0.3s ease;
}

:deep(.el-table__expand-icon:hover) {
  background-color: #409EFF;
  color: white;
  transform: scale(1.1);
}

:deep(.el-table__expand-icon.el-table__expand-icon--expanded) {
  background-color: #409EFF;
  color: white;
}

/* 主任务行样式 */
:deep(.el-table__row--level-0) {
  background-color: #fafbfc;
  border-left: 3px solid #409EFF;
}

:deep(.el-table__row--level-0:hover) {
  background-color: #f0f9ff;
}

/* 子任务行样式 */
:deep(.el-table__row--level-1) {
  background-color: #f8f9fa;
  border-left: 3px solid #e4e7ed;
}

:deep(.el-table__row--level-1:hover) {
  background-color: #f5f7fa;
}

:deep(.el-table__row--level-1 .task-info) {
  padding-left: 20px;
}

/* 展开图标所在单元格的样式 */
:deep(.el-table__cell .cell) {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

/* 错误日志项样式 */
.error-log-item {
  background-color: #fef0f0;
  border-left: 3px solid #f56c6c;
  padding-left: 10px;
  margin-left: -10px;
  border-radius: 4px;
}

.error-message {
  color: #f56c6c;
  font-weight: 500;
}

.log-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.log-message {
  word-break: break-word;
  line-height: 1.4;
}

/* 错误信息区域样式 */
.error-info {
  border: 1px solid #f56c6c;
  border-radius: 4px;
  padding: 15px;
  background-color: #fef0f0;
}

.error-summary {
  line-height: 1.5;
}

/* 日志统计样式 */
.log-stats {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 日志级别样式增强 */
.log-level-error {
  background-color: #ffebee;
  color: #c62828;
  font-weight: bold;
}

.log-level-warning {
  background-color: #fff3e0;
  color: #f57c00;
  font-weight: bold;
}

.log-level-info {
  background-color: #e1f5fe;
  color: #0277bd;
}

.log-level-success {
  background-color: #e8f5e8;
  color: #2e7d32;
  font-weight: bold;
}

/* 状态与进度列样式 */
.status-progress-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 失败信息样式 */
.failure-info {
  margin-top: 2px;
}

.failure-reason {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #f56c6c;
  cursor: pointer;
  padding: 2px 4px;
  background-color: #fef0f0;
  border-radius: 3px;
  border: 1px solid #fbc4c4;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.failure-reason:hover {
  background-color: #fde2e2;
  border-color: #f56c6c;
}

.failure-icon {
  font-size: 11px;
  color: #f56c6c;
  flex-shrink: 0;
}

/* 成功结果样式 */
.success-info {
  margin-top: 2px;
}

.success-result {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #67c23a;
  cursor: pointer;
  padding: 2px 4px;
  background-color: #f0f9ff;
  border-radius: 3px;
  border: 1px solid #b3e5fc;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.success-result:hover {
  background-color: #e1f5fe;
  border-color: #67c23a;
}

.success-icon {
  font-size: 11px;
  color: #67c23a;
  flex-shrink: 0;
}

/* 当前步骤样式 */
.current-step {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #409eff;
  padding: 2px 4px;
  background-color: #ecf5ff;
  border-radius: 3px;
  border: 1px solid #d9ecff;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.step-icon {
  font-size: 11px;
  color: #409eff;
  flex-shrink: 0;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
