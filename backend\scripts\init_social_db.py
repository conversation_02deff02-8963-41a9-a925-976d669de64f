#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ThunderHub 社交媒体数据库初始化脚本
- 根据新的数据库设计初始化MongoDB集合和索引
- 从模拟.txt文件导入YouTube账号数据
- 创建平台、应用和账号数据

使用方法:
python init_social_db.py [--reset] [--import-file FILE]

参数:
  --reset         重置数据库（删除现有集合）
  --import-file   从指定的文本文件导入账号数据（默认为模拟.txt）
"""

import os
import sys
import json
import logging
import argparse
import datetime
import re
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("init_social_db")

# 导入项目模块
try:
    from app.config.database import DatabaseConfig
    from app.services.consul_discovery import ConsulDiscovery
except ImportError as e:
    logger.warning(f"导入模块失败: {str(e)}")
    logger.info("尝试使用自定义配置...")

    # 定义自定义配置类
    class DatabaseConfig:
        def __init__(self):
            self.mongodb_url = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
            self.mongodb_name = os.getenv("MONGODB_NAME", "thunderhub")
            self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
            self.consul_url = os.getenv("CONSUL_URL", "http://localhost:8500")

            logger.info(f"使用配置: MongoDB={self.mongodb_url}, DB={self.mongodb_name}")
            logger.info(f"使用配置: Redis={self.redis_url}, Consul={self.consul_url}")


class SocialDatabaseInitializer:
    """社交媒体数据库初始化器"""

    def __init__(self):
        """初始化数据库连接"""
        try:
            self.db_config = DatabaseConfig()

            # 连接MongoDB
            try:
                from pymongo import MongoClient
                self.client = MongoClient(self.db_config.mongodb_url, serverSelectionTimeoutMS=5000)
                # 测试连接
                self.client.admin.command('ping')
                self.db = self.client[self.db_config.mongodb_name]
                logger.info(f"MongoDB连接成功: {self.db_config.mongodb_url}/{self.db_config.mongodb_name}")
            except ImportError:
                logger.error("未安装pymongo模块，请先安装: pip install pymongo")
                sys.exit(1)
            except Exception as e:
                logger.error(f"MongoDB连接失败: {str(e)}")
                sys.exit(1)

        except Exception as e:
            logger.error(f"初始化数据库连接失败: {str(e)}")
            raise

    def reset_database(self) -> None:
        """重置数据库（删除相关集合）"""
        collections = [
            "social_platforms", 
            "platform_apps",
            "social_accounts", 
            "device_account_mappings"
        ]
        
        for collection in collections:
            try:
                self.db[collection].drop()
                logger.info(f"已删除集合: {collection}")
            except Exception as e:
                logger.error(f"删除集合 {collection} 失败: {str(e)}")

        logger.info("社交媒体相关集合重置完成")

    def create_indexes(self) -> None:
        """创建索引"""
        try:
            # 社交平台索引
            self.db.social_platforms.create_index("id", unique=True)
            self.db.social_platforms.create_index("status")
            self.db.social_platforms.create_index("created_at")

            # 平台应用索引
            self.db.platform_apps.create_index("id", unique=True)
            self.db.platform_apps.create_index("platform_id")
            self.db.platform_apps.create_index("type")
            self.db.platform_apps.create_index("status")
            self.db.platform_apps.create_index([("platform_id", 1), ("type", 1)])

            # 社交账号索引
            self.db.social_accounts.create_index("id", unique=True)
            self.db.social_accounts.create_index([("username", 1), ("platform_id", 1)], unique=True)
            self.db.social_accounts.create_index("platform_id")
            self.db.social_accounts.create_index("core_service_id")
            self.db.social_accounts.create_index("status")
            self.db.social_accounts.create_index([("platform_id", 1), ("status", 1)])
            self.db.social_accounts.create_index([("core_service_id", 1), ("platform_id", 1)])
            self.db.social_accounts.create_index("created_at")
            self.db.social_accounts.create_index("updated_at")
            
            # 设备账号映射索引
            self.db.device_account_mappings.create_index("device_id")
            self.db.device_account_mappings.create_index("account_id")
            self.db.device_account_mappings.create_index("platform_id")
            self.db.device_account_mappings.create_index("app_id")
            self.db.device_account_mappings.create_index("core_service_id")
            self.db.device_account_mappings.create_index("status")
            self.db.device_account_mappings.create_index([("device_id", 1), ("platform_id", 1)], unique=True)
            self.db.device_account_mappings.create_index([("device_id", 1), ("account_id", 1)], unique=True)
            
            logger.info("索引创建完成")
        except Exception as e:
            logger.error(f"创建索引失败: {str(e)}")

    def init_social_platforms(self) -> None:
        """初始化社交媒体平台数据"""
        platforms = [
            {
                "id": "youtube",
                "name": "YouTube",
                "icon": "youtube-icon.png",
                "website": "https://www.youtube.com",
                "status": "active",
                "features": ["video", "live", "comment"],
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "tiktok",
                "name": "TikTok",
                "icon": "tiktok-icon.png",
                "website": "https://www.tiktok.com",
                "status": "active",
                "features": ["video", "live", "comment"],
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "facebook",
                "name": "Facebook",
                "icon": "facebook-icon.png",
                "website": "https://www.facebook.com",
                "status": "active",
                "features": ["post", "video", "live", "comment"],
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "twitter",
                "name": "Twitter",
                "icon": "twitter-icon.png",
                "website": "https://twitter.com",
                "status": "active",
                "features": ["post", "comment", "retweet"],
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "instagram",
                "name": "Instagram",
                "icon": "instagram-icon.png",
                "website": "https://www.instagram.com",
                "status": "active",
                "features": ["post", "story", "comment"],
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            }
        ]

        for platform in platforms:
            try:
                # 使用upsert确保不会重复插入
                self.db.social_platforms.update_one(
                    {"id": platform["id"]},
                    {"$set": platform},
                    upsert=True
                )
                logger.info(f"已添加/更新平台: {platform['name']}")
            except Exception as e:
                logger.error(f"添加平台 {platform['name']} 失败: {str(e)}")

        logger.info("社交媒体平台初始化完成")
        
        # 初始化平台应用
        self.init_platform_apps()

    def init_platform_apps(self) -> None:
        """初始化平台应用数据"""
        apps = [
            {
                "id": "youtube_android",
                "platform_id": "youtube",
                "name": "YouTube Android App",
                "type": "android",
                "status": "active",
                "version": "18.20.38",
                "app_info": {
                    "package_name": "com.google.android.youtube",
                    "main_activity": "com.google.android.youtube.HomeActivity",
                    "min_android_version": "8.0",
                    "download_url": "https://play.google.com/store/apps/details?id=com.google.android.youtube"
                },
                "automation": {
                    "type": "appium",
                    "selectors": {
                        "login_button": "com.google.android.youtube:id/sign_in_button",
                        "post_button": "com.google.android.youtube:id/upload_button",
                        "comment_button": "com.google.android.youtube:id/comment_button",
                        "like_button": "com.google.android.youtube:id/like_button",
                        "share_button": "com.google.android.youtube:id/share_button"
                    },
                    "commands": {
                        "start_app": "adb shell am start -n com.google.android.youtube/com.google.android.youtube.HomeActivity",
                        "stop_app": "adb shell am force-stop com.google.android.youtube",
                        "clear_data": "adb shell pm clear com.google.android.youtube"
                    }
                },
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "youtube_web",
                "platform_id": "youtube",
                "name": "YouTube Web",
                "type": "web",
                "status": "active",
                "version": "latest",
                "app_info": {
                    "download_url": "https://www.youtube.com"
                },
                "automation": {
                    "type": "selenium",
                    "selectors": {
                        "login_button": "//a[contains(@href, 'signin')]",
                        "post_button": "//a[contains(@href, 'upload')]",
                        "comment_button": "//div[@id='comment-section']",
                        "like_button": "//button[@aria-label='Like']",
                        "share_button": "//button[@aria-label='Share']"
                    }
                },
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            },
            {
                "id": "facebook_android",
                "platform_id": "facebook",
                "name": "Facebook Android App",
                "type": "android",
                "status": "active",
                "version": "latest",
                "app_info": {
                    "package_name": "com.facebook.katana",
                    "main_activity": "com.facebook.katana.LoginActivity",
                    "min_android_version": "8.0",
                    "download_url": "https://play.google.com/store/apps/details?id=com.facebook.katana"
                },
                "automation": {
                    "type": "appium",
                    "selectors": {
                        "login_button": "com.facebook.katana:id/login_button",
                        "post_button": "com.facebook.katana:id/composer_button",
                        "comment_button": "com.facebook.katana:id/comment_button",
                        "like_button": "com.facebook.katana:id/like_button",
                        "share_button": "com.facebook.katana:id/share_button"
                    },
                    "commands": {
                        "start_app": "adb shell am start -n com.facebook.katana/com.facebook.katana.LoginActivity",
                        "stop_app": "adb shell am force-stop com.facebook.katana",
                        "clear_data": "adb shell pm clear com.facebook.katana"
                    }
                },
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now()
            }
        ]

        for app in apps:
            try:
                # 使用upsert确保不会重复插入
                self.db.platform_apps.update_one(
                    {"id": app["id"]},
                    {"$set": app},
                    upsert=True
                )
                logger.info(f"已添加/更新应用: {app['name']}")
            except Exception as e:
                logger.error(f"添加应用 {app['name']} 失败: {str(e)}")

        logger.info("平台应用初始化完成")
