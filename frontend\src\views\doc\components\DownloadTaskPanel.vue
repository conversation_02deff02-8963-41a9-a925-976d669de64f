<template>
  <div class="download-task-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3>下载任务</h3>
      <div class="header-actions">
        <el-button size="small" @click="refreshTasks">
          <el-icon><Refresh /></el-icon>
        </el-button>
        <el-button size="small" @click="$emit('close')">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 任务统计 -->
    <div class="task-stats">
      <div class="stat-item">
        <span class="label">总任务</span>
        <span class="value">{{ tasks.length }}</span>
      </div>
      <div class="stat-item">
        <span class="label">运行中</span>
        <span class="value running">{{ runningCount }}</span>
      </div>
      <div class="stat-item">
        <span class="label">已完成</span>
        <span class="value completed">{{ completedCount }}</span>
      </div>
      <div class="stat-item">
        <span class="label">失败</span>
        <span class="value failed">{{ failedCount }}</span>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list" v-loading="loading">
      <div 
        v-for="task in tasks" 
        :key="task._id"
        class="task-item"
        :class="`status-${task.status}`"
      >
        <div class="task-header">
          <div class="task-info">
            <h4 class="task-name">{{ task.task_name }}</h4>
            <div class="task-meta">
              <el-tag size="small" :type="getStatusType(task.status)">
                {{ getStatusLabel(task.status) }}
              </el-tag>
              <span class="task-type">{{ getTaskTypeLabel(task.task_type) }}</span>
              <span class="task-platform">{{ task.target_platform }}</span>
            </div>
          </div>
          
          <div class="task-actions">
            <el-dropdown @command="(cmd) => handleTaskAction(cmd, task)">
              <el-button size="small" text>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="detail">查看详情</el-dropdown-item>
                  <el-dropdown-item 
                    command="cancel" 
                    v-if="task.status === 'running' || task.status === 'pending'"
                  >
                    取消任务
                  </el-dropdown-item>
                  <el-dropdown-item 
                    command="retry" 
                    v-if="task.status === 'failed'"
                  >
                    重试任务
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 进度条 -->
        <div class="task-progress" v-if="task.status === 'running' || task.status === 'pending'">
          <el-progress 
            :percentage="task.progress.percentage"
            :status="task.status === 'failed' ? 'exception' : undefined"
            :stroke-width="6"
          />
          <div class="progress-info">
            <span>{{ task.progress.completed_items }}/{{ task.progress.total_items }}</span>
            <span v-if="task.progress.current_item" class="current-item">
              正在处理: {{ task.progress.current_item }}
            </span>
          </div>
        </div>

        <!-- 任务结果 -->
        <div class="task-result" v-if="task.result && task.status === 'completed'">
          <div class="result-stats">
            <span class="success">成功: {{ task.result.success_count }}</span>
            <span class="failed" v-if="task.result.failed_count > 0">
              失败: {{ task.result.failed_count }}
            </span>
            <span class="size">大小: {{ formatFileSize(task.result.total_size) }}</span>
          </div>
        </div>

        <!-- 失败信息 -->
        <div class="task-error" v-if="task.status === 'failed' && task.result?.failed_urls">
          <el-collapse>
            <el-collapse-item title="失败详情" name="error">
              <div class="failed-urls">
                <div v-for="url in task.result.failed_urls.slice(0, 5)" :key="url" class="failed-url">
                  {{ url }}
                </div>
                <div v-if="task.result.failed_urls.length > 5" class="more-failed">
                  还有 {{ task.result.failed_urls.length - 5 }} 个失败项目...
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 任务时间 -->
        <div class="task-time">
          <span>创建时间: {{ formatDate(task.created_at) }}</span>
          <span v-if="task.updated_at !== task.created_at">
            更新时间: {{ formatDate(task.updated_at) }}
          </span>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty v-if="!loading && tasks.length === 0" description="暂无下载任务" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, 
  Close, 
  MoreFilled 
} from '@element-plus/icons-vue'
import { 
  getDownloadTasks, 
  cancelDownloadTask, 
  retryDownloadTask,
  type DownloadTask 
} from '@/api/content'

const emit = defineEmits<{
  close: []
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const tasks = ref<DownloadTask[]>([])

// 定时刷新
let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const runningCount = computed(() => 
  tasks.value.filter(task => task.status === 'running' || task.status === 'pending').length
)

const completedCount = computed(() => 
  tasks.value.filter(task => task.status === 'completed').length
)

const failedCount = computed(() => 
  tasks.value.filter(task => task.status === 'failed').length
)

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    const response = await getDownloadTasks({ limit: 50 })
    tasks.value = response
  } catch (error) {
    console.error('加载下载任务失败:', error)
    ElMessage.error('加载下载任务失败')
  } finally {
    loading.value = false
  }
}

const refreshTasks = () => {
  loadTasks()
  emit('refresh')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const types = {
    pending: 'info',
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'warning'
  }
  return types[status as keyof typeof types] || 'info'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return labels[status as keyof typeof labels] || status
}

// 获取任务类型标签
const getTaskTypeLabel = (type: string) => {
  const labels = {
    single: '单个下载',
    batch: '批量下载',
    channel: '频道下载',
    playlist: '播放列表'
  }
  return labels[type as keyof typeof labels] || type
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 处理任务操作
const handleTaskAction = async (command: string, task: DownloadTask) => {
  try {
    switch (command) {
      case 'detail':
        // TODO: 显示任务详情对话框
        break
        
      case 'cancel':
        await ElMessageBox.confirm('确定要取消这个任务吗？', '确认取消', {
          type: 'warning'
        })
        await cancelDownloadTask(task._id!)
        ElMessage.success('任务已取消')
        loadTasks()
        break
        
      case 'retry':
        await ElMessageBox.confirm('确定要重试这个任务吗？', '确认重试', {
          type: 'info'
        })
        await retryDownloadTask(task._id!)
        ElMessage.success('任务已重新开始')
        loadTasks()
        break
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('任务操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 生命周期
onMounted(() => {
  loadTasks()
  
  // 设置定时刷新
  refreshTimer = setInterval(() => {
    if (runningCount.value > 0) {
      loadTasks()
    }
  }, 3000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.download-task-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafbfc;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.task-stats {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-item .label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-item .value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.stat-item .value.running {
  color: #409eff;
}

.stat-item .value.completed {
  color: #67c23a;
}

.stat-item .value.failed {
  color: #f56c6c;
}

.task-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.task-item {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.2s;
}

.task-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-item.status-running {
  border-left: 4px solid #409eff;
}

.task-item.status-completed {
  border-left: 4px solid #67c23a;
}

.task-item.status-failed {
  border-left: 4px solid #f56c6c;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.task-name {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.task-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.task-type,
.task-platform {
  font-size: 12px;
  color: #909399;
}

.task-progress {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

.current-item {
  color: #409eff;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-result {
  margin-bottom: 12px;
}

.result-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.result-stats .success {
  color: #67c23a;
}

.result-stats .failed {
  color: #f56c6c;
}

.result-stats .size {
  color: #606266;
}

.task-error {
  margin-bottom: 12px;
}

.failed-urls {
  max-height: 120px;
  overflow-y: auto;
}

.failed-url {
  font-size: 12px;
  color: #f56c6c;
  margin-bottom: 4px;
  word-break: break-all;
}

.more-failed {
  font-size: 12px;
  color: #909399;
  font-style: italic;
}

.task-time {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #909399;
  border-top: 1px solid #f0f2f5;
  padding-top: 8px;
}

/* 滚动条样式 */
.task-list::-webkit-scrollbar {
  width: 6px;
}

.task-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
