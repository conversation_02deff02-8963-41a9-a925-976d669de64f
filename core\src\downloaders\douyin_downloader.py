"""
抖音下载器
基于工作流的抖音内容下载器
"""

import os
import re
import json
import asyncio
import aiohttp
import logging
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse, parse_qs

from .base_downloader import BaseDownloader

logger = logging.getLogger(__name__)


class DouyinDownloader(BaseDownloader):
    """抖音下载器类"""

    def __init__(self, download_path: str):
        """初始化抖音下载器
        
        Args:
            download_path: 下载路径
        """
        super().__init__("douyin", download_path)
        
        # 抖音API相关配置
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.douyin.com/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        
        # 会话对象
        self.session: Optional[aiohttp.ClientSession] = None

    async def _init_session(self):
        """初始化HTTP会话"""
        if not self.session:
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                headers=self.headers,
                connector=connector,
                timeout=timeout
            )

    async def _close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None

    def extract_user_id(self, account_url: str) -> str:
        """从URL提取用户ID
        
        Args:
            account_url: 账号URL
            
        Returns:
            str: 用户ID
        """
        try:
            # 支持多种URL格式
            # https://www.douyin.com/user/MS4wLjABAAAA...
            # https://v.douyin.com/xxx/
            
            if '/user/' in account_url:
                user_id = account_url.split('/user/')[-1].split('?')[0]
                return user_id
            elif 'v.douyin.com' in account_url:
                # 短链接需要解析
                return self._resolve_short_url(account_url)
            else:
                raise ValueError(f"不支持的URL格式: {account_url}")
                
        except Exception as e:
            logger.error(f"提取用户ID失败: {str(e)}")
            return ""

    def _resolve_short_url(self, short_url: str) -> str:
        """解析短链接获取用户ID
        
        Args:
            short_url: 短链接
            
        Returns:
            str: 用户ID
        """
        # TODO: 实现短链接解析逻辑
        # 这里需要发送HTTP请求获取重定向后的真实URL
        logger.warning(f"短链接解析暂未实现: {short_url}")
        return ""

    async def get_account_info(self, account_url: str) -> Dict[str, Any]:
        """获取账号信息
        
        Args:
            account_url: 账号URL
            
        Returns:
            Dict: 账号信息
        """
        try:
            await self._init_session()
            
            user_id = self.extract_user_id(account_url)
            if not user_id:
                raise ValueError("无法提取用户ID")
            
            self.add_log(f"获取账号信息: {user_id}")
            
            # 构建API URL
            api_url = f"https://www.douyin.com/aweme/v1/web/aweme/profile/?sec_uid={user_id}"
            
            async with self.session.get(api_url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 解析账号信息
                    user_info = data.get('user_info', {})
                    account_info = {
                        'user_id': user_id,
                        'nickname': user_info.get('nickname', ''),
                        'signature': user_info.get('signature', ''),
                        'follower_count': user_info.get('follower_count', 0),
                        'following_count': user_info.get('following_count', 0),
                        'aweme_count': user_info.get('aweme_count', 0),
                        'avatar_url': user_info.get('avatar_larger', {}).get('url_list', [''])[0],
                        'verification_type': user_info.get('verification_type', 0),
                        'custom_verify': user_info.get('custom_verify', ''),
                    }
                    
                    self.add_log(f"账号信息获取成功: {account_info['nickname']}")
                    return account_info
                else:
                    raise Exception(f"API请求失败: {response.status}")
                    
        except Exception as e:
            error_msg = f"获取账号信息失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return {}

    async def get_video_list(
        self, 
        account_url: str, 
        max_count: int = 50,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """获取视频列表
        
        Args:
            account_url: 账号URL
            max_count: 最大数量
            filters: 过滤条件
            
        Returns:
            List: 视频列表
        """
        try:
            await self._init_session()
            
            user_id = self.extract_user_id(account_url)
            if not user_id:
                raise ValueError("无法提取用户ID")
            
            self.add_log(f"获取视频列表: {user_id}, 最大数量: {max_count}")
            
            videos = []
            max_cursor = 0
            page_size = 20  # 每页数量
            
            while len(videos) < max_count:
                # 构建API URL
                api_url = f"https://www.douyin.com/aweme/v1/web/aweme/post/?sec_uid={user_id}&max_cursor={max_cursor}&count={page_size}"
                
                async with self.session.get(api_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        aweme_list = data.get('aweme_list', [])
                        if not aweme_list:
                            break
                        
                        for aweme in aweme_list:
                            if len(videos) >= max_count:
                                break
                                
                            video_info = self._parse_video_info(aweme)
                            
                            # 应用过滤条件
                            if self._apply_filters(video_info, filters):
                                videos.append(video_info)
                        
                        # 检查是否还有更多数据
                        if not data.get('has_more', False):
                            break
                            
                        max_cursor = data.get('max_cursor', 0)
                        
                        # 避免请求过快
                        await asyncio.sleep(1)
                    else:
                        raise Exception(f"API请求失败: {response.status}")
            
            self.add_log(f"获取到 {len(videos)} 个视频")
            return videos[:max_count]
            
        except Exception as e:
            error_msg = f"获取视频列表失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return []

    def _parse_video_info(self, aweme: Dict[str, Any]) -> Dict[str, Any]:
        """解析视频信息
        
        Args:
            aweme: 抖音视频数据
            
        Returns:
            Dict: 解析后的视频信息
        """
        try:
            video_info = {
                'id': aweme.get('aweme_id', ''),
                'title': aweme.get('desc', ''),
                'create_time': aweme.get('create_time', 0),
                'duration': aweme.get('duration', 0),
                'view_count': aweme.get('statistics', {}).get('play_count', 0),
                'like_count': aweme.get('statistics', {}).get('digg_count', 0),
                'comment_count': aweme.get('statistics', {}).get('comment_count', 0),
                'share_count': aweme.get('statistics', {}).get('share_count', 0),
                'download_count': aweme.get('statistics', {}).get('download_count', 0),
                'video_url': '',
                'cover_url': '',
                'author': {
                    'uid': aweme.get('author', {}).get('uid', ''),
                    'nickname': aweme.get('author', {}).get('nickname', ''),
                    'signature': aweme.get('author', {}).get('signature', ''),
                }
            }
            
            # 获取视频下载链接
            video_urls = aweme.get('video', {}).get('play_addr', {}).get('url_list', [])
            if video_urls:
                video_info['video_url'] = video_urls[0]
            
            # 获取封面链接
            cover_urls = aweme.get('video', {}).get('cover', {}).get('url_list', [])
            if cover_urls:
                video_info['cover_url'] = cover_urls[0]
            
            return video_info
            
        except Exception as e:
            logger.error(f"解析视频信息失败: {str(e)}")
            return {}

    def _apply_filters(self, video_info: Dict[str, Any], filters: Optional[Dict[str, Any]]) -> bool:
        """应用过滤条件
        
        Args:
            video_info: 视频信息
            filters: 过滤条件
            
        Returns:
            bool: 是否通过过滤
        """
        if not filters:
            return True
        
        try:
            # 最小播放量过滤
            min_views = filters.get('min_views')
            if min_views and video_info.get('view_count', 0) < min_views:
                return False
            
            # 最小点赞数过滤
            min_likes = filters.get('min_likes')
            if min_likes and video_info.get('like_count', 0) < min_likes:
                return False
            
            # 关键词过滤
            keywords = filters.get('keywords', [])
            if keywords:
                title = video_info.get('title', '').lower()
                if not any(keyword.lower() in title for keyword in keywords):
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"应用过滤条件失败: {str(e)}")
            return True

    async def download_video(
        self, 
        video_info: Dict[str, Any], 
        save_path: str
    ) -> bool:
        """下载单个视频
        
        Args:
            video_info: 视频信息
            save_path: 保存路径
            
        Returns:
            bool: 是否下载成功
        """
        try:
            video_url = video_info.get('video_url')
            if not video_url:
                raise ValueError("视频下载链接为空")
            
            self.add_log(f"开始下载视频: {video_info.get('title', 'Unknown')}")
            
            await self._init_session()
            
            async with self.session.get(video_url) as response:
                if response.status == 200:
                    # 创建目录
                    os.makedirs(os.path.dirname(save_path), exist_ok=True)
                    
                    # 下载文件
                    with open(save_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                    
                    # 验证文件
                    if os.path.exists(save_path) and os.path.getsize(save_path) > 0:
                        self.add_log(f"视频下载成功: {save_path}")
                        return True
                    else:
                        raise Exception("下载的文件为空")
                else:
                    raise Exception(f"下载请求失败: {response.status}")
                    
        except Exception as e:
            error_msg = f"下载视频失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return False

    async def download_account_content(
        self, 
        account_url: str, 
        account_name: str,
        download_config: Dict[str, Any]
    ) -> bool:
        """下载账号内容
        
        Args:
            account_url: 账号URL
            account_name: 账号名称
            download_config: 下载配置
            
        Returns:
            bool: 是否下载成功
        """
        try:
            self.set_status("downloading", f"开始下载 {account_name} 的内容")
            self.update_progress(0, "初始化下载器")
            
            # 获取账号信息
            account_info = await self.get_account_info(account_url)
            if not account_info:
                raise Exception("获取账号信息失败")
            
            self.update_progress(10, "获取视频列表")
            
            # 获取视频列表
            max_videos = download_config.get('max_videos', 50)
            filters = {
                'min_views': download_config.get('min_views'),
                'min_likes': download_config.get('min_likes'),
                'keywords': download_config.get('keywords', [])
            }
            
            videos = await self.get_video_list(account_url, max_videos, filters)
            if not videos:
                raise Exception("未获取到视频列表")
            
            self.total_count = len(videos)
            self.update_progress(20, f"找到 {self.total_count} 个视频")
            
            # 创建下载目录结构
            videos_dir = os.path.join(self.download_path, "videos")
            metadata_dir = os.path.join(self.download_path, "metadata")
            os.makedirs(videos_dir, exist_ok=True)
            os.makedirs(metadata_dir, exist_ok=True)
            
            # 保存账号信息
            account_info_path = os.path.join(self.download_path, "account_info.json")
            with open(account_info_path, 'w', encoding='utf-8') as f:
                json.dump(account_info, f, ensure_ascii=False, indent=2)
            
            # 下载视频
            naming_rule = download_config.get('naming_rule', 'timestamp')
            include_metadata = download_config.get('include_metadata', True)
            skip_existing = download_config.get('skip_existing', True)
            
            for i, video_info in enumerate(videos):
                try:
                    # 生成文件名
                    filename = self.generate_filename(video_info, naming_rule)
                    video_path = os.path.join(videos_dir, filename)
                    
                    # 检查是否跳过已存在的文件
                    if skip_existing and self.is_file_exists(video_path):
                        self.add_log(f"跳过已存在的文件: {filename}")
                        self.downloaded_count += 1
                        continue
                    
                    # 下载视频
                    success = await self.download_video(video_info, video_path)
                    if success:
                        self.downloaded_count += 1
                        
                        # 保存元数据
                        if include_metadata:
                            self.save_metadata(video_info, video_path)
                    
                    # 更新进度
                    progress = 20 + int((i + 1) / len(videos) * 70)
                    self.update_progress(progress, f"已下载 {self.downloaded_count}/{self.total_count}")
                    
                    # 避免请求过快
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    self.add_log(f"下载视频失败: {video_info.get('title', 'Unknown')} - {str(e)}")
                    continue
            
            # 生成下载报告
            self._generate_download_report(account_info, videos)
            
            self.update_progress(100, f"下载完成，成功下载 {self.downloaded_count} 个视频")
            self.set_status("completed", "下载任务完成")
            
            return True
            
        except Exception as e:
            error_msg = f"下载账号内容失败: {str(e)}"
            self.set_status("error", error_msg)
            self.error_message = error_msg
            logger.error(error_msg)
            return False
        finally:
            await self._close_session()

    def _generate_download_report(self, account_info: Dict[str, Any], videos: List[Dict[str, Any]]):
        """生成下载报告
        
        Args:
            account_info: 账号信息
            videos: 视频列表
        """
        try:
            report = {
                'download_time': datetime.now().isoformat(),
                'account_info': account_info,
                'total_videos': len(videos),
                'downloaded_count': self.downloaded_count,
                'success_rate': f"{(self.downloaded_count / len(videos) * 100):.1f}%" if videos else "0%",
                'download_path': self.download_path,
                'platform': self.platform,
                'logs': self.logs[-50:]  # 最近50条日志
            }
            
            report_path = os.path.join(self.download_path, "download_report.json")
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.add_log(f"下载报告已生成: {report_path}")
            
        except Exception as e:
            self.add_log(f"生成下载报告失败: {str(e)}")

    async def cleanup(self):
        """清理资源"""
        await self._close_session()
        await super().cleanup()
