@echo off
echo 正在安装数据库初始化工具所需的依赖...

REM 检查Python环境
python --version 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到Python，请安装Python 3.7或更高版本
    exit /b 1
)

REM 检查虚拟环境
if exist "..\venv\" (
    echo 使用已存在的虚拟环境
    call ..\venv\Scripts\activate
) else (
    echo 创建新的虚拟环境
    python -m venv ..\venv
    call ..\venv\Scripts\activate
)

REM 安装依赖
echo 安装依赖包...
pip install python-dotenv pymongo redis python-consul

echo 依赖安装完成！
echo 现在您可以运行 "python init_database.py" 来初始化数据库

pause
