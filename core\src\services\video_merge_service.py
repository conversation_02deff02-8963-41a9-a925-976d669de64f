"""
视频合并服务
负责批量视频合并功能
"""

import os
import logging
import asyncio
import random
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class VideoMergeService:
    """视频合并服务类"""

    def __init__(self):
        """初始化视频合并服务"""
        self.transition_effects = [
            'fade', 'fadeblack', 'fadewhite', 'distance', 'wipeleft', 'wiperight',
            'wipeup', 'wipedown', 'slideleft', 'slideright', 'slideup', 'slidedown',
            'circlecrop', 'rectcrop', 'circleopen', 'circleclose', 'vertopen', 'vertclose',
            'horzopen', 'horzclose', 'dissolve', 'pixelize', 'radial', 'smoothleft',
            'smoothright', 'smoothup', 'smoothdown'
        ]
        logger.info("视频合并服务初始化完成")

    def scan_video_files(self, folder_path: str) -> List[Dict[str, Any]]:
        """扫描文件夹中的视频文件"""
        try:
            video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
            video_files = []

            if not os.path.exists(folder_path):
                logger.error(f"文件夹不存在: {folder_path}")
                return []

            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)
                
                if os.path.isfile(file_path):
                    file_ext = os.path.splitext(file_name)[1].lower()
                    
                    if file_ext in video_extensions:
                        # 获取视频时长
                        duration = self.get_video_duration(file_path)
                        if duration > 0:
                            video_files.append({
                                'name': file_name,
                                'path': file_path,
                                'duration': duration,
                                'size': os.path.getsize(file_path)
                            })

            logger.info(f"扫描到 {len(video_files)} 个视频文件")
            return sorted(video_files, key=lambda x: x['name'])

        except Exception as e:
            logger.error(f"扫描视频文件异常: {str(e)}", exc_info=True)
            return []

    def get_video_duration(self, video_path: str) -> float:
        """获取视频时长"""
        try:
            import subprocess
            import json

            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', video_path
            ]

            # 修复编码问题：明确指定UTF-8编码
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',  # 忽略编码错误
                timeout=30
            )

            if result.returncode == 0 and result.stdout:
                try:
                    data = json.loads(result.stdout)
                    if 'format' in data and 'duration' in data['format']:
                        duration = float(data['format']['duration'])
                        logger.debug(f"获取视频时长成功: {video_path} = {duration:.1f}秒")
                        return duration
                    else:
                        logger.warning(f"ffprobe输出格式异常: {video_path}")
                        return 0.0
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    logger.warning(f"解析ffprobe输出失败: {video_path}, {str(e)}")
                    return 0.0
            else:
                error_msg = result.stderr if result.stderr else "未知错误"
                logger.warning(f"ffprobe执行失败: {video_path}, 错误: {error_msg}")
                return 0.0

        except subprocess.TimeoutExpired:
            logger.warning(f"获取视频时长超时: {video_path}")
            return 0.0
        except Exception as e:
            logger.warning(f"获取视频时长异常: {video_path}, {str(e)}")
            return 0.0

    def get_video_resolution(self, video_path: str) -> tuple:
        """获取视频分辨率"""
        try:
            import subprocess
            import json

            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_streams', '-select_streams', 'v:0', video_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=30
            )

            if result.returncode == 0 and result.stdout:
                try:
                    data = json.loads(result.stdout)
                    if 'streams' in data and len(data['streams']) > 0:
                        stream = data['streams'][0]
                        width = int(stream.get('width', 0))
                        height = int(stream.get('height', 0))
                        logger.debug(f"获取视频分辨率成功: {video_path} = {width}x{height}")
                        return (width, height)
                    else:
                        logger.warning(f"ffprobe未找到视频流: {video_path}")
                        return (0, 0)
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    logger.warning(f"解析ffprobe分辨率输出失败: {video_path}, {str(e)}")
                    return (0, 0)
            else:
                error_msg = result.stderr if result.stderr else "未知错误"
                logger.warning(f"ffprobe获取分辨率失败: {video_path}, 错误: {error_msg}")
                return (0, 0)

        except subprocess.TimeoutExpired:
            logger.warning(f"获取视频分辨率超时: {video_path}")
            return (0, 0)
        except Exception as e:
            logger.warning(f"获取视频分辨率异常: {video_path}, {str(e)}")
            return (0, 0)

    def group_videos_by_duration(self, video_files: List[Dict[str, Any]], 
                                target_min: int, target_max: int, 
                                max_videos_per_group: int) -> List[List[Dict[str, Any]]]:
        """根据时长将视频分组"""
        try:
            groups = []
            current_group = []
            current_duration = 0.0

            for video in video_files:
                video_duration = video['duration']
                
                # 检查是否可以添加到当前组
                if (len(current_group) < max_videos_per_group and
                    current_duration + video_duration <= target_max):

                    current_group.append(video)
                    current_duration += video_duration

                    logger.debug(f"添加视频到当前组: {video['name']}, 组内视频数: {len(current_group)}, 总时长: {current_duration:.1f}秒")

                else:
                    # 当前组已满或添加会超时，结束当前组
                    if current_group and current_duration >= target_min:
                        groups.append(current_group)
                        logger.info(f"完成第 {len(groups)} 组，包含 {len(current_group)} 个视频，总时长: {current_duration:.1f}秒")
                    elif current_group:
                        logger.warning(f"当前组时长不足 {target_min}秒，但仍保留: {current_duration:.1f}秒")
                        groups.append(current_group)

                    # 开始新组
                    current_group = [video]
                    current_duration = video_duration
                    logger.debug(f"开始新组，首个视频: {video['name']}, 时长: {video_duration:.1f}秒")

            # 处理最后一组
            if current_group:
                if current_duration >= target_min:
                    groups.append(current_group)
                elif groups:
                    # 如果最后一组时长不够，合并到前一组
                    groups[-1].extend(current_group)
                else:
                    # 如果只有一组且时长不够，仍然保留
                    groups.append(current_group)

            logger.info(f"视频分组完成，共 {len(groups)} 组")
            return groups

        except Exception as e:
            logger.error(f"视频分组异常: {str(e)}", exc_info=True)
            return []

    async def merge_video_group(self, video_group: List[Dict[str, Any]],
                              output_path: str, group_index: int,
                              enable_transitions: bool, output_quality: str) -> bool:
        """合并一组视频"""
        try:
            logger.info(f"开始合并第 {group_index + 1} 组视频，包含 {len(video_group)} 个文件")

            # 如果只有一个视频，直接复制
            if len(video_group) == 1:
                import shutil
                shutil.copy2(video_group[0]['path'], output_path)
                logger.info(f"单个视频直接复制: {output_path}")
                return True

            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y']  # -y 覆盖输出文件

            # 添加输入文件
            for video in video_group:
                cmd.extend(['-i', video['path']])

            # 构建复杂滤镜
            filter_complex = []

            # 多个视频处理
            logger.info(f"多个视频处理，共 {len(video_group)} 个视频，启用转场: {enable_transitions}")

            if enable_transitions and len(video_group) > 1:
                # 使用转场特效 - 现在分辨率已统一，可以安全使用
                logger.info("使用转场特效合并视频")

                # 获取第一个视频的分辨率作为目标分辨率
                first_video_path = video_group[0]['path']
                target_resolution = self.get_video_resolution(first_video_path)

                if target_resolution == (0, 0):
                    logger.warning("无法获取视频分辨率，使用默认分辨率")
                    target_width, target_height = 1080, 1920  # 默认竖屏分辨率
                else:
                    target_width, target_height = target_resolution

                logger.info(f"转场特效统一视频分辨率为: {target_width}x{target_height}")

                # 预处理每个视频：统一分辨率、帧率和音频格式
                for i in range(len(video_group)):
                    # 统一分辨率和帧率（保持宽高比，用黑边填充）
                    filter_complex.append(
                        f"[{i}:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,"
                        f"pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black,fps=30[v{i}]"
                    )
                    filter_complex.append(
                        f"[{i}:a]aresample=44100,aformat=sample_fmts=fltp:channel_layouts=stereo[a{i}]"
                    )

                # 使用改进的转场特效实现
                logger.info("开始构建转场特效滤镜")

                # 分类的转场特效库
                transition_categories = {
                    'gentle': [  # 温和的转场
                        'fade', 'fadeblack', 'fadewhite', 'fadegrays', 'dissolve'
                    ],
                    'dynamic': [  # 动态的转场
                        'wipeleft', 'wiperight', 'wipeup', 'wipedown',
                        'slideleft', 'slideright', 'slideup', 'slidedown'
                    ],
                    'creative': [  # 创意转场
                        'circleopen', 'circleclose', 'pixelize',
                        'diagtl', 'diagtr', 'diagbl', 'diagbr'
                    ],
                    'advanced': [  # 高级转场
                        'wipetl', 'wipetr', 'wipebl', 'wipebr',
                        'vertopen', 'vertclose', 'horzopen', 'horzclose',
                        'hlslice', 'hrslice', 'vuslice', 'vdslice',
                        'smoothleft', 'smoothright', 'smoothup', 'smoothdown'
                    ]
                }

                # 合并所有转场特效
                all_transitions = []
                for category, effects in transition_categories.items():
                    all_transitions.extend(effects)

                logger.info(f"转场特效库包含 {len(all_transitions)} 种特效，分为 {len(transition_categories)} 个类别")

                # 为每个类别设置权重（温和转场概率更高，保证稳定性）
                category_weights = {
                    'gentle': 0.4,    # 40% 概率选择温和转场
                    'dynamic': 0.3,   # 30% 概率选择动态转场
                    'creative': 0.2,  # 20% 概率选择创意转场
                    'advanced': 0.1   # 10% 概率选择高级转场
                }

                # 计算每个视频的时长和累积时长
                video_durations = []
                for video in video_group:
                    duration = video.get('duration', 0)
                    if duration <= 0:
                        duration = self.get_video_duration(video['path'])
                    video_durations.append(duration)

                logger.info(f"视频时长列表: {video_durations}")

                # 构建转场链
                current_video = "v0"
                current_audio = "a0"
                cumulative_duration = video_durations[0]

                for i in range(1, len(video_group)):
                    import random

                    # 智能选择转场特效（基于权重）
                    rand_val = random.random()
                    cumulative_weight = 0
                    selected_category = 'gentle'  # 默认选择温和转场

                    for category, weight in category_weights.items():
                        cumulative_weight += weight
                        if rand_val <= cumulative_weight:
                            selected_category = category
                            break

                    # 从选中的类别中随机选择一个转场特效
                    transition_effect = random.choice(transition_categories[selected_category])

                    logger.info(f"选择转场类别: {selected_category}, 特效: {transition_effect}")

                    # 动态调整转场时长（0.5-2秒之间，不超过视频时长的15%）
                    max_duration_by_video = min(video_durations[i-1] * 0.15, video_durations[i] * 0.15)
                    transition_duration = min(random.uniform(0.5, 2.0), max_duration_by_video)
                    transition_duration = max(0.5, transition_duration)  # 最少0.5秒

                    # 计算转场开始时间（在前一个视频结束前开始）
                    offset = cumulative_duration - transition_duration

                    logger.info(f"视频 {i-1} → {i}: 转场={transition_effect}, 时长={transition_duration:.1f}s, 偏移={offset:.1f}s")

                    # 视频转场 - 添加正确的offset参数
                    filter_complex.append(
                        f"[{current_video}][v{i}]xfade=transition={transition_effect}:duration={transition_duration}:offset={offset}[v{i}_trans]"
                    )

                    # 音频交叉淡入淡出
                    filter_complex.append(
                        f"[{current_audio}][a{i}]acrossfade=d={transition_duration}[a{i}_trans]"
                    )

                    current_video = f"v{i}_trans"
                    current_audio = f"a{i}_trans"

                    # 更新累积时长（减去转场重叠的部分）
                    cumulative_duration += video_durations[i] - transition_duration

                # 添加滤镜到命令
                cmd.extend(['-filter_complex', ';'.join(filter_complex)])
                cmd.extend(['-map', f'[{current_video}]', '-map', f'[{current_audio}]'])

            else:
                # 简单拼接，不使用转场特效 - 统一分辨率确保兼容性
                logger.info("使用简单拼接合并视频")

                # 获取第一个视频的分辨率作为目标分辨率
                first_video_path = video_group[0]['path']
                target_resolution = self.get_video_resolution(first_video_path)

                if target_resolution == (0, 0):
                    logger.warning("无法获取视频分辨率，使用默认分辨率")
                    target_width, target_height = 1080, 1920  # 默认竖屏分辨率
                else:
                    target_width, target_height = target_resolution

                logger.info(f"简单拼接统一视频分辨率为: {target_width}x{target_height}")

                # 预处理每个视频：统一分辨率、帧率和音频格式
                for i in range(len(video_group)):
                    # 统一分辨率和帧率（保持宽高比，用黑边填充）
                    filter_complex.append(
                        f"[{i}:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,"
                        f"pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black,fps=30[v{i}]"
                    )
                    filter_complex.append(
                        f"[{i}:a]aresample=44100,aformat=sample_fmts=fltp:channel_layouts=stereo[a{i}]"
                    )

                # 拼接所有视频和音频
                video_inputs = ''.join([f'[v{i}]' for i in range(len(video_group))])
                audio_inputs = ''.join([f'[a{i}]' for i in range(len(video_group))])

                filter_complex.append(f"{video_inputs}concat=n={len(video_group)}:v=1:a=0[outv]")
                filter_complex.append(f"{audio_inputs}concat=n={len(video_group)}:v=0:a=1[outa]")

                # 添加滤镜到命令
                cmd.extend(['-filter_complex', ';'.join(filter_complex)])
                cmd.extend(['-map', '[outv]', '-map', '[outa]'])

            # 设置YouTube兼容的输出质量和编码参数
            if output_quality == "high":
                cmd.extend([
                    '-c:v', 'libx264',           # H.264视频编码
                    '-preset', 'medium',         # 编码预设
                    '-crf', '18',               # 高质量CRF值
                    '-profile:v', 'high',       # H.264 High Profile
                    '-level', '4.0',            # H.264 Level 4.0
                    '-pix_fmt', 'yuv420p'       # 像素格式（YouTube兼容）
                ])
            elif output_quality == "medium":
                cmd.extend([
                    '-c:v', 'libx264',           # H.264视频编码
                    '-preset', 'fast',           # 编码预设
                    '-crf', '23',               # 中等质量CRF值
                    '-profile:v', 'high',       # H.264 High Profile
                    '-level', '4.0',            # H.264 Level 4.0
                    '-pix_fmt', 'yuv420p'       # 像素格式（YouTube兼容）
                ])
            else:  # low
                cmd.extend([
                    '-c:v', 'libx264',           # H.264视频编码
                    '-preset', 'fast',           # 编码预设
                    '-crf', '28',               # 低质量CRF值
                    '-profile:v', 'main',       # H.264 Main Profile（兼容性更好）
                    '-level', '3.1',            # H.264 Level 3.1
                    '-pix_fmt', 'yuv420p'       # 像素格式（YouTube兼容）
                ])

            # 设置YouTube兼容的音频编码
            cmd.extend([
                '-c:a', 'aac',              # AAC音频编码
                '-b:a', '128k',             # 音频比特率
                '-ar', '44100',             # 采样率
                '-ac', '2'                  # 立体声
            ])

            # 添加YouTube优化参数
            cmd.extend([
                '-movflags', '+faststart',   # 优化流媒体播放
                '-f', 'mp4'                 # 强制MP4格式
            ])

            cmd.append(output_path)

            # 记录完整的ffmpeg命令用于调试
            cmd_str = ' '.join([f'"{arg}"' if ' ' in arg else arg for arg in cmd])
            logger.info(f"执行ffmpeg命令: {cmd_str}")

            # 执行ffmpeg命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            # 详细的错误处理和日志
            stdout_str = stdout.decode('utf-8', errors='ignore') if stdout else ""
            stderr_str = stderr.decode('utf-8', errors='ignore') if stderr else ""

            logger.info(f"ffmpeg返回码: {process.returncode}")
            if stdout_str:
                logger.debug(f"ffmpeg stdout: {stdout_str}")
            if stderr_str:
                logger.info(f"ffmpeg stderr: {stderr_str}")

            # 检查输出文件是否真的生成了
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.info(f"输出文件大小: {file_size} 字节")

                if file_size == 0:
                    logger.error(f"生成的视频文件为0字节: {output_path}")
                    logger.error(f"ffmpeg错误信息: {stderr_str}")
                    return False
                elif file_size < 1024:  # 小于1KB可能也有问题
                    logger.warning(f"生成的视频文件很小: {file_size} 字节")

                logger.info(f"视频合并成功: {output_path} ({file_size} 字节)")
                return True
            else:
                logger.error(f"输出文件未生成: {output_path}")
                logger.error(f"ffmpeg错误信息: {stderr_str}")
                return False

        except Exception as e:
            logger.error(f"合并视频组异常: {str(e)}", exc_info=True)
            return False

    async def merge_videos(self, folder_path: str, target_duration_min: int, 
                          target_duration_max: int, enable_transitions: bool,
                          output_quality: str, max_videos_per_merge: int) -> Dict[str, Any]:
        """批量合并视频的主方法"""
        try:
            logger.info(f"开始批量视频合并: {folder_path}")

            # 1. 扫描视频文件
            video_files = self.scan_video_files(folder_path)
            if not video_files:
                return {"success": False, "error": "未找到有效的视频文件", "processed_videos": 0}

            # 2. 视频分组
            video_groups = self.group_videos_by_duration(
                video_files, target_duration_min, target_duration_max, max_videos_per_merge
            )

            if not video_groups:
                return {"success": False, "error": "无法创建有效的视频分组", "processed_videos": 0}

            # 3. 创建输出目录
            output_folder = os.path.join(folder_path, 'output')
            os.makedirs(output_folder, exist_ok=True)

            # 4. 逐组合并视频
            successful_merges = 0
            output_files = []

            for i, video_group in enumerate(video_groups):
                try:
                    # 生成输出文件名 - 使用首位文件的复合命名
                    first_video = video_group[0]
                    first_video_name = os.path.splitext(first_video['name'])[0]  # 去掉扩展名
                    total_duration = sum(v['duration'] for v in video_group)
                    video_count = len(video_group)

                    # 构建文件名：首位文件名_合并{视频数量}个_{总时长}s
                    output_filename = f"{first_video_name}_合并{video_count}个_{int(total_duration)}s.mp4"
                    output_path = os.path.join(output_folder, output_filename)

                    # 执行合并
                    success = await self.merge_video_group(
                        video_group, output_path, i,
                        enable_transitions, output_quality
                    )

                    if success:
                        successful_merges += 1
                        output_files.append(output_filename)
                        logger.info(f"成功合并第 {i+1} 组视频: {output_filename}")
                    else:
                        logger.error(f"合并第 {i+1} 组视频失败")

                except Exception as e:
                    logger.error(f"处理第 {i+1} 组视频异常: {str(e)}")

            # 5. 返回结果
            if successful_merges > 0:
                logger.info(f"视频合并完成，成功合并 {successful_merges}/{len(video_groups)} 组")
                return {
                    "success": True,
                    "output_files": output_files,
                    "processed_videos": len(video_files),
                    "successful_merges": successful_merges
                }
            else:
                error_msg = "所有视频合并都失败了"
                logger.error(error_msg)
                return {"success": False, "error": error_msg, "processed_videos": len(video_files)}

        except Exception as e:
            error_msg = f"批量视频合并异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg, "processed_videos": 0}
