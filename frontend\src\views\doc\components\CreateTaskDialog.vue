<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建下载任务"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <!-- 基本信息 -->
      <el-form-item label="任务名称" prop="task_name">
        <el-input
          v-model="form.task_name"
          placeholder="请输入任务名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="任务类型" prop="task_type">
        <el-select v-model="form.task_type" placeholder="请选择任务类型" style="width: 100%">
          <el-option label="单个URL下载" value="single" />
          <el-option label="批量URL下载" value="batch" />
          <el-option label="频道/用户下载" value="channel" />
          <el-option label="播放列表下载" value="playlist" />
        </el-select>
      </el-form-item>

      <el-form-item label="目标平台" prop="target_platform">
        <el-select v-model="form.target_platform" placeholder="请选择平台" style="width: 100%">
          <el-option label="YouTube" value="youtube" />
          <el-option label="TikTok" value="tiktok" />
          <el-option label="Instagram" value="instagram" />
          <el-option label="Bilibili" value="bilibili" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <!-- URL输入 -->
      <el-form-item label="源URL" prop="source_urls">
        <div class="url-input-area">
          <el-input
            v-model="urlInput"
            type="textarea"
            :rows="4"
            placeholder="请输入URL，每行一个&#10;支持的格式：&#10;- 视频URL: https://www.youtube.com/watch?v=xxx&#10;- 频道URL: https://www.youtube.com/channel/xxx&#10;- 播放列表URL: https://www.youtube.com/playlist?list=xxx"
            @input="parseUrls"
          />
          <div class="url-count" v-if="form.source_urls.length > 0">
            已识别 {{ form.source_urls.length }} 个URL
          </div>
        </div>
      </el-form-item>

      <!-- 下载配置 -->
      <el-divider content-position="left">下载配置</el-divider>

      <el-form-item label="视频质量">
        <el-select v-model="form.download_config.quality" style="width: 100%">
          <el-option label="最佳质量" value="best" />
          <el-option label="最差质量" value="worst" />
          <el-option label="1080p" value="1080p" />
          <el-option label="720p" value="720p" />
          <el-option label="480p" value="480p" />
          <el-option label="360p" value="360p" />
        </el-select>
      </el-form-item>

      <el-form-item label="文件格式">
        <el-select v-model="form.download_config.format" style="width: 100%">
          <el-option label="MP4" value="mp4" />
          <el-option label="WebM" value="webm" />
          <el-option label="MKV" value="mkv" />
          <el-option label="AVI" value="avi" />
        </el-select>
      </el-form-item>

      <el-form-item label="附加选项">
        <el-checkbox-group v-model="downloadOptions">
          <el-checkbox label="include_subtitles">包含字幕</el-checkbox>
          <el-checkbox label="include_thumbnail">包含缩略图</el-checkbox>
          <el-checkbox label="include_metadata">包含元数据</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 过滤配置 -->
      <el-divider content-position="left">过滤配置（可选）</el-divider>

      <el-form-item label="时长范围">
        <div class="duration-range">
          <el-input-number
            v-model="form.filter_config.min_duration"
            :min="0"
            placeholder="最小时长"
            controls-position="right"
            style="width: 120px"
          />
          <span style="margin: 0 8px">-</span>
          <el-input-number
            v-model="form.filter_config.max_duration"
            :min="0"
            placeholder="最大时长"
            controls-position="right"
            style="width: 120px"
          />
          <span style="margin-left: 8px; font-size: 12px; color: #909399">秒</span>
        </div>
      </el-form-item>

      <el-form-item label="包含关键词">
        <el-input
          v-model="keywordsInput"
          placeholder="请输入关键词，用逗号分隔"
          @input="parseKeywords"
        />
      </el-form-item>

      <el-form-item label="排除关键词">
        <el-input
          v-model="excludeKeywordsInput"
          placeholder="请输入要排除的关键词，用逗号分隔"
          @input="parseExcludeKeywords"
        />
      </el-form-item>

      <!-- 日期范围 -->
      <el-form-item label="发布日期">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="updateDateRange"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          创建任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { createDownloadTask } from '@/api/content'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const urlInput = ref('')
const keywordsInput = ref('')
const excludeKeywordsInput = ref('')
const dateRange = ref<[string, string] | null>(null)
const downloadOptions = ref<string[]>(['include_thumbnail', 'include_metadata'])

// 表单数据
const form = reactive({
  task_name: '',
  task_type: 'single',
  target_platform: 'youtube',
  source_urls: [] as string[],
  download_config: {
    quality: 'best',
    format: 'mp4',
    include_subtitles: false,
    include_thumbnail: true,
    include_metadata: true
  },
  filter_config: {
    min_duration: undefined as number | undefined,
    max_duration: undefined as number | undefined,
    keywords: [] as string[],
    exclude_keywords: [] as string[],
    date_range: undefined as { start: string; end: string } | undefined
  }
})

// 表单验证规则
const rules: FormRules = {
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '任务名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  task_type: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  target_platform: [
    { required: true, message: '请选择目标平台', trigger: 'change' }
  ],
  source_urls: [
    { 
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请输入至少一个URL'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const parseUrls = () => {
  const urls = urlInput.value
    .split('\n')
    .map(url => url.trim())
    .filter(url => url && isValidUrl(url))
  
  form.source_urls = urls
}

const isValidUrl = (string: string) => {
  try {
    new URL(string)
    return true
  } catch (_) {
    return false
  }
}

const parseKeywords = () => {
  form.filter_config.keywords = keywordsInput.value
    .split(',')
    .map(keyword => keyword.trim())
    .filter(keyword => keyword)
}

const parseExcludeKeywords = () => {
  form.filter_config.exclude_keywords = excludeKeywordsInput.value
    .split(',')
    .map(keyword => keyword.trim())
    .filter(keyword => keyword)
}

const updateDateRange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    form.filter_config.date_range = {
      start: dateRange.value[0],
      end: dateRange.value[1]
    }
  } else {
    form.filter_config.date_range = undefined
  }
}

const resetForm = () => {
  form.task_name = ''
  form.task_type = 'single'
  form.target_platform = 'youtube'
  form.source_urls = []
  form.download_config = {
    quality: 'best',
    format: 'mp4',
    include_subtitles: false,
    include_thumbnail: true,
    include_metadata: true
  }
  form.filter_config = {
    min_duration: undefined,
    max_duration: undefined,
    keywords: [],
    exclude_keywords: [],
    date_range: undefined
  }
  
  urlInput.value = ''
  keywordsInput.value = ''
  excludeKeywordsInput.value = ''
  dateRange.value = null
  downloadOptions.value = ['include_thumbnail', 'include_metadata']
  
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 更新下载配置
    form.download_config.include_subtitles = downloadOptions.value.includes('include_subtitles')
    form.download_config.include_thumbnail = downloadOptions.value.includes('include_thumbnail')
    form.download_config.include_metadata = downloadOptions.value.includes('include_metadata')
    
    // 清理空的过滤配置
    const filterConfig = { ...form.filter_config }
    if (!filterConfig.min_duration) delete filterConfig.min_duration
    if (!filterConfig.max_duration) delete filterConfig.max_duration
    if (filterConfig.keywords.length === 0) delete filterConfig.keywords
    if (filterConfig.exclude_keywords.length === 0) delete filterConfig.exclude_keywords
    if (!filterConfig.date_range) delete filterConfig.date_range
    
    const taskData = {
      task_name: form.task_name,
      task_type: form.task_type,
      source_urls: form.source_urls,
      target_platform: form.target_platform,
      download_config: form.download_config,
      filter_config: Object.keys(filterConfig).length > 0 ? filterConfig : undefined
    }
    
    await createDownloadTask(taskData)
    
    ElMessage.success('下载任务创建成功')
    emit('success')
    handleClose()
    
  } catch (error) {
    console.error('创建下载任务失败:', error)
    ElMessage.error('创建下载任务失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}

// 监听下载选项变化
watch(downloadOptions, (newOptions) => {
  form.download_config.include_subtitles = newOptions.includes('include_subtitles')
  form.download_config.include_thumbnail = newOptions.includes('include_thumbnail')
  form.download_config.include_metadata = newOptions.includes('include_metadata')
}, { deep: true })
</script>

<style scoped>
.url-input-area {
  width: 100%;
}

.url-count {
  margin-top: 8px;
  font-size: 12px;
  color: #67c23a;
}

.duration-range {
  display: flex;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #303133;
}
</style>
