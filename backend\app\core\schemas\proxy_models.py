from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)


class ProxyIPModel(BaseModel):
    """代理IP数据模型"""
    id: Optional[str] = Field(None, description="代理IP ID")
    region: str = Field(..., description="地区，如：HK、US、JP等")
    ip_address: str = Field(..., description="IP地址")
    port: int = Field(..., description="端口")
    proxy_type: str = Field(..., description="代理类型：vless、vmess、http、socks5等")
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    v2ray_config: Optional[str] = Field(None, description="v2rayN格式配置，如vless://...、vmess://...等")
    socks_config: Optional[str] = Field(None, description="socks格式配置，格式：IP:端口:用户名:密码")
    expire_date: Optional[datetime] = Field(None, description="到期时间")
    payment_card: Optional[str] = Field(None, description="付款卡信息")
    provider: Optional[str] = Field(None, description="服务商")
    status: str = Field(default="active", description="状态：active、inactive、expired")
    notes: Optional[str] = Field(None, description="备注")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }


class ProxyIPCreateRequest(BaseModel):
    """创建代理IP请求模型"""
    region: str = Field(..., description="地区")
    ip_address: str = Field(..., description="IP地址")
    port: int = Field(..., description="端口")
    proxy_type: str = Field(..., description="代理类型")
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    v2ray_config: Optional[str] = Field(None, description="v2rayN格式配置")
    socks_config: Optional[str] = Field(None, description="socks格式配置")
    expire_date: Optional[datetime] = Field(None, description="到期时间")
    payment_card: Optional[str] = Field(None, description="付款卡信息")
    provider: Optional[str] = Field(None, description="服务商")
    notes: Optional[str] = Field(None, description="备注")


class ProxyIPUpdateRequest(BaseModel):
    """更新代理IP请求模型"""
    region: Optional[str] = Field(None, description="地区")
    ip_address: Optional[str] = Field(None, description="IP地址")
    port: Optional[int] = Field(None, description="端口")
    proxy_type: Optional[str] = Field(None, description="代理类型")
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    v2ray_config: Optional[str] = Field(None, description="v2rayN格式配置")
    socks_config: Optional[str] = Field(None, description="socks格式配置")
    expire_date: Optional[datetime] = Field(None, description="到期时间")
    payment_card: Optional[str] = Field(None, description="付款卡信息")
    provider: Optional[str] = Field(None, description="服务商")
    status: Optional[str] = Field(None, description="状态")
    notes: Optional[str] = Field(None, description="备注")


class DeviceProxyMapping(BaseModel):
    """设备代理IP关联模型"""
    id: Optional[str] = Field(None, description="关联ID")
    device_id: str = Field(..., description="设备ID")
    proxy_id: str = Field(..., description="代理IP ID")
    status: str = Field(default="active", description="关联状态：active、inactive")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }


class ProxyIPResponse(BaseModel):
    """代理IP响应模型"""
    id: str
    region: str
    ip_address: str
    port: int
    proxy_type: str
    username: Optional[str] = None
    password: Optional[str] = None
    v2ray_config: Optional[str] = None
    socks_config: Optional[str] = None
    expire_date: Optional[datetime] = None
    payment_card: Optional[str] = None
    provider: Optional[str] = None
    status: str
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    # 关联的设备信息
    associated_devices: List[Dict[str, Any]] = Field(default_factory=list, description="关联的设备列表")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }


class ProxyIPListResponse(BaseModel):
    """代理IP列表响应模型"""
    total: int = Field(..., description="总数量")
    items: List[ProxyIPResponse] = Field(..., description="代理IP列表")
    regions: List[str] = Field(default_factory=list, description="所有地区列表")


class ProxyIPService:
    """代理IP服务类"""
    
    def __init__(self, db):
        self.db = db
        self.collection = db.proxy_ips
        self.mapping_collection = db.device_proxy_mappings
    
    async def create_proxy_ip(self, proxy_data: ProxyIPCreateRequest) -> str:
        """创建代理IP"""
        proxy_dict = proxy_data.dict()
        proxy_dict["created_at"] = datetime.now()
        proxy_dict["updated_at"] = datetime.now()
        proxy_dict["status"] = "active"

        result = await self.collection.insert_one(proxy_dict)
        return str(result.inserted_id)
    
    async def get_proxy_ip_list(self, region: Optional[str] = None, status: Optional[str] = None) -> ProxyIPListResponse:
        """获取代理IP列表"""
        query = {}
        if region:
            query["region"] = region
        if status:
            query["status"] = status

        # 获取总数
        total = await self.collection.count_documents(query)

        # 获取列表
        cursor = self.collection.find(query).sort("created_at", -1)
        items = []

        async for doc in cursor:
            doc["id"] = str(doc["_id"])
            del doc["_id"]

            # 获取关联的设备信息
            device_mappings = await self.mapping_collection.find(
                {"proxy_id": doc["id"], "status": "active"}
            ).to_list(None)

            associated_devices = []
            for mapping in device_mappings:
                # 查询设备详细信息
                device_info = await self.db.devices.find_one({"_id": mapping["device_id"]}) or \
                             await self.db.devices.find_one({"id": mapping["device_id"]})

                device_data = {
                    "device_id": mapping["device_id"],
                    "mapping_id": str(mapping["_id"]),
                    "device_name": device_info.get("name", f"设备-{mapping['device_id']}") if device_info else f"设备-{mapping['device_id']}",
                    "device_status": device_info.get("status", "unknown") if device_info else "unknown",
                    "device_type": device_info.get("type", device_info.get("device_type", "unknown")) if device_info else "unknown"
                }
                associated_devices.append(device_data)

            doc["associated_devices"] = associated_devices
            items.append(ProxyIPResponse(**doc))

        # 获取所有地区
        regions = await self.collection.distinct("region")

        return ProxyIPListResponse(
            total=total,
            items=items,
            regions=sorted(regions)
        )
    
    async def get_proxy_ip_by_id(self, proxy_id: str) -> Optional[ProxyIPResponse]:
        """根据ID获取代理IP"""
        try:
            from bson import ObjectId
            doc = await self.collection.find_one({"_id": ObjectId(proxy_id)})
            if not doc:
                return None

            doc["id"] = str(doc["_id"])
            del doc["_id"]

            # 获取关联的设备信息
            device_mappings = await self.mapping_collection.find(
                {"proxy_id": proxy_id, "status": "active"}
            ).to_list(None)

            associated_devices = []
            for mapping in device_mappings:
                # 查询设备详细信息
                device_info = await self.db.devices.find_one({"_id": mapping["device_id"]}) or \
                             await self.db.devices.find_one({"id": mapping["device_id"]})

                device_data = {
                    "device_id": mapping["device_id"],
                    "mapping_id": str(mapping["_id"]),
                    "device_name": device_info.get("name", f"设备-{mapping['device_id']}") if device_info else f"设备-{mapping['device_id']}",
                    "device_status": device_info.get("status", "unknown") if device_info else "unknown",
                    "device_type": device_info.get("type", device_info.get("device_type", "unknown")) if device_info else "unknown"
                }
                associated_devices.append(device_data)

            doc["associated_devices"] = associated_devices
            return ProxyIPResponse(**doc)
        except Exception:
            return None
    
    async def update_proxy_ip(self, proxy_id: str, update_data: ProxyIPUpdateRequest) -> bool:
        """更新代理IP"""
        try:
            from bson import ObjectId
            update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
            update_dict["updated_at"] = datetime.now()

            result = await self.collection.update_one(
                {"_id": ObjectId(proxy_id)},
                {"$set": update_dict}
            )
            return result.modified_count > 0
        except Exception:
            return False
    
    async def delete_proxy_ip(self, proxy_id: str) -> bool:
        """删除代理IP"""
        try:
            from bson import ObjectId
            # 先删除相关的设备关联
            await self.mapping_collection.delete_many({"proxy_id": proxy_id})

            # 删除代理IP
            result = await self.collection.delete_one({"_id": ObjectId(proxy_id)})
            return result.deleted_count > 0
        except Exception:
            return False
    
    async def associate_device(self, device_id: str, proxy_id: str) -> str:
        """关联设备和代理IP"""
        try:
            # 检查代理IP是否存在
            from bson import ObjectId
            proxy_exists = await self.collection.find_one({"_id": ObjectId(proxy_id)})
            if not proxy_exists:
                raise ValueError(f"代理IP不存在: {proxy_id}")

            # 检查设备是否存在且为活跃状态
            device_exists = await self.db.devices.find_one({
                "$or": [{"_id": device_id}, {"id": device_id}],
                "status": {"$in": ["running", "starting"]}
            })
            if not device_exists:
                raise ValueError(f"设备不存在或不是活跃状态: {device_id}")

            # 检查是否已存在关联
            existing = await self.mapping_collection.find_one({
                "device_id": device_id,
                "proxy_id": proxy_id,
                "status": "active"
            })

            if existing:
                logger.info(f"设备 {device_id} 和代理IP {proxy_id} 已存在关联")
                return str(existing["_id"])

            mapping_data = {
                "device_id": device_id,
                "proxy_id": proxy_id,
                "status": "active",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }

            result = await self.mapping_collection.insert_one(mapping_data)
            logger.info(f"成功创建设备关联: 设备 {device_id} <-> 代理IP {proxy_id}")
            return str(result.inserted_id)

        except ValueError:
            raise
        except Exception as e:
            logger.error(f"关联设备和代理IP失败: {str(e)}")
            raise
    
    async def disassociate_device(self, device_id: str, proxy_id: str) -> bool:
        """取消设备和代理IP关联"""
        result = await self.mapping_collection.update_many(
            {"device_id": device_id, "proxy_id": proxy_id},
            {"$set": {"status": "inactive", "updated_at": datetime.now()}}
        )
        return result.modified_count > 0

    async def get_active_devices(self) -> List[Dict[str, Any]]:
        """获取关联了活跃账号的设备列表，用于关联选择"""
        try:
            # 1. 首先查询活跃状态的账号
            active_accounts_cursor = self.db.social_accounts.find({
                "status": "active"
            })

            active_account_ids = []
            async for account in active_accounts_cursor:
                active_account_ids.append(account.get("id", str(account.get("_id"))))

            logger.info(f"找到 {len(active_account_ids)} 个活跃账号")

            if not active_account_ids:
                logger.warning("没有找到活跃账号")
                return []

            # 2. 查询关联了这些活跃账号的设备映射
            device_mappings_cursor = self.db.device_account_mappings.find({
                "account_id": {"$in": active_account_ids},
                "status": "active"
            })

            device_ids = set()
            async for mapping in device_mappings_cursor:
                device_ids.add(mapping.get("device_id"))

            logger.info(f"找到 {len(device_ids)} 个关联了活跃账号的设备")

            if not device_ids:
                logger.warning("没有找到关联了活跃账号的设备")
                return []

            # 3. 查询这些设备的详细信息
            devices_cursor = self.db.devices.find({
                "$or": [
                    {"_id": {"$in": list(device_ids)}},
                    {"id": {"$in": list(device_ids)}}
                ]
            }).sort("name", 1)

            active_devices = []
            async for device in devices_cursor:
                device_info = {
                    "id": str(device.get("_id", device.get("id", ""))),
                    "name": device.get("name", f"设备-{device.get('_id', 'unknown')}"),
                    "status": device.get("status", "unknown"),
                    "type": device.get("type", device.get("device_type", "unknown")),
                    "core_id": device.get("core_id", "default"),
                    "updated_at": device.get("updated_at", datetime.now()).isoformat() if isinstance(device.get("updated_at"), datetime) else str(device.get("updated_at", ""))
                }
                active_devices.append(device_info)

            logger.info(f"返回 {len(active_devices)} 个关联了活跃账号的设备")
            return active_devices

        except Exception as e:
            logger.error(f"获取活跃设备列表失败: {str(e)}")
            return []
