# ThunderHub API 设计文档

**版本**: v1.3.0
**最后更新**: 2025/05/15

---

## 1. API 概述

ThunderHub API 采用 RESTful 设计风格，提供以下主要功能：

- 认证与用户管理
- 设备管理
- 任务系统
- 社媒应用管理
- 文件管理系统
- 报表中心

所有 API 均通过 HTTPS 提供，并使用 JWT 进行认证。

---

## 2. 认证 API

### 2.1 获取访问令牌

```http
POST /auth/token
```

**请求参数**:

```json
{
  "username": "admin",
  "password": "password"
}
```

**响应**:

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### 2.2 获取当前用户信息

```http
GET /auth/users/me
```

**请求头**:

```http
Authorization: Bearer {access_token}
```

**响应**:

```json
{
  "id": "user123",
  "username": "admin",
  "email": "<EMAIL>",
  "roles": ["admin"],
  "created_at": "2025-01-01T00:00:00Z"
}
```

---

## 3. 设备管理 API

### 3.1 获取设备列表

```http
GET /api/devices
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| skip | integer | 否 | 跳过的记录数，默认为0 |
| limit | integer | 否 | 返回的最大记录数，默认为100 |
| include_config | boolean | 否 | 是否包含设备配置信息，默认为false |
| core_id | string | 否 | 按Core服务ID筛选设备 |

**响应**:

```json
[
  {
    "id": "0",
    "name": "雷电模拟器",
    "type": "雷电模拟器",
    "status": "running",
    "core_id": "server1",
    "updated_at": "2025-05-09T15:53:02.000Z",
    "cpu": "5.2%",
    "memory": "30.5%",
    "network": "已连接",
    "display_info": {
      "width": 1080,
      "height": 1920,
      "dpi": 320
    },
    "config": {
      "resolution": "1080x1920",
      "cpu_cores": 2,
      "memory": 2048,
      "adb_port": 5555
    }
  }
]
```

### 3.2 获取单个设备详情

```http
GET /api/devices/{id}
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 设备ID |
| core_id | string | 否 | Core服务ID，如果不提供则自动查找 |

**响应**:

```json
{
  "id": "0",
  "name": "雷电模拟器",
  "type": "雷电模拟器",
  "status": "running",
  "core_id": "server1",
  "updated_at": "2025-05-09T15:53:02.000Z",
  "cpu": "5.2%",
  "memory": "30.5%",
  "network": "已连接",
  "display_info": {
    "width": 1080,
    "height": 1920,
    "dpi": 320
  },
  "config": {
    "resolution": "1080x1920",
    "cpu_cores": 2,
    "memory": 2048,
    "adb_port": 5555
  },
  "window_info": {
    "top_window": "12345678",
    "bound_window": "87654321"
  },
  "process_info": {
    "pid": "1234",
    "vbox_pid": "5678"
  },
  "last_change": {
    "old_status": "stopped",
    "new_status": "running",
    "timestamp": 1746777182
  },
  "created_at": "2025-05-09T16:02:09.339Z"
}
```

### 3.3 获取设备状态历史

```http
GET /api/devices/{id}/history
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 设备ID |
| core_id | string | 否 | Core服务ID，如果不提供则自动查找 |
| start_time | string | 否 | 开始时间，ISO 8601格式 |
| end_time | string | 否 | 结束时间，ISO 8601格式 |
| limit | integer | 否 | 返回的最大记录数，默认为100 |

**响应**:

```json
[
  {
    "timestamp": "2025-05-09T15:53:02.000Z",
    "status": "running",
    "cpu_usage": "5.2%",
    "memory_usage": "30.5%",
    "network_status": "已连接"
  },
  {
    "timestamp": "2025-05-09T15:50:02.000Z",
    "status": "starting",
    "cpu_usage": "10.5%",
    "memory_usage": "15.2%",
    "network_status": "已连接"
  }
]
```

### 3.4 获取Core服务列表

```http
GET /api/v1/cores
```

**响应**:

```json
[
  {
    "id": "server1",
    "name": "服务器1",
    "ip": "*************",
    "status": "online",
    "device_count": 10,
    "updated_at": "2025-05-09T15:53:02.000Z"
  },
  {
    "id": "server2",
    "name": "服务器2",
    "ip": "*************",
    "status": "online",
    "device_count": 5,
    "updated_at": "2025-05-09T15:53:02.000Z"
  }
]
```

### 3.5 获取单个Core服务详情

```http
GET /api/v1/cores/{id}
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | Core服务ID |

**响应**:

```json
{
  "id": "server1",
  "name": "服务器1",
  "ip": "*************",
  "status": "online",
  "device_count": 10,
  "updated_at": "2025-05-09T15:53:02.000Z",
  "cpu_usage": "25.5%",
  "memory_usage": "40.2%",
  "disk_usage": "60.5%",
  "created_at": "2025-01-01T00:00:00Z"
}
```

---

## 4. 社交媒体管理 API

### 4.1 社交平台 API (v1)

#### 4.1.1 获取平台列表

```http
GET /api/v1/social/platforms
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| status | string | 否 | 平台状态筛选，如 "active", "inactive" |
| skip | integer | 否 | 分页起始位置，默认为0 |
| limit | integer | 否 | 分页大小，默认为100 |

**响应**:

```json
[
  {
    "id": "youtube",
    "name": "YouTube",
    "icon": "youtube.png",
    "website": "https://www.youtube.com",
    "status": "active",
    "features": ["video", "live", "comment"],
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-05-15T10:30:00Z"
  },
  {
    "id": "facebook",
    "name": "Facebook",
    "icon": "facebook.png",
    "website": "https://www.facebook.com",
    "status": "active",
    "features": ["post", "video", "comment"],
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-05-15T10:30:00Z"
  }
]
```

#### 4.1.2 获取单个平台

```http
GET /api/v1/social/platforms/{platform_id}
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| platform_id | string | 是 | 平台ID |

**响应**:

```json
{
  "id": "youtube",
  "name": "YouTube",
  "icon": "youtube.png",
  "website": "https://www.youtube.com",
  "status": "active",
  "features": ["video", "live", "comment"],
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-05-15T10:30:00Z"
}
```

#### 4.1.3 创建平台

```http
POST /api/v1/social/platforms
```

**请求参数**:

```json
{
  "id": "tiktok",
  "name": "TikTok",
  "icon": "tiktok.png",
  "website": "https://www.tiktok.com",
  "status": "active",
  "features": ["video", "comment"]
}
```

**响应**:

```json
{
  "id": "tiktok",
  "name": "TikTok",
  "icon": "tiktok.png",
  "website": "https://www.tiktok.com",
  "status": "active",
  "features": ["video", "comment"],
  "created_at": "2025-05-15T11:30:00Z",
  "updated_at": "2025-05-15T11:30:00Z"
}
```

#### 4.1.4 更新平台

```http
PUT /api/v1/social/platforms/{platform_id}
```

**请求参数**:

```json
{
  "name": "TikTok Global",
  "status": "active",
  "features": ["video", "comment", "live"]
}
```

**响应**:

``
{
  "id": "tiktok",
  "name": "TikTok Global",
  "icon": "tiktok.png",
  "website": "https://www.tiktok.com",
  "status": "active",
  "features": ["video", "comment", "live"],
  "created_at": "2025-05-15T11:30:00Z",
  "updated_at": "2025-05-15T12:00:00Z"
}
```

#### 4.1.5 删除平台

```http
DELETE /api/v1/social/platforms/{platform_id}
```

**响应**:

```json
{
  "deleted": true
}
```

### 4.2 平台应用 API (v1)

#### 4.2.1 获取平台应用列表

```http
GET /api/v1/social/platform_apps
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| platform_id | string | 否 | 平台ID筛选 |
| type | string | 否 | 应用类型筛选，如 "android", "ios", "web" |
| status | string | 否 | 应用状态筛选，如 "active", "inactive" |
| skip | integer | 否 | 分页起始位置，默认为0 |
| limit | integer | 否 | 分页大小，默认为100 |

**响应**:

```json
[
  {
    "id": "youtube_android",
    "platform_id": "youtube",
    "name": "YouTube Android App",
    "type": "android",
    "status": "active",
    "version": "18.20.38",
    "app_info": {
      "package_name": "com.google.android.youtube",
      "main_activity": "com.google.android.youtube.HomeActivity",
      "min_android_version": "8.0",
      "download_url": "https://example.com/youtube.apk"
    },
    "automation": {
      "type": "appium",
      "selectors": {
        "login_button": "com.google.android.youtube:id/sign_in_button",
        "post_button": "com.google.android.youtube:id/upload_button"
      }
    },
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-05-15T10:30:00Z"
  }
]
```

#### 4.2.2 获取单个平台应用

```http
GET /api/v1/social/platform_apps/{app_id}
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| app_id | string | 是 | 应用ID |

**响应**:

```json
{
  "id": "youtube_android",
  "platform_id": "youtube",
  "name": "YouTube Android App",
  "type": "android",
  "status": "active",
  "version": "18.20.38",
  "app_info": {
    "package_name": "com.google.android.youtube",
    "main_activity": "com.google.android.youtube.HomeActivity",
    "min_android_version": "8.0",
    "download_url": "https://example.com/youtube.apk"
  },
  "automation": {
    "type": "appium",
    "selectors": {
      "login_button": "com.google.android.youtube:id/sign_in_button",
      "post_button": "com.google.android.youtube:id/upload_button"
    }
  },
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-05-15T10:30:00Z"
}
```

#### 4.2.3 创建平台应用

```http
POST /api/v1/social/platform_apps
```

**请求参数**:

```json
{
  "platform_id": "tiktok",
  "name": "TikTok Android App",
  "type": "android",
  "status": "active",
  "version": "26.7.5",
  "app_info": {
    "package_name": "com.zhiliaoapp.musically",
    "main_activity": "com.zhiliaoapp.musically.MainActivity",
    "min_android_version": "5.0"
  },
  "automation": {
    "type": "appium"
  }
}
```

**响应**:

```json
{
  "id": "tiktok_android",
  "platform_id": "tiktok",
  "name": "TikTok Android App",
  "type": "android",
  "status": "active",
  "version": "26.7.5",
  "app_info": {
    "package_name": "com.zhiliaoapp.musically",
    "main_activity": "com.zhiliaoapp.musically.MainActivity",
    "min_android_version": "5.0"
  },
  "automation": {
    "type": "appium"
  },
  "created_at": "2025-05-15T12:30:00Z",
  "updated_at": "2025-05-15T12:30:00Z"
}
```

#### 4.2.4 更新平台应用

```http
PUT /api/v1/social/platform_apps/{app_id}
```

**请求参数**:

```json
{
  "version": "26.8.0",
  "status": "active",
  "automation": {
    "type": "appium",
    "selectors": {
      "login_button": "com.zhiliaoapp.musically:id/login_button"
    }
  }
}
```

**响应**:

```json
{
  "id": "tiktok_android",
  "platform_id": "tiktok",
  "name": "TikTok Android App",
  "type": "android",
  "status": "active",
  "version": "26.8.0",
  "app_info": {
    "package_name": "com.zhiliaoapp.musically",
    "main_activity": "com.zhiliaoapp.musically.MainActivity",
    "min_android_version": "5.0"
  },
  "automation": {
    "type": "appium",
    "selectors": {
      "login_button": "com.zhiliaoapp.musically:id/login_button"
    }
  },
  "created_at": "2025-05-15T12:30:00Z",
  "updated_at": "2025-05-15T13:00:00Z"
}
```

#### 4.2.5 删除平台应用

```http
DELETE /api/v1/social/platform_apps/{app_id}
```

**响应**:

```json
{
  "deleted": true
}
```

### 4.3 社交账号 API (v1)

#### 4.3.1 获取账号列表

```http
GET /api/v1/social/accounts
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| platform_id | string | 否 | 平台ID筛选 |
| core_service_id | string | 否 | Core服务ID筛选 |
| status | string | 否 | 账号状态筛选，如 "active", "inactive", "suspended" |
| skip | integer | 否 | 分页起始位置，默认为0 |
| limit | integer | 否 | 分页大小，默认为100 |

**响应**:

```json
[
  {
    "id": "acc123",
    "username": "thunderhub_official",
    "display_name": "雷电中心官方",
    "platform_id": "youtube",
    "core_service_id": "server1",
    "status": "active",
    "avatar": "https://example.com/avatar.jpg",
    "description": "雷电中心官方账号",
    "followers": 1000,
    "following": 50,
    "posts_count": 30,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-05-15T10:30:00Z"
  }
]
```

#### 4.3.2 创建账号

```http
POST /api/v1/social/accounts
```

**请求参数**:

```json
{
  "username": "new_account",
  "password": "password123",
  "display_name": "新账号",
  "platform_id": "youtube",
  "core_service_id": "server1",
  "status": "active",
  "description": "测试账号"
}
```

**响应**:

```json
{
  "id": "acc456",
  "username": "new_account",
  "display_name": "新账号",
  "platform_id": "youtube",
  "core_service_id": "server1",
  "status": "active",
  "description": "测试账号",
  "created_at": "2025-05-15T14:00:00Z",
  "updated_at": "2025-05-15T14:00:00Z"
}
```

#### 4.3.3 更新账号

```http
PUT /api/v1/social/accounts/{account_id}
```

**请求参数**:

```json
{
  "display_name": "更新后的账号名称",
  "status": "inactive",
  "description": "已更新的描述"
}
```

**响应**:

```json
{
  "id": "acc456",
  "username": "new_account",
  "display_name": "更新后的账号名称",
  "platform_id": "youtube",
  "core_service_id": "server1",
  "status": "inactive",
  "description": "已更新的描述",
  "created_at": "2025-05-15T14:00:00Z",
  "updated_at": "2025-05-15T14:30:00Z"
}
```

#### 4.3.4 删除账号

```http
DELETE /api/v1/social/accounts/{account_id}
```

**响应**:

```json
{
  "deleted": true
}
```

#### 4.3.5 批量更新账号

```http
POST /api/v1/social/accounts/batch_update
```

**请求参数**:

```json
{
  "account_ids": ["acc123", "acc456"],
  "update_data": {
    "status": "inactive",
    "tags": ["批量更新"]
  }
}
```

**响应**:

```json
{
  "updated_count": 2
}
```

#### 4.3.6 批量删除账号

```http
POST /api/v1/social/accounts/batch_delete
```

**请求参数**:

```json
{
  "account_ids": ["acc123", "acc456"]
}
```

**响应**:

```json
{
  "deleted_count": 2
}
```

#### 4.3.7 导入账号

```http
POST /api/v1/social/accounts/import
```

**请求参数**:

```json
{
  "text_content": "GG：account1----password1----备注1\nGG：account2----password2----备注2",
  "platform_mapping": {
    "GG": "youtube",
    "FB": "facebook"
  },
  "core_service_id": "server1"
}
```

**响应**:

```json
{
  "imported_count": 2,
  "errors": []
}
```

#### 4.3.8 导出账号

```http
GET /api/v1/social/accounts/export
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| platform_id | string | 否 | 平台ID筛选 |
| core_service_id | string | 否 | Core服务ID筛选 |

**响应**:

```json
{
  "accounts": [
    {
      "id": "acc123",
      "username": "thunderhub_official",
      "display_name": "雷电中心官方",
      "platform_id": "youtube",
      "core_service_id": "server1",
      "status": "active"
    }
  ],
  "export_time": "2025-05-15T15:00:00Z"
}
```

## 5. WebSocket API

### 5.1 设备状态更新

**连接**:

```http
WebSocket: /device-socket.io
```

**事件**:

```text
device_status_update
```

**数据**:

```json
{
  "id": "0",
  "core_id": "server1",
  "status": "running",
  "updated_at": "2025-05-09T15:53:02.000Z"
}
```

---

## 5. 错误处理

所有API都使用标准HTTP状态码表示请求的结果：

- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器错误

错误响应格式：

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  }
}
```
