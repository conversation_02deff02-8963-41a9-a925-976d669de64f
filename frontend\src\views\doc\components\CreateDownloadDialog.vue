<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建下载任务"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="download-dialog-container">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="选择账号" description="选择要下载对标内容的我们的账号" />
        <el-step title="配置下载" description="设置下载参数和过滤条件" />
        <el-step title="确认创建" description="确认配置并创建下载任务" />
      </el-steps>

      <!-- 步骤1: 选择账号 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="step-header">
          <h3>📋 选择我们的账号</h3>
          <p>选择要为其下载对标账号内容的我们的账号</p>
        </div>

        <!-- 筛选工具栏 -->
        <div class="filter-toolbar">
          <el-select
            v-model="filterCoreService"
            placeholder="筛选Core服务"
            clearable
            style="width: 200px; margin-right: 10px;"
            @change="filterAccounts"
          >
            <el-option
              v-for="service in coreServices"
              :key="service.id"
              :label="service.name"
              :value="service.id"
            />
          </el-select>

          <el-select
            v-model="filterPlatform"
            placeholder="筛选平台"
            clearable
            style="width: 150px; margin-right: 10px;"
            @change="filterAccounts"
          >
            <el-option label="YouTube" value="681efeeecd836bd64b9c2a1e" />
            <el-option label="TikTok" value="681efeeecd836bd64b9c2a20" />
            <el-option label="抖音" value="681efeeecd836bd64b9c2a22" />
            <el-option label="Instagram" value="681efeeecd836bd64b9c2a24" />
          </el-select>

          <el-checkbox
            v-model="onlyAccountsWithBenchmarks"
            @change="filterAccounts"
          >
            仅显示有对标账号的账号
          </el-checkbox>
        </div>

        <!-- 账号选择表格 -->
        <el-table
          :data="filteredAccounts"
          @selection-change="handleAccountSelection"
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="账号信息" min-width="200">
            <template #default="scope">
              <div class="account-info">
                <div class="account-name">
                  {{ getPlatformDisplayName(scope.row.platform_id) }} - {{ formatOurAccountName(scope.row) }}
                </div>
                <div class="account-meta">
                  <el-tag size="small" type="info">
                    {{ getAccountCoreService(scope.row) }}
                  </el-tag>
                  <el-tag
                    size="small"
                    :type="getDeviceStatusColor(scope.row.device_status)"
                    style="margin-left: 4px;"
                  >
                    {{ getDeviceStatusText(scope.row.device_status) }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="对标账号" min-width="150">
            <template #default="scope">
              <div v-if="scope.row.benchmark_accounts && scope.row.benchmark_accounts.length > 0">
                <el-tag
                  v-for="benchmark in scope.row.benchmark_accounts.slice(0, 2)"
                  :key="benchmark._id"
                  size="small"
                  :type="getBenchmarkTypeColor(benchmark.benchmark_type)"
                  style="margin: 2px;"
                >
                  {{ benchmark.account_name }}
                </el-tag>
                <span v-if="scope.row.benchmark_accounts.length > 2" class="more-count">
                  +{{ scope.row.benchmark_accounts.length - 2 }}个
                </span>
              </div>
              <span v-else class="no-data">暂无对标账号</span>
            </template>
          </el-table-column>
          <el-table-column label="对标数量" width="100" align="center">
            <template #default="scope">
              <el-tag
                size="small"
                :type="getBenchmarkCountColor(scope.row.benchmark_count || 0)"
              >
                {{ scope.row.benchmark_count || 0 }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 步骤2: 配置下载 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="step-header">
          <h3>⚙️ 配置下载参数</h3>
          <p>设置下载内容类型、数量限制和过滤条件</p>
        </div>

        <el-form class="download-config-form" label-width="120px">
          <el-form-item label="基础路径">
            <el-input
              v-model="downloadConfig.basePath"
              placeholder="下载文件保存的基础路径"
            >
              <template #prepend>保存到</template>
            </el-input>
            <div class="path-preview">
              <strong>完整路径预览：</strong>{{ getFullDownloadPath() }}
            </div>
            <div class="path-explanation">
              文件将保存到：基础路径/我们的平台/我们的账号/对标账号名称/发布月份/
            </div>
          </el-form-item>

          <el-form-item label="内容类型">
            <el-checkbox-group v-model="downloadConfig.contentTypes">
              <el-checkbox label="video">视频</el-checkbox>
              <el-checkbox label="image">图片</el-checkbox>
              <el-checkbox label="audio">音频</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="数量限制">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-input-number
                  v-model="downloadConfig.maxCount"
                  :min="1"
                  :max="1000"
                  placeholder="每个账号最多下载数量"
                />
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item label="时间范围">
            <el-select v-model="downloadConfig.timeRange" placeholder="选择时间范围">
              <el-option label="最近1周" value="1week" />
              <el-option label="最近1个月" value="1month" />
              <el-option label="最近3个月" value="3months" />
              <el-option label="最近6个月" value="6months" />
              <el-option label="全部" value="all" />
            </el-select>
          </el-form-item>

          <el-form-item label="下载模式">
            <el-radio-group v-model="downloadConfig.downloadMode">
              <el-radio label="auto">自动下载</el-radio>
              <el-radio label="manual">手动确认</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="命名规则">
            <el-select v-model="downloadConfig.namingRule" placeholder="选择文件命名规则">
              <el-option label="时间戳" value="timestamp" />
              <el-option label="原始标题" value="original" />
              <el-option label="自定义格式" value="custom" />
            </el-select>
          </el-form-item>

          <el-form-item label="过滤条件">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-input
                  v-model="downloadConfig.minViews"
                  placeholder="最小播放量"
                >
                  <template #prepend>播放量 ≥</template>
                </el-input>
              </el-col>
              <el-col :span="8">
                <el-input
                  v-model="downloadConfig.minLikes"
                  placeholder="最小点赞数"
                >
                  <template #prepend>点赞数 ≥</template>
                </el-input>
              </el-col>
              <el-col :span="8">
                <el-input
                  v-model="downloadConfig.keywords"
                  placeholder="关键词过滤"
                >
                  <template #prepend>包含关键词</template>
                </el-input>
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤3: 确认并创建 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="step-header">
          <h3>✅ 确认下载任务</h3>
          <p>请确认以下下载配置，点击创建任务后将开始下载</p>
        </div>

        <div class="download-summary">
          <el-card class="summary-card">
            <template #header>
              <span>📊 下载任务摘要</span>
            </template>

            <el-descriptions :column="2" border>
              <el-descriptions-item label="选中账号">
                {{ selectedAccounts.length }} 个
              </el-descriptions-item>
              <el-descriptions-item label="内容类型">
                {{ downloadConfig.contentTypes.join(', ') }}
              </el-descriptions-item>
              <el-descriptions-item label="每账号数量">
                最多 {{ downloadConfig.maxCount }} 个
              </el-descriptions-item>
              <el-descriptions-item label="时间范围">
                {{ getTimeRangeText(downloadConfig.timeRange) }}
              </el-descriptions-item>
              <el-descriptions-item label="下载模式">
                {{ downloadConfig.downloadMode === 'auto' ? '自动下载' : '手动确认' }}
              </el-descriptions-item>
              <el-descriptions-item label="保存路径">
                {{ downloadConfig.basePath }}
              </el-descriptions-item>
            </el-descriptions>

            <div class="selected-accounts" style="margin-top: 20px;">
              <h4>选中的我们的账号:</h4>
              <div class="account-chips">
                <el-tag
                  v-for="account in selectedAccounts"
                  :key="account.id"
                  size="large"
                  style="margin: 4px;"
                >
                  {{ account.platform_name }} - {{ account.display_name || account.username }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="currentStep > 0" @click="currentStep--">上一步</el-button>
        <el-button
          v-if="currentStep < 2"
          type="primary"
          @click="nextStep"
          :disabled="!canProceedToNextStep"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 2"
          type="success"
          @click="createDownloadTask"
          :loading="creating"
        >
          创建任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
  coreServices: any[]
  ourAccounts: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'created': []
}>()

// 响应式数据
const dialogVisible = ref(false)
const currentStep = ref(0)
const creating = ref(false)
const loadingAccounts = ref(false)

const selectedAccounts = ref<any[]>([])
const filteredAccounts = ref<any[]>([])

// 筛选条件
const filterCoreService = ref('')
const filterPlatform = ref('')
const onlyAccountsWithBenchmarks = ref(true)

// 下载配置
const downloadConfig = reactive({
  basePath: 'H:\\PublishSystem\\',
  contentTypes: ['video'],
  maxCount: 10,
  timeRange: '1month',
  downloadMode: 'auto',
  namingRule: 'timestamp',
  minViews: '',
  minLikes: '',
  keywords: ''
})

// 计算属性
const canProceedToNextStep = computed(() => {
  if (currentStep.value === 0) {
    return selectedAccounts.value.length > 0
  }
  if (currentStep.value === 1) {
    return downloadConfig.contentTypes.length > 0 && downloadConfig.maxCount > 0
  }
  return true
})

// 监听器
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    resetDialog()
    filterAccounts()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 工具方法
const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getPlatformText = (platform: string) => {
  const textMap = {
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'douyin': '抖音',
    'instagram': 'Instagram',
    'facebook': 'Facebook',
    'twitter': 'Twitter'
  }
  return textMap[platform as keyof typeof textMap] || platform
}

const getPlatformDisplayName = (platformId: string) => {
  const platformIdMap = {
    '681efeeecd836bd64b9c2a1e': 'YouTube',
    '681efeeecd836bd64b9c2a20': 'TikTok',
    '681efeeecd836bd64b9c2a22': '抖音',
    '681efeeecd836bd64b9c2a24': 'Instagram'
  }
  return platformIdMap[platformId as keyof typeof platformIdMap] || platformId
}

const formatOurAccountName = (accountInfo: any) => {
  if (!accountInfo) return '未知账号'

  let name = accountInfo.display_name
  if (!name && accountInfo.username) {
    if (accountInfo.username.includes('@')) {
      name = accountInfo.username.split('@')[0]
    } else {
      name = accountInfo.username
    }
  }

  if (!name) {
    name = accountInfo.id ? accountInfo.id.substring(0, 8) : '未知账号'
  }

  return name
}

const getAccountCoreService = (accountInfo: any) => {
  if (!accountInfo || !accountInfo.core_service_id) return '未知'

  const coreService = props.coreServices.find(s => s.id === accountInfo.core_service_id)
  return coreService ? coreService.name : accountInfo.core_service_id
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getDeviceStatusColor = (status: string) => {
  const colorMap = {
    'running': 'success',
    'stopped': 'info',
    'error': 'danger',
    'starting': 'warning',
    'stopping': 'warning',
    'unknown': ''
  }
  return colorMap[status as keyof typeof colorMap] || ''
}

const getDeviceStatusText = (status: string) => {
  const textMap = {
    'running': '运行中',
    'stopped': '已停止',
    'error': '错误',
    'starting': '启动中',
    'stopping': '停止中',
    'unknown': '未知'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

const getBenchmarkCountColor = (count: number) => {
  if (count === 0) return 'info'
  if (count <= 3) return 'warning'
  return 'success'
}

const getTimeRangeText = (range: string) => {
  const rangeMap = {
    '1week': '最近1周',
    '1month': '最近1个月',
    '3months': '最近3个月',
    '6months': '最近6个月',
    'all': '全部'
  }
  return rangeMap[range as keyof typeof rangeMap] || range
}

const getFullDownloadPath = () => {
  if (selectedAccounts.value.length === 0) {
    return downloadConfig.basePath + '我们的平台/我们的账号/对标账号名称/发布月份/'
  }
  const account = selectedAccounts.value[0]
  const currentMonth = new Date().toISOString().slice(0, 7)

  // 这里account是我们的账号，不是对标账号
  return `${downloadConfig.basePath}${account.platform_name}/${account.display_name || account.username}/对标账号名称/${currentMonth}/`
}

// 业务方法
const filterAccounts = () => {
  let accounts = [...props.ourAccounts]

  // 按Core服务筛选
  if (filterCoreService.value) {
    accounts = accounts.filter(account => account.core_service_id === filterCoreService.value)
  }

  // 按平台筛选
  if (filterPlatform.value) {
    accounts = accounts.filter(account => account.platform_id === filterPlatform.value)
  }

  // 仅显示有对标账号的账号
  if (onlyAccountsWithBenchmarks.value) {
    accounts = accounts.filter(account => account.benchmark_count && account.benchmark_count > 0)
  }

  filteredAccounts.value = accounts
}

const handleAccountSelection = (selection: any[]) => {
  selectedAccounts.value = selection
}

const nextStep = () => {
  if (currentStep.value === 0 && selectedAccounts.value.length === 0) {
    ElMessage.warning('请至少选择一个我们的账号')
    return
  }

  if (currentStep.value === 1 && downloadConfig.contentTypes.length === 0) {
    ElMessage.warning('请至少选择一种内容类型')
    return
  }

  currentStep.value++
}

const createDownloadTask = async () => {
  creating.value = true
  try {
    // 构建下载任务数据 - 现在selectedAccounts是我们的账号，需要为每个账号的对标账号创建下载任务
    const downloadTasks: any[] = []

    selectedAccounts.value.forEach(ourAccount => {
      const currentMonth = new Date().toISOString().slice(0, 7)

      // 为该账号的每个对标账号创建下载任务
      if (ourAccount.benchmark_accounts && ourAccount.benchmark_accounts.length > 0) {
        ourAccount.benchmark_accounts.forEach((benchmarkAccount: any) => {
          const downloadPath = `${downloadConfig.basePath}${ourAccount.platform_name}/${ourAccount.display_name || ourAccount.username}/${benchmarkAccount.account_name}/${currentMonth}/`

          downloadTasks.push({
            our_account_id: ourAccount.id,
            benchmark_account_id: benchmarkAccount._id,
            account_name: benchmarkAccount.account_name,
            account_url: benchmarkAccount.account_url || `https://www.douyin.com/user/${benchmarkAccount.account_name}`, // 🔧 添加账号URL
            platform: benchmarkAccount.platform,
            download_path: downloadPath,
            content_types: downloadConfig.contentTypes,
            max_count: downloadConfig.maxCount,
            time_range: downloadConfig.timeRange,
            download_mode: downloadConfig.downloadMode,
            naming_rule: downloadConfig.namingRule,
            filters: {
              min_views: downloadConfig.minViews ? parseInt(downloadConfig.minViews) : null,
              min_likes: downloadConfig.minLikes ? parseInt(downloadConfig.minLikes) : null,
              keywords: downloadConfig.keywords ? downloadConfig.keywords.split(',').map(k => k.trim()) : []
            }
          })
        })
      } else {
        // 如果没有对标账号，创建一个占位任务
        downloadTasks.push({
          our_account_id: ourAccount.id,
          account_name: ourAccount.display_name || ourAccount.username,
          platform: ourAccount.platform_name,
          download_path: `${downloadConfig.basePath}${ourAccount.platform_name}/${ourAccount.display_name || ourAccount.username}/`,
          content_types: downloadConfig.contentTypes,
          max_count: downloadConfig.maxCount,
          time_range: downloadConfig.timeRange,
          download_mode: downloadConfig.downloadMode,
          naming_rule: downloadConfig.namingRule,
          note: '该账号暂无对标账号',
          filters: {
            min_views: downloadConfig.minViews ? parseInt(downloadConfig.minViews) : null,
            min_likes: downloadConfig.minLikes ? parseInt(downloadConfig.minLikes) : null,
            keywords: downloadConfig.keywords ? downloadConfig.keywords.split(',').map(k => k.trim()) : []
          }
        })
      }
    })

    if (downloadTasks.length === 0) {
      ElMessage.warning('没有可创建的下载任务')
      return
    }

    console.log('准备创建的下载任务:', downloadTasks)

    // 🔧 修复：调用真实的API创建下载任务
    for (const task of downloadTasks) {
      // 🔧 修复：构建符合Core服务期望的任务数据格式
      const taskData = {
        task_type: 'benchmark_download',
        platform: task.platform,
        benchmark_account_url: task.account_url || `https://www.douyin.com/user/${task.account_name}`, // 修复字段名
        benchmark_account_name: task.account_name, // 修复字段名
        download_path: task.download_path,
        download_config: {
          content_types: task.content_types,
          max_count: task.max_count,
          time_range: task.time_range,
          download_mode: task.download_mode,
          naming_rule: task.naming_rule,
          filters: task.filters
        },
        our_account_id: task.our_account_id,
        benchmark_account_id: task.benchmark_account_id
      }

      // 🔧 修复：调用正确的API路径
      const response = await fetch('/api/v1/benchmark/download/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(taskData)
      })

      if (!response.ok) {
        throw new Error(`创建任务失败: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('任务创建结果:', result)

      // 🔧 重要修复：记录真实的任务ID
      if (result.task_id) {
        console.log(`✅ 任务创建成功，真实任务ID: ${result.task_id}`)
        // 可以在这里添加任务ID到某个列表中，用于后续跟踪
      }
    }

    ElMessage.success(`下载任务创建成功，共创建 ${downloadTasks.length} 个任务`)
    emit('created')
    handleClose()

  } catch (error) {
    console.error('创建下载任务失败:', error)
    ElMessage.error(`创建下载任务失败: ${(error as Error).message}`)
  } finally {
    creating.value = false
  }
}

const resetDialog = () => {
  currentStep.value = 0
  selectedAccounts.value = []
  filterCoreService.value = ''
  filterPlatform.value = ''
  onlyAccountsWithBenchmarks.value = true

  // 重置下载配置
  downloadConfig.contentTypes = ['video']
  downloadConfig.maxCount = 10
  downloadConfig.timeRange = '1month'
  downloadConfig.downloadMode = 'auto'
  downloadConfig.namingRule = 'timestamp'
  downloadConfig.minViews = ''
  downloadConfig.minLikes = ''
  downloadConfig.keywords = ''
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.download-dialog-container {
  min-height: 400px;
}

.step-content {
  margin: 20px 0;
}

.step-header {
  margin-bottom: 20px;
  text-align: center;
}

.step-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.step-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.account-info {
  padding: 4px 0;
}

.account-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.account-meta {
  display: flex;
  align-items: center;
  gap: 4px;
}

.linked-account .platform-name {
  font-weight: 600;
  color: #409eff;
  font-size: 12px;
}

.linked-account .account-name {
  color: #303133;
  font-size: 12px;
}

.priority-stars {
  color: #f39c12;
  font-size: 12px;
}

.no-data {
  color: #c0c4cc;
}

.download-config-form {
  margin-top: 20px;
}

.path-preview {
  margin-top: 8px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

.path-explanation {
  margin-top: 4px;
  color: #909399;
}

.form-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.download-summary {
  margin-top: 20px;
}

.summary-card {
  border: 1px solid #e4e7ed;
}

.selected-accounts h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.account-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.more-count {
  color: #909399;
  font-size: 12px;
}
</style>
