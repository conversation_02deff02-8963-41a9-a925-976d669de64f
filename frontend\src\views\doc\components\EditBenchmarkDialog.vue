<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑对标账号"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-if="account"
    >
      <el-form-item label="关联账号" prop="our_account_id">
        <el-select
          v-model="form.our_account_id"
          placeholder="请选择我们的账号"
          style="width: 100%"
        >
          <el-option
            v-for="ourAccount in ourAccounts"
            :key="ourAccount.id"
            :label="formatAccountLabel(ourAccount)"
            :value="ourAccount.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="账号名称" prop="account_name">
        <el-input
          v-model="form.account_name"
          placeholder="请输入对标账号名称"
        />
      </el-form-item>

      <el-form-item label="账号链接" prop="account_url">
        <el-input
          v-model="form.account_url"
          placeholder="请输入对标账号链接"
        />
      </el-form-item>

      <el-form-item label="对标类型" prop="benchmark_type">
        <el-radio-group v-model="form.benchmark_type">
          <el-radio value="original">原创</el-radio>
          <el-radio value="recreate">二创</el-radio>
          <el-radio value="repost">搬运</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="账号描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入账号描述（可选）"
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="请选择或输入标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="优先级">
        <el-rate
          v-model="form.priority"
          :max="5"
          show-text
          :texts="['很低', '低', '中等', '高', '很高']"
        />
      </el-form-item>

      <el-form-item label="状态">
        <el-radio-group v-model="form.status">
          <el-radio value="active">活跃</el-radio>
          <el-radio value="inactive">非活跃</el-radio>
          <el-radio value="monitoring">监控中</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { updateBenchmarkAccount, type BenchmarkAccount } from '@/api/content'
import { getAccounts } from '@/api/social'

interface Props {
  modelValue: boolean
  account: BenchmarkAccount | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  our_account_id: '',
  account_name: '',
  account_url: '',
  benchmark_type: 'original' as 'original' | 'recreate' | 'repost',
  description: '',
  tags: [] as string[],
  priority: 3,
  status: 'active' as 'active' | 'inactive' | 'monitoring',
  notes: ''
})

// 我们的账号数据
const ourAccounts = ref<any[]>([])

// 常用标签
const commonTags = [
  '科技', '教育', '娱乐', '生活', '美食', '旅游', '时尚', '健康',
  '财经', '游戏', '音乐', '电影', '体育', '新闻', '搞笑', '萌宠',
  '美妆', '母婴', '汽车', '房产', '职场', '创业', '投资', '理财'
]

// 表单验证规则
const rules: FormRules = {
  our_account_id: [
    { required: true, message: '请选择关联账号', trigger: 'change' }
  ],
  account_name: [
    { required: true, message: '请输入账号名称', trigger: 'blur' },
    { min: 1, max: 100, message: '账号名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  account_url: [
    { required: true, message: '请输入账号链接', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  benchmark_type: [
    { required: true, message: '请选择对标类型', trigger: 'change' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  }
})

// 监听账号数据变化，填充表单
watch(() => props.account, (account) => {
  if (account) {
    Object.assign(form, {
      our_account_id: account.our_account_id,
      account_name: account.account_name,
      account_url: account.account_url,
      benchmark_type: account.benchmark_type,
      description: account.description || '',
      tags: [...account.tags],
      priority: account.priority,
      status: account.status,
      notes: account.notes || ''
    })
  }
}, { immediate: true })

// 方法
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    our_account_id: '',
    account_name: '',
    account_url: '',
    benchmark_type: 'original',
    description: '',
    tags: [],
    priority: 3,
    status: 'active',
    notes: ''
  })
}

const handleSubmit = async () => {
  if (!formRef.value || !props.account) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    await updateBenchmarkAccount(props.account._id!, {
      our_account_id: form.our_account_id,
      account_name: form.account_name,
      account_url: form.account_url,
      benchmark_type: form.benchmark_type,
      description: form.description || undefined,
      tags: form.tags,
      priority: form.priority,
      status: form.status,
      notes: form.notes || undefined
    })

    ElMessage.success('对标账号更新成功')
    emit('success')
  } catch (error) {
    console.error('更新对标账号失败:', error)
    ElMessage.error('更新对标账号失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

// 加载我们的账号列表
const loadOurAccounts = async () => {
  try {
    const response = await getAccounts()
    ourAccounts.value = response.data?.data || []
  } catch (error) {
    console.error('加载账号列表失败:', error)
    ElMessage.error('加载账号列表失败')
  }
}

// 格式化账号标签
const formatAccountLabel = (account: any) => {
  console.log('EditBenchmarkDialog formatAccountLabel 输入数据:', account)

  // 获取平台名称 - 优先使用后端提供的platform_name
  let platformName = account.platform_name || account.platform_display_name || '未知平台'

  // 获取账号名称，优先使用display_name，然后是username的前缀部分
  let name = account.display_name
  if (!name && account.username) {
    // 如果username是邮箱格式，取@前面的部分
    name = account.username.includes('@') ? account.username.split('@')[0] : account.username
  }
  if (!name) {
    name = '未命名账号'
  }

  const result = `${platformName}-${name}`
  console.log('EditBenchmarkDialog formatAccountLabel 结果:', result)
  return result
}

// 生命周期
onMounted(() => {
  loadOurAccounts()
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-rate__text) {
  font-size: 12px;
  color: #606266;
}
</style>
