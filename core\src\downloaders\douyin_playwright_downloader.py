"""
基于Playwright的抖音下载器
通过浏览器自动化获取视频数据，避免API限制
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

try:
    from playwright.async_api import async_playwright, <PERSON>rowser, <PERSON>, BrowserContext
except ImportError:
    async_playwright = None
    Browser = None
    Page = None
    BrowserContext = None

from .base_downloader import BaseDownloader

logger = logging.getLogger(__name__)


class DouyinPlaywrightDownloader(BaseDownloader):
    """基于Playwright的抖音下载器"""
    
    def __init__(self, download_path: str, task_id: str = None, debug_mode: bool = False, backend_api_client=None):
        super().__init__("douyin", download_path)  # 🔧 修复参数顺序
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.debug_mode = debug_mode  # 🔧 添加调试模式
        self.task_id = task_id

        # 🔧 使用Backend API客户端而不是直接数据库操作
        self.backend_api = backend_api_client

        # 检查Playwright是否可用
        if async_playwright is None:
            raise ImportError("Playwright未安装，请运行: pip install playwright")
    
    async def _init_browser(self):
        """初始化浏览器"""
        try:
            if self.browser is None:
                self.add_log("🔧 初始化Playwright浏览器")

                self.playwright = await async_playwright().start()
                self.add_log("✅ Playwright已启动")

                # 🔧 根据调试模式决定是否显示浏览器界面
                headless_mode = not self.debug_mode
                self.add_log(f"🎭 浏览器模式: {'无头模式' if headless_mode else '有界面模式'}")

                # 启动浏览器（使用Chromium）
                browser_args = [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-blink-features=AutomationControlled',  # 🔧 隐藏自动化标识
                    '--disable-web-security',  # 🔧 禁用网络安全限制
                    '--disable-features=VizDisplayCompositor',  # 🔧 禁用某些功能
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',  # 🔧 设置真实UA
                    '--accept-lang=zh-CN,zh;q=0.9,en;q=0.8',  # 🔧 设置语言
                    '--disable-extensions',  # 🔧 禁用扩展
                    '--disable-plugins',  # 🔧 禁用插件
                    '--disable-images',  # 🔧 禁用图片加载以提高速度
                ]

                if self.debug_mode:
                    # 调试模式下的额外参数
                    browser_args.extend([
                        '--start-maximized',  # 最大化窗口
                    ])
                else:
                    # 生产模式下的优化参数
                    browser_args.extend([
                        '--disable-gpu',  # 禁用GPU加速
                        '--disable-accelerated-2d-canvas',  # 禁用2D加速
                    ])

                self.browser = await self.playwright.chromium.launch(
                    headless=headless_mode,
                    args=browser_args,
                    slow_mo=1000 if self.debug_mode else 0  # 🔧 调试模式下减慢操作速度
                )
                self.add_log("✅ Chromium浏览器已启动")
                
                # 创建浏览器上下文
                context_options = {
                    'viewport': {'width': 1920, 'height': 1080},
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'locale': 'zh-CN',  # 🔧 设置中文环境
                    'timezone_id': 'Asia/Shanghai',  # 🔧 设置时区
                    'extra_http_headers': {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache',
                        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                        'Sec-Ch-Ua-Mobile': '?0',
                        'Sec-Ch-Ua-Platform': '"Windows"',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'none',
                        'Sec-Fetch-User': '?1',
                        'Upgrade-Insecure-Requests': '1',
                    }
                }

                self.context = await self.browser.new_context(**context_options)
                self.add_log("✅ 浏览器上下文已创建")

                # 🔧 添加反检测脚本
                await self.context.add_init_script("""
                    // 移除webdriver属性
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });

                    // 伪造chrome对象
                    window.chrome = {
                        runtime: {},
                        loadTimes: function() {},
                        csi: function() {},
                        app: {}
                    };

                    // 伪造权限查询
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' ?
                            Promise.resolve({ state: Notification.permission }) :
                            originalQuery(parameters)
                    );

                    // 伪造插件
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });

                    // 伪造语言
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en'],
                    });
                """)

                # 创建页面
                self.page = await self.context.new_page()
                self.add_log("✅ 页面已创建")

                # 🔧 设置页面事件监听
                self.page.on('console', lambda msg: self.add_log(f"🖥️ 控制台: {msg.text}"))
                self.page.on('pageerror', lambda error: self.add_log(f"❌ 页面错误: {error}"))
                self.page.on('requestfailed', lambda request: self.add_log(f"🚫 请求失败: {request.url}"))

                # 🔧 设置资源拦截策略
                if not self.debug_mode:
                    # 生产模式：只拦截图片和字体，保留CSS以确保页面正常渲染
                    await self.page.route("**/*.{png,jpg,jpeg,gif,svg,woff,woff2,ttf,eot}", lambda route: route.abort())
                    self.add_log("🚫 已设置资源拦截（图片、字体）")
                else:
                    # 调试模式：不拦截任何资源
                    self.add_log("🔧 调试模式：不拦截资源")

                # 🔧 设置更长的超时时间
                self.page.set_default_timeout(120000)  # 120秒
                self.page.set_default_navigation_timeout(120000)  # 120秒

                self.add_log("🎉 浏览器初始化完成")
                
        except Exception as e:
            error_msg = f"初始化浏览器失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            raise
    
    async def _close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            
            if self.context:
                await self.context.close()
                self.context = None
            
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
                
            self.add_log("浏览器已关闭")
            
        except Exception as e:
            logger.error(f"关闭浏览器失败: {str(e)}")
    
    async def get_account_info(self, account_url: str) -> Dict[str, Any]:
        """获取账号信息
        
        Args:
            account_url: 账号URL
            
        Returns:
            Dict: 账号信息
        """
        try:
            await self._init_browser()

            self.add_log(f"🌐 开始访问账号页面: {account_url}")

            # 🔧 分步骤访问页面，添加详细日志
            self.add_log("📡 正在发送页面请求...")

            # 🔧 增加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.add_log(f"🔄 尝试访问页面 (第 {attempt + 1}/{max_retries} 次)")

                    # 访问账号页面，使用更宽松的等待条件
                    response = await self.page.goto(
                        account_url,
                        wait_until='domcontentloaded',  # 只等待DOM加载完成
                        timeout=120000  # 120秒超时
                    )

                    if response:
                        self.add_log(f"✅ 页面响应状态: {response.status}")

                        # 检查响应状态
                        if response.status >= 400:
                            self.add_log(f"⚠️ 页面返回错误状态: {response.status}")
                            if attempt < max_retries - 1:
                                await asyncio.sleep(5)  # 等待5秒后重试
                                continue

                    # 等待页面基本内容加载
                    self.add_log("⏳ 等待页面基本内容加载...")
                    await self.page.wait_for_timeout(8000)  # 增加等待时间

                    # 🔧 检查页面是否正确加载
                    current_url = self.page.url
                    page_title = await self.page.title()
                    self.add_log(f"📄 当前URL: {current_url}")
                    self.add_log(f"📄 页面标题: {page_title}")

                    # 🔧 检查是否被重定向或遇到错误页面
                    if 'error' in current_url.lower() or 'not-found' in current_url.lower():
                        self.add_log("⚠️ 检测到错误页面或重定向")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(10)  # 等待更长时间后重试
                            continue

                    # 🔧 尝试等待网络空闲，但不强制要求
                    try:
                        self.add_log("🌐 尝试等待网络请求完成...")
                        await self.page.wait_for_load_state('networkidle', timeout=60000)
                        self.add_log("✅ 网络请求已完成")
                    except Exception as network_error:
                        self.add_log(f"⚠️ 网络等待超时，但继续执行: {network_error}")

                    # 如果到这里说明页面访问成功
                    break

                except Exception as nav_error:
                    self.add_log(f"❌ 页面导航失败 (第 {attempt + 1} 次): {nav_error}")

                    # 尝试截图调试
                    if self.debug_mode:
                        try:
                            screenshot_path = os.path.join(self.download_path, f"debug_navigation_error_attempt_{attempt + 1}.png")
                            os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
                            await self.page.screenshot(path=screenshot_path)
                            self.add_log(f"📸 已保存错误截图: {screenshot_path}")
                        except Exception as screenshot_error:
                            self.add_log(f"📸 截图失败: {screenshot_error}")

                    # 如果是最后一次尝试，抛出异常
                    if attempt == max_retries - 1:
                        raise nav_error

                    # 等待后重试
                    wait_time = (attempt + 1) * 10  # 递增等待时间
                    self.add_log(f"⏳ 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
            
            # 提取账号信息
            account_info = await self.page.evaluate("""
                () => {
                    try {
                        // 尝试从页面中提取账号信息
                        const nickname = document.querySelector('[data-e2e="user-title"]')?.textContent?.trim() || 
                                       document.querySelector('.account-name')?.textContent?.trim() || '';
                        
                        const signature = document.querySelector('[data-e2e="user-description"]')?.textContent?.trim() || 
                                        document.querySelector('.account-desc')?.textContent?.trim() || '';
                        
                        const followerCount = document.querySelector('[data-e2e="followers-count"]')?.textContent?.trim() || '0';
                        const followingCount = document.querySelector('[data-e2e="following-count"]')?.textContent?.trim() || '0';
                        
                        // 获取头像
                        const avatar = document.querySelector('[data-e2e="user-avatar"] img')?.src || 
                                     document.querySelector('.account-avatar img')?.src || '';
                        
                        return {
                            nickname: nickname,
                            signature: signature,
                            follower_count: followerCount,
                            following_count: followingCount,
                            avatar_url: avatar,
                            page_title: document.title
                        };
                    } catch (e) {
                        return {
                            error: e.message,
                            page_title: document.title,
                            page_url: window.location.href
                        };
                    }
                }
            """)
            
            if account_info.get('error'):
                self.add_log(f"页面信息提取出错: {account_info['error']}")
            
            self.add_log(f"账号信息获取成功: {account_info.get('nickname', '未知')}")
            return account_info
            
        except Exception as e:
            error_msg = f"获取账号信息失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return {}
    
    async def get_video_list(
        self,
        account_url: str = None,
        max_count: int = 50,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """获取视频列表

        Args:
            account_url: 账号URL（可选，如果页面已加载则不需要）
            max_count: 最大数量
            filters: 过滤条件

        Returns:
            List: 视频列表
        """
        try:
            # 🔧 检查浏览器是否已初始化，如果没有则初始化
            if not self.page:
                await self._init_browser()
                if account_url:
                    self.add_log(f"访问账号页面: {account_url}")
                    await self.page.goto(account_url, wait_until='domcontentloaded', timeout=120000)
                    await self.page.wait_for_timeout(5000)

            self.add_log(f"开始获取视频列表，目标数量: {max_count}")

            # 🔧 如果页面已经加载，直接使用当前页面，不重新访问
            current_url = self.page.url
            self.add_log(f"当前页面URL: {current_url}")

            # 等待页面稳定
            await self.page.wait_for_timeout(2000)

            # 🔧 新增：检查是否有月份筛选功能
            available_months = await self.get_available_months()

            if available_months and filters and filters.get('use_month_filter'):
                # 按月份获取视频
                all_videos = []
                target_months = filters.get('target_months', [])

                if not target_months:
                    # 如果没有指定月份，获取所有月份
                    target_months = available_months

                for month_info in target_months:
                    if len(all_videos) >= max_count:
                        break

                    month_videos = await self.get_videos_by_month(month_info['year'], month_info['month'])
                    all_videos.extend(month_videos)

                # 限制数量
                all_videos = all_videos[:max_count]
                self.add_log(f"📅 按月份筛选获取到 {len(all_videos)} 个视频")
                return all_videos

            # 🔧 原有的滚动获取逻辑（作为备用方案）
            videos = []
            scroll_count = 0
            max_scrolls = 20  # 最大滚动次数
            
            while len(videos) < max_count and scroll_count < max_scrolls:
                # 滚动页面加载更多视频
                await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await self.page.wait_for_timeout(2000)
                
                # 提取当前页面的视频信息
                current_videos = await self.page.evaluate("""
                    () => {
                        const videoElements = document.querySelectorAll('[data-e2e="user-post-item"], .video-item, .aweme-item');
                        const videos = [];
                        
                        videoElements.forEach((element, index) => {
                            try {
                                // 提取视频信息
                                const link = element.querySelector('a')?.href || '';
                                const title = element.querySelector('[data-e2e="video-desc"]')?.textContent?.trim() || 
                                            element.querySelector('.video-title')?.textContent?.trim() || '';
                                
                                const cover = element.querySelector('img')?.src || '';
                                
                                // 提取统计数据
                                const likeCount = element.querySelector('[data-e2e="video-like-count"]')?.textContent?.trim() || '0';
                                const viewCount = element.querySelector('[data-e2e="video-play-count"]')?.textContent?.trim() || '0';
                                
                                if (link && link.includes('/video/')) {
                                    videos.push({
                                        id: link.split('/video/')[1]?.split('?')[0] || `video_${index}`,
                                        title: title,
                                        video_url: link,
                                        cover_url: cover,
                                        like_count: likeCount,
                                        view_count: viewCount,
                                        create_time: Date.now(),
                                        element_index: index
                                    });
                                }
                            } catch (e) {
                                console.log('提取视频信息失败:', e);
                            }
                        });
                        
                        return videos;
                    }
                """)
                
                # 去重并添加新视频
                existing_ids = {v['id'] for v in videos}
                new_videos = [v for v in current_videos if v['id'] not in existing_ids]
                videos.extend(new_videos)
                
                self.add_log(f"滚动 {scroll_count + 1} 次，当前获取到 {len(videos)} 个视频")
                
                # 如果没有新视频，说明已经到底了
                if not new_videos:
                    break
                
                scroll_count += 1
            
            # 应用过滤条件
            if filters:
                videos = [v for v in videos if self._apply_filters(v, filters)]
            
            # 限制数量
            videos = videos[:max_count]
            
            self.add_log(f"最终获取到 {len(videos)} 个视频")
            return videos
            
        except Exception as e:
            error_msg = f"获取视频列表失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return []
    
    def _apply_filters(self, video_info: Dict[str, Any], filters: Dict[str, Any]) -> bool:
        """应用过滤条件"""
        if not filters:
            return True
        
        try:
            # 关键词过滤
            keywords = filters.get('keywords', [])
            if keywords:
                title = video_info.get('title', '').lower()
                if not any(keyword.lower() in title for keyword in keywords):
                    return False
            
            # 这里可以添加更多过滤条件
            return True
            
        except Exception as e:
            logger.error(f"应用过滤条件失败: {str(e)}")
            return True

    async def download_video(self, video_info: Dict[str, Any], save_path: str) -> bool:
        """下载单个视频

        Args:
            video_info: 视频信息
            save_path: 保存路径

        Returns:
            bool: 是否下载成功
        """
        try:
            video_url = video_info.get('video_url')
            if not video_url:
                raise ValueError("视频URL为空")

            self.add_log(f"开始下载视频: {video_info.get('title', 'Unknown')}")

            await self._init_browser()

            # 访问视频页面
            await self.page.goto(video_url, wait_until='networkidle', timeout=30000)
            await self.page.wait_for_timeout(3000)

            # 尝试获取视频下载链接
            download_url = await self.page.evaluate("""
                () => {
                    try {
                        // 尝试从页面中提取视频下载链接
                        const videoElement = document.querySelector('video');
                        if (videoElement && videoElement.src) {
                            return videoElement.src;
                        }

                        // 尝试从网络请求中获取
                        const videos = document.querySelectorAll('video source');
                        for (let source of videos) {
                            if (source.src) {
                                return source.src;
                            }
                        }

                        return null;
                    } catch (e) {
                        return null;
                    }
                }
            """)

            if not download_url:
                # 如果无法直接获取，尝试监听网络请求
                self.add_log("尝试通过网络监听获取视频链接")

                video_urls = []

                def handle_response(response):
                    if response.url.endswith('.mp4') or 'video' in response.headers.get('content-type', ''):
                        video_urls.append(response.url)

                self.page.on('response', handle_response)

                # 重新加载页面触发视频加载
                await self.page.reload(wait_until='networkidle')
                await self.page.wait_for_timeout(5000)

                if video_urls:
                    download_url = video_urls[0]
                    self.add_log(f"通过网络监听获取到视频链接: {download_url}")

            if download_url:
                # 使用浏览器下载视频
                async with self.context.expect_download() as download_info:
                    await self.page.goto(download_url)

                download = await download_info.value

                # 创建目录
                os.makedirs(os.path.dirname(save_path), exist_ok=True)

                # 保存文件
                await download.save_as(save_path)

                # 验证文件
                if os.path.exists(save_path) and os.path.getsize(save_path) > 0:
                    self.add_log(f"视频下载成功: {save_path}")
                    return True
                else:
                    raise Exception("下载的文件为空")
            else:
                raise Exception("无法获取视频下载链接")

        except Exception as e:
            error_msg = f"下载视频失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return False

    async def download_account_content(
        self,
        account_url: str,
        account_name: str,
        download_config: Dict[str, Any]
    ) -> bool:
        """下载账号内容

        Args:
            account_url: 账号URL
            account_name: 账号名称
            download_config: 下载配置

        Returns:
            bool: 是否下载成功
        """
        try:
            self.set_status("downloading", f"开始下载 {account_name} 的内容")
            self.update_progress(0, "初始化Playwright浏览器")

            # 获取账号信息
            account_info = await self.get_account_info(account_url)
            if not account_info or account_info.get('error'):
                raise Exception("获取账号信息失败")

            self.update_progress(10, "获取视频列表")

            # 获取视频列表（复用已加载的页面）
            max_videos = download_config.get('max_videos', 50)
            filters = {
                'keywords': download_config.get('keywords', [])
            }

            videos = await self.get_video_list(None, max_videos, filters)  # 🔧 不传URL，复用当前页面
            if not videos:
                raise Exception("未获取到视频列表")

            self.total_count = len(videos)
            self.update_progress(20, f"找到 {self.total_count} 个视频")

            # 创建下载目录结构
            videos_dir = os.path.join(self.download_path, "videos")
            metadata_dir = os.path.join(self.download_path, "metadata")
            os.makedirs(videos_dir, exist_ok=True)
            os.makedirs(metadata_dir, exist_ok=True)

            # 保存账号信息
            account_info_path = os.path.join(self.download_path, "account_info.json")
            with open(account_info_path, 'w', encoding='utf-8') as f:
                json.dump(account_info, f, ensure_ascii=False, indent=2)

            # 下载视频
            naming_rule = download_config.get('naming_rule', 'timestamp')
            include_metadata = download_config.get('include_metadata', True)
            skip_existing = download_config.get('skip_existing', True)

            for i, video_info in enumerate(videos):
                try:
                    # 生成文件名
                    filename = self.generate_filename(video_info, naming_rule)
                    video_path = os.path.join(videos_dir, filename)

                    # 检查是否跳过已存在的文件
                    if skip_existing and self.is_file_exists(video_path):
                        self.add_log(f"跳过已存在的文件: {filename}")
                        self.downloaded_count += 1
                        continue

                    # 下载视频
                    success = await self.download_video(video_info, video_path)
                    if success:
                        self.downloaded_count += 1

                        # 保存元数据
                        if include_metadata:
                            self.save_metadata(video_info, video_path)

                    # 更新进度
                    progress = 20 + int((i + 1) / len(videos) * 70)
                    self.update_progress(progress, f"已下载 {self.downloaded_count}/{self.total_count}")

                    # 避免请求过快
                    await asyncio.sleep(3)

                except Exception as e:
                    self.add_log(f"下载视频失败: {video_info.get('title', 'Unknown')} - {str(e)}")
                    continue

            # 生成下载报告
            self._generate_download_report(account_info, videos)

            self.update_progress(100, f"下载完成，成功下载 {self.downloaded_count} 个视频")
            self.set_status("completed", "下载任务完成")

            return True

        except Exception as e:
            error_msg = f"下载账号内容失败: {str(e)}"
            self.set_status("error", error_msg)
            self.error_message = error_msg
            logger.error(error_msg)
            return False
        finally:
            await self._close_browser()

    def _generate_download_report(self, account_info: Dict[str, Any], videos: List[Dict[str, Any]]):
        """生成下载报告"""
        try:
            report = {
                'download_time': datetime.now().isoformat(),
                'account_info': account_info,
                'total_videos': len(videos),
                'downloaded_count': self.downloaded_count,
                'success_rate': f"{(self.downloaded_count / len(videos) * 100):.1f}%" if videos else "0%",
                'download_path': self.download_path,
                'platform': self.platform,
                'method': 'playwright',
                'logs': self.logs[-50:]  # 最近50条日志
            }

            report_path = os.path.join(self.download_path, "download_report.json")
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            self.add_log(f"下载报告已生成: {report_path}")

        except Exception as e:
            self.add_log(f"生成下载报告失败: {str(e)}")

    async def get_available_months(self) -> List[Dict[str, Any]]:
        """获取账号可用的发布月份

        Returns:
            List: 可用的年份和月份列表
        """
        try:
            self.add_log("🗓️ 开始获取可用的发布月份")

            if not self.page:
                raise Exception("页面未加载")

            # 查找日期筛选元素
            date_filter_selector = '.eIibRZ5K'

            try:
                # 等待日期筛选元素出现
                await self.page.wait_for_selector(date_filter_selector, timeout=10000)

                # 检查是否包含"日期筛选"文本
                filter_elements = await self.page.query_selector_all(date_filter_selector)
                date_filter_element = None

                for element in filter_elements:
                    text = await element.text_content()
                    if "日期筛选" in text:
                        date_filter_element = element
                        break

                if not date_filter_element:
                    self.add_log("⚠️ 未找到日期筛选元素")
                    return []

                self.add_log("✅ 找到日期筛选元素")

                # 悬浮到日期筛选元素
                await date_filter_element.hover()
                self.add_log("🖱️ 悬浮到日期筛选元素")

                # 等待月份选择器出现
                await self.page.wait_for_timeout(1000)

                # 🔧 使用Playwright原生方法悬浮到年份上
                months_data = []

                try:
                    # 查找月份容器
                    months_container = await self.page.query_selector('.JhNW3ZH3')
                    if not months_container:
                        self.add_log("⚠️ 未找到月份容器")
                        return []

                    # 获取所有年份元素
                    year_elements = await months_container.query_selector_all('.URphBgKP:first-child .qaJJOy6j')

                    for year_element in year_elements:
                        year_text = await year_element.text_content()
                        year_text = year_text.strip() if year_text else ""

                        if year_text != '全部' and '年' in year_text:
                            year = year_text.replace('年', '')
                            self.add_log(f"🗓️ 悬浮到年份: {year_text}")

                            # 悬浮到年份元素上
                            await year_element.hover()
                            await self.page.wait_for_timeout(500)

                            # 获取该年份下的月份
                            month_elements = await months_container.query_selector_all('.URphBgKP:last-child .qaJJOy6j')

                            for month_element in month_elements:
                                month_text = await month_element.text_content()
                                month_text = month_text.strip() if month_text else ""

                                if '月' in month_text:
                                    month = month_text.replace('月', '')
                                    months_data.append({
                                        'year': year,
                                        'month': month.zfill(2),
                                        'display': f"{year}年{month}月",
                                        'element': month_element  # 🔧 保存元素引用，直接点击
                                    })
                                    self.add_log(f"📅 找到月份: {year}年{month}月")

                            # 悬浮完一个年份后稍等一下
                            await self.page.wait_for_timeout(200)

                except Exception as e:
                    self.add_log(f"⚠️ 获取月份时出错: {str(e)}")
                    return []

                self.add_log(f"📅 获取到 {len(months_data)} 个可用月份: {[m['display'] for m in months_data]}")
                return months_data

            except Exception as e:
                self.add_log(f"⚠️ 未找到日期筛选功能，可能该账号没有按月份分类: {e}")
                return []

        except Exception as e:
            error_msg = f"获取可用月份失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return []

    async def get_videos_by_month(self, year: str, month: str) -> List[Dict[str, Any]]:
        """获取指定月份的视频列表

        Args:
            year: 年份
            month: 月份

        Returns:
            List: 视频列表
        """
        try:
            self.add_log(f"📅 开始获取 {year}年{month}月 的视频")

            if not self.page:
                raise Exception("页面未加载")

            # 悬浮到日期筛选并选择月份
            date_filter_selector = '.eIibRZ5K'
            filter_elements = await self.page.query_selector_all(date_filter_selector)
            date_filter_element = None

            for element in filter_elements:
                text = await element.text_content()
                if "日期筛选" in text:
                    date_filter_element = element
                    break

            if not date_filter_element:
                raise Exception("未找到日期筛选元素")

            await date_filter_element.hover()
            await self.page.wait_for_timeout(1000)
            self.add_log(f"🖱️ 悬浮到日期筛选元素")

            # 🔧 使用Playwright原生方法：悬浮日期筛选 → 悬浮年份 → 点击月份
            try:
                # 步骤1：等待下拉菜单稳定
                await self.page.wait_for_timeout(1000)
                self.add_log(f"🔍 开始选择 {year}年{month}月")

                # 步骤2：查找并悬浮到目标年份
                year_text = f"{year}年"
                try:
                    # 使用text定位器查找年份元素
                    year_locator = self.page.locator(f"text={year_text}").first
                    await year_locator.wait_for(timeout=5000)

                    self.add_log(f"✅ 找到年份元素: {year_text}")

                    # 悬浮到年份元素以显示月份
                    await year_locator.hover()
                    self.add_log(f"🖱️ 悬浮到年份: {year_text}")

                except Exception as e:
                    self.add_log(f"❌ 未找到年份元素 {year_text}: {e}")
                    return []

                # 步骤3：等待月份显示
                await self.page.wait_for_timeout(1000)

                # 步骤4：查找并点击目标月份
                month_text = f"{int(month)}月"
                try:
                    # 使用text定位器查找月份元素
                    month_locator = self.page.locator(f"text={month_text}").first
                    await month_locator.wait_for(timeout=5000)

                    self.add_log(f"✅ 找到月份元素: {month_text}")

                    # 点击月份
                    await month_locator.click()
                    self.add_log(f"🖱️ 点击了月份: {month_text}")

                except Exception as e:
                    self.add_log(f"❌ 未找到月份元素 {month_text}: {e}")
                    return []

                # 步骤5：等待页面加载月份内容
                await self.page.wait_for_timeout(3000)
                self.add_log(f"✅ 成功选择 {year}年{month}月")

            except Exception as e:
                self.add_log(f"⚠️ 选择月份失败: {e}")
                return []

            # 等待月份视频容器加载
            month_container_id = f"#{year}-{int(month)}-monthvideobox"

            try:
                await self.page.wait_for_selector(month_container_id, timeout=10000)
                self.add_log(f"✅ 找到月份容器: {month_container_id}")
            except Exception as e:
                self.add_log(f"⚠️ 未找到月份容器，尝试通用选择器")
                month_container_id = ".LPkaLYSW"

            # 提取该月份的视频信息
            videos = await self.page.evaluate(f"""
                (containerId) => {{
                    const container = document.querySelector(containerId) || document.querySelector('.LPkaLYSW');
                    if (!container) return [];

                    // 🔧 使用更稳定的语义化选择器，提高泛化能力
                    // 优先使用ID前缀选择器，更稳定
                    let videoCards = container.querySelectorAll('[id^="videocard-"]');

                    // 如果没找到，尝试通过视频链接查找父容器
                    if (videoCards.length === 0) {{
                        const videoLinks = container.querySelectorAll('a[href*="/video/"]');
                        videoCards = Array.from(videoLinks).map(link => {{
                            // 向上查找包含视频信息的容器
                            let parent = link.parentElement;
                            while (parent && parent !== container) {{
                                if (parent.querySelector('img') && parent.querySelector('a[href*="/video/"]')) {{
                                    return parent;
                                }}
                                parent = parent.parentElement;
                            }}
                            return link.parentElement;
                        }}).filter(Boolean);
                    }}

                    const videos = [];
                    console.log('找到视频卡片数量:', videoCards.length);

                    videoCards.forEach((card, index) => {{
                        try {{
                            const link = card.querySelector('a[href*="/video/"]');
                            if (!link) return;

                            const videoUrl = link.href;
                            const videoId = videoUrl.split('/video/')[1]?.split('?')[0];

                            if (!videoId) return;

                            // 提取视频信息
                            const title = card.querySelector('.Ja95nb2Z, .ztA3qIFr')?.textContent?.trim() || '';
                            const cover = card.querySelector('img')?.src || '';
                            const likeCount = card.querySelector('.b3Dh2ia8')?.textContent?.trim() || '0';

                            videos.push({{
                                id: videoId,
                                title: title,
                                video_url: videoUrl,
                                cover_url: cover,
                                like_count: likeCount,
                                year: '{year}',
                                month: '{month}',
                                create_time: Date.now(),
                                element_index: index
                            }});
                        }} catch (e) {{
                            console.log('提取视频信息失败:', e);
                        }}
                    }});

                    return videos;
                }}
            """, month_container_id)

            self.add_log(f"📹 {year}年{month}月 获取到 {len(videos)} 个视频")
            return videos

        except Exception as e:
            error_msg = f"获取 {year}年{month}月 视频失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return []

    async def get_videos_by_month_optimized(self, year: str, month: str) -> List[Dict[str, Any]]:
        """优化版本：直接选择月份并获取视频，避免重复操作

        Args:
            year: 年份
            month: 月份

        Returns:
            List[Dict]: 视频信息列表
        """
        try:
            self.add_log(f"📅 开始获取 {year}年{month}月 的视频")

            # 查找日期筛选元素
            date_filter_element = await self.page.query_selector('.Ej8Ej8Ej')
            if not date_filter_element:
                raise Exception("未找到日期筛选元素")

            # 悬浮到日期筛选元素
            await date_filter_element.hover()
            await self.page.wait_for_timeout(1000)
            self.add_log(f"🖱️ 悬浮到日期筛选元素")

            # 🔧 一次性完成：悬浮年份 → 点击月份
            try:
                # 步骤1：查找并悬浮到目标年份
                year_text = f"{year}年"
                year_locator = self.page.locator(f"text={year_text}").first
                await year_locator.wait_for(timeout=5000)

                self.add_log(f"✅ 找到年份元素: {year_text}")

                # 悬浮到年份元素以显示月份
                await year_locator.hover()
                self.add_log(f"🖱️ 悬浮到年份: {year_text}")

                # 步骤2：等待月份显示
                await self.page.wait_for_timeout(1000)

                # 步骤3：直接点击目标月份
                month_text = f"{int(month)}月"
                month_locator = self.page.locator(f"text={month_text}").first
                await month_locator.wait_for(timeout=5000)

                self.add_log(f"✅ 找到月份元素: {month_text}")

                # 点击月份
                await month_locator.click()
                self.add_log(f"🖱️ 点击了月份: {month_text}")

                # 步骤4：等待页面加载月份内容
                await self.page.wait_for_timeout(3000)
                self.add_log(f"✅ 成功选择 {year}年{month}月")

            except Exception as e:
                self.add_log(f"❌ 选择月份失败: {e}")
                return []

            # 等待月份视频容器加载
            month_container_id = f"#{year}-{int(month)}-monthvideobox"

            try:
                await self.page.wait_for_selector(month_container_id, timeout=10000)
                self.add_log(f"✅ 找到月份容器: {month_container_id}")
            except Exception as e:
                self.add_log(f"⚠️ 未找到月份容器，尝试通用选择器")
                month_container_id = ".LPkaLYSW"

            # 提取该月份的视频信息
            videos = await self.page.evaluate(f"""
                (containerId) => {{
                    const container = document.querySelector(containerId) || document.querySelector('.LPkaLYSW');
                    if (!container) return [];

                    // 🔧 使用更稳定的语义化选择器，提高泛化能力
                    // 优先使用ID前缀选择器，更稳定
                    let videoCards = container.querySelectorAll('[id^="videocard-"]');

                    // 如果没找到，尝试通过视频链接查找父容器
                    if (videoCards.length === 0) {{
                        const videoLinks = container.querySelectorAll('a[href*="/video/"]');
                        videoCards = Array.from(videoLinks).map(link => {{
                            // 向上查找包含视频信息的容器
                            let parent = link.parentElement;
                            while (parent && parent !== container) {{
                                if (parent.querySelector('img') && parent.querySelector('a[href*="/video/"]')) {{
                                    return parent;
                                }}
                                parent = parent.parentElement;
                            }}
                            return link.parentElement;
                        }}).filter(Boolean);
                    }}

                    const videos = [];
                    console.log('找到视频卡片数量:', videoCards.length);

                    videoCards.forEach((card, index) => {{
                        try {{
                            const link = card.querySelector('a[href*="/video/"]');
                            if (!link) return;

                            const videoUrl = link.href;
                            const videoId = videoUrl.split('/video/')[1]?.split('?')[0];

                            if (!videoId) return;

                            // 提取视频信息
                            const title = card.querySelector('.Ja95nb2Z, .ztA3qIFr')?.textContent?.trim() || '';
                            const cover = card.querySelector('img')?.src || '';
                            const likeCount = card.querySelector('.b3Dh2ia8')?.textContent?.trim() || '0';

                            videos.push({{
                                id: videoId,
                                title: title,
                                video_url: videoUrl,
                                cover_url: cover,
                                like_count: likeCount,
                                year: '{year}',
                                month: '{month}',
                                create_time: Date.now(),
                                element_index: index
                            }});
                        }} catch (e) {{
                            console.log('提取视频信息失败:', e);
                        }}
                    }});

                    return videos;
                }}
            """, month_container_id)

            self.add_log(f"📹 {year}年{month}月 获取到 {len(videos)} 个视频")
            return videos

        except Exception as e:
            error_msg = f"获取 {year}年{month}月 视频失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return []

    async def extract_month_videos(self, year: str, month: str) -> List[Dict[str, Any]]:
        """提取当前页面的月份视频信息

        Args:
            year: 年份
            month: 月份

        Returns:
            List[Dict]: 视频信息列表
        """
        try:
            # 等待月份视频容器加载
            month_container_id = f"#{year}-{int(month)}-monthvideobox"

            try:
                await self.page.wait_for_selector(month_container_id, timeout=10000)
                self.add_log(f"✅ 找到月份容器: {month_container_id}")
            except Exception as e:
                self.add_log(f"⚠️ 未找到月份容器，尝试通用选择器")
                month_container_id = ".LPkaLYSW"

            # 提取该月份的视频信息
            videos = await self.page.evaluate(f"""
                (containerId) => {{
                    const container = document.querySelector(containerId) || document.querySelector('.LPkaLYSW');
                    if (!container) return [];

                    // 🔧 使用更稳定的语义化选择器，提高泛化能力
                    // 优先使用ID前缀选择器，更稳定
                    let videoCards = container.querySelectorAll('[id^="videocard-"]');

                    // 如果没找到，尝试通过视频链接查找父容器
                    if (videoCards.length === 0) {{
                        const videoLinks = container.querySelectorAll('a[href*="/video/"]');
                        videoCards = Array.from(videoLinks).map(link => {{
                            // 向上查找包含视频信息的容器
                            let parent = link.parentElement;
                            while (parent && parent !== container) {{
                                if (parent.querySelector('img') && parent.querySelector('a[href*="/video/"]')) {{
                                    return parent;
                                }}
                                parent = parent.parentElement;
                            }}
                            return link.parentElement;
                        }}).filter(Boolean);
                    }}

                    const videos = [];
                    console.log('找到视频卡片数量:', videoCards.length);

                    videoCards.forEach((card, index) => {{
                        try {{
                            const link = card.querySelector('a[href*="/video/"]');
                            if (!link) return;

                            const videoUrl = link.href;
                            const videoId = videoUrl.split('/video/')[1]?.split('?')[0];

                            if (!videoId) return;

                            // 提取视频信息
                            const title = card.querySelector('.Ja95nb2Z, .ztA3qIFr')?.textContent?.trim() || '';
                            const cover = card.querySelector('img')?.src || '';
                            const likeCount = card.querySelector('.b3Dh2ia8')?.textContent?.trim() || '0';

                            videos.push({{
                                id: videoId,
                                title: title,
                                video_url: videoUrl,
                                cover_url: cover,
                                like_count: likeCount,
                                year: '{year}',
                                month: '{month}',
                                create_time: Date.now(),
                                element_index: index
                            }});
                        }} catch (e) {{
                            console.log('提取视频信息失败:', e);
                        }}
                    }});

                    return videos;
                }}
            """, month_container_id)

            self.add_log(f"📹 {year}年{month}月 获取到 {len(videos)} 个视频")
            return videos

        except Exception as e:
            error_msg = f"提取 {year}年{month}月 视频失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return []

    async def get_video_real_url(self, video_url: str) -> Optional[str]:
        """获取视频的真实下载地址

        Args:
            video_url: 视频页面URL

        Returns:
            str: 真实的视频下载地址
        """
        try:
            self.add_log(f"🎬 开始获取视频真实地址: {video_url}")

            if not self.page:
                await self._init_browser()

            # 访问视频页面
            await self.page.goto(video_url, wait_until='domcontentloaded', timeout=60000)
            await self.page.wait_for_timeout(3000)

            # 等待video元素加载
            try:
                await self.page.wait_for_selector('video', timeout=15000)
                self.add_log("✅ 找到video元素")
            except Exception as e:
                self.add_log(f"⚠️ 未找到video元素: {e}")
                return None

            # 提取真实的视频下载地址
            real_url = await self.page.evaluate("""
                () => {
                    const video = document.querySelector('video');
                    if (!video) return null;

                    // 优先获取第一个source的src
                    const sources = video.querySelectorAll('source');
                    if (sources.length > 0) {
                        for (let source of sources) {
                            const src = source.src;
                            if (src && (src.includes('.mp4') || src.includes('douyinvod.com'))) {
                                return src;
                            }
                        }
                    }

                    // 如果没有source，尝试video的src
                    if (video.src && (video.src.includes('.mp4') || video.src.includes('douyinvod.com'))) {
                        return video.src;
                    }

                    return null;
                }
            """)

            if real_url:
                self.add_log(f"✅ 获取到真实视频地址: {real_url[:100]}...")
                return real_url
            else:
                self.add_log("❌ 未能获取到真实视频地址")
                return None

        except Exception as e:
            error_msg = f"获取视频真实地址失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return None

    async def collect_videos_to_database(
        self,
        account_url: str,
        account_name: str,
        account_id: str,
        collect_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """采集视频信息到数据库

        Args:
            account_url: 账号URL
            account_name: 账号名称
            account_id: 账号ID
            collect_config: 采集配置

        Returns:
            Dict: 采集结果统计
        """
        try:
            self.add_log(f"🎯 开始采集账号视频到数据库: {account_name}")

            if not self.backend_api:
                raise Exception("Backend API未配置，无法保存视频信息")

            # 获取账号信息
            account_info = await self.get_account_info(account_url)
            if not account_info or account_info.get('error'):
                raise Exception("获取账号信息失败")

            # 获取可用月份
            try:
                available_months = await self.get_available_months()
                self.add_log(f"🔍 调试信息: get_available_months() 返回类型: {type(available_months)}")
                self.add_log(f"🔍 调试信息: available_months = {available_months}")
                self.add_log(f"🔍 调试信息: len(available_months) = {len(available_months) if available_months else 0}")
                self.add_log(f"🔍 调试信息: bool(available_months) = {bool(available_months)}")
            except Exception as e:
                self.add_log(f"❌ 获取可用月份异常: {e}")
                available_months = []

            if not available_months:
                self.add_log("⚠️ 该账号没有按月份分类，使用普通方式采集")
                # 使用普通方式获取视频列表
                videos = await self.get_video_list(None, collect_config.get('max_videos', 100))
            else:
                # 按月份采集
                self.add_log(f"✅ 检测到 {len(available_months)} 个可用月份，开始按月份采集")
                target_months = collect_config.get('target_months', [])
                if not target_months:
                    # 如果没有指定月份，采集所有月份
                    target_months = available_months

                self.add_log(f"📅 将采集 {len(target_months)} 个月份的视频")

                # 🔧 简化：直接点击月份并获取视频，一步到位
                all_videos = []
                for month_info in target_months:
                    self.add_log(f"📅 开始采集月份: {month_info}")

                    # 直接点击月份元素（如果有保存的元素引用）
                    if 'element' in month_info:
                        try:
                            await month_info['element'].click()
                            self.add_log(f"🖱️ 直接点击月份: {month_info['display']}")

                            # 等待页面加载
                            await self.page.wait_for_timeout(3000)

                            # 获取该月份的视频
                            month_videos = await self.extract_month_videos(month_info['year'], month_info['month'])

                        except Exception as e:
                            self.add_log(f"❌ 点击月份失败: {e}")
                            month_videos = []
                    else:
                        # 如果没有元素引用，使用原来的方法
                        month_videos = await self.get_videos_by_month_optimized(month_info['year'], month_info['month'])

                    # 为每个视频添加账号信息
                    for video in month_videos:
                        video['account_id'] = account_id
                        video['account_name'] = account_name

                    all_videos.extend(month_videos)
                    self.add_log(f"📹 {month_info['display']} 采集到 {len(month_videos)} 个视频")

                videos = all_videos

            if not videos:
                self.add_log("❌ 未采集到任何视频")
                return {'total': 0, 'saved': 0, 'with_real_url': 0}

            self.add_log(f"📊 总共采集到 {len(videos)} 个视频，开始保存到数据库")

            # 批量保存视频基础信息到数据库
            for video in videos:
                video['account_id'] = account_id
                video['account_name'] = account_name

            saved_count = await self.backend_api.batch_save_videos(videos)
            self.add_log(f"💾 成功保存 {saved_count} 个视频基础信息到数据库")

            # 获取真实下载地址（可选）
            get_real_urls = collect_config.get('get_real_urls', False)
            real_url_count = 0

            if get_real_urls:
                self.add_log("🔗 开始获取视频真实下载地址")

                for i, video in enumerate(videos):
                    try:
                        self.add_log(f"🎬 获取真实地址 ({i+1}/{len(videos)}): {video['title'][:50]}...")

                        real_url = await self.get_video_real_url(video['video_url'])
                        if real_url:
                            # 更新数据库中的真实地址
                            await self.backend_api.update_video_real_url(video['id'], real_url)
                            real_url_count += 1
                            self.add_log(f"✅ 获取真实地址成功")
                        else:
                            self.add_log(f"❌ 获取真实地址失败")

                        # 避免请求过快
                        await asyncio.sleep(2)

                    except Exception as e:
                        self.add_log(f"❌ 获取真实地址异常: {str(e)}")
                        continue

                self.add_log(f"🔗 完成真实地址获取，成功 {real_url_count}/{len(videos)}")

            # 返回统计结果
            result = {
                'total': len(videos),
                'saved': saved_count,
                'with_real_url': real_url_count,
                'account_info': account_info,
                'available_months': available_months
            }

            self.add_log(f"🎉 视频采集完成: {result}")
            return result

        except Exception as e:
            error_msg = f"采集视频到数据库失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return {'total': 0, 'saved': 0, 'with_real_url': 0, 'error': error_msg}

    async def download_videos_from_database(
        self,
        account_id: str,
        download_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """从数据库下载视频文件

        Args:
            account_id: 账号ID
            download_config: 下载配置

        Returns:
            Dict: 下载结果统计
        """
        try:
            self.add_log(f"📥 开始从数据库下载视频文件: {account_id}")

            if not self.backend_api:
                raise Exception("Backend API未配置，无法获取视频信息")

            # 获取待下载的视频列表
            max_downloads = download_config.get('max_downloads', 50)
            pending_videos = await self.backend_api.get_pending_downloads(max_downloads)

            if not pending_videos:
                self.add_log("📭 没有待下载的视频")
                return {'total': 0, 'downloaded': 0, 'failed': 0}

            self.add_log(f"📋 找到 {len(pending_videos)} 个待下载视频")

            downloaded_count = 0
            failed_count = 0

            for i, video in enumerate(pending_videos):
                try:
                    self.add_log(f"⬇️ 下载视频 ({i+1}/{len(pending_videos)}): {video['title'][:50]}...")

                    # 更新状态为下载中
                    await self.backend_api.update_download_status(video['video_id'], 'downloading')

                    # 生成文件路径
                    filename = f"{video['video_id']}.mp4"
                    video_path = os.path.join(self.download_path, "videos", filename)

                    # 下载视频文件
                    success = await self._download_video_file(video['real_video_url'], video_path)

                    if success:
                        # 更新状态为完成
                        await self.backend_api.update_download_status(
                            video['video_id'], 'completed', video_path
                        )
                        downloaded_count += 1
                        self.add_log(f"✅ 下载成功: {filename}")
                    else:
                        # 更新状态为失败
                        await self.backend_api.update_download_status(video['video_id'], 'failed')
                        failed_count += 1
                        self.add_log(f"❌ 下载失败: {filename}")

                    # 避免请求过快
                    await asyncio.sleep(1)

                except Exception as e:
                    await self.backend_api.update_download_status(video['video_id'], 'failed')
                    failed_count += 1
                    self.add_log(f"❌ 下载异常: {str(e)}")
                    continue

            result = {
                'total': len(pending_videos),
                'downloaded': downloaded_count,
                'failed': failed_count
            }

            self.add_log(f"📥 视频下载完成: {result}")
            return result

        except Exception as e:
            error_msg = f"从数据库下载视频失败: {str(e)}"
            self.add_log(error_msg)
            logger.error(error_msg)
            return {'total': 0, 'downloaded': 0, 'failed': 0, 'error': error_msg}

    async def _download_video_file(self, video_url: str, save_path: str) -> bool:
        """下载视频文件

        Args:
            video_url: 视频URL
            save_path: 保存路径

        Returns:
            bool: 是否下载成功
        """
        try:
            # 创建目录
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            # 使用aiohttp下载文件
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.get(video_url) as response:
                    if response.status == 200:
                        with open(save_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                f.write(chunk)

                        # 验证文件
                        if os.path.exists(save_path) and os.path.getsize(save_path) > 0:
                            return True
                        else:
                            return False
                    else:
                        self.add_log(f"下载失败，HTTP状态码: {response.status}")
                        return False

        except Exception as e:
            self.add_log(f"下载文件异常: {str(e)}")
            return False

    async def cleanup(self):
        """清理资源"""
        await self._close_browser()
        await super().cleanup()
