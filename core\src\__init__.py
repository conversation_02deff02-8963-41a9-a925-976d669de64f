# core package initialization
import logging

# 🔧 移除旧的日志配置，改为使用新的分文件日志系统
# 日志配置现在由 src.config.logging_config 统一管理

# 设置core模块日志级别
logger = logging.getLogger('core')
logger.setLevel(logging.DEBUG)
logger.propagate = True

# 只有在日志系统还未初始化时才记录初始化信息
if not logger.handlers:
    # 临时使用控制台输出，直到正式的日志系统初始化
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(console_handler)
    logger.info("Core模块日志系统初始化完成")
    logger.removeHandler(console_handler)  # 移除临时处理器