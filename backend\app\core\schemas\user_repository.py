from typing import Optional
from pymongo import MongoClient
from datetime import datetime
from app.models.user import UserInDB, get_password_hash, verify_password
import logging

class UserRepository:
    def __init__(self, db):
        self.db = db
        self.collection = db.users
        
    async def get_user_by_username(self, username: str) -> Optional[UserInDB]:
        """根据用户名获取用户"""
        user = await self.collection.find_one({"username": username})
        if user:
            return UserInDB(**user)
        return None
        
    async def authenticate_user(self, username: str, password: str) -> Optional[UserInDB]:
        """验证用户凭据"""
        user = await self.get_user_by_username(username)
        if not user:
            return None
            
        if not verify_password(password, user.hashed_password):
            return None
            
        return user
        
    async def create_user(self, username: str, password: str, email: str = None) -> UserInDB:
        """创建新用户"""
        existing_user = await self.get_user_by_username(username)
        if existing_user:
            raise ValueError("Username already exists")
            
        hashed_password = get_password_hash(password)
        user_data = {
            "username": username,
            "hashed_password": hashed_password,
            "email": email,
            "disabled": False,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        result = await self.collection.insert_one(user_data)
        user_data["_id"] = result.inserted_id
        return UserInDB(**user_data)
        
    async def update_user(self, username: str, update_data: dict) -> Optional[UserInDB]:
        """更新用户信息"""
        update_data["updated_at"] = datetime.utcnow()
        result = await self.collection.update_one(
            {"username": username},
            {"$set": update_data}
        )
        
        if result.modified_count == 0:
            return None
            
        return await self.get_user_by_username(username)
        
    async def delete_user(self, username: str) -> bool:
        """删除用户"""
        result = await self.collection.delete_one({"username": username})
        return result.deleted_count > 0