from fastapi import APIRouter, HTTPException, Depends, Request
from typing import List, Optional
import logging
from app.core.schemas.proxy_models import (
    ProxyIPCreateRequest, 
    ProxyIPUpdateRequest, 
    ProxyIPResponse, 
    ProxyIPListResponse,
    ProxyIPService
)
from app.api.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/v1/proxy",
    tags=["代理IP管理"],
    dependencies=[Depends(get_current_user)]
)


def get_proxy_service(request: Request) -> ProxyIPService:
    """获取代理IP服务实例"""
    return ProxyIPService(request.app.state.mongo_db)


@router.get("/list", response_model=ProxyIPListResponse)
async def get_proxy_list(
    region: Optional[str] = None,
    status: Optional[str] = None,
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """获取代理IP列表"""
    try:
        return await proxy_service.get_proxy_ip_list(region=region, status=status)
    except Exception as e:
        logger.error(f"获取代理IP列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取代理IP列表失败")


@router.get("/{proxy_id}", response_model=ProxyIPResponse)
async def get_proxy_by_id(
    proxy_id: str,
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """根据ID获取代理IP详情"""
    try:
        proxy = await proxy_service.get_proxy_ip_by_id(proxy_id)
        if not proxy:
            raise HTTPException(status_code=404, detail="代理IP不存在")
        return proxy
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取代理IP详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取代理IP详情失败")


@router.post("/create", response_model=dict)
async def create_proxy(
    proxy_data: ProxyIPCreateRequest,
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """创建代理IP"""
    try:
        proxy_id = await proxy_service.create_proxy_ip(proxy_data)
        return {"success": True, "proxy_id": proxy_id, "message": "代理IP创建成功"}
    except Exception as e:
        logger.error(f"创建代理IP失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建代理IP失败")


@router.put("/{proxy_id}", response_model=dict)
async def update_proxy(
    proxy_id: str,
    update_data: ProxyIPUpdateRequest,
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """更新代理IP"""
    try:
        success = await proxy_service.update_proxy_ip(proxy_id, update_data)
        if not success:
            raise HTTPException(status_code=404, detail="代理IP不存在或更新失败")
        return {"success": True, "message": "代理IP更新成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新代理IP失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新代理IP失败")


@router.delete("/{proxy_id}", response_model=dict)
async def delete_proxy(
    proxy_id: str,
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """删除代理IP"""
    try:
        success = await proxy_service.delete_proxy_ip(proxy_id)
        if not success:
            raise HTTPException(status_code=404, detail="代理IP不存在或删除失败")
        return {"success": True, "message": "代理IP删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除代理IP失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除代理IP失败")


@router.post("/{proxy_id}/associate/{device_id}", response_model=dict)
async def associate_device_proxy(
    proxy_id: str,
    device_id: str,
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """关联设备和代理IP"""
    try:
        mapping_id = await proxy_service.associate_device(device_id, proxy_id)
        return {"success": True, "mapping_id": mapping_id, "message": "设备关联成功"}
    except Exception as e:
        logger.error(f"关联设备和代理IP失败: {str(e)}")
        raise HTTPException(status_code=500, detail="关联设备和代理IP失败")


@router.delete("/{proxy_id}/disassociate/{device_id}", response_model=dict)
async def disassociate_device_proxy(
    proxy_id: str,
    device_id: str,
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """取消设备和代理IP关联"""
    try:
        success = await proxy_service.disassociate_device(device_id, proxy_id)
        if not success:
            raise HTTPException(status_code=404, detail="关联关系不存在或取消失败")
        return {"success": True, "message": "取消关联成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消设备和代理IP关联失败: {str(e)}")
        raise HTTPException(status_code=500, detail="取消设备和代理IP关联失败")


@router.get("/devices/active", response_model=List[dict])
async def get_active_devices(
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """获取活跃状态的设备列表，用于关联选择"""
    try:
        devices = await proxy_service.get_active_devices()
        return devices
    except Exception as e:
        logger.error(f"获取活跃设备列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取活跃设备列表失败")


@router.get("/regions/list", response_model=List[str])
async def get_regions(
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """获取所有地区列表"""
    try:
        result = await proxy_service.get_proxy_ip_list()
        return result.regions
    except Exception as e:
        logger.error(f"获取地区列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取地区列表失败")


@router.get("/stats/summary", response_model=dict)
async def get_proxy_stats(
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """获取代理IP统计信息"""
    try:
        # 获取总体统计
        total_result = await proxy_service.get_proxy_ip_list()
        active_result = await proxy_service.get_proxy_ip_list(status="active")
        expired_result = await proxy_service.get_proxy_ip_list(status="expired")
        
        # 按地区统计
        region_stats = {}
        for region in total_result.regions:
            region_result = await proxy_service.get_proxy_ip_list(region=region)
            region_stats[region] = region_result.total
        
        return {
            "total": total_result.total,
            "active": active_result.total,
            "expired": expired_result.total,
            "regions": region_stats
        }
    except Exception as e:
        logger.error(f"获取代理IP统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取代理IP统计信息失败")


@router.post("/batch/import", response_model=dict)
async def batch_import_proxies(
    proxies: List[ProxyIPCreateRequest],
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """批量导入代理IP"""
    try:
        success_count = 0
        failed_count = 0
        errors = []
        
        for i, proxy_data in enumerate(proxies):
            try:
                await proxy_service.create_proxy_ip(proxy_data)
                success_count += 1
            except Exception as e:
                failed_count += 1
                errors.append(f"第{i+1}条记录导入失败: {str(e)}")
        
        return {
            "success": True,
            "message": f"批量导入完成，成功{success_count}条，失败{failed_count}条",
            "success_count": success_count,
            "failed_count": failed_count,
            "errors": errors
        }
    except Exception as e:
        logger.error(f"批量导入代理IP失败: {str(e)}")
        raise HTTPException(status_code=500, detail="批量导入代理IP失败")


@router.get("/expiring/list", response_model=ProxyIPListResponse)
async def get_expiring_proxies(
    days: int = 30,
    proxy_service: ProxyIPService = Depends(get_proxy_service)
):
    """获取即将到期的代理IP列表"""
    try:
        from datetime import datetime, timedelta
        
        # 获取所有代理IP
        all_proxies = await proxy_service.get_proxy_ip_list()
        
        # 筛选即将到期的代理IP
        expiring_date = datetime.now() + timedelta(days=days)
        expiring_proxies = []
        
        for proxy in all_proxies.items:
            if proxy.expire_date and proxy.expire_date <= expiring_date:
                expiring_proxies.append(proxy)
        
        return ProxyIPListResponse(
            total=len(expiring_proxies),
            items=expiring_proxies,
            regions=all_proxies.regions
        )
    except Exception as e:
        logger.error(f"获取即将到期的代理IP列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取即将到期的代理IP列表失败")
