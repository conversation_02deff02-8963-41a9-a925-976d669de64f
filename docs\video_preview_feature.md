# 视频预览功能设计文档

## 功能概述

视频预览功能为文件管理系统添加了强大的视频处理和预览能力，允许用户在不离开文件管理界面的情况下快速预览视频内容。

## 架构设计

### 三层架构

```
Frontend (Vue3)     Backend (FastAPI)     Core (Python)
     |                      |                   |
     |-- 视频预览组件        |-- API路由          |-- 视频处理服务
     |-- 缩略图显示          |-- gRPC客户端       |-- ffmpeg集成
     |-- 懒加载机制          |-- 业务逻辑         |-- 缓存管理
     |-- 缓存管理            |                   |-- gRPC服务
```

### 核心组件

#### 1. Core服务 (视频处理层)
- **VideoPreviewService**: 核心视频处理服务
  - 视频缩略图生成
  - 视频元数据提取
  - 预览片段生成
  - 本地缓存管理

- **gRPC接口**:
  - `GenerateVideoThumbnail`: 生成视频缩略图
  - `GetVideoPreviewInfo`: 获取视频预览信息
  - `GenerateVideoPreviewClip`: 生成视频预览片段

#### 2. Backend服务 (API层)
- **REST API接口**:
  - `POST /api/v1/filesystem/video/thumbnail`: 生成缩略图
  - `POST /api/v1/filesystem/video/preview-info`: 获取预览信息
  - `POST /api/v1/filesystem/video/preview-clip`: 生成预览片段
  - `POST /api/v1/filesystem/video/batch-preview-info`: 批量获取预览信息

- **gRPC客户端**: 调用Core服务的视频处理功能

#### 3. Frontend服务 (UI层)
- **VideoPreviewDialog**: 视频预览对话框组件
- **缩略图显示**: 在文件列表中显示视频缩略图
- **懒加载机制**: 使用Intersection Observer优化性能
- **缓存管理**: 前端缓存已生成的缩略图

## 功能特性

### 1. 视频缩略图
- 自动生成视频缩略图（默认320x180像素）
- 支持自定义时间点截取
- 智能缓存机制，避免重复生成
- 懒加载，只有可见视频才生成缩略图

### 2. 视频预览
- 点击缩略图或预览按钮打开预览对话框
- 显示视频基本信息（时长、分辨率、编码等）
- 支持在线播放视频
- 可生成30秒预览片段

### 3. 性能优化
- **缓存策略**: 
  - Core服务本地文件缓存
  - Frontend内存缓存
  - 基于文件路径+修改时间的缓存键
- **懒加载**: 使用Intersection Observer只处理可见视频
- **异步处理**: 缩略图生成不阻塞界面

### 4. 支持的视频格式
- MP4, AVI, MOV, MKV, WMV, FLV, M4V
- WebM, 3GP, MPG, MPEG, TS, MTS

## 使用方法

### 1. 查看视频缩略图
在文件管理界面中，视频文件会自动显示缩略图：
- 缩略图显示在文件名左侧
- 鼠标悬停时缩略图会放大
- 点击缩略图可打开预览对话框

### 2. 预览视频
- 点击文件操作列中的播放按钮
- 或点击视频缩略图
- 在预览对话框中可以：
  - 查看视频详细信息
  - 播放视频
  - 生成预览片段
  - 在新窗口打开视频

### 3. 批量操作
- 选择多个视频文件
- 系统会批量获取预览信息
- 支持批量生成缩略图

## 配置选项

### Core服务配置
```python
# 缓存目录配置
cache_dir = ".video_cache"

# 缩略图默认设置
default_thumbnail_width = 320
default_thumbnail_height = 180
default_quality = 85

# 预览片段默认设置
default_preview_duration = 30
default_output_quality = "medium"
```

### Frontend配置
```typescript
// 缓存配置
const thumbnailCache = new Map<string, string>()

// Intersection Observer配置
const observerOptions = {
  root: null,
  rootMargin: '50px',
  threshold: 0.1
}
```

## API接口文档

### 生成视频缩略图
```http
POST /api/v1/filesystem/video/thumbnail
Content-Type: application/json

{
  "video_path": "C:/videos/sample.mp4",
  "width": 320,
  "height": 180,
  "quality": 85,
  "timestamp": 10.5,
  "force_regenerate": false
}
```

### 获取视频预览信息
```http
POST /api/v1/filesystem/video/preview-info
Content-Type: application/json

{
  "video_path": "C:/videos/sample.mp4",
  "include_thumbnail": true,
  "include_detailed_metadata": false
}
```

### 生成视频预览片段
```http
POST /api/v1/filesystem/video/preview-clip
Content-Type: application/json

{
  "video_path": "C:/videos/sample.mp4",
  "start_time": 0,
  "duration": 30,
  "output_quality": "medium",
  "force_regenerate": false
}
```

## 故障排除

### 常见问题

1. **缩略图不显示**
   - 检查Core服务是否正常运行
   - 确认ffmpeg已正确安装
   - 查看浏览器控制台错误信息

2. **视频预览失败**
   - 确认视频文件格式受支持
   - 检查文件路径是否正确
   - 验证Core服务的gRPC连接

3. **性能问题**
   - 清理缓存目录
   - 调整Intersection Observer配置
   - 减少同时处理的视频数量

### 日志查看
- Core服务日志: 查看视频处理详细信息
- Backend日志: 查看API调用和gRPC通信
- Frontend控制台: 查看缓存和UI相关信息

## 未来扩展

1. **视频编辑功能**: 简单的剪辑、旋转、水印处理
2. **批量处理**: 批量生成缩略图和预览片段
3. **云存储支持**: 支持云端视频文件预览
4. **AI分析**: 视频内容智能分析和标签
5. **格式转换**: 视频格式转换功能

## 技术依赖

- **ffmpeg**: 视频处理核心引擎
- **gRPC**: 服务间通信协议
- **Vue3**: 前端框架
- **Element Plus**: UI组件库
- **FastAPI**: 后端API框架
- **Intersection Observer**: 懒加载实现
