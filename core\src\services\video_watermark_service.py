"""
视频水印检测和清除服务
支持动态水印检测、模板匹配、区域清除等功能
"""

import os
import asyncio
import subprocess
import logging
import json
import time
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path

# 尝试导入OpenCV和NumPy，如果失败则使用占位符
try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    # 创建占位符类型
    class np:
        ndarray = object
    cv2 = None

logger = logging.getLogger(__name__)


class VideoWatermarkService:
    """视频水印检测和清除服务类"""

    def __init__(self):
        """初始化视频水印服务"""
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
        self.temp_dir = "temp_watermark"
        self.detection_templates = {}  # 缓存检测模板
        
        # 创建临时目录
        os.makedirs(self.temp_dir, exist_ok=True)
        
        logger.info("视频水印服务初始化完成")

    def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """获取视频基本信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode != 0:
                logger.error(f"获取视频信息失败: {result.stderr}")
                return {}
            
            data = json.loads(result.stdout)
            
            # 提取视频流信息
            video_stream = None
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if not video_stream:
                return {}
            
            return {
                'duration': float(data.get('format', {}).get('duration', 0)),
                'width': video_stream.get('width', 0),
                'height': video_stream.get('height', 0),
                'fps': eval(video_stream.get('r_frame_rate', '0/1')),
                'codec': video_stream.get('codec_name', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"获取视频信息异常: {str(e)}")
            return {}

    def _extract_frames(self, video_path: str, sample_count: int = 10) -> List[str]:
        """从视频中提取关键帧用于分析"""
        try:
            video_info = self._get_video_info(video_path)
            if not video_info:
                return []
            
            duration = video_info['duration']
            if duration <= 0:
                return []
            
            # 计算采样间隔
            interval = max(1, duration / sample_count)
            frame_paths = []
            
            for i in range(sample_count):
                timestamp = i * interval
                frame_filename = f"frame_{i:03d}.png"
                frame_path = os.path.join(self.temp_dir, frame_filename)
                
                # 使用ffmpeg提取帧
                cmd = [
                    'ffmpeg', '-y', '-ss', str(timestamp), '-i', video_path,
                    '-vframes', '1', '-q:v', '2', frame_path
                ]
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30,
                    encoding='utf-8',
                    errors='ignore'
                )
                
                if result.returncode == 0 and os.path.exists(frame_path):
                    frame_paths.append(frame_path)
                    logger.debug(f"提取帧成功: {frame_path} (时间: {timestamp:.2f}s)")
                else:
                    logger.warning(f"提取帧失败: 时间 {timestamp:.2f}s")
            
            logger.info(f"成功提取 {len(frame_paths)} 个关键帧")
            return frame_paths
            
        except Exception as e:
            logger.error(f"提取视频帧异常: {str(e)}")
            return []

    def _detect_watermark_in_frame(self, frame_path: str, detection_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """在单个帧中检测水印"""
        try:
            if not CV2_AVAILABLE:
                logger.warning("OpenCV未安装，无法进行水印检测")
                return []

            # 读取图像
            image = cv2.imread(frame_path)
            if image is None:
                logger.warning(f"无法读取图像: {frame_path}")
                return []
            
            height, width = image.shape[:2]
            watermarks = []
            
            detection_mode = detection_config.get('detection_mode', 'auto')
            sensitivity = detection_config.get('sensitivity', 0.7)
            
            if detection_mode == 'template' and detection_config.get('template_path'):
                # 模板匹配检测
                watermarks.extend(self._template_matching_detection(
                    image, detection_config['template_path'], sensitivity
                ))
            
            elif detection_mode == 'region' and detection_config.get('detection_region'):
                # 区域检测
                watermarks.extend(self._region_based_detection(
                    image, detection_config['detection_region'], sensitivity
                ))
            
            else:
                # 自动检测
                watermarks.extend(self._auto_watermark_detection(image, sensitivity))
            
            logger.debug(f"在帧 {frame_path} 中检测到 {len(watermarks)} 个水印")
            return watermarks
            
        except Exception as e:
            logger.error(f"帧水印检测异常: {str(e)}")
            return []

    def _template_matching_detection(self, image: np.ndarray, template_path: str, sensitivity: float) -> List[Dict[str, Any]]:
        """模板匹配水印检测"""
        try:
            # 读取模板
            template = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
            if template is None:
                logger.warning(f"无法读取模板: {template_path}")
                return []
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 模板匹配
            result = cv2.matchTemplate(gray, template, cv2.TM_CCOEFF_NORMED)
            locations = np.where(result >= sensitivity)
            
            watermarks = []
            template_h, template_w = template.shape
            
            for pt in zip(*locations[::-1]):
                watermarks.append({
                    'watermark_type': 'template_match',
                    'position': f"{pt[0]},{pt[1]},{template_w},{template_h}",
                    'confidence': float(result[pt[1], pt[0]]),
                    'description': f'模板匹配水印 (置信度: {result[pt[1], pt[0]]:.3f})'
                })
            
            return watermarks
            
        except Exception as e:
            logger.error(f"模板匹配检测异常: {str(e)}")
            return []

    def _region_based_detection(self, image: np.ndarray, region_str: str, sensitivity: float) -> List[Dict[str, Any]]:
        """基于区域的水印检测"""
        try:
            # 解析区域坐标
            x, y, w, h = map(int, region_str.split(','))
            
            # 提取区域
            roi = image[y:y+h, x:x+w]
            
            # 转换为灰度图
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            
            # 计算区域的标准差，判断是否有内容
            std_dev = np.std(gray_roi)
            
            # 如果标准差超过阈值，认为有水印
            threshold = 20 * sensitivity  # 根据敏感度调整阈值
            
            if std_dev > threshold:
                return [{
                    'watermark_type': 'region_detected',
                    'position': region_str,
                    'confidence': min(1.0, std_dev / 50.0),  # 归一化置信度
                    'description': f'区域检测到内容 (标准差: {std_dev:.2f})'
                }]
            
            return []
            
        except Exception as e:
            logger.error(f"区域检测异常: {str(e)}")
            return []

    def _auto_watermark_detection(self, image: np.ndarray, sensitivity: float) -> List[Dict[str, Any]]:
        """自动水印检测"""
        try:
            watermarks = []
            height, width = image.shape[:2]
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 检测常见水印位置（四个角落和中心）
            regions = [
                (0, 0, width//4, height//4),  # 左上角
                (width*3//4, 0, width//4, height//4),  # 右上角
                (0, height*3//4, width//4, height//4),  # 左下角
                (width*3//4, height*3//4, width//4, height//4),  # 右下角
                (width//3, height//3, width//3, height//3),  # 中心区域
            ]
            
            region_names = ['左上角', '右上角', '左下角', '右下角', '中心']
            
            for i, (x, y, w, h) in enumerate(regions):
                roi = gray[y:y+h, x:x+w]
                
                # 边缘检测
                edges = cv2.Canny(roi, 50, 150)
                edge_density = np.sum(edges > 0) / (w * h)
                
                # 如果边缘密度超过阈值，可能有水印
                threshold = 0.05 * sensitivity
                
                if edge_density > threshold:
                    watermarks.append({
                        'watermark_type': 'auto_detected',
                        'position': f"{x},{y},{w},{h}",
                        'confidence': min(1.0, edge_density / 0.1),
                        'description': f'{region_names[i]}检测到可能的水印 (边缘密度: {edge_density:.4f})'
                    })
            
            return watermarks
            
        except Exception as e:
            logger.error(f"自动检测异常: {str(e)}")
            return []

    async def detect_watermark(self, video_path: str, detection_config: Dict[str, Any]) -> Dict[str, Any]:
        """检测视频中的水印"""
        try:
            start_time = time.time()
            logger.info(f"开始检测视频水印: {video_path}")
            
            # 检查文件是否存在
            if not os.path.exists(video_path):
                return {
                    'success': False,
                    'error': f'视频文件不存在: {video_path}',
                    'watermark_detected': False,
                    'watermarks': [],
                    'detection_time_ms': 0
                }
            
            # 提取关键帧
            frame_paths = self._extract_frames(video_path, sample_count=10)
            if not frame_paths:
                return {
                    'success': False,
                    'error': '无法提取视频帧',
                    'watermark_detected': False,
                    'watermarks': [],
                    'detection_time_ms': int((time.time() - start_time) * 1000)
                }
            
            # 在每个帧中检测水印
            all_watermarks = []
            watermark_counts = {}
            
            for frame_path in frame_paths:
                frame_watermarks = self._detect_watermark_in_frame(frame_path, detection_config)
                all_watermarks.extend(frame_watermarks)
                
                # 统计水印出现次数
                for wm in frame_watermarks:
                    key = f"{wm['watermark_type']}_{wm['position']}"
                    watermark_counts[key] = watermark_counts.get(key, 0) + 1
            
            # 过滤出现频率高的水印（可能是真正的水印）
            min_frequency = max(1, len(frame_paths) // 3)  # 至少在1/3的帧中出现
            confirmed_watermarks = []
            
            for wm in all_watermarks:
                key = f"{wm['watermark_type']}_{wm['position']}"
                if watermark_counts[key] >= min_frequency:
                    wm['frequency'] = watermark_counts[key]
                    wm['confidence'] = min(1.0, wm['confidence'] * (watermark_counts[key] / len(frame_paths)))
                    if wm not in confirmed_watermarks:
                        confirmed_watermarks.append(wm)
            
            # 清理临时文件
            for frame_path in frame_paths:
                try:
                    os.remove(frame_path)
                except:
                    pass
            
            detection_time = int((time.time() - start_time) * 1000)
            
            result = {
                'success': True,
                'error': '',
                'watermark_detected': len(confirmed_watermarks) > 0,
                'watermarks': confirmed_watermarks,
                'detection_result_path': '',
                'detection_time_ms': detection_time
            }
            
            logger.info(f"水印检测完成: 检测到 {len(confirmed_watermarks)} 个水印，耗时 {detection_time}ms")
            return result
            
        except Exception as e:
            logger.error(f"水印检测异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': f'水印检测异常: {str(e)}',
                'watermark_detected': False,
                'watermarks': [],
                'detection_time_ms': int((time.time() - start_time) * 1000) if 'start_time' in locals() else 0
            }

    async def remove_watermark(self, input_video_path: str, output_video_path: str, removal_config: Dict[str, Any]) -> Dict[str, Any]:
        """清除视频中的水印"""
        try:
            start_time = time.time()
            logger.info(f"开始清除视频水印: {input_video_path} -> {output_video_path}")

            # 检查输入文件
            if not os.path.exists(input_video_path):
                return {
                    'success': False,
                    'error': f'输入视频文件不存在: {input_video_path}',
                    'output_file_path': '',
                    'processing_time_ms': 0,
                    'original_file_size': 0,
                    'output_file_size': 0,
                    'removed_watermarks_count': 0
                }

            # 获取原始文件大小
            original_file_size = os.path.getsize(input_video_path)

            # 解析配置
            removal_mode = removal_config.get('removal_mode', 'auto')
            watermark_regions = removal_config.get('watermark_regions', [])
            inpaint_method = removal_config.get('inpaint_method', 'blur')
            output_quality = removal_config.get('output_quality', 'medium')
            preserve_encoding = removal_config.get('preserve_encoding', False)

            # 构建ffmpeg命令
            success = False

            if removal_mode == 'manual' and watermark_regions:
                # 手动指定区域清除
                success = await self._remove_watermark_by_regions(
                    input_video_path, output_video_path, watermark_regions,
                    inpaint_method, output_quality, preserve_encoding
                )
            elif removal_mode == 'auto':
                # 自动检测并清除
                success = await self._auto_remove_watermark(
                    input_video_path, output_video_path,
                    inpaint_method, output_quality, preserve_encoding
                )
            else:
                return {
                    'success': False,
                    'error': f'不支持的清除模式: {removal_mode}',
                    'output_file_path': '',
                    'processing_time_ms': 0,
                    'original_file_size': original_file_size,
                    'output_file_size': 0,
                    'removed_watermarks_count': 0
                }

            # 检查输出文件
            output_file_size = 0
            if success and os.path.exists(output_video_path):
                output_file_size = os.path.getsize(output_video_path)

            processing_time = int((time.time() - start_time) * 1000)

            result = {
                'success': success,
                'error': '' if success else '水印清除失败',
                'output_file_path': output_video_path if success else '',
                'processing_time_ms': processing_time,
                'original_file_size': original_file_size,
                'output_file_size': output_file_size,
                'removed_watermarks_count': len(watermark_regions) if watermark_regions else 1
            }

            if success:
                logger.info(f"水印清除完成: {output_video_path}，耗时 {processing_time}ms")
            else:
                logger.error(f"水印清除失败: {input_video_path}")

            return result

        except Exception as e:
            logger.error(f"水印清除异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': f'水印清除异常: {str(e)}',
                'output_file_path': '',
                'processing_time_ms': int((time.time() - start_time) * 1000) if 'start_time' in locals() else 0,
                'original_file_size': os.path.getsize(input_video_path) if os.path.exists(input_video_path) else 0,
                'output_file_size': 0,
                'removed_watermarks_count': 0
            }

    async def _remove_watermark_by_regions(self, input_path: str, output_path: str,
                                         regions: List[str], inpaint_method: str,
                                         output_quality: str, preserve_encoding: bool) -> bool:
        """根据指定区域清除水印"""
        try:
            # 构建ffmpeg滤镜
            filters = []

            for i, region in enumerate(regions):
                try:
                    x, y, w, h = map(int, region.split(','))

                    if inpaint_method == 'blur':
                        # 使用模糊滤镜
                        filter_str = f"[0:v]crop={w}:{h}:{x}:{y},boxblur=10:1[blurred{i}]; [0:v][blurred{i}]overlay={x}:{y}[v{i}]"
                    elif inpaint_method == 'median':
                        # 使用中值滤波
                        filter_str = f"[0:v]crop={w}:{h}:{x}:{y},median=5[filtered{i}]; [0:v][filtered{i}]overlay={x}:{y}[v{i}]"
                    else:
                        # 默认使用模糊
                        filter_str = f"[0:v]crop={w}:{h}:{x}:{y},boxblur=10:1[blurred{i}]; [0:v][blurred{i}]overlay={x}:{y}[v{i}]"

                    filters.append(filter_str)

                except ValueError:
                    logger.warning(f"无效的区域格式: {region}")
                    continue

            if not filters:
                logger.error("没有有效的水印区域")
                return False

            # 构建完整的滤镜链
            filter_complex = '; '.join(filters)

            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y', '-i', input_path]

            if filters:
                cmd.extend(['-filter_complex', filter_complex])
                cmd.extend(['-map', f'[v{len(filters)-1}]', '-map', '0:a?'])

            # 设置编码参数
            if preserve_encoding:
                cmd.extend(['-c:v', 'copy'])
            else:
                cmd.extend(['-c:v', 'libx264', '-preset', 'fast'])

                # 根据质量设置CRF
                if output_quality == 'high':
                    cmd.extend(['-crf', '18'])
                elif output_quality == 'medium':
                    cmd.extend(['-crf', '23'])
                else:
                    cmd.extend(['-crf', '28'])

            cmd.extend(['-c:a', 'aac', '-b:a', '128k'])
            cmd.append(output_path)

            logger.debug(f"执行ffmpeg命令: {' '.join(cmd[:10])}...")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"区域水印清除成功: {output_path}")
                return True
            else:
                logger.error(f"ffmpeg执行失败: {stderr.decode('utf-8', errors='ignore')}")
                return False

        except Exception as e:
            logger.error(f"区域水印清除异常: {str(e)}")
            return False

    async def _auto_remove_watermark(self, input_path: str, output_path: str,
                                   inpaint_method: str, output_quality: str,
                                   preserve_encoding: bool) -> bool:
        """自动检测并清除水印"""
        try:
            # 首先自动检测水印
            detection_config = {
                'detection_mode': 'auto',
                'sensitivity': 0.7,
                'save_detection_result': False
            }

            detection_result = await self.detect_watermark(input_path, detection_config)

            if not detection_result['success'] or not detection_result['watermark_detected']:
                logger.info("未检测到水印，直接复制文件")
                # 如果没有检测到水印，直接复制文件
                import shutil
                shutil.copy2(input_path, output_path)
                return True

            # 提取检测到的水印区域
            watermark_regions = []
            for watermark in detection_result['watermarks']:
                if watermark['confidence'] > 0.5:  # 只处理置信度较高的水印
                    watermark_regions.append(watermark['position'])

            if not watermark_regions:
                logger.info("未找到高置信度水印，直接复制文件")
                import shutil
                shutil.copy2(input_path, output_path)
                return True

            # 使用检测到的区域清除水印
            return await self._remove_watermark_by_regions(
                input_path, output_path, watermark_regions,
                inpaint_method, output_quality, preserve_encoding
            )

        except Exception as e:
            logger.error(f"自动水印清除异常: {str(e)}")
            return False

    async def batch_process_watermark(self, batch_config: Dict[str, Any]) -> Dict[str, Any]:
        """批量处理视频水印"""
        try:
            start_time = time.time()
            logger.info(f"开始批量处理视频水印")

            # 解析配置
            input_folder = batch_config.get('input_folder_path', '')
            output_folder = batch_config.get('output_folder_path', '')
            process_mode = batch_config.get('process_mode', 'detect_and_remove')
            file_filters = batch_config.get('file_filters', ['*.mp4'])
            recursive = batch_config.get('recursive', False)
            max_concurrent = batch_config.get('max_concurrent', 3)

            # 检查输入文件夹
            if not os.path.exists(input_folder):
                return {
                    'success': False,
                    'error': f'输入文件夹不存在: {input_folder}',
                    'results': [],
                    'total_files': 0,
                    'successful_files': 0,
                    'failed_files': 0,
                    'total_processing_time_ms': 0
                }

            # 创建输出文件夹
            os.makedirs(output_folder, exist_ok=True)

            # 扫描视频文件
            video_files = self._scan_video_files(input_folder, file_filters, recursive)

            if not video_files:
                return {
                    'success': True,
                    'error': '',
                    'results': [],
                    'total_files': 0,
                    'successful_files': 0,
                    'failed_files': 0,
                    'total_processing_time_ms': int((time.time() - start_time) * 1000)
                }

            logger.info(f"找到 {len(video_files)} 个视频文件待处理")

            # 批量处理
            results = []
            successful_count = 0
            failed_count = 0

            # 使用信号量控制并发数
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_single_file(video_file):
                async with semaphore:
                    return await self._process_single_video(
                        video_file, output_folder, process_mode, batch_config
                    )

            # 并发处理所有文件
            tasks = [process_single_file(video_file) for video_file in video_files]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    logger.error(f"处理文件异常: {str(result)}")
                elif result.get('status') == 'success':
                    successful_count += 1
                else:
                    failed_count += 1

            total_time = int((time.time() - start_time) * 1000)

            batch_result = {
                'success': True,
                'error': '',
                'results': [r for r in results if not isinstance(r, Exception)],
                'total_files': len(video_files),
                'successful_files': successful_count,
                'failed_files': failed_count,
                'total_processing_time_ms': total_time
            }

            logger.info(f"批量处理完成: 总计 {len(video_files)} 个文件，成功 {successful_count} 个，失败 {failed_count} 个，耗时 {total_time}ms")
            return batch_result

        except Exception as e:
            logger.error(f"批量处理异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': f'批量处理异常: {str(e)}',
                'results': [],
                'total_files': 0,
                'successful_files': 0,
                'failed_files': 0,
                'total_processing_time_ms': int((time.time() - start_time) * 1000) if 'start_time' in locals() else 0
            }

    def _scan_video_files(self, folder_path: str, file_filters: List[str], recursive: bool) -> List[str]:
        """扫描视频文件"""
        try:
            import glob

            video_files = []

            for file_filter in file_filters:
                if recursive:
                    pattern = os.path.join(folder_path, '**', file_filter)
                    files = glob.glob(pattern, recursive=True)
                else:
                    pattern = os.path.join(folder_path, file_filter)
                    files = glob.glob(pattern)

                video_files.extend(files)

            # 去重并过滤有效的视频文件
            unique_files = list(set(video_files))
            valid_files = []

            for file_path in unique_files:
                if os.path.isfile(file_path):
                    ext = os.path.splitext(file_path)[1].lower().lstrip('.')
                    if ext in self.supported_video_extensions:
                        valid_files.append(file_path)

            logger.info(f"扫描到 {len(valid_files)} 个有效视频文件")
            return valid_files

        except Exception as e:
            logger.error(f"扫描视频文件异常: {str(e)}")
            return []

    async def _process_single_video(self, video_file: str, output_folder: str,
                                  process_mode: str, batch_config: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个视频文件"""
        try:
            file_start_time = time.time()
            filename = os.path.basename(video_file)
            name, ext = os.path.splitext(filename)

            result = {
                'file_path': video_file,
                'status': 'success',
                'error_message': '',
                'detection_result': None,
                'removal_result': None,
                'processing_time_ms': 0
            }

            # 根据处理模式执行相应操作
            if process_mode in ['detect_only', 'detect_and_remove']:
                # 执行检测
                detection_config = batch_config.get('detection_config', {})
                detection_result = await self.detect_watermark(video_file, detection_config)
                result['detection_result'] = detection_result

                if not detection_result['success']:
                    result['status'] = 'failed'
                    result['error_message'] = f"检测失败: {detection_result['error']}"
                    return result

            if process_mode in ['remove_only', 'detect_and_remove']:
                # 执行清除
                output_file = os.path.join(output_folder, f"{name}_no_watermark{ext}")
                removal_config = batch_config.get('removal_config', {})

                removal_result = await self.remove_watermark(video_file, output_file, removal_config)
                result['removal_result'] = removal_result

                if not removal_result['success']:
                    result['status'] = 'failed'
                    result['error_message'] = f"清除失败: {removal_result['error']}"
                    return result

            result['processing_time_ms'] = int((time.time() - file_start_time) * 1000)
            logger.debug(f"处理文件完成: {filename}，耗时 {result['processing_time_ms']}ms")

            return result

        except Exception as e:
            logger.error(f"处理单个视频文件异常: {str(e)}")
            return {
                'file_path': video_file,
                'status': 'failed',
                'error_message': f'处理异常: {str(e)}',
                'detection_result': None,
                'removal_result': None,
                'processing_time_ms': int((time.time() - file_start_time) * 1000) if 'file_start_time' in locals() else 0
            }
