<template>
  <div class="content-list">
    <!-- 网格视图 -->
    <div v-if="viewMode === 'grid'" class="grid-view">
      <div
        v-for="item in items"
        :key="item._id"
        class="content-card"
        :class="{
          selected: selectedItems.includes(item._id!),
          'long-video': isLongVideo(item)
        }"
      >
        <!-- 选择框 -->
        <div class="card-header">
          <el-checkbox 
            :model-value="selectedItems.includes(item._id!)"
            @change="handleSelect(item._id!, $event)"
          />
          <div class="card-actions">
            <el-dropdown @command="(cmd) => handleAction(cmd, item._id!)">
              <el-button size="small" text>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="view">查看详情</el-dropdown-item>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 内容预览 -->
        <div class="card-preview" @click="$emit('view-detail', item._id)">
          <div class="preview-image">
            <el-icon class="content-type-icon" :class="`type-${item.content_type}`">
              <component :is="getContentTypeIcon(item.content_type)" />
            </el-icon>
            <div class="platform-badge">{{ item.platform }}</div>
          </div>
        </div>

        <!-- 内容信息 -->
        <div class="card-content">
          <h4 class="content-title" :title="item.title">
            {{ item.title }}
            <el-tag
              v-if="isLongVideo(item)"
              type="warning"
              size="small"
              class="long-video-tag"
            >
              长视频
            </el-tag>
          </h4>
          <p class="content-description" :title="item.description">
            {{ item.description || '暂无描述' }}
          </p>

          <div class="content-meta">
            <div class="meta-item">
              <el-icon><User /></el-icon>
              <span>{{ item.author.name }}</span>
            </div>
            <div class="meta-item">
              <el-icon><Calendar /></el-icon>
              <span>{{ formatDate(item.created_at) }}</span>
            </div>
            <div v-if="item.file_info.duration && item.content_type === 'video'" class="meta-item">
              <el-icon><VideoPlay /></el-icon>
              <span>{{ formatDuration(item.file_info.duration) }}</span>
            </div>
          </div>

          <div class="content-tags" v-if="item.metadata.tags.length > 0">
            <el-tag 
              v-for="tag in item.metadata.tags.slice(0, 3)" 
              :key="tag"
              size="small"
              type="info"
            >
              {{ tag }}
            </el-tag>
            <span v-if="item.metadata.tags.length > 3" class="more-tags">
              +{{ item.metadata.tags.length - 3 }}
            </span>
          </div>

          <div class="content-stats">
            <div class="stat-item" v-if="item.metadata.view_count">
              <el-icon><View /></el-icon>
              <span>{{ formatNumber(item.metadata.view_count) }}</span>
            </div>
            <div class="stat-item" v-if="item.metadata.like_count">
              <el-icon><Star /></el-icon>
              <span>{{ formatNumber(item.metadata.like_count) }}</span>
            </div>
            <div class="stat-item">
              <el-icon><Download /></el-icon>
              <span>{{ formatFileSize(item.file_info.file_size) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-else class="list-view">
      <el-table
        :data="items"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        :row-class-name="getRowClassName"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="内容" min-width="300">
          <template #default="{ row }">
            <div class="list-content" :class="{ 'long-video': isLongVideo(row) }">
              <div class="content-icon">
                <el-icon :class="`type-${row.content_type}`">
                  <component :is="getContentTypeIcon(row.content_type)" />
                </el-icon>
              </div>
              <div class="content-info">
                <div class="content-title">
                  {{ row.title }}
                  <el-tag
                    v-if="isLongVideo(row)"
                    type="warning"
                    size="small"
                    class="long-video-tag"
                  >
                    长视频
                  </el-tag>
                </div>
                <div class="content-description">
                  {{ row.description || '暂无描述' }}
                  <span v-if="row.file_info.duration && row.content_type === 'video'" class="video-duration-inline">
                    🎬 {{ formatDuration(row.file_info.duration) }}
                  </span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="平台" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ row.platform }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="作者" width="150">
          <template #default="{ row }">
            {{ row.author.name }}
          </template>
        </el-table-column>
        
        <el-table-column label="类型" width="80">
          <template #default="{ row }">
            <el-tag size="small" :type="getContentTypeColor(row.content_type)">
              {{ getContentTypeLabel(row.content_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.file_info.file_size) }}
          </template>
        </el-table-column>
        
        <el-table-column label="下载时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.download_info.download_date) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" text @click="$emit('view-detail', row._id)">
              查看
            </el-button>
            <el-button size="small" text @click="$emit('edit', row._id)">
              编辑
            </el-button>
            <el-button size="small" text type="danger" @click="$emit('delete', row._id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  MoreFilled,
  VideoPlay,
  Picture,
  Headphones,
  Document,
  User,
  Calendar,
  View,
  Star,
  Download
} from '@element-plus/icons-vue'
import type { CompetitorContent } from '@/api/content'
import {
  calculateVideoDurationCategory,
  getVideoDurationClass,
  isVideoFile as isVideoFileUtil,
  getVideoDurationFromFile
} from '@/utils/videoDuration'

interface Props {
  items: CompetitorContent[]
  viewMode: 'grid' | 'list'
  selectedItems: string[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  select: [itemIds: string[]]
  'view-detail': [contentId: string]
  edit: [contentId: string]
  delete: [contentId: string]
}>()

// 内容类型图标映射
const contentTypeIcons = {
  video: VideoPlay,
  image: Picture,
  audio: Headphones,
  text: Document
}

// 获取内容类型图标
const getContentTypeIcon = (type: string) => {
  return contentTypeIcons[type as keyof typeof contentTypeIcons] || Document
}

// 获取内容类型标签
const getContentTypeLabel = (type: string) => {
  const labels = {
    video: '视频',
    image: '图片',
    audio: '音频',
    text: '文本'
  }
  return labels[type as keyof typeof labels] || '未知'
}

// 获取内容类型颜色
const getContentTypeColor = (type: string) => {
  const colors = {
    video: 'primary',
    image: 'success',
    audio: 'warning',
    text: 'info'
  }
  return colors[type as keyof typeof colors] || 'info'
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化视频时长
const formatDuration = (seconds: number) => {
  if (!seconds) return '未知'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

// 判断是否为长视频（超过35秒）
const isLongVideo = (content: any) => {
  return content.content_type === 'video' && content.file_info?.duration && content.file_info.duration > 35
}

// 获取表格行的类名，用于设置整行背景色
const getRowClassName = ({ row }: { row: any }) => {
  if (row.content_type === 'video') {
    const duration = row.file_info?.duration || 0
    const durationInfo = calculateVideoDurationCategory(duration)
    return getVideoDurationClass(durationInfo.category)
  }
  return ''
}

// 处理单个选择
const handleSelect = (itemId: string, checked: boolean) => {
  const newSelection = [...props.selectedItems]
  const index = newSelection.indexOf(itemId)
  
  if (checked && index === -1) {
    newSelection.push(itemId)
  } else if (!checked && index > -1) {
    newSelection.splice(index, 1)
  }
  
  emit('select', newSelection)
}

// 处理表格选择变化
const handleSelectionChange = (selection: CompetitorContent[]) => {
  const selectedIds = selection.map(item => item._id!).filter(Boolean)
  emit('select', selectedIds)
}

// 处理操作
const handleAction = (command: string, itemId: string) => {
  switch (command) {
    case 'view':
      emit('view-detail', itemId)
      break
    case 'edit':
      emit('edit', itemId)
      break
    case 'delete':
      emit('delete', itemId)
      break
  }
}
</script>

<style scoped>
.content-list {
  width: 100%;
}

/* 网格视图样式 */
.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 4px;
}

.content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  overflow: hidden;
  border: 2px solid transparent;
}

.content-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.content-card.selected {
  border-color: #409eff;
}

.content-card.long-video {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
  border-left: 4px solid #ffc107;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.content-card.long-video:hover {
  box-shadow: 0 4px 16px rgba(255, 193, 7, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

.card-preview {
  position: relative;
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.preview-image {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-type-icon {
  font-size: 32px;
  color: white;
  opacity: 0.8;
}

.platform-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.card-content {
  padding: 16px;
}

.content-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.content-description {
  margin: 0 0 12px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #909399;
}

.content-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.more-tags {
  font-size: 12px;
  color: #909399;
}

.content-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

/* 列表视图样式 */
.list-view {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.list-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
}

.content-icon .type-video {
  color: #409eff;
}

.content-icon .type-image {
  color: #67c23a;
}

.content-icon .type-audio {
  color: #e6a23c;
}

.content-icon .type-text {
  color: #909399;
}

.list-content .content-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.long-video-tag {
  flex-shrink: 0;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.video-duration-inline {
  background: #f3e8ff;
  color: #7c3aed;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  margin-left: 8px;
}

.list-content.long-video {
  background: linear-gradient(90deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
  border-left: 4px solid #ffc107;
  padding-left: 12px;
  margin-left: -8px;
  border-radius: 4px;
}

.list-content .content-description {
  font-size: 12px;
  color: #909399;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 视频时长分类整行背景色样式 */
:deep(.el-table .video-very-short) {
  background-color: #f0f9ff !important;
}

:deep(.el-table .video-very-short:hover) {
  background-color: #e1f5fe !important;
}

:deep(.el-table .video-short) {
  background-color: #fdf6ec !important;
}

:deep(.el-table .video-short:hover) {
  background-color: #faecd8 !important;
}

:deep(.el-table .video-medium) {
  background-color: #ecf5ff !important;
}

:deep(.el-table .video-medium:hover) {
  background-color: #d9ecff !important;
}

:deep(.el-table .video-long) {
  background-color: #fef0f0 !important;
}

:deep(.el-table .video-long:hover) {
  background-color: #fde2e2 !important;
}

:deep(.el-table .video-very-long) {
  background-color: #fef0f0 !important;
  border-left: 4px solid #f56c6c !important;
}

:deep(.el-table .video-very-long:hover) {
  background-color: #fde2e2 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-view {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-card {
    margin: 0 8px;
  }
}
</style>
