<template>
  <el-dialog
    v-model="dialogVisible"
    title="内容详情"
    width="900px"
    :before-close="handleClose"
  >
    <div class="content-detail" v-loading="loading">
      <div v-if="content" class="detail-container">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="标题">
              <el-input 
                v-if="editing" 
                v-model="editForm.title" 
                placeholder="请输入标题"
              />
              <span v-else>{{ content.title }}</span>
            </el-descriptions-item>
            
            <el-descriptions-item label="平台">
              <el-tag>{{ content.platform }}</el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="内容类型">
              <el-tag :type="getContentTypeColor(content.content_type)">
                {{ getContentTypeLabel(content.content_type) }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="作者">
              {{ content.author.name }}
            </el-descriptions-item>
            
            <el-descriptions-item label="原始链接" span="2">
              <el-link :href="content.original_url" target="_blank" type="primary">
                {{ content.original_url }}
              </el-link>
            </el-descriptions-item>
            
            <el-descriptions-item label="描述" span="2">
              <el-input 
                v-if="editing" 
                v-model="editForm.description" 
                type="textarea"
                :rows="3"
                placeholder="请输入描述"
              />
              <span v-else>{{ content.description || '暂无描述' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 文件信息 -->
        <div class="info-section">
          <h3>文件信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件大小">
              {{ formatFileSize(content.file_info.file_size) }}
            </el-descriptions-item>
            
            <el-descriptions-item label="文件格式">
              {{ content.file_info.file_format }}
            </el-descriptions-item>
            
            <el-descriptions-item label="分辨率" v-if="content.file_info.resolution">
              {{ content.file_info.resolution }}
            </el-descriptions-item>
            
            <el-descriptions-item label="时长" v-if="content.file_info.duration">
              {{ formatDuration(content.file_info.duration) }}
            </el-descriptions-item>
            
            <el-descriptions-item label="本地路径" span="2">
              <el-input 
                :value="content.file_info.local_path" 
                readonly
                style="font-family: monospace"
              >
                <template #append>
                  <el-button @click="copyToClipboard(content.file_info.local_path)">
                    复制
                  </el-button>
                </template>
              </el-input>
            </el-descriptions-item>
            
            <el-descriptions-item label="文件哈希" span="2">
              <el-input 
                :value="content.file_info.hash" 
                readonly
                style="font-family: monospace"
              >
                <template #append>
                  <el-button @click="copyToClipboard(content.file_info.hash)">
                    复制
                  </el-button>
                </template>
              </el-input>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 元数据 -->
        <div class="info-section">
          <h3>元数据</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="分类">
              <el-select 
                v-if="editing" 
                v-model="editForm.metadata.category" 
                placeholder="选择分类"
                clearable
              >
                <el-option 
                  v-for="category in categories" 
                  :key="category._id"
                  :label="category.name" 
                  :value="category.name"
                />
              </el-select>
              <span v-else>{{ content.metadata.category || '未分类' }}</span>
            </el-descriptions-item>
            
            <el-descriptions-item label="语言">
              {{ content.metadata.language || '未知' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="发布时间" v-if="content.metadata.publish_date">
              {{ formatDate(content.metadata.publish_date) }}
            </el-descriptions-item>
            
            <el-descriptions-item label="下载时间">
              {{ formatDate(content.download_info.download_date) }}
            </el-descriptions-item>
            
            <el-descriptions-item label="观看数" v-if="content.metadata.view_count">
              {{ formatNumber(content.metadata.view_count) }}
            </el-descriptions-item>
            
            <el-descriptions-item label="点赞数" v-if="content.metadata.like_count">
              {{ formatNumber(content.metadata.like_count) }}
            </el-descriptions-item>
            
            <el-descriptions-item label="评论数" v-if="content.metadata.comment_count">
              {{ formatNumber(content.metadata.comment_count) }}
            </el-descriptions-item>
            
            <el-descriptions-item label="下载状态">
              <el-tag :type="getDownloadStatusColor(content.download_info.status)">
                {{ getDownloadStatusLabel(content.download_info.status) }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="标签" span="2">
              <div v-if="editing" class="tag-editor">
                <el-tag
                  v-for="tag in editForm.metadata.tags"
                  :key="tag"
                  closable
                  @close="removeTag(tag)"
                  style="margin-right: 8px; margin-bottom: 8px"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  v-if="inputVisible"
                  ref="inputRef"
                  v-model="inputValue"
                  size="small"
                  style="width: 100px"
                  @keyup.enter="handleInputConfirm"
                  @blur="handleInputConfirm"
                />
                <el-button v-else size="small" @click="showInput">
                  + 新标签
                </el-button>
              </div>
              <div v-else class="tag-list">
                <el-tag 
                  v-for="tag in content.metadata.tags" 
                  :key="tag"
                  style="margin-right: 8px; margin-bottom: 8px"
                >
                  {{ tag }}
                </el-tag>
                <span v-if="content.metadata.tags.length === 0" class="no-tags">
                  暂无标签
                </span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 分析数据 -->
        <div class="info-section" v-if="content.analysis">
          <h3>分析数据</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="情感分析" v-if="content.analysis.sentiment">
              <el-tag :type="getSentimentColor(content.analysis.sentiment)">
                {{ getSentimentLabel(content.analysis.sentiment) }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="互动率" v-if="content.analysis.engagement_rate">
              {{ (content.analysis.engagement_rate * 100).toFixed(2) }}%
            </el-descriptions-item>
            
            <el-descriptions-item label="关键词" span="2" v-if="content.analysis.keywords.length > 0">
              <div class="keyword-list">
                <el-tag 
                  v-for="keyword in content.analysis.keywords" 
                  :key="keyword"
                  type="info"
                  size="small"
                  style="margin-right: 8px; margin-bottom: 8px"
                >
                  {{ keyword }}
                </el-tag>
              </div>
            </el-descriptions-item>
            
            <el-descriptions-item label="主题" span="2" v-if="content.analysis.topics.length > 0">
              <div class="topic-list">
                <el-tag 
                  v-for="topic in content.analysis.topics" 
                  :key="topic"
                  type="warning"
                  size="small"
                  style="margin-right: 8px; margin-bottom: 8px"
                >
                  {{ topic }}
                </el-tag>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="!editing" @click="startEdit">编辑</el-button>
        <template v-else>
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveEdit" :loading="saving">保存</el-button>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getContentDetail, 
  updateContent,
  getCategories,
  type CompetitorContent,
  type ContentCategory 
} from '@/api/content'

interface Props {
  modelValue: boolean
  contentId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  updated: []
}>()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const editing = ref(false)
const content = ref<CompetitorContent | null>(null)
const categories = ref<ContentCategory[]>([])
const inputVisible = ref(false)
const inputValue = ref('')
const inputRef = ref()

// 编辑表单
const editForm = reactive({
  title: '',
  description: '',
  metadata: {
    category: '',
    tags: [] as string[]
  }
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const loadContent = async () => {
  if (!props.contentId) return
  
  loading.value = true
  try {
    content.value = await getContentDetail(props.contentId)
    resetEditForm()
  } catch (error) {
    console.error('加载内容详情失败:', error)
    ElMessage.error('加载内容详情失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    categories.value = await getCategories()
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const resetEditForm = () => {
  if (!content.value) return
  
  editForm.title = content.value.title
  editForm.description = content.value.description || ''
  editForm.metadata.category = content.value.metadata.category || ''
  editForm.metadata.tags = [...content.value.metadata.tags]
}

const startEdit = () => {
  editing.value = true
  resetEditForm()
}

const cancelEdit = () => {
  editing.value = false
  resetEditForm()
}

const saveEdit = async () => {
  if (!content.value) return
  
  saving.value = true
  try {
    const updateData = {
      title: editForm.title,
      description: editForm.description,
      metadata: {
        ...content.value.metadata,
        category: editForm.metadata.category,
        tags: editForm.metadata.tags
      }
    }
    
    await updateContent(content.value._id!, updateData)
    ElMessage.success('内容更新成功')
    
    // 重新加载内容
    await loadContent()
    editing.value = false
    emit('updated')
    
  } catch (error) {
    console.error('更新内容失败:', error)
    ElMessage.error('更新内容失败')
  } finally {
    saving.value = false
  }
}

// 标签编辑
const removeTag = (tag: string) => {
  const index = editForm.metadata.tags.indexOf(tag)
  if (index > -1) {
    editForm.metadata.tags.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !editForm.metadata.tags.includes(inputValue.value)) {
    editForm.metadata.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 工具方法
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const getContentTypeLabel = (type: string) => {
  const labels = { video: '视频', image: '图片', audio: '音频', text: '文本' }
  return labels[type as keyof typeof labels] || '未知'
}

const getContentTypeColor = (type: string) => {
  const colors = { video: 'primary', image: 'success', audio: 'warning', text: 'info' }
  return colors[type as keyof typeof colors] || 'info'
}

const getDownloadStatusLabel = (status: string) => {
  const labels = { downloaded: '已下载', processing: '处理中', failed: '失败' }
  return labels[status as keyof typeof labels] || status
}

const getDownloadStatusColor = (status: string) => {
  const colors = { downloaded: 'success', processing: 'primary', failed: 'danger' }
  return colors[status as keyof typeof colors] || 'info'
}

const getSentimentLabel = (sentiment: string) => {
  const labels = { positive: '积极', negative: '消极', neutral: '中性' }
  return labels[sentiment as keyof typeof labels] || sentiment
}

const getSentimentColor = (sentiment: string) => {
  const colors = { positive: 'success', negative: 'danger', neutral: 'info' }
  return colors[sentiment as keyof typeof colors] || 'info'
}

const handleClose = () => {
  editing.value = false
  dialogVisible.value = false
}

// 监听器
watch(() => props.contentId, (newId) => {
  if (newId && dialogVisible.value) {
    loadContent()
  }
})

watch(dialogVisible, (visible) => {
  if (visible && props.contentId) {
    loadContent()
    loadCategories()
  }
})
</script>

<style scoped>
.content-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-container {
  padding: 4px;
}

.info-section {
  margin-bottom: 24px;
}

.info-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.tag-editor {
  min-height: 32px;
}

.tag-list {
  min-height: 24px;
}

.no-tags {
  color: #909399;
  font-style: italic;
}

.keyword-list,
.topic-list {
  line-height: 1.8;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  width: 120px;
}

:deep(.el-descriptions__content) {
  word-break: break-all;
}

/* 滚动条样式 */
.content-detail::-webkit-scrollbar {
  width: 6px;
}

.content-detail::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-detail::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-detail::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
