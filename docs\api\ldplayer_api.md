# 雷电模拟器命令行工具API文档

本文档整合了雷电模拟器官方文档中的API信息，用于优化设备同步服务。

-https://www.ldmnq.com/forum/30.html

-https://www.ldmnq.com/forum/9178.html

## 命令行工具

雷电模拟器提供了两种命令行工具：

1. `ldconsole.exe` - 主要的命令行工具，用于管理模拟器
2. `ld.exe` - 更稳定的命令行工具，可替代adb shell命令，不会出现断线问题

## ldconsole.exe 命令

### 获取设备列表

```bash
ldconsole.exe list
```

返回所有模拟器的序号，以空格分隔。

```bash
ldconsole.exe list2
```

返回所有模拟器的详细信息，格式为CSV：
`序号,标题,顶层窗口句柄,绑定窗口句柄,是否进入android,进程PID,VBox进程PID`

### 启动模拟器

```bash
ldconsole.exe launch --index <index>
```

启动序号为`<index>`的模拟器。

### 关闭模拟器

```bash
ldconsole.exe quit --index <index>
```

关闭序号为`<index>`的模拟器。

### 重启模拟器

```bash
ldconsole.exe reboot --index <index>
```

重启序号为`<index>`的模拟器。

### 批量操作

```bash
ldconsole.exe launchall
```

启动所有模拟器。

```bash
ldconsole.exe quitall
```

关闭所有模拟器。

### 查询模拟器状态

```bash
ldconsole.exe isrunning --index <index>
```

查询序号为`<index>`的模拟器是否在运行，返回`1`表示正在运行，`0`表示未运行。

```bash
ldconsole.exe runninglist
```

返回正在运行的模拟器序号，以空格分隔。

## 高级命令

### 获取模拟器属性

```bash
ldconsole.exe getprop --index <index> --key <key>
```

获取序号为`<index>`的模拟器的属性`<key>`的值。

常用属性：

- `resolution.width`: 分辨率宽度
- `resolution.height`: 分辨率高度
- `display.dpi`: DPI
- `android.version`: Android版本
- `imei`: IMEI
- `mac.address`: MAC地址
- `phone.number`: 手机号码

### 设置模拟器属性

```bash
ldconsole.exe setprop --index <index> --key <key> --value <value>
```

设置序号为`<index>`的模拟器的属性`<key>`的值为`<value>`。

### 安装APK

```bash
ldconsole.exe installapp --index <index> --filename <apk_path>
```

在序号为`<index>`的模拟器上安装APK文件。

### 卸载应用

```bash
ldconsole.exe uninstallapp --index <index> --packagename <package_name>
```

在序号为`<index>`的模拟器上卸载包名为`<package_name>`的应用。

### 运行应用

```bash
ldconsole.exe runapp --index <index> --packagename <package_name>
```

在序号为`<index>`的模拟器上运行包名为`<package_name>`的应用。

### 关闭应用

```bash
ldconsole.exe killapp --index <index> --packagename <package_name>
```

在序号为`<index>`的模拟器上关闭包名为`<package_name>`的应用。

### 获取已安装应用列表

```bash
ldconsole.exe adb --index <index> --command "shell pm list packages"
```

获取序号为`<index>`的模拟器上已安装的应用列表。

### 执行ADB命令

```bash
ldconsole.exe adb --index <index> --command "<command>"
```

在序号为`<index>`的模拟器上执行ADB命令`<command>`。

## ld.exe 命令

ld.exe是一个更稳定的命令行工具，可以替代adb shell命令，不会出现断线问题。

### 基本用法

```bash
ld -s <index> <command>
```

其中`<index>`是模拟器序号，`<command>`是要执行的命令。

也可以直接使用：

```bash
ld <command>
```

默认对第一个模拟器执行命令。

### 常用命令

#### 包管理命令

```bash
ld pm list packages
```

获取所有已安装的包名。

```bash
ld pm list packages -f
```

获取所有已安装的包名及其APK路径。

```bash
ld pm path <packageName>
```

获取指定包名的APK路径。

```bash
ld pm clear <packageName>
```

清理应用数据。

```bash
ld pm install <path>
```

安装应用。

```bash
ld pm uninstall <packageName>
```

卸载应用。

#### 输入模拟命令

```bash
ld input text <text>
```

模拟文本输入。

```bash
ld input keyevent <keycode>
```

模拟按键。

```bash
ld input tap <x> <y>
```

模拟点击屏幕坐标。

```bash
ld input swipe <x1> <y1> <x2> <y2>
```

模拟滑动屏幕。

#### 属性命令

```bash
ld getprop <key>
```

获取属性值。

```bash
ld setprop <key> <value>
```

设置属性值。

常用属性设置：

```bash
ld setprop phone.imei <imei>
ld setprop phone.imsi <imsi>
ld setprop ro.product.manufacturer <manufacturer>
ld setprop ro.product.model <model>
ld setprop phone.linenum <phone_number>
ld setprop phone.simserial <sim_serial>
ld setprop phone.androidid <android_id>
```

## 设备同步服务优化建议

基于雷电模拟器提供的API，我们可以对设备同步服务进行以下优化：

### 批量管理优化

1. 使用`list2`命令一次性获取所有设备的详细信息，包括运行状态、PID等
2. 使用`runninglist`命令获取所有正在运行的设备列表，避免多次调用`isrunning`
3. 使用`launchall`和`quitall`命令批量启动和关闭设备

### 性能优化

1. 减少单个设备的查询次数，尽量批量获取信息
2. 使用异步操作处理多个设备
3. 实现本地缓存，避免频繁查询相同信息
4. 对于状态监控，使用事件驱动而非轮询方式
5. 使用`list2`和`runninglist`命令替代多次调用`isrunning`命令

### 稳定性优化

1. 使用更稳定的`ld.exe`替代`adb shell`命令，避免断线问题
2. 对于需要获取设备详细信息的场景，可以使用`ld getprop`获取设备属性
3. 实现错误重试机制，提高系统稳定性

### 同步流程优化

1. 先使用`list2`获取所有设备基本信息
2. 使用`runninglist`确定哪些设备正在运行
3. 只对运行中的设备获取更详细的信息
4. 对于未运行的设备，使用基本信息构建设备状态
5. 将设备状态变更推送到Redis或其他消息队列
