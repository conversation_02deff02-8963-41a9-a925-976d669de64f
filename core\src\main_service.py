"""
Core主服务
负责管理设备和服务生命周期
"""

import os
import logging
import yaml
import asyncio
from typing import Dict, List, Any, Optional

from src.devices.ldplayer.manager import LDPlayerManager
from src.devices.base import DeviceInfo
from src.services.task_executor import TaskExecutor
from src.services.task_scheduler import TaskScheduler
from src.downloaders.download_task_executor import DownloadTaskExecutor

logger = logging.getLogger(__name__)

class CoreMainService:
    """Core主服务类"""

    def __init__(self, config_path: Optional[str] = None, settings = None):
        """初始化Core主服务

        Args:
            config_path: 配置文件路径
            settings: 配置对象，如果提供则优先使用
        """
        self.config_path = config_path
        self.settings = settings  # 使用传入的settings对象
        self.ldplayer_manager = None
        self.task_executor = None
        self.task_scheduler = None  # 任务调度器
        self.download_task_executor = None  # 下载任务执行器
        self.video_collect_api = None  # 🔧 新增：视频采集API
        self.is_initialized = False
        self.device_configs = {}

        logger.info("Core主服务初始化")

    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 加载配置
            await self._load_config()

            # 从settings获取ldconsole_path（如果可用）
            ldconsole_path = None
            if self.settings:
                ldconsole_path = self.settings.ldconsole_path
                logger.info(f"从settings获取ldconsole_path: {ldconsole_path}")
            else:
                # 否则从device_configs获取
                ldconsole_path = self.device_configs.get('ldconsole_path')
                logger.info(f"从device_configs获取ldconsole_path: {ldconsole_path}")

            if not ldconsole_path:
                logger.error("配置中缺少ldconsole_path")
                return False

            # 检查ldconsole.exe是否存在
            if not os.path.exists(ldconsole_path):
                logger.error(f"ldconsole.exe不存在: {ldconsole_path}")
                logger.info("请在core_config.yaml中配置正确的ldconsole_path路径")
                return False

            self.ldplayer_manager = LDPlayerManager(ldconsole_path)
            init_success = await self.ldplayer_manager.initialize()
            if not init_success:
                logger.error("初始化雷电模拟器管理器失败")
                return False

            # 初始化任务执行器
            self.task_executor = TaskExecutor(self)
            task_executor_init = await self.task_executor.initialize()
            if not task_executor_init:
                logger.error("初始化任务执行器失败")
                return False

            # 初始化任务调度器
            self.task_scheduler = TaskScheduler(self)
            task_scheduler_init = await self.task_scheduler.initialize()
            if not task_scheduler_init:
                logger.error("初始化任务调度器失败")
                return False

            # 初始化下载任务执行器
            self.download_task_executor = DownloadTaskExecutor(self)
            logger.info("下载任务执行器初始化完成")

            # 🔧 新增：初始化视频采集API
            from src.api.video_collect_api import VideoCollectAPI
            self.video_collect_api = VideoCollectAPI(self.settings)
            logger.info("视频采集API初始化完成")

            self.is_initialized = True
            logger.info("Core主服务初始化完成")
            return True

        except Exception as e:
            logger.error(f"初始化Core主服务异常: {str(e)}", exc_info=True)
            return False

    async def _load_config(self) -> None:
        """加载配置"""
        # 默认配置
        self.device_configs = {
            'ldconsole_path': 'C:\\LDPlayer\\LDPlayer9\\ldconsole.exe',
            'auto_start_devices': []
        }

        # 如果提供了配置文件，从文件加载配置
        if self.config_path and os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                if config and isinstance(config, dict):
                    # 从devices部分获取配置
                    if 'devices' in config:
                        devices_config = config['devices']
                        if 'ldconsole_path' in devices_config:
                            self.device_configs['ldconsole_path'] = devices_config['ldconsole_path']
                        if 'auto_start_devices' in devices_config:
                            self.device_configs['auto_start_devices'] = devices_config['auto_start_devices']

                logger.info(f"从文件{self.config_path}加载配置成功")

            except Exception as e:
                logger.error(f"从文件{self.config_path}加载配置失败: {str(e)}", exc_info=True)

    async def get_device_list(self) -> List[DeviceInfo]:
        """获取设备列表"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return []

        return await self.ldplayer_manager.get_all_devices()

    async def start_device(self, device_id: str) -> bool:
        """启动设备"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return False

        return await self.ldplayer_manager.start_device(device_id)

    async def stop_device(self, device_id: str) -> bool:
        """停止设备"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return False

        return await self.ldplayer_manager.stop_device(device_id)

    async def restart_device(self, device_id: str) -> bool:
        """重启设备"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return False

        return await self.ldplayer_manager.restart_device(device_id)

    async def execute_device_command(self, device_id: str, command: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行设备命令"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return {"success": False, "error": "Core主服务未初始化"}

        device = self.ldplayer_manager.devices.get(device_id)
        if not device:
            logger.error(f"设备{device_id}不存在")
            return {"success": False, "error": f"设备{device_id}不存在"}

        return await device.execute_command(command, params or {})

    async def create_task(self, task_data: Dict[str, Any]) -> bool:
        """创建任务"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return False

        return await self.task_executor.create_task(task_data)

    async def start_task(self, task_id: str) -> bool:
        """开始执行任务"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return False

        return await self.task_executor.start_task(task_id)

    async def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return False

        return await self.task_executor.pause_task(task_id)

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return False

        return await self.task_executor.cancel_task(task_id)

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return None

        return await self.task_executor.get_task_status(task_id)

    async def get_task_logs(self, task_id: str) -> List[Dict[str, Any]]:
        """获取任务日志"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return []

        return await self.task_executor.get_task_logs(task_id)

    # 任务调度器相关方法
    async def create_scheduled_task(self, task_config: Dict[str, Any]) -> str:
        """创建调度任务"""
        if not self.is_initialized or not self.task_scheduler:
            logger.error("Core主服务或任务调度器未初始化")
            raise Exception("Core主服务或任务调度器未初始化")

        return await self.task_scheduler.create_scheduled_task(task_config)

    async def pause_scheduled_task(self, task_id: str) -> bool:
        """暂停调度任务"""
        if not self.is_initialized or not self.task_scheduler:
            logger.error("Core主服务或任务调度器未初始化")
            return False

        return await self.task_scheduler.pause_scheduled_task(task_id)

    async def resume_scheduled_task(self, task_id: str) -> bool:
        """恢复调度任务"""
        if not self.is_initialized or not self.task_scheduler:
            logger.error("Core主服务或任务调度器未初始化")
            return False

        return await self.task_scheduler.resume_scheduled_task(task_id)

    async def delete_scheduled_task(self, task_id: str) -> bool:
        """删除调度任务"""
        if not self.is_initialized or not self.task_scheduler:
            logger.error("Core主服务或任务调度器未初始化")
            return False

        return await self.task_scheduler.delete_scheduled_task(task_id)

    async def get_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """获取所有调度任务"""
        if not self.is_initialized or not self.task_scheduler:
            logger.error("Core主服务或任务调度器未初始化")
            return []

        return await self.task_scheduler.get_scheduled_tasks()

    # 下载任务相关方法
    async def execute_download_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """执行下载任务"""
        if not self.is_initialized or not self.download_task_executor:
            logger.error("Core主服务或下载任务执行器未初始化")
            return False

        # 定义回调函数
        def add_task_log(task_id: str, level: str, message: str):
            logger.info(f"[{task_id}] {level}: {message}")

        def publish_task_status(task_id: str, status: str, message: str, extra_data: Dict[str, Any] = None):
            logger.info(f"[{task_id}] 状态: {status} - {message}")
            if extra_data:
                logger.info(f"[{task_id}] 额外数据: {extra_data}")

        return await self.download_task_executor.execute_download_task(
            task_id, task_data, add_task_log, publish_task_status
        )

    async def execute_batch_download_task(self, batch_id: str, tasks: List[Dict[str, Any]]) -> bool:
        """执行批量下载任务"""
        if not self.is_initialized or not self.download_task_executor:
            logger.error("Core主服务或下载任务执行器未初始化")
            return False

        # 定义回调函数
        def add_task_log(task_id: str, level: str, message: str):
            logger.info(f"[{task_id}] {level}: {message}")

        def publish_task_status(task_id: str, status: str, message: str, extra_data: Dict[str, Any] = None):
            logger.info(f"[{task_id}] 状态: {status} - {message}")
            if extra_data:
                logger.info(f"[{task_id}] 额外数据: {extra_data}")

        return await self.download_task_executor.execute_batch_download_task(
            batch_id, tasks, add_task_log, publish_task_status
        )

    async def cancel_download_task(self, task_id: str) -> bool:
        """取消下载任务"""
        if not self.is_initialized or not self.download_task_executor:
            logger.error("Core主服务或下载任务执行器未初始化")
            return False

        return await self.download_task_executor.cancel_download_task(task_id)

    # 采集任务相关方法
    async def execute_collect_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """执行采集任务"""
        try:
            logger.info(f"开始执行采集任务: {task_id}")

            # 🔧 重要：从params中获取任务参数
            params = task_data.get("params", {})
            account_url = params.get("account_url", task_data.get("account_url", ""))
            account_name = params.get("account_name", task_data.get("account_name", ""))
            account_id = task_data.get("account_id", "")
            platform = params.get("platform", task_data.get("platform", "douyin"))

            logger.info(f"🔍 采集任务参数详情:")
            logger.info(f"  - account_url: '{account_url}' (长度: {len(account_url)})")
            logger.info(f"  - account_name: '{account_name}' (长度: {len(account_name)})")
            logger.info(f"  - account_id: '{account_id}' (长度: {len(account_id)})")
            logger.info(f"  - platform: '{platform}' (长度: {len(platform)})")
            logger.info(f"🔍 原始任务数据: {task_data}")

            # 🔧 重要：使用真正的视频采集API
            if self.video_collect_api:
                logger.info("使用视频采集API执行采集任务")

                # 调用视频采集API
                result = await self.video_collect_api.start_collect_task(
                    task_id=task_id,
                    account_url=account_url,
                    account_name=account_name,
                    account_id=account_id,
                    collect_mode="basic_collect"  # 基础采集模式
                )

                if result.get("success"):
                    logger.info(f"采集任务{task_id}执行成功")
                    return True
                else:
                    logger.error(f"采集任务{task_id}执行失败: {result.get('error', 'Unknown error')}")
                    return False
            else:
                # 模拟采集任务执行
                logger.warning("视频采集API未初始化，使用模拟执行")

                # 模拟采集过程
                await asyncio.sleep(3)  # 模拟采集时间

                logger.info(f"模拟采集任务{task_id}执行完成")
                return True

        except Exception as e:
            logger.error(f"执行采集任务{task_id}异常: {str(e)}", exc_info=True)
            return False

    def get_active_download_tasks(self) -> List[str]:
        """获取活跃的下载任务列表"""
        if not self.is_initialized or not self.download_task_executor:
            return []

        return self.download_task_executor.get_active_tasks()

    def get_download_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取下载任务状态"""
        if not self.is_initialized or not self.download_task_executor:
            return None

        return self.download_task_executor.get_task_status(task_id)

    async def shutdown(self) -> None:
        """关闭服务"""
        if self.ldplayer_manager:
            try:
                await self.ldplayer_manager.shutdown()
            except Exception as e:
                logger.error(f"关闭雷电模拟器管理器异常: {str(e)}", exc_info=True)

        if self.task_executor:
            try:
                await self.task_executor.shutdown()
            except Exception as e:
                logger.error(f"关闭任务执行器异常: {str(e)}", exc_info=True)

        if self.task_scheduler:
            try:
                await self.task_scheduler.shutdown()
            except Exception as e:
                logger.error(f"关闭任务调度器异常: {str(e)}", exc_info=True)

        if self.download_task_executor:
            try:
                # 取消所有活跃的下载任务
                active_tasks = self.download_task_executor.get_active_tasks()
                for task_id in active_tasks:
                    await self.download_task_executor.cancel_download_task(task_id)
                logger.info("下载任务执行器已关闭")
            except Exception as e:
                logger.error(f"关闭下载任务执行器异常: {str(e)}", exc_info=True)

        # 🔧 新增：清理视频采集API
        if self.video_collect_api:
            try:
                # 取消所有活跃的采集任务
                for task_id in list(self.video_collect_api.active_tasks.keys()):
                    await self.video_collect_api.cancel_task(task_id)
                logger.info("视频采集API已关闭")
            except Exception as e:
                logger.error(f"关闭视频采集API异常: {str(e)}", exc_info=True)

        self.is_initialized = False
        logger.info("Core主服务已关闭")

    async def stop(self) -> None:
        """停止服务（用于热重载）"""
        logger.info("正在停止Core主服务（热重载）...")
        await self.shutdown()
        logger.info("Core主服务已停止（热重载）")

