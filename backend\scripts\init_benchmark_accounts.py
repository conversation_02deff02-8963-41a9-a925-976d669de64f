#!/usr/bin/env python3
"""
对标账号数据初始化脚本
用于创建测试的对标账号数据
"""

import sys
import os
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings

def init_benchmark_accounts():
    """初始化对标账号数据"""
    
    # 连接数据库
    client = MongoClient(settings.MONGODB_URL)
    db = client[settings.MONGODB_DB_NAME]
    
    print("开始初始化对标账号数据...")
    
    # 1. 首先检查是否有社交账号数据
    social_accounts = list(db.social_accounts.find({}, {"_id": 1, "id": 1, "username": 1, "display_name": 1, "platform_id": 1}))
    print(f"找到 {len(social_accounts)} 个社交账号:")
    
    for account in social_accounts:
        print(f"  - ID: {account.get('id', 'N/A')}, 用户名: {account.get('username', 'N/A')}, 显示名: {account.get('display_name', 'N/A')}, 平台: {account.get('platform_id', 'N/A')}")
    
    if not social_accounts:
        print("没有找到社交账号数据，请先添加社交账号")
        return
    
    # 2. 检查是否已有对标账号数据
    existing_benchmark_accounts = db.benchmark_accounts.count_documents({})
    print(f"当前对标账号数量: {existing_benchmark_accounts}")
    
    if existing_benchmark_accounts > 0:
        choice = input("已存在对标账号数据，是否要清空并重新创建? (y/n): ")
        if choice.lower() == 'y':
            result = db.benchmark_accounts.delete_many({})
            print(f"已删除 {result.deleted_count} 个对标账号")
        else:
            print("保留现有数据，退出...")
            return
    
    # 3. 为每个社交账号创建一些测试的对标账号
    benchmark_accounts_data = []
    
    for account in social_accounts:
        our_account_id = account.get('id')
        platform_id = account.get('platform_id', 'youtube')
        
        if not our_account_id:
            print(f"跳过账号 {account.get('username', 'Unknown')}，因为没有ID")
            continue
        
        # 根据平台创建不同的对标账号
        if platform_id == 'youtube' or platform_id == '681efeeecd836bd64b9c2a1e':
            benchmark_accounts_data.extend([
                {
                    "our_account_id": our_account_id,
                    "platform": "YouTube",
                    "account_name": "MrBeast",
                    "account_url": "https://www.youtube.com/@MrBeast",
                    "benchmark_type": "original",
                    "description": "知名YouTube创作者，以慈善和挑战视频著称",
                    "tags": ["慈善", "挑战", "娱乐"],
                    "priority": 5,
                    "status": "active",
                    "account_data": {
                        "followers": *********,
                        "following": 0,
                        "posts_count": 800,
                        "avg_views": ********,
                        "avg_likes": 2000000,
                        "engagement_rate": 0.04,
                        "last_post_date": datetime.now(),
                        "growth_rate": 0.15
                    },
                    "created_at": datetime.now(),
                    "updated_at": datetime.now(),
                    "created_by": "system"
                },
                {
                    "our_account_id": our_account_id,
                    "platform": "YouTube",
                    "account_name": "PewDiePie",
                    "account_url": "https://www.youtube.com/@PewDiePie",
                    "benchmark_type": "original",
                    "description": "游戏和娱乐内容创作者",
                    "tags": ["游戏", "娱乐", "评论"],
                    "priority": 4,
                    "status": "active",
                    "account_data": {
                        "followers": *********,
                        "following": 0,
                        "posts_count": 4500,
                        "avg_views": 3000000,
                        "avg_likes": 200000,
                        "engagement_rate": 0.067,
                        "last_post_date": datetime.now(),
                        "growth_rate": 0.02
                    },
                    "created_at": datetime.now(),
                    "updated_at": datetime.now(),
                    "created_by": "system"
                }
            ])
        
        elif platform_id == 'tiktok' or platform_id == '681efeeecd836bd64b9c2a20':
            benchmark_accounts_data.extend([
                {
                    "our_account_id": our_account_id,
                    "platform": "TikTok",
                    "account_name": "charlidamelio",
                    "account_url": "https://www.tiktok.com/@charlidamelio",
                    "benchmark_type": "original",
                    "description": "舞蹈和生活方式内容创作者",
                    "tags": ["舞蹈", "生活", "时尚"],
                    "priority": 5,
                    "status": "active",
                    "account_data": {
                        "followers": *********,
                        "following": 1500,
                        "posts_count": 2000,
                        "avg_views": ********,
                        "avg_likes": 1000000,
                        "engagement_rate": 0.1,
                        "last_post_date": datetime.now(),
                        "growth_rate": 0.05
                    },
                    "created_at": datetime.now(),
                    "updated_at": datetime.now(),
                    "created_by": "system"
                }
            ])
        
        elif platform_id == 'douyin' or platform_id == '681efeeecd836bd64b9c2a22':
            benchmark_accounts_data.extend([
                {
                    "our_account_id": our_account_id,
                    "platform": "抖音",
                    "account_name": "李子柒",
                    "account_url": "https://www.douyin.com/user/liziqi",
                    "benchmark_type": "original",
                    "description": "传统文化和美食内容创作者",
                    "tags": ["美食", "传统文化", "田园生活"],
                    "priority": 5,
                    "status": "active",
                    "account_data": {
                        "followers": ********,
                        "following": 100,
                        "posts_count": 500,
                        "avg_views": 5000000,
                        "avg_likes": 500000,
                        "engagement_rate": 0.1,
                        "last_post_date": datetime.now(),
                        "growth_rate": 0.03
                    },
                    "created_at": datetime.now(),
                    "updated_at": datetime.now(),
                    "created_by": "system"
                }
            ])
        
        # 为所有平台添加一个通用的对标账号
        benchmark_accounts_data.append({
            "our_account_id": our_account_id,
            "platform": "通用",
            "account_name": "测试对标账号",
            "account_url": "https://example.com/test-account",
            "benchmark_type": "recreate",
            "description": "这是一个测试用的对标账号",
            "tags": ["测试", "示例"],
            "priority": 3,
            "status": "active",
            "account_data": {
                "followers": 10000,
                "following": 500,
                "posts_count": 100,
                "avg_views": 1000,
                "avg_likes": 100,
                "engagement_rate": 0.1,
                "last_post_date": datetime.now(),
                "growth_rate": 0.05
            },
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "created_by": "system"
        })
    
    # 4. 插入对标账号数据
    if benchmark_accounts_data:
        result = db.benchmark_accounts.insert_many(benchmark_accounts_data)
        print(f"成功创建 {len(result.inserted_ids)} 个对标账号")
        
        # 5. 验证数据
        print("\n创建的对标账号:")
        for account in db.benchmark_accounts.find({}):
            print(f"  - {account['account_name']} ({account['platform']}) -> 关联账号ID: {account['our_account_id']}")
    else:
        print("没有创建任何对标账号")
    
    print("对标账号数据初始化完成!")

if __name__ == "__main__":
    init_benchmark_accounts()
